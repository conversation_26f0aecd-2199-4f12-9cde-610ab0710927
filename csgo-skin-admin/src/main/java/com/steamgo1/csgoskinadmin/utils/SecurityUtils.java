package com.steamgo1.csgoskinadmin.utils;

import cn.hutool.jwt.JWT;
import com.steamgo1.csgoskinadmin.config.jwt.JwtProvider;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * 安全框架工具类封装
 *
 * <AUTHOR>
 */
public class SecurityUtils {

    private static final BCryptPasswordEncoder PASSWORD_ENCODER = new BCryptPasswordEncoder();

    /**
     * 获取登录者的信息
     */
    public static JWT getInfo() {
        return (JWT) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    }

    /**
     * 获取登录者的id
     */
    public static Long getUserId() {
        JWT info = getInfo();
        return Long.valueOf(String.valueOf(info.getPayload("userId")));
//        return info.getPayload("userId");
    }

    /**
     * 获取登录者的权限
     */
    public static String getAuths() {
        return (String) getInfo().getPayload(JwtProvider.AUTHORITY);
    }

    /**
     * 密码加密
     *
     * @param password 明文密码
     * @return 加密后的密码
     */
    public static String passwordEncoder(String password) {
        return PASSWORD_ENCODER.encode(password);
    }

    /**
     * 密码比对
     *
     * @param rawPassword     明文密码
     * @param encodedPassword 加密后的密码
     * @return 是否通过
     */
    public static boolean passwordMatches(CharSequence rawPassword, String encodedPassword) {
        return PASSWORD_ENCODER.matches(rawPassword, encodedPassword);
    }

    public static void main(String[] args) {
        System.out.println(SecurityUtils.passwordEncoder("admin2025"));
    }

}
