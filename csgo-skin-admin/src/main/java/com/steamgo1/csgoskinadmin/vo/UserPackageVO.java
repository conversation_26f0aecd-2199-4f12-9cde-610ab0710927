package com.steamgo1.csgoskinadmin.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("用户背包")
public class UserPackageVO {
    @ApiModelProperty("用户基础信息")
    private UserBaseInfoVO userBaseInfoVO;

    @ApiModelProperty("用户背包存数")
    private Integer totalStock = 0;

    @ApiModelProperty("用户背包总价值")
    private BigDecimal totalValue = BigDecimal.ZERO;
}
