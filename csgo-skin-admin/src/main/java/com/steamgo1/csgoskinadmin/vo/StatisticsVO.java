package com.steamgo1.csgoskinadmin.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@ApiModel("首页统计")
public class StatisticsVO {
    @ApiModelProperty("用户统计")
    private User user;

    @ApiModelProperty("在线统计")
    private OnlineUser onlineUser;

    @ApiModelProperty("取回统计")
    private PickUp pickUp;

    @ApiModelProperty("待处理统计")
    private Integer waitPickUp;

    @ApiModelProperty("充值统计")
    private Charge charge;

    @ApiModelProperty("免费宝箱开启率")
    private String freeBoxRatio;

    @ApiModelProperty("今日宝箱开启率")
    private String todayFreeBoxRatio;


    @Data
    public static class User {
        @ApiModelProperty("用户总数")
        private Integer totalUsers;

        @ApiModelProperty("今日新增")
        private Integer dayUsersQuantity;

        @ApiModelProperty("7日新增情况")
        private List<Map<Date, Integer>> weekUser;
    }

    @Data
    public static class OnlineUser {
        @ApiModelProperty("当前在线数量")
        private Integer totalUsers;

        @ApiModelProperty("今日累计在线数量")
        private Integer dayUsersQuantity;

        @ApiModelProperty("7日在线数量")
        private List<Map<Date, Integer>> weekOnlineUser;
    }


    @Data
    public static class PickUp {
        @ApiModelProperty("今日取回金额")
        private BigDecimal totalAmount;

        @ApiModelProperty("取回率")
        private Double rate;

        @ApiModelProperty("7日取回金额")
        private List<Map<Date, BigDecimal>> weekPickUp;
    }

    @Data
    public static class Charge {
        @ApiModelProperty("今日充值金额")
        private BigDecimal totalAmount;

        @ApiModelProperty("支付率")
        private Double rate;

        @ApiModelProperty("7日七日金额")
        private List<Map<Date, BigDecimal>> weekCharge;
    }

}
