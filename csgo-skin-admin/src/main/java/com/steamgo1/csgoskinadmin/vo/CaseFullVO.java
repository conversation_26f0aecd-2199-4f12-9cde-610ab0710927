package com.steamgo1.csgoskinadmin.vo;

import com.steamgo1.csgoskincommon.converter.I18nFieldConverter;
import com.steamgo1.csgoskincommon.entity.SkinEntity;
import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Convert;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel("全量箱子信息")
public class CaseFullVO {
    @ApiModelProperty("箱子ID")
    private Long id;
    /**
     * 新手或者老手
     */
    @ApiModelProperty("箱子类型 1 新手箱子 2 普通箱子")
    private Long type;

//    @ApiModelProperty("箱子类型 1 新手箱子 2 普通箱子")
//    private String typeName;

    @ApiModelProperty("箱子名（多语言）")
    private I18nField i18nFieldName;

    @ApiModelProperty("箱子名")
    private String name;

    @ApiModelProperty("箱子价格")
    private Double price;

    @ApiModelProperty("箱子前景图")
    private String foregroundPicture;

    @ApiModelProperty("箱子背景图")
    private String backgroundPicture;

    @Column(name = "skin_total")
    @ApiModelProperty("箱子包含皮肤数量")
    private Integer skinTotal;

    @Column(name = "probability")
    @ApiModelProperty("箱子总概率")
    private Double probability;

    @ApiModelProperty("箱子类别")
    private Long categoryId;

//    @ApiModelProperty("箱子类别名")
//    private String categoryName;

    @ApiModelProperty("箱子优先级(用作排序，越大越靠前)")
    private Integer gradle;

    @Column(name = "is_recommend")
    @ApiModelProperty("箱子是否推荐")
    private Boolean isRecommend;

    @ApiModelProperty("箱子是否在售")
    private Boolean isSale;

    @ApiModelProperty("箱子中饰品信息")
    private List<CaseLevelVo> levels = new ArrayList<>();

    @ApiModelProperty("箱子明细")
    private CaseStatisticsVO caseStatistics;

    @ApiModelProperty("基础返奖率")
    private BigDecimal returnRate;

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getName() {
        return I18nUtils.getI18nFieldValue(i18nFieldName, I18nUtils.getCurrentLanguageEnum());
    }

    @Data
    public static class CaseLevelVo {
        @ApiModelProperty("等级Id")
        private Long id;

        @ApiModelProperty("箱子id")
        private Long caseId;

        @ApiModelProperty("等级")
        private Integer levelNum;

        @ApiModelProperty("等级对应饰品")
        private List<CaseSkinVO> skins = new ArrayList<>();
    }

    //    @ApiModel("箱子中饰品信息")
    @Data
    public static class CaseSkinVO {
        @ApiModelProperty("饰品箱子Id")
        private Long caseSkinId;

        @ApiModelProperty("饰品信息")
        private SkinEntity skinInfo;

        @ApiModelProperty("颜色")
        private Long skinRarityColorId;

        @ApiModelProperty("中奖概率")
        private Double probability;

        @ApiModelProperty("中奖数量统计")
        private Integer winningTotal;

        @ApiModelProperty("参考价格")
        private BigDecimal referencePrice;
    }

//    public void setType(String type){
//        if(type.)
//        this.type = CaseType.NOVICE.getType();
//    }
}
