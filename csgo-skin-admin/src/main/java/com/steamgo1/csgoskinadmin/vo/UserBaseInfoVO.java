package com.steamgo1.csgoskinadmin.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("用户信息")
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserBaseInfoVO {
    @ApiModelProperty("用户ID")
    private Long id;

    @ApiModelProperty("昵称")
    private String nickname;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("用户类型")
    private Integer type;

    @ApiModelProperty("用户类型")
    private String typeValue;

}
