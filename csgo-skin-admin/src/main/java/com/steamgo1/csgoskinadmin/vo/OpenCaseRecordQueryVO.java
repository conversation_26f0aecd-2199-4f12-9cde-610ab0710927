package com.steamgo1.csgoskinadmin.vo;


import com.steamgo1.csgoskincommon.entity.CaseEntity;
import com.steamgo1.csgoskincommon.entity.SkinEntity;
import com.steamgo1.csgoskincommon.entity.UserEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel("开箱记录结果")
public class OpenCaseRecordQueryVO {
    private Long id;
    private UserEntity user;
    private SkinEntity skin;
    private CaseEntity box;

}
