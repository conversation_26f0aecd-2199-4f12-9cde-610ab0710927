package com.steamgo1.csgoskinadmin.vo;

import com.steamgo1.csgoskincommon.utils.I18nField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 国际化演示VO - 前台使用
 * 只返回当前语言的数据
 * 
 * <AUTHOR>
 */
@Data
@ApiModel("国际化演示信息")
public class I18nDemoVO {
    
    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("文本内容（多语言）")
    private I18nField i18nFieldText;
    
    @ApiModelProperty("文本内容（当前语言）")
    private String text;
    
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;
}
