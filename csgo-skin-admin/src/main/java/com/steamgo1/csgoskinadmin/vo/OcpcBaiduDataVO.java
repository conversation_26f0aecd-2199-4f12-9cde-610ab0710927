package com.steamgo1.csgoskinadmin.vo;


import com.steamgo1.csgoskincommon.entity.OcpcBaiduDataEntity;
import com.steamgo1.csgoskincommon.entity.OcpcChannelEntity;
import com.steamgo1.csgoskincommon.entity.OrderChargeEntity;
import com.steamgo1.csgoskincommon.entity.UserEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel("百度ocpc数据")
public class OcpcBaiduDataVO {
    private OcpcBaiduDataEntity ocpcBaiduData;

    private OcpcChannelEntity channel;

    private UserEntity user;

    private OrderChargeEntity orderCharge;
}
