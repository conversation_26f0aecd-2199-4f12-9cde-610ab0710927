package com.steamgo1.csgoskinadmin.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;


@Data
@ApiModel("口令红包")
public class RedPacketVO {
    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("红包名称")
    private String name;

    @ApiModelProperty("红包名称（多语言）")
    private I18nField i18nFieldName;

    @ApiModelProperty("兑换码")
    private String code;

    @ApiModelProperty("金币数量")
    private BigDecimal coin;

    @ApiModelProperty("到期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    @ApiModelProperty("总份数")
    private Integer total;

    @ApiModelProperty("已兑换数量")
    private Integer totalCurrent;

    @ApiModelProperty("消费阈值")
    private BigDecimal consumeThreshold;

    @ApiModelProperty("红包类型")
    private Integer type;

    @ApiModelProperty("红包类型值")
    private String typeValue;

    @ApiModelProperty("红包模式")
    private Integer method;

    @ApiModelProperty("红包模式值")
    private String methodValue;

    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getName() {
        return I18nUtils.getI18nFieldValue(i18nFieldName, I18nUtils.getCurrentLanguageEnum());
    }
}
