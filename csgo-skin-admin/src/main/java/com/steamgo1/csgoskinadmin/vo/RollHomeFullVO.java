package com.steamgo1.csgoskinadmin.vo;

import com.steamgo1.csgoskincommon.entity.AdminUserEntity;
import com.steamgo1.csgoskincommon.entity.RollHomeSkinEntity;
import com.steamgo1.csgoskincommon.entity.RollHomeUserEntity;
import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Data
public class RollHomeFullVO {
    @ApiModelProperty("roll房饰品")
    List<RollHomeSkinEntity> rollHomeSkin;
    @ApiModelProperty("roll房用户")
    List<RollHomeUserEntity> rollHomeUser;
    @ApiModelProperty("Roll房ID")
    private Long id;
    @ApiModelProperty("房间名称")
    private String name;
    @ApiModelProperty("房间名称（多语言）")
    private I18nField i18nFieldName;
    @ApiModelProperty("创建人")
    private AdminUserEntity adminUser;
    @ApiModelProperty("房间类型")
    private Integer rollHomeType;
    @ApiModelProperty("房间类型描述")
    private String rollHomeTypeValue;
    @ApiModelProperty("开奖方式")
    private Integer rollHomeLotteryMethod;
    @ApiModelProperty("开奖方式描述")
    private String rollHomeLotteryMethodValue;
    @ApiModelProperty("最高人数")
    private Integer maxPeople;
    @ApiModelProperty("开奖时间")
    private Date lotteryTime;
    @ApiModelProperty("参入阈值")
    private BigDecimal consumeThreshold;
    @ApiModelProperty("参入阈值")
    private Integer thresholdType;
    @ApiModelProperty("参入阈值类型描述")
    private String thresholdTypeValue;
    @ApiModelProperty("房间密码")
    private String password;
    @ApiModelProperty("Roll房备注")
    private String remarks;
    @ApiModelProperty("Roll房备注（多语言）")
    private I18nField i18nFieldRemarks;
    @ApiModelProperty("是否推荐")
    private Boolean isRecommend = false;
    @Column(name = "secret_hash")
    private String secretHash;
    @Column(name = "secret_salt")
    private String secretSalt;
    @Column(name = "public_hash")
    private String publicHash;
    @Column(name = "client_seed", length = 128)
    private String clientSeed;
    @ApiModelProperty("状态")
    private Integer status;
    @ApiModelProperty("状态描述")
    private String statusValue;

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getName() {
        return I18nUtils.getI18nFieldValue(i18nFieldName, I18nUtils.getCurrentLanguageEnum());
    }

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getRemarks() {
        return I18nUtils.getI18nFieldValue(i18nFieldRemarks, I18nUtils.getCurrentLanguageEnum());
    }
}
