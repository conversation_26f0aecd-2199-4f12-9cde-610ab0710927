package com.steamgo1.csgoskinadmin.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@ApiModel("饰品筛选条件")
@Accessors(chain = true)
public class SkinQueryParamVO {
    @ApiModelProperty("饰品原型分类")
    List<PrototypeCategory> prototypeCategory;

    @ApiModelProperty("饰品品质")
    List<Rarity> rarity;

    @ApiModelProperty("饰品外观")
    List<Exterior> exterior;

    @ApiModelProperty("饰品质量")
    List<Quality> quality;


    @Data
    public static class Rarity {
        Long id;
        @ApiModelProperty("稀有度标识")
        String name;

        @ApiModelProperty("稀有度code")
        String code;
    }

    @Data
    public static class Quality {
        Long id;
        @ApiModelProperty("质量标识")
        String name;

        @ApiModelProperty("质量标识code")
        String code;
    }

    @Data
    public static class Exterior {
        Long id;
        @ApiModelProperty("外观标识")
        String name;

        @ApiModelProperty("外观标识code")
        String code;
    }

    @Data
    public static class PrototypeCategory {
        Long id;
        @ApiModelProperty("饰品原型分类图片")
        String img;
        @ApiModelProperty("饰品原型分类名")
        String name;

        @ApiModelProperty("饰品原型")
        List<Prototype> prototypes;
    }

    @Data
    public static class Prototype {
        Long id;

        @ApiModelProperty("饰品原型名")
        String name;

        @ApiModelProperty("饰品原型code")
        String code;

        @ApiModelProperty("饰品原型图片")
        String img;

    }


}
