package com.steamgo1.csgoskinadmin.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("箱子统计")
public class CaseStatisticsVO {
    @ApiModelProperty("开箱次数")
    private Integer totalOpen;

    @ApiModelProperty("销售额")
    private BigDecimal salesVolume;

    @ApiModelProperty("开出饰品总价值")
    private BigDecimal expenditure;

    @ApiModelProperty("利润")
    private BigDecimal revenue;
}
