package com.steamgo1.csgoskinadmin.vo;


import com.steamgo1.csgoskincommon.entity.SkinEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("用户背包详情")
public class UserPackageDetialsVO {
    @ApiModelProperty("是否已取回")
    Boolean isReceived = false;
    @ApiModelProperty("是否已出售")
    Boolean isSelled = false;
    @ApiModelProperty("背包ID")
    private Long packageId;
    @ApiModelProperty("获取时间")
    private Date createTime;
    @ApiModelProperty("更新时间")
    private Date updateTime;
    @ApiModelProperty("饰品")
    private SkinEntity skin;
    @ApiModelProperty("来源")
    private Integer source;
    @ApiModelProperty("箱子ID")
    private Long caseId;
    @ApiModelProperty("箱子名")
    private String caseName;
    @ApiModelProperty("来源值")
    private String sourceValue;
}
