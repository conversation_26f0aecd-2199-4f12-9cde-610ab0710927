package com.steamgo1.csgoskinadmin.vo;

import com.steamgo1.csgoskincommon.entity.SkinEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("用户取回订单")
public class OrderPickupVO {
    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("扎比特订单号")
    private Long thirdOrderNo;

    @ApiModelProperty("steam交易链接")
    private String steamTradeUrl;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("状态值")
    private String statusValue;

    @ApiModelProperty("备注(失败原因)")
    private String remarks;
    @ApiModelProperty("用户信息")
    private UserBaseInfoVO user;

    @ApiModelProperty("订单商品")
    private SkinEntity skin;

}
