package com.steamgo1.csgoskinadmin.vo;

import com.steamgo1.csgoskincommon.entity.OcpcChannelEntity;
import com.steamgo1.csgoskincommon.entity.OcpcGoogleDataEntity;
import com.steamgo1.csgoskincommon.entity.OrderChargeEntity;
import com.steamgo1.csgoskincommon.entity.UserEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel("Google OCPC数据")
public class OcpcGoogleDataVO {
    private OcpcGoogleDataEntity ocpcGoogleData;

    private OcpcChannelEntity channel;

    private UserEntity user;

    private OrderChargeEntity orderCharge;
}
