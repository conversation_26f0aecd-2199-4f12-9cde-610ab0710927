package com.steamgo1.csgoskinadmin.vo;


import com.steamgo1.csgoskincommon.utils.I18nField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("追梦饰品")
public class PercentageSkinQueryVO {
    @ApiModelProperty("饰品ID")
    private Long id;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("饰品名称（多语言）")
    private I18nField i18nFieldName;

    @ApiModelProperty("饰品名称")
    private String name;

    @Column(name = "picture", length = 512)
    @ApiModelProperty("饰品图片")
    private String picture;
    @ApiModelProperty("饰品价值(zbt)")
    private BigDecimal price;

    @ApiModelProperty("饰品价值(钻石)")
    private BigDecimal diamond;

    @ApiModelProperty("销量")
    private Integer selms;
    @ApiModelProperty("是否上架")
    private Boolean isSell;


}
