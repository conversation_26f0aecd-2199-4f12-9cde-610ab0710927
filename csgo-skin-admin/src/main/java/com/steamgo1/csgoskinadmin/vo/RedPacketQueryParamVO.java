package com.steamgo1.csgoskinadmin.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("红包查询条件")
public class RedPacketQueryParamVO {
    @ApiModelProperty("状态")
    List<EnumMap> typeList;

    @ApiModelProperty("模式")
    List<EnumMap> methodList;

    @ApiModelProperty("阀值类型")
    List<EnumMap> thresholdTypeList;

    @Data
    public static class EnumMap {
        @ApiModelProperty("标识码")
        Integer code;
        @ApiModelProperty("值")
        String value;
    }

}
