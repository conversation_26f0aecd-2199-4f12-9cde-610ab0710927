package com.steamgo1.csgoskinadmin.vo;

import com.steamgo1.csgoskincommon.entity.ChargeGoodsEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("用户充值订单")
public class OrderChargeVO {
    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("标价")
    private BigDecimal amount;

    @ApiModelProperty("实际付款金额")
    private BigDecimal actualAmount;

    @ApiModelProperty("支付类型")
    private Integer payType;

    @ApiModelProperty("支付类型值")
    private String payTypeValue;

    @ApiModelProperty("订单状态")
    private Integer orderStatus;

    @ApiModelProperty("订单状态值")
    private String orderStatusValue;

    @ApiModelProperty("用户信息")
    private UserBaseInfoVO user;

    @ApiModelProperty("订单商品")
    private ChargeGoodsEntity chargeGoods;


}
