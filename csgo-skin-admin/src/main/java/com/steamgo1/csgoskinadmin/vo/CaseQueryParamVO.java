package com.steamgo1.csgoskinadmin.vo;

import com.steamgo1.csgoskincommon.entity.SkinRarityColorEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@ApiModel("箱子筛选条件")
@Accessors(chain = true)
public class CaseQueryParamVO {
    @ApiModelProperty("箱子分类")
    List<CaseQueryParamVO.Category> category;

    @ApiModelProperty("箱子类型")
    List<Type> type;

    @ApiModelProperty("箱子饰品颜色")
    List<SkinRarityColorEntity> skinRarityColor;

    @Data
    public static class Category {
        @ApiModelProperty("箱子分类ID")
        private Long id;

        @ApiModelProperty("箱子分类名")
        private String name;
    }

    @Data
    public static class Type {
        @ApiModelProperty("类别码")
        private Integer code;

        @ApiModelProperty("类别名")
        private String value;
    }
}
