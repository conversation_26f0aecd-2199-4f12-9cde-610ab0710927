package com.steamgo1.csgoskinadmin.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ReportForms1VO {
    //        日期
    //        注册数
    //        新用户付费数
    //        老用户付费数
    //        新用户付费率
    //        新用户付费金额
    //        老用户付费金额总付费
    //        arpu值
    //        新用户付费占比 取回成功订单数充值-取回
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date date;
    private Integer registerCount;
    private Integer fufeiCount;
    private Integer oldFufeiCount;
    private BigDecimal fufeiRate;
    private BigDecimal fufeiAmount;
    private BigDecimal oldFufeiAmount;
    private BigDecimal allAmount;
    private BigDecimal arpu;
    private BigDecimal newUserFufeiRate;
    private Integer quhuiCount;
    private BigDecimal chognzhiquhuiCha;
}
