package com.steamgo1.csgoskinadmin.vo;

import com.steamgo1.csgoskincommon.entity.UserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


@Data
@ApiModel("用户功能禁用")
public class UserDisableQueryVO {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("用户")
    private UserEntity user;

    @ApiModelProperty("禁用类型")
    private Integer type;

    @ApiModelProperty("禁用类型名")
    private String typeValue;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("禁用到期时间")
    private Date disableExpire;


    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("是否生效")
    private Boolean isEffective;
}
