package com.steamgo1.csgoskinadmin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("对战房查询结果")
public class BattleQueryVO {
    private Long id;
    private Date createTime;

    @ApiModelProperty("对战房模式")
    private Integer battleHomeMethod;

    @ApiModelProperty("对战房模式值")
    private String battleHomeMethodValue;


    @ApiModelProperty("对战房人数")
    private Integer totalPlayer;


    @ApiModelProperty("对战房总消耗")
    private BigDecimal amount;

    @ApiModelProperty("对战房局数")
    private Integer rounds;

    @ApiModelProperty("对战房总局数")
    private Integer totalRounds;

    @ApiModelProperty("对战房状态")
    private Integer battleHomeStatus;

    @ApiModelProperty("对战房状态值")
    private String battleHomeStatusValue;
}
