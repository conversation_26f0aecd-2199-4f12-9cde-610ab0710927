package com.steamgo1.csgoskinadmin.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@Data
@ApiModel("用户信息")
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserDiamondRecordVO {
    @ApiModelProperty("记录ID")
    private Long id;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("变更来源")
    private Integer source;

    @ApiModelProperty("变更来源值")
    private String sourceValue;

    @ApiModelProperty("变更来源ID")
    private Long sourceId;

    @ApiModelProperty("是否正增长")
    private Boolean isPositive;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("变更后金额")
    private BigDecimal afterAmount;

}
