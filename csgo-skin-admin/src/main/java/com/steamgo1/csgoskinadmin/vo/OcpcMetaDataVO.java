package com.steamgo1.csgoskinadmin.vo;

import com.steamgo1.csgoskincommon.entity.OcpcChannelEntity;
import com.steamgo1.csgoskincommon.entity.OcpcMetaDataEntity;
import com.steamgo1.csgoskincommon.entity.OrderChargeEntity;
import com.steamgo1.csgoskincommon.entity.UserEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel("Meta OCPC数据")
public class OcpcMetaDataVO {
    private OcpcMetaDataEntity ocpcMetaData;

    private OcpcChannelEntity channel;

    private UserEntity user;

    private OrderChargeEntity orderCharge;
}
