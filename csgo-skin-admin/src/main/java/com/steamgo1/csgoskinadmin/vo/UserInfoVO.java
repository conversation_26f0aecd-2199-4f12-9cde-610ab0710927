package com.steamgo1.csgoskinadmin.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.steamgo1.csgoskincommon.entity.OcpcChannelEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("用户信息")
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserInfoVO {
    @ApiModelProperty("用户ID")
    private Long id;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("昵称")
    private String nickname;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("微信ID")
    private String wxOpenid;

    @ApiModelProperty("steam交易链接")
    private String tradeOfferAccessUrl;

    @ApiModelProperty("钻石")
    private BigDecimal diamond;

    @ApiModelProperty("金币")
    private BigDecimal coin;

    @ApiModelProperty("等级")
    private Integer lavel;

    @ApiModelProperty("经验")
    private Integer experience;

    @ApiModelProperty("私密哈希")
    private String secretHash;

    @ApiModelProperty("私密盐值")
    private String secretSalt;

    @ApiModelProperty("公共哈希")
    private String publicHash;

    @ApiModelProperty("回合数")
    private Integer rounds;

    @ApiModelProperty("用户种子")
    private String clientSeed;

    @ApiModelProperty("用户类型")
    private Integer type;

    @ApiModelProperty("用户类型")
    private String typeValue;

    @ApiModelProperty("充值总金额")
    private BigDecimal totalCharge = BigDecimal.ZERO;

    @ApiModelProperty("用户渠道")
    private OcpcChannelEntity channel;

    @ApiModelProperty("有效库存价值")
    private BigDecimal packageValue = BigDecimal.ZERO;

    @ApiModelProperty("取回库存价值")
    private BigDecimal pickUpPackageValue = BigDecimal.ZERO;

    @ApiModelProperty("充值-取回")
    private BigDecimal lossValue = BigDecimal.ZERO;

    @ApiModelProperty("用户总共获取金币")
    private BigDecimal totalCoin;

    @ApiModelProperty("用户总共获取金币/充值")
    private String totalCoinRate;


    @ApiModelProperty("是否禁用")
    private Boolean isBan;

    @ApiModelProperty("最后登录时间")
    private Date lastLoginTime;

}
