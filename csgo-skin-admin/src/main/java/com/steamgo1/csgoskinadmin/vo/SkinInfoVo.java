package com.steamgo1.csgoskinadmin.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SkinInfoVo {
    @ApiModelProperty("饰品名称")
    private String name;

    @ApiModelProperty("饰品唯一英文")
    private String englishName;

    @ApiModelProperty("饰品图片")
    private String picture;

    @ApiModelProperty("在售数量")
    private Integer onSaleNum;

    @ApiModelProperty("参考价格")
    private BigDecimal referencePrice;

    @ApiModelProperty("价格")
    private BigDecimal price;

    @ApiModelProperty("原型")
    private Long prototype;

//    @ApiModelProperty("原型名")
//    private Long prototypeName;

    @ApiModelProperty("稀有度")
    private Long rarity;

//    @ApiModelProperty("稀有度名")
//    private String rarityName;

    @ApiModelProperty("质量")
    private Long quality;

//    @ApiModelProperty("质量名")
//    private String qualityName;

    @ApiModelProperty("外观")
    private Long exterior;

//    @ApiModelProperty("外观名")
//    private String exteriorName;
}
