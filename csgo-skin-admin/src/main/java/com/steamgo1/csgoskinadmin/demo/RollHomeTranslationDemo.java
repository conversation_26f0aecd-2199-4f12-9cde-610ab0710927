package com.steamgo1.csgoskinadmin.demo;

import com.steamgo1.csgoskinadmin.dto.RollHomeAddDTO;
import com.steamgo1.csgoskinadmin.dto.RollHomeUpdateDTO;
import com.steamgo1.csgoskinadmin.service.RollHomeService;
import com.steamgo1.csgoskincommon.enums.LanguageEnum;
import com.steamgo1.csgoskincommon.utils.I18nField;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;

/**
 * RollHome多语言翻译功能演示
 * 展示各种翻译场景的使用方法
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class RollHomeTranslationDemo {

    @Autowired
    private RollHomeService rollHomeService;

    /**
     * 演示1：完全依赖翻译的场景
     * 只提供中文内容，让系统自动翻译到所有语言
     */
    public void demoFullTranslation() {
        log.info("=== 演示1：完全依赖翻译 ===");
        
        RollHomeAddDTO dto = new RollHomeAddDTO();
        dto.setName("VIP专属竞技房");
        dto.setRemarks("仅限VIP会员参与的高级竞技房间");
        dto.setRollHomeType(1);
        dto.setRollHomeLotteryMethod(1);
        dto.setMaxPeople(10);
        dto.setLotteryTime(new Date());
        dto.setConsumeThreshold(new BigDecimal("1000"));
        dto.setThresholdType(1);
        dto.setIsRecommend(true);
        dto.setRollHomeSkin(new ArrayList<>());
        
        // 不设置多语言字段，完全依赖翻译
        log.info("添加Roll房，中文名称: {}", dto.getName());
        log.info("添加Roll房，中文备注: {}", dto.getRemarks());
        
        try {
            // rollHomeService.addRollHome(dto);
            log.info("系统会自动将中文内容翻译到所有支持的语言");
        } catch (Exception e) {
            log.error("演示执行失败: {}", e.getMessage());
        }
    }

    /**
     * 演示2：部分翻译补充的场景
     * 已有部分语言内容，系统补充缺失的语言
     */
    public void demoPartialTranslation() {
        log.info("=== 演示2：部分翻译补充 ===");
        
        RollHomeAddDTO dto = new RollHomeAddDTO();
        dto.setName("高级对战房");
        dto.setRemarks("专业玩家对战专用");
        dto.setRollHomeType(1);
        dto.setRollHomeLotteryMethod(1);
        dto.setMaxPeople(8);
        dto.setLotteryTime(new Date());
        dto.setConsumeThreshold(new BigDecimal("500"));
        dto.setThresholdType(1);
        dto.setIsRecommend(false);
        dto.setRollHomeSkin(new ArrayList<>());
        
        // 设置部分多语言内容
        I18nField nameField = new I18nField();
        nameField.put(LanguageEnum.ENGLISH, "Advanced Battle Room"); // 已有英文
        // 缺少葡萄牙语，系统会自动翻译
        
        I18nField remarksField = new I18nField();
        remarksField.put(LanguageEnum.PORTUGUESE, "Para jogadores profissionais"); // 已有葡萄牙语
        // 缺少英文，系统会自动翻译
        
        dto.setI18nFieldName(nameField);
        dto.setI18nFieldRemarks(remarksField);
        
        log.info("名称字段已有英文: {}", nameField.get(LanguageEnum.ENGLISH));
        log.info("备注字段已有葡萄牙语: {}", remarksField.get(LanguageEnum.PORTUGUESE));
        
        try {
            // rollHomeService.addRollHome(dto);
            log.info("系统会补充缺失的语言翻译");
        } catch (Exception e) {
            log.error("演示执行失败: {}", e.getMessage());
        }
    }

    /**
     * 演示3：中文内容变化重新翻译的场景
     * 更新时中文内容发生变化，系统重新翻译所有语言
     */
    public void demoChineseContentChanged() {
        log.info("=== 演示3：中文内容变化重新翻译 ===");
        
        RollHomeUpdateDTO dto = new RollHomeUpdateDTO();
        dto.setId(1L);
        dto.setName("超级VIP至尊房间");  // 新的中文内容
        dto.setRemarks("为超级VIP用户量身定制的顶级房间");  // 新的中文内容
        dto.setRollHomeType(1);
        dto.setRollHomeLotteryMethod(1);
        dto.setMaxPeople(5);
        dto.setLotteryTime(new Date());
        dto.setConsumeThreshold(new BigDecimal("2000"));
        dto.setThresholdType(1);
        dto.setIsRecommend(true);
        dto.setRollHomeSkin(new ArrayList<>());
        
        // 模拟原有的多语言字段（已有完整翻译）
        I18nField existingNameField = new I18nField();
        existingNameField.put(LanguageEnum.CHINESE, "普通VIP房间");  // 旧的中文内容
        existingNameField.put(LanguageEnum.ENGLISH, "Regular VIP Room");
        existingNameField.put(LanguageEnum.PORTUGUESE, "Sala VIP Regular");
        
        I18nField existingRemarksField = new I18nField();
        existingRemarksField.put(LanguageEnum.CHINESE, "VIP用户专用房间");  // 旧的中文内容
        existingRemarksField.put(LanguageEnum.ENGLISH, "Dedicated room for VIP users");
        existingRemarksField.put(LanguageEnum.PORTUGUESE, "Sala dedicada para usuários VIP");
        
        dto.setI18nFieldName(existingNameField);
        dto.setI18nFieldRemarks(existingRemarksField);
        
        log.info("原中文名称: {}", existingNameField.get(LanguageEnum.CHINESE));
        log.info("新中文名称: {}", dto.getName());
        log.info("原中文备注: {}", existingRemarksField.get(LanguageEnum.CHINESE));
        log.info("新中文备注: {}", dto.getRemarks());
        
        try {
            // rollHomeService.updateRollHome(dto);
            log.info("系统检测到中文内容变化，会重新翻译所有语言");
        } catch (Exception e) {
            log.error("演示执行失败: {}", e.getMessage());
        }
    }

    /**
     * 演示4：中文内容未变化的场景
     * 中文内容相同时，保留已有的翻译内容
     */
    public void demoChineseContentUnchanged() {
        log.info("=== 演示4：中文内容未变化 ===");
        
        RollHomeUpdateDTO dto = new RollHomeUpdateDTO();
        dto.setId(2L);
        dto.setName("经典竞技房");  // 与原有中文内容相同
        dto.setRemarks("传统竞技模式房间");  // 与原有中文内容相同
        dto.setRollHomeType(1);
        dto.setRollHomeLotteryMethod(2);
        dto.setMaxPeople(12);
        dto.setLotteryTime(new Date());
        dto.setConsumeThreshold(new BigDecimal("300"));
        dto.setThresholdType(1);
        dto.setIsRecommend(false);
        dto.setRollHomeSkin(new ArrayList<>());
        
        // 模拟原有的多语言字段
        I18nField existingNameField = new I18nField();
        existingNameField.put(LanguageEnum.CHINESE, "经典竞技房");  // 相同的中文内容
        existingNameField.put(LanguageEnum.ENGLISH, "Classic Arena");
        existingNameField.put(LanguageEnum.PORTUGUESE, "Arena Clássica");
        
        I18nField existingRemarksField = new I18nField();
        existingRemarksField.put(LanguageEnum.CHINESE, "传统竞技模式房间");  // 相同的中文内容
        existingRemarksField.put(LanguageEnum.ENGLISH, "Traditional competitive mode room");
        existingRemarksField.put(LanguageEnum.PORTUGUESE, "Sala de modo competitivo tradicional");
        
        dto.setI18nFieldName(existingNameField);
        dto.setI18nFieldRemarks(existingRemarksField);
        
        log.info("中文名称未变化: {}", dto.getName());
        log.info("中文备注未变化: {}", dto.getRemarks());
        
        try {
            // rollHomeService.updateRollHome(dto);
            log.info("系统检测到中文内容未变化，会保留已有的翻译内容");
        } catch (Exception e) {
            log.error("演示执行失败: {}", e.getMessage());
        }
    }

    /**
     * 演示5：混合场景
     * 一个字段中文变化，另一个字段中文未变化
     */
    public void demoMixedScenario() {
        log.info("=== 演示5：混合场景 ===");
        
        RollHomeUpdateDTO dto = new RollHomeUpdateDTO();
        dto.setId(3L);
        dto.setName("终极挑战房");  // 中文内容变化
        dto.setRemarks("高手专用房间");  // 中文内容未变化
        dto.setRollHomeType(2);
        dto.setRollHomeLotteryMethod(1);
        dto.setMaxPeople(6);
        dto.setLotteryTime(new Date());
        dto.setConsumeThreshold(new BigDecimal("1500"));
        dto.setThresholdType(1);
        dto.setIsRecommend(true);
        dto.setRollHomeSkin(new ArrayList<>());
        
        // 名称字段 - 中文内容变化
        I18nField existingNameField = new I18nField();
        existingNameField.put(LanguageEnum.CHINESE, "普通挑战房");  // 旧的中文内容
        existingNameField.put(LanguageEnum.ENGLISH, "Regular Challenge Room");
        existingNameField.put(LanguageEnum.PORTUGUESE, "Sala de Desafio Regular");
        
        // 备注字段 - 中文内容未变化
        I18nField existingRemarksField = new I18nField();
        existingRemarksField.put(LanguageEnum.CHINESE, "高手专用房间");  // 相同的中文内容
        existingRemarksField.put(LanguageEnum.ENGLISH, "Room for experts");
        existingRemarksField.put(LanguageEnum.PORTUGUESE, "Sala para especialistas");
        
        dto.setI18nFieldName(existingNameField);
        dto.setI18nFieldRemarks(existingRemarksField);
        
        log.info("名称字段 - 原中文: {}, 新中文: {}", existingNameField.get(LanguageEnum.CHINESE), dto.getName());
        log.info("备注字段 - 中文未变化: {}", dto.getRemarks());
        
        try {
            // rollHomeService.updateRollHome(dto);
            log.info("系统会重新翻译名称字段，保留备注字段的翻译");
        } catch (Exception e) {
            log.error("演示执行失败: {}", e.getMessage());
        }
    }

    /**
     * 运行所有演示
     */
    public void runAllDemos() {
        log.info("开始运行RollHome多语言翻译功能演示");
        
        try {
            demoFullTranslation();
            demoPartialTranslation();
            demoChineseContentChanged();
            demoChineseContentUnchanged();
            demoMixedScenario();
            
            log.info("所有演示运行完成");
        } catch (Exception e) {
            log.error("演示运行过程中发生错误: {}", e.getMessage(), e);
        }
    }
}
