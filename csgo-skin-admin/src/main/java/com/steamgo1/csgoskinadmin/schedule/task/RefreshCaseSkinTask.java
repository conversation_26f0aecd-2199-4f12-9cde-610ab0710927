package com.steamgo1.csgoskinadmin.schedule.task;

import com.steamgo1.csgoskinadmin.service.CaseSkinService;
import com.steamgo1.csgoskincommon.config.ConfigurerScheduling;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;

@Slf4j
@Configuration
public class RefreshCaseSkinTask extends ConfigurerScheduling {
    @Value("${schedule.interval.refreshCaseSkin}")
    private String runInterval;

    @Autowired
    private CaseSkinService caseSkinService;

    @Override
    protected void processTask() {
        log.info("refreshCaseSkin 定时任务 {}, 线程名称： {}, 线程ID: {}", LocalDateTime.now(), Thread.currentThread().getName(), Thread.currentThread().getId());
        caseSkinService.updateCaseSkinTaskByZbt();
        log.info("refreshCaseSkin 定时任务 {}, 线程名称： {}, 线程ID: {}", LocalDateTime.now(), Thread.currentThread().getName(), Thread.currentThread().getId());

    }

    @Override
    protected String getCron() {
        return runInterval;
    }

}
