package com.steamgo1.csgoskinadmin.schedule;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Component
public class DeadLetterSender {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Value("${rabbitmq.exchange.csgo}")
    private String rollHomeExchageName;

    public void send1() {
        Map<String, Object> map = new HashMap<>();
        String messageId = String.valueOf(UUID.randomUUID());
        String messageData = "test message, hello!";
        String createTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        map.put("messageId", messageId);
        map.put("messageData", messageData);
        map.put("createTime", createTime);
        rabbitTemplate.convertAndSend(rollHomeExchageName, "user.abc", map);
    }

    /**
     * 延迟队列测试(基于消息)
     *
     * @param msg
     * @return
     */
    public void send2(String msg, String time) {
        String messageId = String.valueOf(UUID.randomUUID());
//        String messageData = "七里香 DirectMessage";
        String createTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        HashMap<String, Object> map = new HashMap<>();
        map.put("messageId", messageId);
        map.put("messageData", msg);
        map.put("createTime", createTime);
        map.put("type", "延迟消息测试");
        //将消息主题：topic.man 发送到交换机TestDirectExchange
        log.info("rollHomeExchageName: {}, routingkey: {}, data: {}, expiratime: {}", rollHomeExchageName, "rollhome.test", JSONObject.toJSON(map), time);
        rabbitTemplate.convertAndSend(rollHomeExchageName, "rollhome.test", JSONObject.toJSON(map), a -> {
            a.getMessageProperties().setExpiration(time);
            return a;
        });
    }
}
