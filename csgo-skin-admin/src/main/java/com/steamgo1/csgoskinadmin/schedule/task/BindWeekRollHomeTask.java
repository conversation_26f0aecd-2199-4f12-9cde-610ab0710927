package com.steamgo1.csgoskinadmin.schedule.task;

import com.steamgo1.csgoskinadmin.service.RollHomeService;
import com.steamgo1.csgoskincommon.config.ConfigurerScheduling;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class BindWeekRollHomeTask extends ConfigurerScheduling {
    @Autowired
    private RollHomeService rollHomeService;

    @Value("${schedule.interval.rollHomeAdd}")
    private String rollHomeAdd;

    @Override
    protected void processTask() {
        log.info("BindWeekRollHomeTask开始执行！！！");
        rollHomeService.bindRollHomeWeek();
    }

    @Override
    protected String getCron() {
        return rollHomeAdd;
    }
}
