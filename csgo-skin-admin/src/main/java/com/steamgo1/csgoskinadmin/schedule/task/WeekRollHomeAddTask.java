package com.steamgo1.csgoskinadmin.schedule.task;

import com.steamgo1.csgoskinadmin.service.RollHomeService;
import com.steamgo1.csgoskincommon.config.ConfigurerScheduling;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class WeekRollHomeAddTask extends ConfigurerScheduling {
    @Value("${schedule.interval.weekRollHomeAdd}")
    private String runInterval;

    @Autowired
    private RollHomeService rollHomeService;

    @Override
    protected void processTask() {
        rollHomeService.createRollHomeEnoughWeek();
    }

    @Override
    protected String getCron() {
        return runInterval;
    }

}
