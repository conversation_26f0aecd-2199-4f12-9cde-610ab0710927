package com.steamgo1.csgoskinadmin.schedule.producer.impl;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinadmin.schedule.producer.ProducerService;
import com.steamgo1.csgoskincommon.dao.CardCollectRepository;
import com.steamgo1.csgoskincommon.dao.RollHomeRepository;
import com.steamgo1.csgoskincommon.dto.RabbitActivityVO;
import com.steamgo1.csgoskincommon.dto.RabbitRomeHomeVO;
import com.steamgo1.csgoskincommon.entity.CardCollectEntity;
import com.steamgo1.csgoskincommon.entity.RollHomeEntity;
import com.steamgo1.csgoskincommon.enums.RabbitActivityType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.UUID;


@Slf4j
@Service
public class ProducerServiceImpl implements ProducerService {

    @Value("${rabbitmq.exchange.csgo}")
    private String csgoExchageName;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private RollHomeRepository rollHomeRepository;

    @Autowired
    private CardCollectRepository cardCollectRepository;


    @Override
    @Transactional
    public void addRollHomeDelayedTask(Long rollHomeId) {
        RollHomeEntity rollHomeEntity = rollHomeRepository.findById(rollHomeId).orElse(null);
        if (rollHomeEntity == null) {
            log.error("添加Roll房失败，发送任务时候Rollhome不存在 {}", rollHomeId);
            return;
        }
        String messageId = String.valueOf(UUID.randomUUID());
        RabbitRomeHomeVO rabbitRomeHomeVO = new RabbitRomeHomeVO();
        rabbitRomeHomeVO.setRollHomeId(rollHomeId);
        rabbitRomeHomeVO.setMessageId(messageId);
        rabbitRomeHomeVO.setCreateTime(rollHomeEntity.getCreateTime());
        rollHomeEntity.setRabbitMessageId(messageId);
        rollHomeRepository.save(rollHomeEntity);
        log.info("任务终止时间：{} 当前时间： {} 相差{} 毫秒", rollHomeEntity.getLotteryTime(), new Date(), rollHomeEntity.getLotteryTime().getTime() - System.currentTimeMillis());
        Long expiratime = rollHomeEntity.getLotteryTime().getTime() - System.currentTimeMillis();
        log.info("RollHome:{} 开始一个延时任务:{},  过期时间: {}", rollHomeId, messageId, expiratime);
        String routingKey = "";
        switch (rollHomeEntity.getRollHomeType()) {
            case DAY:
                routingKey = "rollhome.day";
                break;
            case WEEK:
                routingKey = "rollhome.week";
                break;
            case MONTH:
                routingKey = "rollhome.month";
                break;
            case NOVICE:
                routingKey = "rollhome.novice";
                break;
            default:
                routingKey = "rollhome.test";

        }
        log.info("csgoExchageName: {}, routingkey: {}, data: {}, expiratime: {}", csgoExchageName, routingKey, JSONObject.toJSON(rabbitRomeHomeVO), expiratime);
        rabbitTemplate.convertAndSend(csgoExchageName, routingKey, JSONObject.toJSON(rabbitRomeHomeVO), a -> {
            a.getMessageProperties().setExpiration(String.valueOf(expiratime));
            return a;
        });
    }

    @Override
    public void addCardCollectDelayedTask(Long cardCollectId) {
        CardCollectEntity cardCollectEntity = cardCollectRepository.findById(cardCollectId).orElse(null);
        if (cardCollectEntity == null) {
            log.error("添加集卡活动失败，发送任务时候实体不存在 {}", cardCollectId);
            return;
        }
        String messageId = String.valueOf(UUID.randomUUID());
        cardCollectEntity.setRabbitMessageId(messageId);
        cardCollectRepository.save(cardCollectEntity);
        log.info("任务终止时间：{} 当前时间： {} 相差{} 毫秒", cardCollectEntity.getExpireTime(), new Date(), cardCollectEntity.getExpireTime().getTime() - System.currentTimeMillis());
        Long expiratime = cardCollectEntity.getExpireTime().getTime() - System.currentTimeMillis();
        RabbitActivityVO rabbitActivityVO = new RabbitActivityVO() {{
            setType(RabbitActivityType.CARD_COLLECT);
            setId(cardCollectId);
            setMessageId(messageId);
        }};
        rabbitTemplate.convertAndSend(csgoExchageName, "activity.card-collect", JSONObject.toJSON(rabbitActivityVO), a -> {
            a.getMessageProperties().setExpiration(String.valueOf(expiratime));
            return a;
        });
    }
}
