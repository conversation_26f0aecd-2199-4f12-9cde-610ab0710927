package com.steamgo1.csgoskinadmin.schedule.consumer;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinadmin.dto.RollHomeAddUserParamDTO;
import com.steamgo1.csgoskinadmin.service.RollHomeService;
import com.steamgo1.csgoskincommon.contant.CsgoContants;
import com.steamgo1.csgoskincommon.entity.enums.RollHomeType;
import com.steamgo1.csgoskincommon.entity.enums.UserType;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;

@Slf4j
@Component
@RabbitListener(queues = "${rabbitmq.queue.roll-home-user-dead-letter}")
public class RollHomeUserComsumer {
    @Autowired
    private RollHomeService rollHomeService;
    @Value("${spring.redis.prefix.roll-home}")
    private String redisRollHomePrefix;

    @RabbitHandler
    public void process(String json) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        log.info("RollHomeUserComsumer process jsonObject = {}", jsonObject.toJSONString());
        RollHomeAddUserParamDTO rollHomeAddUserParamDTO = JSONObject.toJavaObject(jsonObject, RollHomeAddUserParamDTO.class);
        rollHomeService.joinRollHome(rollHomeAddUserParamDTO);

        if (rollHomeAddUserParamDTO.getRollHomeType().equals(RollHomeType.WEEK)) {
            return;
        }

        String key = redisRollHomePrefix + ":" + UserType.CHEAT_ROBOT + ":" + rollHomeAddUserParamDTO.getUserId();
        String userDateKey = redisRollHomePrefix + ":" + UserType.CHEAT_ROBOT + ":" +
                CsgoContants.rollHomeJoinParam.ROLL_HOME_JOIN_DATA + ":" + rollHomeAddUserParamDTO.getUserId();
        if (!RedisUtils.hasKey(key)) {
            RedisUtils.save(key, BigDecimal.ZERO);
        }

        BigDecimal charged = RedisUtils.get(key, BigDecimal.class);
        if (charged == null) {
            charged = BigDecimal.ZERO;
        }
        if (rollHomeAddUserParamDTO.getAmount() == null) {
            rollHomeAddUserParamDTO.setAmount(charged);
        }

        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date date = calendar.getTime();
        Date userJoinDate = RedisUtils.get(userDateKey, Date.class);
        if (!date.equals(userJoinDate)) {
            RedisUtils.save(key, rollHomeAddUserParamDTO.getAmount().add(charged));
            RedisUtils.save(userDateKey, date);
        } else {
            if (rollHomeAddUserParamDTO.getAmount().compareTo(charged) > 0) {
                RedisUtils.save(key, rollHomeAddUserParamDTO.getAmount());
            }
        }


    }
}
