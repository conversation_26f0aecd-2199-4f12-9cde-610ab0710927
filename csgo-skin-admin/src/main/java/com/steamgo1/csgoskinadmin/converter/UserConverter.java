package com.steamgo1.csgoskinadmin.converter;

import com.steamgo1.csgoskinadmin.vo.*;
import com.steamgo1.csgoskincommon.entity.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.Date;

@Mapper(componentModel = "Spring")
public interface UserConverter {
    UserConverter INSTANCE = Mappers.getMapper(UserConverter.class);

    @Mapping(source = "type.code", target = "type")
    @Mapping(source = "type.value", target = "typeValue")
    UserBaseInfoVO toUserBaseInfoVO(UserEntity user);

    @Mapping(source = "user.id", target = "id")
    @Mapping(source = "user.type.code", target = "type")
    @Mapping(source = "user.type.value", target = "typeValue")
    @Mapping(source = "user.createTime", target = "createTime")
    @Mapping(source = "user.updateTime", target = "updateTime")
    @Mapping(source = "user.isBan", target = "isBan")
    @Mapping(source = "totalCharge", target = "totalCharge")
    @Mapping(source = "channel", target = "channel")
    @Mapping(source = "packageValue", target = "packageValue")
    @Mapping(source = "totalCoin", target = "totalCoin")
    @Mapping(source = "totalCoinRate", target = "totalCoinRate")
    @Mapping(source = "lastLoginTime", target = "lastLoginTime")
    UserInfoVO toUserInfoVO(UserEntity user, UserProfileEntity userProfile, AlgorithmDataEntity algorithmData, OcpcChannelEntity channel, BigDecimal totalCharge, BigDecimal packageValue, BigDecimal totalCoin, String totalCoinRate, Date lastLoginTime);


    @Mapping(source = "user", target = "user")
    @Mapping(source = "userDisable.type.code", target = "type")
    @Mapping(source = "userDisable.type.value", target = "typeValue")
    @Mapping(source = "userDisable.disableExpire", target = "disableExpire")
    @Mapping(source = "userDisable.isEffective", target = "isEffective")
    @Mapping(source = "userDisable.createTime", target = "createTime")
    @Mapping(source = "userDisable.id", target = "id")
    UserDisableQueryVO toUserDisableVO(UserDisableEntity userDisable, UserEntity user);


    // 用户背包总览
    UserPackageVO toUserPackageVO(UserBaseInfoVO userBaseInfoVO, Integer totalStock, BigDecimal totalValue);

    // 用户背包明细
    @Mapping(source = "source.code", target = "source")
    @Mapping(source = "source.value", target = "sourceValue")
    @Mapping(source = "id", target = "packageId")
    UserPackageDetialsVO toUserPackageDetails(UserPackageEntity userPackage);

    @Mapping(source = "source.code", target = "source")
    @Mapping(source = "source.value", target = "sourceValue")
    UserCoinRecordVO toUserCoinRecordVO(UserCoinRecordEntity userCoinRecord);

    @Mapping(source = "source.code", target = "source")
    @Mapping(source = "source.value", target = "sourceValue")
    UserDiamondRecordVO toUserDiamondRecordVO(UserDiamondRecordEntity userDiamondRecordEntity);

}
