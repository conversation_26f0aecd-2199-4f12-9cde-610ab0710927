package com.steamgo1.csgoskinadmin.converter;

import com.steamgo1.csgoskinadmin.vo.BattleQueryVO;
import com.steamgo1.csgoskincommon.entity.BattleHomeEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "Spring")
public interface BattleHomeConverter {
    BattleHomeConverter INSTANCE = Mappers.getMapper(BattleHomeConverter.class);

    @Mapping(source = "battleHomeMethod.code", target = "battleHomeMethod")
    @Mapping(source = "battleHomeMethod.value", target = "battleHomeMethodValue")
    @Mapping(source = "battleHomeStatus.code", target = "battleHomeStatus")
    @Mapping(source = "battleHomeStatus.value", target = "battleHomeStatusValue")
    BattleQueryVO toBattleQueryVO(BattleHomeEntity battleHome);
}
