package com.steamgo1.csgoskinadmin.converter;

import com.steamgo1.csgoskinadmin.vo.OrderChargeVO;
import com.steamgo1.csgoskinadmin.vo.OrderPickupVO;
import com.steamgo1.csgoskinadmin.vo.UserBaseInfoVO;
import com.steamgo1.csgoskincommon.entity.ChargeGoodsEntity;
import com.steamgo1.csgoskincommon.entity.OrderChargeEntity;
import com.steamgo1.csgoskincommon.entity.SkinEntity;
import com.steamgo1.csgoskincommon.entity.UserPackagePickupEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "Spring")
public interface OrderChargeConverter {
    OrderChargeConverter INSTANCE = Mappers.getMapper(OrderChargeConverter.class);

    @Mapping(source = "orderChargeEntity.id", target = "id")
    @Mapping(source = "orderChargeEntity.createTime", target = "createTime")
    @Mapping(source = "orderChargeEntity.updateTime", target = "updateTime")
    @Mapping(source = "orderChargeEntity.payType.code", target = "payType")
    @Mapping(source = "orderChargeEntity.payType.value", target = "payTypeValue")
    @Mapping(source = "orderChargeEntity.orderStatus.code", target = "orderStatus")
    @Mapping(source = "orderChargeEntity.orderStatus.value", target = "orderStatusValue")
    @Mapping(source = "userBaseInfoVO", target = "user")
    @Mapping(source = "chargeGoods", target = "chargeGoods")
    OrderChargeVO toOrderChargeVO(OrderChargeEntity orderChargeEntity, UserBaseInfoVO userBaseInfoVO, ChargeGoodsEntity chargeGoods);


    @Mapping(source = "userPackagePickupEntity.id", target = "id")
    @Mapping(source = "userPackagePickupEntity.createTime", target = "createTime")
    @Mapping(source = "userPackagePickupEntity.status.code", target = "status")
    @Mapping(source = "userPackagePickupEntity.status.value", target = "statusValue")
    @Mapping(source = "userBaseInfoVO", target = "user")
    @Mapping(source = "skin", target = "skin")
    OrderPickupVO toOrderPickupVO(UserPackagePickupEntity userPackagePickupEntity, UserBaseInfoVO userBaseInfoVO, SkinEntity skin);
}
