package com.steamgo1.csgoskinadmin.converter;


import com.steamgo1.csgoskincommon.config.MapStructConfig;
import com.steamgo1.csgoskinadmin.dto.DataDictionaryDTO;
import com.steamgo1.csgoskinadmin.dto.DataDictionaryUpdateDTO;
import com.steamgo1.csgoskincommon.entity.DataDictionaryEntity;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(config = MapStructConfig.class)
public interface DataDictionaryConverter {
    DataDictionaryConverter INSTANCE = Mappers.getMapper(DataDictionaryConverter.class);

    DataDictionaryDTO toDataDictionaryDTO(DataDictionaryEntity dataDictionaryEntity);

    DataDictionaryEntity toDataDictionaryEntity(DataDictionaryDTO dataDictionaryDTO);

    DataDictionaryEntity toDataDictionaryEntity(DataDictionaryUpdateDTO dataDictionaryUpdateDTO);
}
