package com.steamgo1.csgoskinadmin.converter;

import com.steamgo1.csgoskinadmin.dto.RollHomeUpdateDTO;
import com.steamgo1.csgoskinadmin.vo.RollHomeFullVO;
import com.steamgo1.csgoskincommon.entity.RollHomeEntity;
import com.steamgo1.csgoskincommon.entity.RollHomeSkinEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "Spring")
public interface RollHomeConverter {
    RollHomeConverter INSTANCE = Mappers.getMapper(RollHomeConverter.class);

    @Mapping(source = "rollHomeLotteryMethod.code", target = "rollHomeLotteryMethod")
    @Mapping(source = "rollHomeType.code", target = "rollHomeType")
    @Mapping(source = "rollHomeLotteryMethod.value", target = "rollHomeLotteryMethodValue")
    @Mapping(source = "rollHomeType.value", target = "rollHomeTypeValue")
    @Mapping(source = "status.code", target = "status")
    @Mapping(source = "status.value", target = "statusValue")
    @Mapping(source = "thresholdType.code", target = "thresholdType")
    @Mapping(source = "thresholdType.value", target = "thresholdTypeValue")
    RollHomeFullVO toRollHomeFullVO(RollHomeEntity rollHomeEntity);

    List<RollHomeFullVO> toRollHomeFullVOList(List<RollHomeEntity> rollHomeEntityList);

//    RollHomeEntity toRollHomeEntity(RollHomeUpdateDTO rollHomeUpdateDTO);

    RollHomeSkinEntity toRollHomeSkinEntityListOfUpdate(RollHomeUpdateDTO.RollHomeSkinDTO rollHomeSkinDTO);
}
