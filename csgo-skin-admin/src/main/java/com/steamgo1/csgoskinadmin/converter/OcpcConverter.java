package com.steamgo1.csgoskinadmin.converter;

import com.steamgo1.csgoskinadmin.vo.OcpcBaiduDataVO;
import com.steamgo1.csgoskinadmin.vo.OcpcMetaDataVO;
import com.steamgo1.csgoskincommon.entity.OcpcBaiduDataEntity;
import com.steamgo1.csgoskincommon.entity.OcpcMetaDataEntity;
import com.steamgo1.csgoskincommon.entity.OcpcChannelEntity;
import com.steamgo1.csgoskincommon.entity.OrderChargeEntity;
import com.steamgo1.csgoskincommon.entity.UserEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "Spring")
public interface OcpcConverter {
    OcpcConverter INSTANCE = Mappers.getMapper(OcpcConverter.class);

    @Mapping(source = "ocpcBaiduData", target = "ocpcBaiduData")
    @Mapping(source = "channel", target = "channel")
    @Mapping(source = "user", target = "user")
    @Mapping(source = "orderCharge", target = "orderCharge")
    OcpcBaiduDataVO toCoverOcpcBaiduDataVO(OcpcBaiduDataEntity ocpcBaiduData, OcpcChannelEntity channel, UserEntity user, OrderChargeEntity orderCharge);

    @Mapping(source = "ocpcMetaData", target = "ocpcMetaData")
    @Mapping(source = "channel", target = "channel")
    @Mapping(source = "user", target = "user")
    @Mapping(source = "orderCharge", target = "orderCharge")
    OcpcMetaDataVO toCoverOcpcMetaDataVO(OcpcMetaDataEntity ocpcMetaData, OcpcChannelEntity channel, UserEntity user, OrderChargeEntity orderCharge);
}
