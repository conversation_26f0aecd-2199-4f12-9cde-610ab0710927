package com.steamgo1.csgoskinadmin.converter;

import com.steamgo1.csgoskincommon.config.MapStructConfig;
import com.steamgo1.csgoskinadmin.dto.I18nDemoDTO;
import com.steamgo1.csgoskinadmin.vo.I18nDemoVO;
import com.steamgo1.csgoskincommon.converter.I18nConverter;
import com.steamgo1.csgoskincommon.entity.I18nDemoEntity;
import org.mapstruct.*;

import java.util.List;

/**
 * 国际化演示转换器
 * 使用MapStruct进行对象映射
 *
 * <AUTHOR>
 */
@Mapper(config = MapStructConfig.class)
public interface I18nDemoConverter extends I18nConverter {

    /**
     * 转换为VO - 前台使用（只返回当前语言）
     */
    @Mapping(target = "text", source = "i18nFieldText", qualifiedByName = "i18nFieldToCurrent")
    I18nDemoVO toI18nDemoVO(I18nDemoEntity entity);

    /**
     * 从DTO转换为Entity
     */
    I18nDemoEntity fromDTO(I18nDemoDTO dto);

    /**
     * 批量转换为VO
     */
    List<I18nDemoVO> toI18nDemoVOList(List<I18nDemoEntity> entities);

    /**
     * 批量从DTO转换
     */
    List<I18nDemoEntity> fromDTOList(List<I18nDemoDTO> dtos);

    // ==================== 高级映射方法 ====================

    /**
     * 更新实体（用于PUT请求）
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateEntityFromDTO(I18nDemoDTO dto, @MappingTarget I18nDemoEntity entity);

    /**
     * 部分更新实体（用于PATCH请求）
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    void patchEntityFromDTO(I18nDemoDTO dto, @MappingTarget I18nDemoEntity entity);

    /**
     * 转换为DTO
     */
    @Mapping(target = "text", source = "i18nFieldText", qualifiedByName = "i18nFieldToCurrent")
    I18nDemoDTO toDTO(I18nDemoEntity entity);

    /**
     * 批量转换为DTO
     */
    List<I18nDemoDTO> toDTOList(List<I18nDemoEntity> entities);

}
