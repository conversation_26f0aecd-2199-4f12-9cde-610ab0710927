package com.steamgo1.csgoskinadmin.converter;

import com.steamgo1.csgoskincommon.config.MapStructConfig;
import com.steamgo1.csgoskinadmin.vo.CaseFullVO;
import com.steamgo1.csgoskincommon.converter.I18nConverter;
import com.steamgo1.csgoskincommon.entity.CaseEntity;
import com.steamgo1.csgoskincommon.entity.CaseSkinEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(config = MapStructConfig.class)
public interface CaseConverter extends I18nConverter {
    CaseConverter INSTANCE = Mappers.getMapper(CaseConverter.class);

    @Mapping(source = "caseType.code", target = "type")
    @Mapping(source = "category", target = "categoryId")
    @Mapping(target = "name", source = "i18nFieldName", qualifiedByName = "i18nFieldToCurrent")
    CaseFullVO toCovertCaseFullVO(CaseEntity caseEntity);


    @Mapping(source = "caseSkinEntity.skinRarityColor.id", target = "skinRarityColorId")
    CaseFullVO.CaseSkinVO toCoverCaseSkin(CaseSkinEntity caseSkinEntity);

    List<CaseFullVO> toConvertList(List<CaseEntity> caseEntityList);
}
