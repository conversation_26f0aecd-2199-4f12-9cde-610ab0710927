package com.steamgo1.csgoskinadmin.converter;

import com.steamgo1.csgoskinadmin.dto.CardCollectAddDTO;
import com.steamgo1.csgoskinadmin.dto.CardCollectUpdateDTO;
import com.steamgo1.csgoskinadmin.vo.CardCollectVO;
import com.steamgo1.csgoskincommon.entity.CardCollectCardEntity;
import com.steamgo1.csgoskincommon.entity.CardCollectEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "Spring")
public interface ActivityConverter {
    ActivityConverter INSTANCE = Mappers.getMapper(ActivityConverter.class);


    CardCollectEntity toCardCollectEntity(CardCollectAddDTO collectAddDTO);

    CardCollectEntity toCardCollectEntity(CardCollectUpdateDTO cardCollectUpdateDTO);


    List<CardCollectCardEntity> toCardCollectCardEntity(List<CardCollectAddDTO.CardCollectCard> collectCardList);

    List<CardCollectCardEntity> toCardCollectCardEntityOfUpdate(List<CardCollectUpdateDTO.CardCollectCard> collectCardList);

    @Mapping(target = "cardList", source = "cardCollectCardEntityList")
    CardCollectVO toCardCollectVO(CardCollectEntity cardCollectEntity, List<CardCollectCardEntity> cardCollectCardEntityList);

}
