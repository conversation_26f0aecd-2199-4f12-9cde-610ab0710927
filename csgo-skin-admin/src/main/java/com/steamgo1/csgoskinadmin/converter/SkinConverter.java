package com.steamgo1.csgoskinadmin.converter;

import com.steamgo1.csgoskinadmin.vo.PercentageSkinQueryVO;
import com.steamgo1.csgoskincommon.entity.SkinEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "Spring")
public interface SkinConverter {
    SkinConverter INSTANCE = Mappers.getMapper(SkinConverter.class);


    @Mapping(source = "isDeleted", target = "isSell")
    PercentageSkinQueryVO toCoverPercentageSkinQueryVO(SkinEntity skin);
}
