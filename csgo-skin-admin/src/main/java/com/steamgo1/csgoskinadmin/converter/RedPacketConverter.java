package com.steamgo1.csgoskinadmin.converter;

import com.steamgo1.csgoskinadmin.vo.RedPacketVO;
import com.steamgo1.csgoskincommon.entity.RedPacketEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "Spring")
public interface RedPacketConverter {
    RedPacketConverter INSTANCE = Mappers.getMapper(RedPacketConverter.class);

    @Mapping(source = "type.code", target = "type")
    @Mapping(source = "type.value", target = "typeValue")
    @Mapping(source = "method.code", target = "method")
    @Mapping(source = "method.value", target = "methodValue")
    RedPacketVO toRedPacketVO(RedPacketEntity redPacketEntity);

    List<RedPacketVO> toRedPacketVOList(List<RedPacketEntity> redPacketEntityList);
}
