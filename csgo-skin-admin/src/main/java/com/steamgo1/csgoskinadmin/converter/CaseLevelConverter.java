package com.steamgo1.csgoskinadmin.converter;

import com.steamgo1.csgoskinadmin.dto.CaseLevelDTO;
import com.steamgo1.csgoskinadmin.vo.CaseFullVO;
import com.steamgo1.csgoskincommon.entity.CaseLevelEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "Spring")
public interface CaseLevelConverter {

    @Mapping(source = "caseLevelEntity.caseEntity.id", target = "caseId")
    CaseFullVO.CaseLevelVo toCovertCaseLevelVo(CaseLevelEntity caseLevelEntity);

    @Mapping(source = "caseLevelEntity.caseEntity.id", target = "caseId")
    @Mapping(source = "id", target = "levelId")
    CaseLevelDTO toCovertCaseLevelDTO(CaseLevelEntity caseLevelEntity);
}
