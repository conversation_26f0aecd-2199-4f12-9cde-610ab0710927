package com.steamgo1.csgoskinadmin.config;

import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.NoSuchElementException;

/**
 * 自定义错误处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Controller
@RestControllerAdvice
public class GlobalExceptionHandlerConfig {


    @ExceptionHandler(BindException.class)
    public Response<String> bindExceptionHandler(BindException e) {
        log.error("BindException: {}", e);
        return ResponseUtil.fail(e.getBindingResult().getFieldErrors().get(0).getDefaultMessage());
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Response<String> methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) {
        log.error("MethodArgumentNotValidException: {}", e);
        return ResponseUtil.fail(e.getBindingResult().getFieldErrors().get(0).getDefaultMessage());
    }

    @ExceptionHandler(CsgoSkinException.class)
    public Response<String> csgoSkinExceptionHandler(CsgoSkinException e) {
        log.error("CsgoSkinException: {}", e.getMessage());
        return ResponseUtil.fail(e.getMessage());
    }

    @ExceptionHandler(NoSuchElementException.class)
    public Response<String> noSuchElementExceptionHandler(NoSuchElementException e) {
        log.error("NoSuchElementException {}", e);
        // return ResponseUtil.fail("资源不存在");
        return ResponseUtil.fail(I18nUtils.getMessage("response.exception.resource.not.found"));
    }
}
