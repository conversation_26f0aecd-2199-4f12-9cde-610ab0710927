package com.steamgo1.csgoskinadmin.config.hander;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskincommon.enums.ResponseCode;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * token认证失败，toke不存在或已失效
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class JwtAuthFailure implements AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException e) throws IOException, ServletException {
        log.info("认证失败，token不存在或已失效");
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write(JSONObject.toJSONString(ResponseUtil.fail(ResponseCode.HTTP_STATUS_401)));
    }
}
