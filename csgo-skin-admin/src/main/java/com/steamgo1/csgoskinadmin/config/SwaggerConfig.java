package com.steamgo1.csgoskinadmin.config;

import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.oas.annotations.EnableOpenApi;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.AuthorizationScope;
import springfox.documentation.service.HttpAuthenticationScheme;
import springfox.documentation.service.SecurityReference;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;

import java.util.Arrays;

/**
 * swagger配置
 *
 * <AUTHOR>
 * @vesion 1.0
 * @date 2020/08/28
 * @since jdk1.8
 */
@Configuration
@EnableOpenApi
//@ConditionalOnProperty(name = "spring.profiles.active", havingValue = "prod", matchIfMissing = false)
public class SwaggerConfig {
    @Value("${swagger.enable: true}")
    private boolean swaggerEnable;
    @Value("${swagger.host}")
    private String host;

    @Value("${swagger.paths}")
    private String paths;

    private String version = "1.0";

    @Bean
    public Docket defaultApi() {
        return new Docket(DocumentationType.OAS_30)
                .host(host)
                .pathMapping(paths)
                .groupName("默认")
                .apiInfo(defaultApiInfo())
                .enable(swaggerEnable)
                .securitySchemes(Arrays.asList(tokenScheme()))
                .securityContexts(Arrays.asList(tokenContext()))
                .select()
                .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
                .paths(PathSelectors.any())
                .build()/*.forCodeGeneration(true)*/;

    }

    private ApiInfo defaultApiInfo() {
        return new ApiInfoBuilder()
                .title("csgo-skin-接口文档")
                .description("http://<IP:PORT>/<context-path>/doc.html")
                //服务条款网址
                .version(version)
                .build();
    }

    private HttpAuthenticationScheme tokenScheme() {
        return HttpAuthenticationScheme.JWT_BEARER_BUILDER.name("Authorization").build();
    }


    private SecurityContext tokenContext() {
        return SecurityContext.builder()
                .securityReferences(Arrays.asList(SecurityReference.builder()
                        .scopes(new AuthorizationScope[0])
                        .reference("Authorization")
                        .build()))
                .operationSelector(o -> o.requestMappingPattern().matches("/.*"))
                .build();
    }
}

