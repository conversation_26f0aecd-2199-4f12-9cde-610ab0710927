package com.steamgo1.csgoskinadmin.service.impl;

import com.steamgo1.csgoskinadmin.service.CaseSkinService;
import com.steamgo1.csgoskincommon.contant.CsgoContants;
import com.steamgo1.csgoskincommon.dao.CaseLevelRepository;
import com.steamgo1.csgoskincommon.dao.CaseSkinRepository;
import com.steamgo1.csgoskincommon.dao.SkinRepository;
import com.steamgo1.csgoskincommon.entity.CaseEntity;
import com.steamgo1.csgoskincommon.entity.CaseLevelEntity;
import com.steamgo1.csgoskincommon.entity.CaseSkinEntity;
import com.steamgo1.csgoskincommon.entity.SkinEntity;
import com.steamgo1.csgoskincommon.utils.BatchQueryUtil;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CaseSkinServiceImpl implements CaseSkinService {
    @Autowired
    private CaseSkinRepository caseSkinRepository;

    @Autowired
    private SkinRepository skinRepository;

    @Autowired
    private CaseLevelRepository caseLevelRepository;

    @Autowired
    @Qualifier(value = "change-case-skin-task")
    private Executor changeCaseSkinTask;

    @Value("${spring.redis.prefix.case}")
    private String redisCasePrefix;
    @Value("${spring.redis.prefix.skin}")
    private String redisSkinPrefix;

    @Value("${spring.redis.prefix.user}")
    private String redisUserPrefix;

    private static List<Long> deWeightCaseSkin(List<CaseSkinEntity> caseSkinEntities) {
        List<Long> caseEntitieIds = caseSkinEntities.stream()
                .filter(new Predicate<CaseSkinEntity>() {
                    private final Map<Object, Boolean> seen = new ConcurrentHashMap<>();

                    @Override
                    public boolean test(CaseSkinEntity caseSkinEntity) {
                        String key = String.valueOf(Optional.of(caseSkinEntity.getCaseEntity())
                                .map(CaseEntity::getId)
                                .orElse(0L));
                        return seen.putIfAbsent(key, Boolean.TRUE) == null;
                    }
                })
                .map(CaseSkinEntity::getCaseEntity)
                .map(CaseEntity::getId)
                .collect(Collectors.toList());
        return caseEntitieIds;
    }

    @Override
    public void updateCaseSkinTaskByZbt() {
        log.info("开始执行：updateCaseSkinTaskByZbt");
        //查询总数量
        Long count = caseSkinRepository.count();
        Long limit = CsgoContants.pageOffset.PAGE_SIZE_1;

        //把总数量分段
        List<BatchQueryUtil.Range> ranges = BatchQueryUtil.generateBatchRanges(count, limit);

        //非空校验
        if (ranges == null || ranges.isEmpty()) {
            log.info("CaseSkinServiceImpl：updateCaseSkinTaskByZbt：ranges：空的！");
            return;
        }

        //分段后的数量调用线程池
        for (BatchQueryUtil.Range range : ranges) {
            changeCaseSkinTask.execute(() -> {
                try {
                    updateCaseSkinByPrice(limit, range.getStart());
                } catch (Exception e) {
                    log.info("CaseSkinServiceImpl-updateCaseSkinTaskByZbt的线程{}执行失败，失败原因{}", Thread.currentThread().getId(), e.getMessage());
                }
            });
        }
    }

    @Override
    @Transactional
    public void updateCaseSkinByPrice(Long limit, Long offset) {
        //查询关联关系
        List<CaseSkinEntity> caseSkinEntities = caseSkinRepository.findByOffset(limit, offset);

        if (caseSkinEntities == null || caseSkinEntities.isEmpty()) {
            log.info("查询从{}开始的前{}没数据！！", limit, offset);
            return;
        }
        //修改绑定关系
        updateBindingCaseSkinByDiamond(caseSkinEntities);

        //删除这些箱子对应的缓存
        delCaseSession(caseSkinEntities);
    }

    private void updateBindingCaseSkinByDiamond(List<CaseSkinEntity> caseSkinEntities) {
        SkinEntity skinEntity;
        for (CaseSkinEntity caseSkinEntity : caseSkinEntities) {
            if (caseSkinEntity == null
                    || caseSkinEntity.getReferencePrice() == null) {
                continue;
            }
            //查询价格最接近的
            skinEntity = skinRepository.findSkinByReference(caseSkinEntity.getReferencePrice(), CsgoContants.BLACK_LIST_SKIN_NAME);

            //饰品数据有问题，跳过
            if (skinEntity == null) {
                continue;
            }

            //饰品和当前饰品是同一个，跳过
            if (skinEntity.getId().equals(caseSkinEntity.getSkin().getId())) {
                continue;
            }
            caseSkinEntity.setSkin(skinEntity);
            caseSkinRepository.save(caseSkinEntity);
        }
    }

    private void delCaseSession(List<CaseSkinEntity> caseSkinEntities) {
        List<Long> caseEntitieIds = deWeightCaseSkin(caseSkinEntities);
        if (caseEntitieIds == null || caseEntitieIds.isEmpty()) {
            return;
        }
        List<CaseLevelEntity> caseLevelEntities = caseLevelRepository.findByCaseEntityIdIn(caseEntitieIds);
        if (caseLevelEntities == null || caseLevelEntities.isEmpty()) {
            return;
        }
        for (CaseLevelEntity caseLevelEntity : caseLevelEntities) {
            if (caseLevelEntity == null
                    || caseLevelEntity.getLevelNum() == null
                    || caseLevelEntity.getCaseEntity() == null
                    || caseLevelEntity.getCaseEntity().getId() == null) {
                continue;
            }
            //删除这些level对应的缓存
            String redisCaseKey = redisCasePrefix + ":case_info:level:" + caseLevelEntity.getCaseEntity().getId() + caseLevelEntity.getLevelNum();
            RedisUtils.delete(redisCaseKey);
        }
    }
}
