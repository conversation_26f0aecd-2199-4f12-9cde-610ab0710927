package com.steamgo1.csgoskinadmin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinadmin.converter.UserConverter;
import com.steamgo1.csgoskinadmin.dto.*;
import com.steamgo1.csgoskinadmin.service.UserService;
import com.steamgo1.csgoskinadmin.vo.*;
import com.steamgo1.csgoskincommon.dao.*;
import com.steamgo1.csgoskincommon.entity.*;
import com.steamgo1.csgoskincommon.entity.enums.PackagePickupStatus;
import com.steamgo1.csgoskincommon.entity.enums.UserCoinChangeSource;
import com.steamgo1.csgoskincommon.entity.enums.UserPackageSource;
import com.steamgo1.csgoskincommon.entity.enums.UserType;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.service.ZBTService;
import com.steamgo1.csgoskincommon.utils.CharUtil;
import com.steamgo1.csgoskincommon.utils.HashUtils;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import com.steamgo1.csgoskincommon.vo.ZBTSteamInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class UserServiceImpl implements UserService {
    @Value("${spring.redis.prefix.token-front}")
    private String redisTokenPrefix;

    @Value("${site.default-user-avator}")
    private String defaultAvator;

    @Value("${rabbitmq.exchange.csgo}")
    private String exchageName;

    @Autowired
    private UserConverter userConverter;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private UserDisableRepository userDisableRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AlgorithmDataRepository algorithmDataRepository;

    @Autowired
    private UserCoinRecordRepository userCoinRecordRepository;

    @Autowired
    private UserPackageRepository userPackageRepository;

    @Autowired
    private OcpcChannelRepository ocpcChannelRepository;

    @Autowired
    private OrderChargeRepository orderChargeRepository;

    @Autowired
    private ZBTService zbtService;

    @Autowired
    private UserInviteRepository userInviteRepository;

    @Autowired
    private UserPackagePickupRepository userPackagePickupRepository;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private UserDiamondRecordRepository userDiamondRecordRepository;


    @Autowired
    private SkinRepository skinRepository;

    @Autowired
    private CaseRepository caseRepository;

    @Autowired
    private UserLogRepository userLogRepository;

    @Override
    public Page<UserInfoVO> queryUser(UserQueryDTO userQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(userQueryDTO.getPage(), userQueryDTO.getSize(), sort);
        Page<UserEntity> userEntityPage = userRepository.findAll((Specification<UserEntity>) (root, query, cb) -> {
            List<Predicate> ps = new ArrayList<>();
            if (!StringUtils.isEmpty(userQueryDTO.getName())) {
                ps.add(cb.like(root.get("nickname"), "%" + userQueryDTO.getName() + "%"));
            }
            if (!StringUtils.isEmpty(userQueryDTO.getPhone())) {
                ps.add(cb.like(root.get("phone"), "%" + userQueryDTO.getPhone() + "%"));
            }
            if (userQueryDTO.getUserId() != null) {
                ps.add(cb.equal(root.get("id"), userQueryDTO.getUserId()));
            }
            if (userQueryDTO.getType() != null) {
                ps.add(cb.equal(root.get("type"), userQueryDTO.getType()));
            }
            // 渠道 TODO
            if (userQueryDTO.getChannel() != null) {
                ps.add(cb.equal(root.get("channel"), userQueryDTO.getChannel()));
            }
            if (userQueryDTO.getStartTime() != null && userQueryDTO.getEndTime() != null) {
                ps.add(cb.between(root.get("createTime"), userQueryDTO.getStartTime(), userQueryDTO.getEndTime()));
            }
            if (userQueryDTO.getIsFirstCharge() != null) {
                ps.add(cb.equal(root.get("isFirstCharge"), userQueryDTO.getIsFirstCharge()));
            }
            return query.where(ps.toArray(new Predicate[ps.size()])).getRestriction();
        }, pageable);
        DecimalFormat decimalFormat = new DecimalFormat("#00.00");
//        String freeBoxRatio = decimalFormat.format(userCoinRecordRepository.totalUserCoin(item.getId())==0?0:((float)orderChargeRepository.userTotalCharge(item.getId())/(float)userCoinRecordRepository.totalUserCoin(item.getId()))*(float)100);
        List<UserInfoVO> userInfoVOList = userEntityPage.getContent()
                .stream()
                .map(item ->
                        userConverter.toUserInfoVO(item,
                                userProfileRepository.findByUser(item),
                                algorithmDataRepository.findByUserIdAndIsUsedIsTrue(item.getId()),
                                item.getChannel(), orderChargeRepository.userTotalCharge(item.getId()),
                                userPackageRepository.userPackageTotalValue(item.getId()),
                                userCoinRecordRepository.totalUserCoin(item.getId()),
                                "",
                                userLogRepository.findTopByUserOrderByLoginTimeDesc(item) == null ? null : userLogRepository.findTopByUserOrderByLoginTimeDesc(item).getLoginTime()))
                .collect(Collectors.toList());
        for (UserInfoVO userInfoVO : userInfoVOList) {
            BigDecimal userPickupValue = userPackageRepository.userPickUpPackageTotalValue(userInfoVO.getId());
            BigDecimal userTotalChange = orderChargeRepository.userTotalCharge(userInfoVO.getId());
            userTotalChange = userTotalChange == null ? BigDecimal.ZERO : userTotalChange;
            userPickupValue = userPickupValue == null ? BigDecimal.ZERO : userPickupValue;
            userInfoVO.setPickUpPackageValue(userPickupValue);
            userInfoVO.setLossValue(userTotalChange.subtract(userPickupValue));
            if (userInfoVO.getTotalCoin() == null) {
                userInfoVO.setTotalCoin(BigDecimal.ZERO);
            }
            if (userInfoVO.getTotalCoin().equals(BigDecimal.ZERO)) {
                userInfoVO.setTotalCoinRate("00.00");
                continue;
            }
            if (userInfoVO.getTotalCharge().equals(BigDecimal.ZERO)) {
                userInfoVO.setTotalCoinRate("00.00");
                continue;
            }
            userInfoVO.setTotalCoinRate(decimalFormat.format(userInfoVO.getTotalCoin().divide(userInfoVO.getTotalCharge(), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100))));
//            userCoinRecordRepository.totalUserCoin(item.getId())==null?"00.00":decimalFormat.format(userCoinRecordRepository.totalUserCoin(item.getId())==null?BigDecimal.ZERO:(orderChargeRepository.userTotalCharge(item.getId()).divide(userCoinRecordRepository.totalUserCoin(item.getId()))).multiply(BigDecimal.valueOf(100)))
        }
        return new PageImpl<>(userInfoVOList, userEntityPage.getPageable(), userEntityPage.getTotalElements());
    }

    @Override
    public Page<UserDisableQueryVO> queryUserDisable(UserDisableQueryDTO userDisableQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(userDisableQueryDTO.getPage(), userDisableQueryDTO.getSize(), sort);
        Page<UserDisableEntity> userDisableEntityPage = userDisableRepository.findAll((Specification<UserDisableEntity>) (root, query, cb) -> {
            Join<UserDisableEntity, UserEntity> join = root.join("user", JoinType.LEFT);
            List<Predicate> ps = new ArrayList<>();
            if (!StringUtils.isEmpty(userDisableQueryDTO.getName())) {
                ps.add(cb.like(join.get("nickname"), "%" + userDisableQueryDTO.getName() + "%"));
            }
            if (userDisableQueryDTO.getUserId() != null) {
                ps.add(cb.equal(join.get("id"), userDisableQueryDTO.getUserId()));
            }
            if (userDisableQueryDTO.getIsEffective() != null) {
                ps.add(cb.equal(root.get("isEffective"), userDisableQueryDTO.getIsEffective()));
            }
            return query.where(ps.toArray(new Predicate[ps.size()])).getRestriction();
        }, pageable);
        List<UserDisableQueryVO> userDisableQueryVOList = userDisableEntityPage.getContent().stream().map(item -> userConverter.toUserDisableVO(item, item.getUser())).collect(Collectors.toList());
        return new PageImpl<>(userDisableQueryVOList, userDisableEntityPage.getPageable(), userDisableEntityPage.getTotalElements());
    }

    @Override
    public void userDisable(Long id) {
        UserDisableEntity userDisable = userDisableRepository.findById(id).orElse(null);
        if (userDisable != null) {
//            userDisable.setIsEffective(true);
            log.info("++++++++++++++++++++++++++++++++++++{}", userDisable.getIsEffective());
            userDisable.setIsEffective(true);
            log.info("++++++++++++++++++++++++++++++++++++{}", userDisable.getIsEffective());
            userDisableRepository.save(userDisable);
        }
    }

    @Override
    public void userEnable(Long id) {
        UserDisableEntity userDisable = userDisableRepository.findById(id).orElse(null);
        if (userDisable != null) {
            log.info("++++++++++++++++++++++++++++++++++++{}", userDisable.getIsEffective());

            userDisable.setIsEffective(false);
            log.info("++++++++++++++++++++++++++++++++++++{}", userDisable.getIsEffective());
            userDisableRepository.save(userDisable);
        }
    }

    @Override
    public Page<UserPackageVO> queryUserPackage(UserQueryDTO userQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(userQueryDTO.getPage(), userQueryDTO.getSize(), sort);
        Page<UserEntity> userEntityPage = userRepository.findAll((Specification<UserEntity>) (root, query, cb) -> {
            List<Predicate> ps = new ArrayList<>();
            if (!StringUtils.isEmpty(userQueryDTO.getName())) {
                ps.add(cb.like(root.get("nickname"), "%" + userQueryDTO.getName() + "%"));
            }
            if (!StringUtils.isEmpty(userQueryDTO.getPhone())) {
                ps.add(cb.like(root.get("phone"), "%" + userQueryDTO.getPhone() + "%"));
            }
            if (userQueryDTO.getUserId() != null) {
                ps.add(cb.equal(root.get("id"), userQueryDTO.getUserId()));
            }
            if (userQueryDTO.getType() != null) {
                ps.add(cb.equal(root.get("type"), userQueryDTO.getType() - 1));
            }
            // 渠道 TODO
            if (userQueryDTO.getStartTime() != null && userQueryDTO.getEndTime() != null) {
                ps.add(cb.between(root.get("updateTime"), userQueryDTO.getStartTime(), userQueryDTO.getEndTime()));
            }
            return query.where(ps.toArray(new Predicate[ps.size()])).getRestriction();
        }, pageable);
        List<UserPackageVO> userPackageVOList = userEntityPage.getContent().stream().map(item -> userConverter.toUserPackageVO(userConverter.toUserBaseInfoVO(item), userPackageRepository.countByUserIdAndIsReceivedIsFalseAndIsSelledIsFalse(item.getId()), userPackageRepository.userPackageTotalValue(item.getId()))).collect(Collectors.toList());
        return new PageImpl<>(userPackageVOList, userEntityPage.getPageable(), userEntityPage.getTotalElements());
    }

    @Override
    public Page<UserPackageDetialsVO> queryUserPackageDetails(UserIdPageQueryDTO userIdPageQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(userIdPageQueryDTO.getPage(), userIdPageQueryDTO.getSize(), sort);
        Page<UserPackageEntity> userPackageEntityPage = userPackageRepository.findAll((Specification<UserPackageEntity>) (root, query, cb) -> {
            List<Predicate> ps = new ArrayList<>();
            if (userIdPageQueryDTO.getUserId() != null) {
                ps.add(cb.equal(root.get("user"), userIdPageQueryDTO.getUserId()));
            }
            return query.where(ps.toArray(new Predicate[ps.size()])).getRestriction();
        }, pageable);
        List<UserPackageDetialsVO> userPackageDetialsVOList = userPackageEntityPage.getContent().stream().map(item ->
        {
            item.getSkin().setDiamond(item.getDiamond());
            return userConverter.toUserPackageDetails(item);
        }).collect(Collectors.toList());
        for (UserPackageDetialsVO userPackageDetialsVO : userPackageDetialsVOList) {
            if (userPackageDetialsVO.getCaseId() != null) {
                CaseEntity caseEntity = caseRepository.findById(userPackageDetialsVO.getCaseId());
                if (caseEntity != null) {
                    userPackageDetialsVO.setCaseName(caseEntity.getName());
                }
            }
        }
        return new PageImpl<>(userPackageDetialsVOList, userPackageEntityPage.getPageable(), userPackageEntityPage.getTotalElements());
    }

    @Override
    public void sysUserCharge(SysUserChangeDTO sysUserChangeDTO) {
        UserEntity user = userRepository.findById(sysUserChangeDTO.getUserId());
        if(user==null){
            throw new CsgoSkinException(I18nUtils.getMessage("exception.user.not.found"));
        }
        if(sysUserChangeDTO.getCoin()==null || sysUserChangeDTO.getCoin().compareTo(BigDecimal.ZERO)==0){
            throw new CsgoSkinException(I18nUtils.getMessage("exception.user.charge.amount.invalid"));
        }
//            用户钱包等数据
        UserProfileEntity userProfile = userProfileRepository.findByUserId(user.getId());
        userProfile.setCoin(userProfile.getCoin().add(sysUserChangeDTO.getCoin()));
        // 金币记录
        UserCoinRecordEntity userCoinRecordEntity = new UserCoinRecordEntity();
        userCoinRecordEntity.setUser(user);
        userCoinRecordEntity.setSource(UserCoinChangeSource.SYS);
        userCoinRecordEntity.setAmount(sysUserChangeDTO.getCoin());
        userCoinRecordEntity.setAfterAmount(userProfile.getCoin());
        userCoinRecordEntity.setIsPositive(true);
        userCoinRecordRepository.save(userCoinRecordEntity);
        userProfileRepository.save(userProfile);
    }

    @Override
    public void disable(Long id) {
        UserEntity user = userRepository.findById(id);
        if(user!=null){
            user.setIsBan(true);
            userRepository.save(user);
            String key = redisTokenPrefix + ":" + user.getId();
            RedisUtils.delete(key);
        }
    }

    @Override
    public void enable(Long id) {
        UserEntity user = userRepository.findById(id);
        if (user != null) {
            user.setIsBan(false);
            userRepository.save(user);
        }
    }

    @Override
    public void syncStreamInfo() {
        for (UserEntity userEntity : userRepository.findAll()) {
            if (!StringUtils.isEmpty(userEntity.getTradeOfferAccessUrl())) {
                ZBTSteamInfoVO zbtSteamInfoVO = zbtService.querySteamInfoByTradeUrl(userEntity.getTradeOfferAccessUrl());
                if (zbtSteamInfoVO != null) {
                    log.info(zbtSteamInfoVO.toString());
                    userEntity.setNickname(zbtSteamInfoVO.getSteamInfo().getNickName());
                    userEntity.setAvatar(zbtSteamInfoVO.getSteamInfo().getAvatar());
                    userRepository.save(userEntity);
                }
            }
        }
    }

    @Override
    public void addRobot(Integer total, UserType type) {
        if (type.equals(UserType.ACTUAL) || type.equals(UserType.INSIDER)) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.user.type.invalid"));
        }
        if (total > 10) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.user.robot.add.limit.exceeded"));
        }
        log.info("当前 {} 数量: {}", type.getValue(), userRepository.countByType(type));
        for (int i = 0; i < total; i++) {
            log.info("开始创建{} : {}", type.getValue(), i);
            UserEntity user = new UserEntity();
            user.setPhone(CharUtil.getRandomNum(11));
            user.setNickname(CharUtil.getRandomString(8).toUpperCase());
            user.setAvatar(defaultAvator);
            user.setType(type);
            user = userRepository.save(user);
//            初始化用户算法数据
            AlgorithmDataEntity algorithmDataEntity = new AlgorithmDataEntity();
            algorithmDataEntity.setUser(user);
            algorithmDataEntity.setRounds(1);
            algorithmDataEntity.setSecretHash(CharUtil.getRandomString(128));
            algorithmDataEntity.setSecretSalt(CharUtil.getRandomString(64));
            algorithmDataEntity.setPublicHash(HashUtils.SHA256(algorithmDataEntity.getSecretHash(), algorithmDataEntity.getSecretHash()));
            algorithmDataEntity.setClientSeed(CharUtil.getRandomString(64));
            algorithmDataEntity.setIsUsed(true);
//            用户钱包等数据
            UserProfileEntity userProfile = new UserProfileEntity();
            userProfile.setUser(user);
            userProfile.setDiamond(BigDecimal.ZERO);
            BigDecimal freeCoin = BigDecimal.valueOf(10000000);
            userProfile.setCoin(freeCoin);
            userProfile.setLavel(1);
            userProfile.setExperience(0);
            // 初始化推广信息
            UserInviteEntity userInviteEntity = new UserInviteEntity();
            userInviteEntity.setUser(user);
            userInviteEntity.setCode(CharUtil.getRandomString(8));
            userInviteEntity.setTotalCharge(BigDecimal.ZERO);
            userInviteEntity.setTotalEncourage(BigDecimal.ZERO);
            userInviteEntity.setTotalRegister(0);
            // 金币记录
            if (freeCoin.compareTo(BigDecimal.ZERO) != 0) {
                UserCoinRecordEntity userCoinRecordEntity = new UserCoinRecordEntity();
                userCoinRecordEntity.setUser(user);
                userCoinRecordEntity.setSource(UserCoinChangeSource.SYS);
                userCoinRecordEntity.setAmount(freeCoin);
                userCoinRecordEntity.setAfterAmount(userProfile.getCoin());
                userCoinRecordEntity.setIsPositive(true);
                userCoinRecordRepository.save(userCoinRecordEntity);
            }
            algorithmDataRepository.save(algorithmDataEntity);
            userProfileRepository.save(userProfile);
            userInviteRepository.save(userInviteEntity);
        }
    }

    @Override
    public void updateUserInfo(UserInfoUpdateVO userInfoUpdateVO) {
//        更新用户名
        UserEntity user = userRepository.findById(userInfoUpdateVO.getId());
        if (user == null) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.user.not.found"));
        }
//        if(user.getType().equals(UserType.ACTUAL)){
//           // todo 国际化
        //throw new CsgoSkinException("不支持修改的用户类型");
//        }
        if (!StringUtils.isEmpty(userInfoUpdateVO.getNickname()) && !user.getType().equals(UserType.ACTUAL)) {
            user.setNickname(userInfoUpdateVO.getNickname());
        }
        if (!StringUtils.isEmpty(userInfoUpdateVO.getAvatar()) && !user.getType().equals(UserType.ACTUAL)) {
            user.setAvatar(userInfoUpdateVO.getAvatar());
        }
        if (userInfoUpdateVO.getType() != null) {
            user.setType(UserType.instance(userInfoUpdateVO.getType()));
        }
        userRepository.save(user);
    }

    @Override
    public void checkUserPickup(UserPickupCheckDTO userPickupCheckDTO) {
        UserPackagePickupEntity userPackagePickupEntity = userPackagePickupRepository.findById(userPickupCheckDTO.getId()).orElse(null);
        if (userPackagePickupEntity != null) {
            if (userPickupCheckDTO.getIsAgree()) {
                UserPackageEntity userPackageEntity = userPackagePickupEntity.getUserPackage();
                userPackageEntity.setPrice(userPackageEntity.getSkin().getPrice());
                userPackageRepository.save(userPackageEntity);
                userPackagePickupEntity.setStatus(PackagePickupStatus.PICKUPING);
                // 放入队列
                new Timer().schedule(new TimerTask() {
                    @Override
                    public void run() {
                        rabbitTemplate.convertAndSend(exchageName, "skin.pickup", JSONObject.toJSON(userPackagePickupEntity));
                    }
                }, 10 * 1000);
            } else {
                userPackagePickupEntity.setStatus(PackagePickupStatus.FAIL);
                userPackagePickupEntity.setRemarks(I18nUtils.getMessage("contact.admin"));
                UserPackageEntity userPackageEntity = userPackagePickupEntity.getUserPackage();
                userPackageEntity.setIsReceived(false);
                userPackageRepository.save(userPackageEntity);
            }
            userPackagePickupRepository.save(userPackagePickupEntity);

        }
    }

    @Override
    public Page<UserCoinRecordVO> queryUserCoinRecord(UserIdPageQueryDTO userRecordQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(userRecordQueryDTO.getPage(), userRecordQueryDTO.getSize(), sort);
        Page<UserCoinRecordEntity> userCoinRecordEntityPage = userCoinRecordRepository.findByUserId(userRecordQueryDTO.getUserId(), pageable);
        List<UserCoinRecordVO> userCoinRecordVOList = userCoinRecordEntityPage.getContent().stream().map(item -> userConverter.toUserCoinRecordVO(item)).collect(Collectors.toList());
        return new PageImpl<>(userCoinRecordVOList, userCoinRecordEntityPage.getPageable(), userCoinRecordEntityPage.getTotalElements());
    }

    @Override
    public Page<UserDiamondRecordVO> queryUserDiamondRecord(UserIdPageQueryDTO userRecordQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(userRecordQueryDTO.getPage(), userRecordQueryDTO.getSize(), sort);
        Page<UserDiamondRecordEntity> userDiamondRecordEntityPage = userDiamondRecordRepository.findByUserId(userRecordQueryDTO.getUserId(), pageable);
        List<UserDiamondRecordVO> userDiamondRecordVOList = userDiamondRecordEntityPage.getContent().stream().map(item -> userConverter.toUserDiamondRecordVO(item)).collect(Collectors.toList());
        return new PageImpl<>(userDiamondRecordVOList, userDiamondRecordEntityPage.getPageable(), userDiamondRecordEntityPage.getTotalElements());
    }

    @Override
    public void updateUserPublicHash() {
        List<AlgorithmDataEntity> algorithmDataEntityList = algorithmDataRepository.findAll();
        log.info("开始更新用户公共hash");
        for (AlgorithmDataEntity algorithmDataEntity : algorithmDataEntityList) {
            algorithmDataEntity.setPublicHash(HashUtils.SHA256(algorithmDataEntity.getSecretHash(), algorithmDataEntity.getSecretSalt()));
            algorithmDataRepository.save(algorithmDataEntity);
        }
        log.info("开始更新用户公共hash完成");
    }

    @Override
    public void addUserPackageSkin(UserAddSkinDTO userAddSkinDTO) {
        UserEntity user = userRepository.findById(userAddSkinDTO.getUserId());
        if (user == null) {
            throw new RuntimeException(I18nUtils.getMessage("exception.user.not.found"));
        }
        SkinEntity skin = skinRepository.findById(userAddSkinDTO.getSkinId()).orElse(null);
        if (skin == null) {
            throw new CsgoSkinException(I18nUtils.getMessage("exception.skin.not.exist"));
        }
        UserPackageEntity userPackage = new UserPackageEntity();
        userPackage.setSkin(skin);
        userPackage.setPrice(skin.getPrice());
        userPackage.setDiamond(skin.getDiamond());
        userPackage.setUser(user);
        userPackage.setSource(UserPackageSource.SYSTEM);
        userPackage.setIsLocked(false);
        userPackage.setIsReceived(false);
        userPackage.setIsSelled(false);
        userPackageRepository.save(userPackage);
    }

}
