package com.steamgo1.csgoskinadmin.service.impl;

import com.steamgo1.csgoskinadmin.converter.SkinConverter;
import com.steamgo1.csgoskinadmin.dto.IdsDTO;
import com.steamgo1.csgoskinadmin.dto.PercentageRecordQueryDTO;
import com.steamgo1.csgoskinadmin.dto.PercentageSkinQueryDTO;
import com.steamgo1.csgoskinadmin.service.PercentageService;
import com.steamgo1.csgoskinadmin.vo.PercentageSkinQueryVO;
import com.steamgo1.csgoskincommon.dao.RecordPercentageRepository;
import com.steamgo1.csgoskincommon.dao.SkinRepository;
import com.steamgo1.csgoskincommon.entity.CaseUserRecordEntity;
import com.steamgo1.csgoskincommon.entity.PercentageUserRecordEntity;
import com.steamgo1.csgoskincommon.entity.SkinEntity;
import com.steamgo1.csgoskincommon.entity.UserEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class PercentageServiceImpl implements PercentageService {

    @Autowired
    private SkinRepository skinRepository;

    @Autowired
    private SkinConverter skinConverter;

    @Autowired
    private RecordPercentageRepository recordPercentageRepository;

    @Override
    public Page<PercentageSkinQueryVO> queryPercentageSkin(PercentageSkinQueryDTO percentageSkinQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(percentageSkinQueryDTO.getPage(), percentageSkinQueryDTO.getSize(), sort);
        Page<SkinEntity> skinEntityPage = skinRepository.findAll((Specification<SkinEntity>) (root, query, cb) -> {
            List<Predicate> ps = new ArrayList<>();
            if (!StringUtils.isEmpty(percentageSkinQueryDTO.getName())) {
                ps.add(cb.like(root.get("name"), "%" + percentageSkinQueryDTO.getName() + "%"));
            }
            ps.add(cb.equal(root.get("enablePercentage"), 1));
            return query.where(ps.toArray(new Predicate[ps.size()])).getRestriction();
        }, pageable);
        List<PercentageSkinQueryVO> percentageSkinQueryVOList = skinEntityPage.getContent().stream().map(item -> skinConverter.toCoverPercentageSkinQueryVO(item)).collect(Collectors.toList());
        for (PercentageSkinQueryVO percentageSkinQueryVO : percentageSkinQueryVOList) {
            percentageSkinQueryVO.setSelms(recordPercentageRepository.countBySkinId(percentageSkinQueryVO.getId()));
        }
        return new PageImpl<>(percentageSkinQueryVOList, skinEntityPage.getPageable(), skinEntityPage.getTotalElements());
    }

    @Override
    public Page<PercentageSkinQueryVO> queryRandomSkin(PercentageSkinQueryDTO percentageSkinQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(percentageSkinQueryDTO.getPage(), percentageSkinQueryDTO.getSize(), sort);
        Page<SkinEntity> skinEntityPage = skinRepository.findAll((Specification<SkinEntity>) (root, query, cb) -> {
            List<Predicate> ps = new ArrayList<>();
            if (!StringUtils.isEmpty(percentageSkinQueryDTO.getName())) {
                ps.add(cb.like(root.get("name"), "%" + percentageSkinQueryDTO.getName() + "%"));
            }
            ps.add(cb.equal(root.get("enableRandom"), 1));
            return query.where(ps.toArray(new Predicate[ps.size()])).getRestriction();
        }, pageable);
        List<PercentageSkinQueryVO> percentageSkinQueryVOList = skinEntityPage.getContent().stream().map(item -> skinConverter.toCoverPercentageSkinQueryVO(item)).collect(Collectors.toList());
        for (PercentageSkinQueryVO percentageSkinQueryVO : percentageSkinQueryVOList) {
            percentageSkinQueryVO.setSelms(recordPercentageRepository.countBySkinId(percentageSkinQueryVO.getId()));
        }
        return new PageImpl<>(percentageSkinQueryVOList, skinEntityPage.getPageable(), skinEntityPage.getTotalElements());
    }

    @Override
    public void addPercentageSkin(IdsDTO percentageSkinAddVO) {
        for (Long id : percentageSkinAddVO.getIds()) {
            SkinEntity skin = skinRepository.findById(id).orElse(null);
            if (skin != null) {
                skin.setEnablePercentage(true);
                skinRepository.save(skin);
            }
        }
    }

    @Override
    public void addPercentageRandomSkinAddVO(IdsDTO percentageRandomSkinAddVO) {
        for (Long id : percentageRandomSkinAddVO.getIds()) {
            SkinEntity skin = skinRepository.findById(id).orElse(null);
            if (skin != null) {
                skin.setEnableRandom(true);
                skinRepository.save(skin);
            }
        }
    }

    @Override
    public void delPercentageSkin(IdsDTO idsDTO) {
        for (Long id : idsDTO.getIds()) {
            SkinEntity skin = skinRepository.findById(id).orElse(null);
            if (skin != null) {
                skin.setEnablePercentage(false);
                skinRepository.save(skin);
            }
        }
    }

    @Override
    public void delPercentageRandomSkin(IdsDTO idsDTO) {
        for (Long id : idsDTO.getIds()) {
            SkinEntity skin = skinRepository.findById(id).orElse(null);
            if (skin != null) {
                skin.setEnableRandom(false);
                skinRepository.save(skin);
            }
        }
    }

    @Override
    public Page<PercentageUserRecordEntity> querPercentageRecord(PercentageRecordQueryDTO percentageRecordQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(percentageRecordQueryDTO.getPage(), percentageRecordQueryDTO.getSize(), sort);
        Page<PercentageUserRecordEntity> percentageUserRecordEntityPage = recordPercentageRepository.findAll((Specification<PercentageUserRecordEntity>) (root, query, cb) -> {
            Join<CaseUserRecordEntity, UserEntity> join = root.join("user", JoinType.LEFT);
            List<Predicate> ps = new ArrayList<>();
            if (percentageRecordQueryDTO.getUserId() != null) {
                ps.add(cb.equal(root.get("user"), percentageRecordQueryDTO.getUserId()));
            }
            if (!cc.siyecao.uid.core.utils.StringUtils.isBlank(percentageRecordQueryDTO.getPhone())) {
                ps.add(cb.like(join.get("phone"), "%" + percentageRecordQueryDTO.getPhone() + "%"));
            }
            if (percentageRecordQueryDTO.getSkinId() != null) {
                ps.add(cb.equal(root.get("skin"), percentageRecordQueryDTO.getSkinId()));
            }
            if (percentageRecordQueryDTO.getIsWin() != null) {
                ps.add(cb.equal(root.get("isWin"), percentageRecordQueryDTO.getIsWin()));
            }
            if (percentageRecordQueryDTO.getStartTime() != null && percentageRecordQueryDTO.getEndTime() != null) {
                ps.add(cb.between(root.get("createTime"), percentageRecordQueryDTO.getStartTime(), percentageRecordQueryDTO.getEndTime()));
            }
            return query.where(ps.toArray(new Predicate[ps.size()])).getRestriction();
        }, pageable);
        return percentageUserRecordEntityPage;
    }

}
