package com.steamgo1.csgoskinadmin.service;

import com.steamgo1.csgoskinadmin.dto.*;
import com.steamgo1.csgoskinadmin.vo.UserDisableQueryParamVO;
import com.steamgo1.csgoskincommon.entity.AnnouncementEntity;
import com.steamgo1.csgoskincommon.entity.DataDictionaryEntity;
import org.springframework.data.domain.Page;

import java.util.List;

public interface SiteService {
    /**
     * 查询字典
     *
     * @param dataDictionaryQueryDTO
     * @return
     */
    Page<DataDictionaryEntity> queryDataDictionary(DataDictionaryQueryDTO dataDictionaryQueryDTO);

    /**
     * 添加字典
     *
     * @param dataDictionaryDTO
     * @return
     */
    DataDictionaryEntity addDataDicationary(DataDictionaryDTO dataDictionaryDTO);


    /**
     * 更新字典
     *
     * @param dataDictionaryUpdateDTO
     * @return
     */
    DataDictionaryEntity updateDataDicationary(DataDictionaryUpdateDTO dataDictionaryUpdateDTO);

    /**
     * 删除字典
     *
     * @param idsDTO
     */
    void delDataDicationary(IdsDTO idsDTO);

    /**
     * 获取风控类型
     *
     * @return
     */
    UserDisableQueryParamVO queryUserDisableQUeryParam();

    /**
     * 添加公告
     */
    AnnouncementEntity addAnnouncement(AnnouncementAddDTO announcementAddDTO);


    /**
     * 查询公告
     */
    List<AnnouncementEntity> queryAnnouncement();

    /**
     * 上线公告
     */
    void onlineAnnouncement(Long id);


    /**
     * 下线公告
     */
    void unOnlineAnnouncement(Long id);

}
