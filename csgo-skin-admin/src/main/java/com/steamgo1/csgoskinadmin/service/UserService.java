package com.steamgo1.csgoskinadmin.service;

import com.steamgo1.csgoskinadmin.dto.*;
import com.steamgo1.csgoskinadmin.vo.*;
import com.steamgo1.csgoskincommon.entity.enums.UserType;
import org.springframework.data.domain.Page;

public interface UserService {
    Page<UserInfoVO> queryUser(UserQueryDTO userQueryDTO);

    Page<UserDisableQueryVO> queryUserDisable(UserDisableQueryDTO userDisableQueryDTO);

    /**
     * 风控ID
     *
     * @param id
     */
    void userDisable(Long id);

    /**
     * 风控ID
     *
     * @param id
     */
    void userEnable(Long id);

    /**
     * 用户背包查询
     *
     * @param userQueryDTO
     * @return
     */
    Page<UserPackageVO> queryUserPackage(UserQueryDTO userQueryDTO);

    /**
     * 用户背包明细
     *
     * @param userIdPageQueryDTO
     * @return
     */
    Page<UserPackageDetialsVO> queryUserPackageDetails(UserIdPageQueryDTO userIdPageQueryDTO);

    /**
     * 系统用户充值
     *
     * @param sysUserChangeDTO
     */
    void sysUserCharge(SysUserChangeDTO sysUserChangeDTO);

    /**
     * ban用户
     *
     * @param id
     */
    void disable(Long id);

    /**
     * 解ban用户
     *
     * @param id
     */
    void enable(Long id);

    /**
     * 同步steam信息
     */
    void syncStreamInfo();

    /**
     * 添加机器人
     */
    void addRobot(Integer total, UserType type);

    /**
     * 更新用户信息
     *
     * @param userInfoUpdateVO
     */
    void updateUserInfo(UserInfoUpdateVO userInfoUpdateVO);

    /**
     * 用户取回审核
     *
     * @param userPickupCheckDTO
     */
    void checkUserPickup(UserPickupCheckDTO userPickupCheckDTO);

    /**
     * 用户金币流水查询
     *
     * @param userIdPageQueryDTO
     * @return
     */
    Page<UserCoinRecordVO> queryUserCoinRecord(UserIdPageQueryDTO userIdPageQueryDTO);

    /**
     * 用户钻石流水查询
     *
     * @param userIdPageQueryDTO
     * @return
     */
    Page<UserDiamondRecordVO> queryUserDiamondRecord(UserIdPageQueryDTO userIdPageQueryDTO);

    /**
     * 更新用户公共hash
     */
    void updateUserPublicHash();

    /**
     * 给用户背包添加饰品
     */
    void addUserPackageSkin(UserAddSkinDTO userAddSkinDTO);
}
