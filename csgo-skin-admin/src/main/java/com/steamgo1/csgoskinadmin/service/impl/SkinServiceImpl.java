package com.steamgo1.csgoskinadmin.service.impl;

import com.steamgo1.csgoskinadmin.dto.IdsDTO;
import com.steamgo1.csgoskinadmin.dto.SkinDTO;
import com.steamgo1.csgoskinadmin.dto.SkinQueryDTO;
import com.steamgo1.csgoskinadmin.service.SkinService;
import com.steamgo1.csgoskinadmin.vo.AsyncOperationVO;
import com.steamgo1.csgoskinadmin.vo.SkinQueryParamVO;
import com.steamgo1.csgoskincommon.dao.*;
import com.steamgo1.csgoskincommon.entity.*;
import com.steamgo1.csgoskincommon.entity.enums.ZbtSyncStatus;
import com.steamgo1.csgoskincommon.entity.enums.ZbtSyncType;
import com.steamgo1.csgoskincommon.enums.LanguageEnum;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.service.IGXEService;
import com.steamgo1.csgoskincommon.service.ZBTService;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import com.steamgo1.csgoskincommon.utils.Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SkinServiceImpl implements SkinService {


    @Autowired
    private SkinRepository skinRepository;

    @Autowired
    private CaseSkinRepository caseSkinRepository;

    @Autowired
    private DataDictionaryRepository dataDictionaryRepository;

    @Autowired
    private ZBTService zbtService;

    @Autowired
    private IGXEService igxeService;

    @Autowired
    private ZbtSyncRecordRepository zbtSyncRecordRepository;

    @Autowired
    private SysExchangeRateRepository sysExchangeRateRepository;


    @Override
    public Page<SkinEntity> querySkins(SkinQueryDTO skinQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(skinQueryDTO.getPage(), skinQueryDTO.getSize(), sort);
        List<Long> prototypeIds;
        if (skinQueryDTO.getPrototypeId() != null) {
            prototypeIds = dataDictionaryRepository.findAllByParentId(skinQueryDTO.getPrototypeId()).stream().map(item -> item.getId()).collect(Collectors.toList());
        } else {
            prototypeIds = new ArrayList<>();
        }
        Page<SkinEntity> skinEntityPage = skinRepository.findAll((Specification<SkinEntity>) (root, query, cb) -> {
            List<Predicate> ps = new ArrayList<>();
            if (!StringUtils.isEmpty(skinQueryDTO.getName())) {
                //ps.add(cb.like(root.get("name"), "%" + skinQueryDTO.getName() + "%"));
                LanguageEnum language = I18nUtils.getCurrentLanguageEnum();
                ps.add(cb.like(cb.function("JSON_EXTRACT", String.class, root.get("i18nFieldName"), cb.literal("$." + language.getCode())), "%" + skinQueryDTO.getName() + "%"));
            }
            if (skinQueryDTO.getSkinRarityId() != null) {
                ps.add(cb.equal(root.get("rarity"), skinQueryDTO.getSkinRarityId()));
            }
            if (skinQueryDTO.getSkinExteriorId() != null) {
                ps.add(cb.equal(root.get("exterior"), skinQueryDTO.getSkinExteriorId()));
            }
            if (skinQueryDTO.getSkinQualityId() != null) {
                ps.add(cb.equal(root.get("quality"), skinQueryDTO.getSkinQualityId()));
            }
            if (!prototypeIds.isEmpty()) {
                ps.add(cb.in(root.get("prototype")).value(prototypeIds));
            }
            if (skinQueryDTO.getEnablePercentage() != null) {
                ps.add(cb.equal(root.get("enablePercentage"), skinQueryDTO.getEnablePercentage() ? 1 : 0));
            }
            if (skinQueryDTO.getEnableRandom() != null) {
                ps.add(cb.equal(root.get("enableRandom"), skinQueryDTO.getEnableRandom() ? 1 : 0));
            }
            if (skinQueryDTO.getLockPrice() != null) {
                ps.add(cb.equal(root.get("lockPrice"), skinQueryDTO.getLockPrice() ? 1 : 0));
            }
            if (skinQueryDTO.getIsAbnormal() != null) {
                ps.add(cb.equal(root.get("isAbnormal"), skinQueryDTO.getIsAbnormal() ? 1 : 0));
            }
            if (skinQueryDTO.getIsSale() != null) {
                ps.add(cb.equal(root.get("isSale"), skinQueryDTO.getIsSale() ? 1 : 0));
            }
            if (skinQueryDTO.getMaxPrice() != null) {
                ps.add(cb.le(root.get("diamond"), skinQueryDTO.getMaxPrice()));
            }
            if (skinQueryDTO.getMinPrice() != null) {
                ps.add(cb.ge(root.get("diamond"), skinQueryDTO.getMinPrice()));
            }
            return query.where(ps.toArray(new Predicate[ps.size()])).getRestriction();
        }, pageable);
        return skinEntityPage;
    }

    @Override
    public SkinEntity addSkin(SkinDTO skinDTO) {
        if (dataDictionaryRepository.existsByIdAndIsDeleted(skinDTO.getSkinQualityId(), false) == null) {
            throw new CsgoSkinException(I18nUtils.getMessage("exception.csgo.skin.quality.not.exist"));
        }
        if (dataDictionaryRepository.existsByIdAndIsDeleted(skinDTO.getSkinRarityId(), false) == null) {
            throw new CsgoSkinException(I18nUtils.getMessage("exception.csgo.skin.rarity.not.exist"));
        }
        if (dataDictionaryRepository.existsByIdAndIsDeleted(skinDTO.getSkinExteriorId(), false) == null) {
            throw new CsgoSkinException(I18nUtils.getMessage("exception.csgo.skin.exterior.not.exist"));
        }
        if (dataDictionaryRepository.existsByIdAndIsDeleted(skinDTO.getPrototypeId(), false) == null) {
            throw new CsgoSkinException(I18nUtils.getMessage("exception.csgo.prototype.not.exist"));
        }
        SkinEntity skinEntity = new SkinEntity();
        skinEntity.setName(skinDTO.getName());
        skinEntity.setPrice(skinDTO.getPrice());
        skinEntity.setQuality(skinDTO.getSkinQualityId());
        skinEntity.setRarity(skinDTO.getSkinRarityId());
        skinEntity.setExterior(skinDTO.getSkinExteriorId());
        skinEntity.setPrototype(skinDTO.getPrototypeId());
        skinEntity.setPicture(skinDTO.getPicture());
        skinEntity.setEnglishName(skinDTO.getEnglishName());
        skinEntity.setQuantity(skinDTO.getOnSaleNum());
        skinEntity.setI18nFieldName(skinDTO.getI18nFieldName());
        return skinRepository.save(skinEntity);
    }

    @Override
    public void delSkin(Long skinId) {
        SkinEntity skinEntity = skinRepository.findById(skinId).get();
        if (skinEntity != null) {
            List<CaseSkinEntity> caseSkinEntities = caseSkinRepository.findBySkin(skinEntity);
            if (!caseSkinEntities.isEmpty()) {
                // todo 国际化
                // throw new CsgoSkinException("改饰品已经绑定箱子，不支持删除");
                throw new CsgoSkinException(I18nUtils.getMessage("exception.skin.bound.to.case.cannot.delete"));
            }
            skinEntity.setIsDeleted(true);
            skinRepository.save(skinEntity);
        }
    }

    @Override
    public SkinEntity querySkinsById(Long skinId) {
        SkinEntity skinEntity = skinRepository.findById(skinId).get();
        if (skinEntity.getIsDeleted()) {
            return null;
        }
        return skinEntity;
    }

    @Override
    public AsyncOperationVO syncAllSkin() {
        String operationNo = Utils.generateOperationNo("SYNCALLSKIN");
        ZbtSyncRecordEntity zbtSyncRecordEntity = new ZbtSyncRecordEntity();
        zbtSyncRecordEntity.setOperationNo(operationNo);
        zbtSyncRecordEntity.setZbtSyncType(ZbtSyncType.ALL);
        zbtSyncRecordEntity.setZbtSyncStatus(ZbtSyncStatus.SYNCING);
        zbtSyncRecordEntity = zbtSyncRecordRepository.save(zbtSyncRecordEntity);
        zbtService.SyncSkinInfo(zbtSyncRecordEntity.getId());
        return new AsyncOperationVO() {{
            setOperationNo(operationNo);
        }};
    }

    @Override
    public AsyncOperationVO syncSkin(Long skinId) {
        String operationNo = Utils.generateOperationNo("SYNCALLSKIN");
        ZbtSyncRecordEntity zbtSyncRecordEntity = new ZbtSyncRecordEntity();
        zbtSyncRecordEntity.setOperationNo(operationNo);
        zbtSyncRecordEntity.setZbtSyncType(ZbtSyncType.ALL);
        zbtSyncRecordEntity.setZbtSyncStatus(ZbtSyncStatus.SYNCING);
        zbtSyncRecordEntity = zbtSyncRecordRepository.save(zbtSyncRecordEntity);
        zbtService.SyncSkinInfo(zbtSyncRecordEntity.getId());
        return new AsyncOperationVO() {{
            setOperationNo(operationNo);
        }};
    }

    @Override
    public void uploadSkinPictureToStorage() {
        zbtService.uploadSkinPictureToStorage();
    }

    @Override
    @Async
    public void checkSkin() {
        log.info("清洗饰品开始");
        SysExchangeRateEntity sysExchangeRateEntity = sysExchangeRateRepository.findFirstByOrderById();
        if (sysExchangeRateEntity == null) {
            return;
        }
        for (SkinEntity skin : skinRepository.findAll()) {
            if (skin.getPrice() == null) {
                skin.setIsDeleted(true);
                skinRepository.save(skin);
                continue;
            }
            if (skin.getDiamond() == null) {
                skin.setDiamond(skin.getPrice().multiply(sysExchangeRateEntity.getZbtToCny()).multiply(sysExchangeRateEntity.getZbtToCnyPremium()));
                skin.setIsDeleted(false);
                skinRepository.save(skin);
            }
        }
        log.info("清洗饰品结束>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
    }

    @Override
    public void lockPrice(IdsDTO idsDTO) {
        for (Long id : idsDTO.getIds()) {
            SkinEntity skin = skinRepository.findById(id).orElse(null);
            if (skin != null) {
                skin.setLockPrice(true);
                skinRepository.save(skin);
            }
        }
    }

    @Override
    public void unLockPrice(IdsDTO idsDTO) {
        for (Long id : idsDTO.getIds()) {
            SkinEntity skin = skinRepository.findById(id).orElse(null);
            if (skin != null) {
                skin.setLockPrice(false);
                skinRepository.save(skin);
            }
        }
    }

    @Override
    public void addAbnormal(IdsDTO idsDTO) {
        for (Long id : idsDTO.getIds()) {
            SkinEntity skin = skinRepository.findById(id).orElse(null);
            if (skin != null) {
                skin.setIsAbnormal(true);
                skinRepository.save(skin);
            }
        }
    }

    @Override
    public void removeAbnormal(IdsDTO idsDTO) {
        for (Long id : idsDTO.getIds()) {
            SkinEntity skin = skinRepository.findById(id).orElse(null);
            if (skin != null) {
                skin.setIsAbnormal(false);
                skinRepository.save(skin);
            }
        }
    }

    @Override
    public List<CaseSkinEntity> querySkinCase(Long skinId) {
        List<CaseSkinEntity> caseSkinEntityList = caseSkinRepository.findBySkinId(skinId);
        return caseSkinEntityList;
    }

    @Override
    public void addSale(IdsDTO idsDTO) {
        for (Long id : idsDTO.getIds()) {
            SkinEntity skin = skinRepository.findById(id).orElse(null);
            if (skin != null) {
                skin.setIsSale(true);
                skinRepository.save(skin);
            }
        }
    }

    @Override
    public void removeSale(IdsDTO idsDTO) {
        for (Long id : idsDTO.getIds()) {
            SkinEntity skin = skinRepository.findById(id).orElse(null);
            if (skin != null) {
                skin.setIsSale(false);
                skinRepository.save(skin);
            }
        }
    }

    @Override
    public SkinQueryParamVO skinQueryParam() {
        List<SkinQueryParamVO.PrototypeCategory> prototypeCategoriesVO = new ArrayList<>();
        DataDictionaryEntity prototypeCategoryParent = dataDictionaryRepository.findByCode("skin_prototype_category");
        if (prototypeCategoryParent != null) {
            for (DataDictionaryEntity prototypeCategory : dataDictionaryRepository.findAllByParentId(prototypeCategoryParent.getId())) {
                SkinQueryParamVO.PrototypeCategory prototypeCategoryVO = new SkinQueryParamVO.PrototypeCategory();
                prototypeCategoryVO.setId(prototypeCategory.getId());
                prototypeCategoryVO.setImg(prototypeCategory.getImg());
                prototypeCategoryVO.setName(prototypeCategory.getName());
                List<SkinQueryParamVO.Prototype> prototypesVO = new ArrayList<>();
                for (DataDictionaryEntity prototype : dataDictionaryRepository.findAllByParentId(prototypeCategory.getId())) {
                    SkinQueryParamVO.Prototype prototypeVO = new SkinQueryParamVO.Prototype();
                    prototypeVO.setId(prototype.getId());
                    prototypeVO.setName(prototype.getName());
                    prototypesVO.add(prototypeVO);
                }
                prototypeCategoryVO.setPrototypes(prototypesVO);
                prototypeCategoriesVO.add(prototypeCategoryVO);
            }
        }

        List<SkinQueryParamVO.Rarity> raritysVO = new ArrayList<>();
        DataDictionaryEntity rarityParent = dataDictionaryRepository.findByCode("skin_rarity");
        if (rarityParent != null) {
            for (DataDictionaryEntity rarity : dataDictionaryRepository.findAllByParentId(rarityParent.getId())) {
                SkinQueryParamVO.Rarity rarityVO = new SkinQueryParamVO.Rarity();
                rarityVO.setId(rarity.getId());
                rarityVO.setName(rarity.getName());
                raritysVO.add(rarityVO);
            }
        }

        List<SkinQueryParamVO.Exterior> exteriorsVO = new ArrayList<>();
        DataDictionaryEntity exteriorParent = dataDictionaryRepository.findByCode("skin_exterior");
        if (exteriorParent != null) {
            for (DataDictionaryEntity exterior : dataDictionaryRepository.findAllByParentId(exteriorParent.getId())) {
                SkinQueryParamVO.Exterior exteriorVO = new SkinQueryParamVO.Exterior();
                exteriorVO.setId(exterior.getId());
                exteriorVO.setName(exterior.getName());
                exteriorsVO.add(exteriorVO);
            }
        }

        List<SkinQueryParamVO.Quality> qualitiesVO = new ArrayList<>();
        DataDictionaryEntity qualityParent = dataDictionaryRepository.findByCode("skin_quality");
        if (qualityParent != null) {
            for (DataDictionaryEntity quality : dataDictionaryRepository.findAllByParentId(qualityParent.getId())) {
                SkinQueryParamVO.Quality qualityVO = new SkinQueryParamVO.Quality();
                qualityVO.setId(quality.getId());
                qualityVO.setName(quality.getName());
                qualitiesVO.add(qualityVO);
            }
        }

        return new SkinQueryParamVO().setPrototypeCategory(prototypeCategoriesVO).setRarity(raritysVO)
                .setQuality(qualitiesVO).setExterior(exteriorsVO);
    }
}
