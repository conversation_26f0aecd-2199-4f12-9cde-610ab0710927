package com.steamgo1.csgoskinadmin.service.impl;

import com.steamgo1.csgoskinadmin.converter.BattleHomeConverter;
import com.steamgo1.csgoskinadmin.dto.BattleHomeQueryDTO;
import com.steamgo1.csgoskinadmin.service.BattleHomeService;
import com.steamgo1.csgoskinadmin.vo.BattleQueryVO;
import com.steamgo1.csgoskincommon.dao.BattleHomeReposiotry;
import com.steamgo1.csgoskincommon.entity.BattleHomeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class BattleHomeServiceImpl implements BattleHomeService {
    @Autowired
    private BattleHomeReposiotry battleHomeReposiotry;

    @Autowired
    private BattleHomeConverter battleHomeConverter;


    @Override
    public Page<BattleQueryVO> queryBattleHomes(BattleHomeQueryDTO battleHomeQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(battleHomeQueryDTO.getPage(), battleHomeQueryDTO.getSize(), sort);
        Page<BattleHomeEntity> battleHomeEntityPage = battleHomeReposiotry.findAll((Specification<BattleHomeEntity>) (root, query, cb) -> {
            List<Predicate> ps = new ArrayList<>();
            if (battleHomeQueryDTO.getId() != null) {
                ps.add(cb.equal(root.get("id"), battleHomeQueryDTO.getId()));
            }
            if (battleHomeQueryDTO.getMethod() != null) {
                ps.add(cb.equal(root.get("battleHomeMethod"), battleHomeQueryDTO.getMethod() - 1));
            }
            if (battleHomeQueryDTO.getId() != null) {
                ps.add(cb.equal(root.get("battleHomeStatus"), battleHomeQueryDTO.getStatus() - 1));
            }
            return query.where(ps.toArray(new Predicate[ps.size()])).getRestriction();
        }, pageable);
        List<BattleQueryVO> battleQueryVOList = battleHomeEntityPage.getContent().stream().map(item -> battleHomeConverter.toBattleQueryVO(item)).collect(Collectors.toList());
        return new PageImpl<>(battleQueryVOList, battleHomeEntityPage.getPageable(), battleHomeEntityPage.getTotalElements());
    }
}
