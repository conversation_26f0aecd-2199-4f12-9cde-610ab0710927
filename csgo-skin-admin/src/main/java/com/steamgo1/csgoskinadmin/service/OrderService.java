package com.steamgo1.csgoskinadmin.service;

import com.steamgo1.csgoskinadmin.dto.UserChargeOrderQueryDTO;
import com.steamgo1.csgoskinadmin.dto.UserPickupOrderCountDTO;
import com.steamgo1.csgoskinadmin.dto.UserPickupOrderQueryDTO;
import com.steamgo1.csgoskinadmin.vo.OrderChargeVO;
import com.steamgo1.csgoskinadmin.vo.OrderPickupVO;
import org.springframework.data.domain.Page;

public interface OrderService {

    /**
     * 查询用户充值订单
     *
     * @param userChargeOrderQueryDTO
     * @return
     */
    Page<OrderChargeVO> querOrderChargeVO(UserChargeOrderQueryDTO userChargeOrderQueryDTO);

    /**
     * 查询用户取回订单
     *
     * @param userPickupOrderQueryDTO
     * @return
     */
    Page<OrderPickupVO> querOrderPickupVO(UserPickupOrderQueryDTO userPickupOrderQueryDTO);


    public Integer CountOrderPickupVO(UserPickupOrderCountDTO userPickupOrderCountDTO);

    Page<OrderChargeVO> querOrderChargeVOByUserId(UserChargeOrderQueryDTO userChargeOrderQueryDTO);

    Page<OrderPickupVO> querOrderPickupVOByUserId(UserPickupOrderQueryDTO userPickupOrderQueryDTO);
}
