package com.steamgo1.csgoskinadmin.service;

import com.steamgo1.csgoskinadmin.dto.IdsDTO;
import com.steamgo1.csgoskinadmin.dto.PercentageRecordQueryDTO;
import com.steamgo1.csgoskinadmin.dto.PercentageSkinQueryDTO;
import com.steamgo1.csgoskinadmin.vo.PercentageSkinQueryVO;
import com.steamgo1.csgoskincommon.entity.PercentageUserRecordEntity;
import org.springframework.data.domain.Page;

public interface PercentageService {
    /**
     * 查询追梦饰品
     *
     * @param percentageSkinQueryDTO
     * @return
     */
    Page<PercentageSkinQueryVO> queryPercentageSkin(PercentageSkinQueryDTO percentageSkinQueryDTO);

    /**
     * 查询随机饰品
     *
     * @param percentageSkinQueryDTO
     * @return
     */
    Page<PercentageSkinQueryVO> queryRandomSkin(PercentageSkinQueryDTO percentageSkinQueryDTO);

    /**
     * 添加追梦商品
     *
     * @param percentageSkinAddVO
     */
    void addPercentageSkin(IdsDTO percentageSkinAddVO);

    /**
     * 添加随机
     *
     * @param percentageRandomSkinAddVO
     */
    void addPercentageRandomSkinAddVO(IdsDTO percentageRandomSkinAddVO);


    /**
     * 删除追梦商品
     *
     * @param idsDTO
     */
    void delPercentageSkin(IdsDTO idsDTO);

    /**
     * 删除随机商品
     *
     * @param idsDTO
     */
    void delPercentageRandomSkin(IdsDTO idsDTO);

    /**
     * 追梦记录查询
     *
     * @param percentageRecordQueryDTO
     * @return
     */
    Page<PercentageUserRecordEntity> querPercentageRecord(PercentageRecordQueryDTO percentageRecordQueryDTO);
}
