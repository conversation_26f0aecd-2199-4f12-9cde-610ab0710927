package com.steamgo1.csgoskinadmin.service;

import com.steamgo1.csgoskinadmin.vo.ReportForms1VO;
import com.steamgo1.csgoskinadmin.vo.StatisticsVO;

import java.util.Date;
import java.util.List;

public interface IndexService {
    StatisticsVO queryStatistics();

    List<ReportForms1VO> queryReportForms1(Date date);

    List<ReportForms1VO> queryReportForms1(Date startDate, Date endDate, Long ocpcChannelId);
}
