package com.steamgo1.csgoskinadmin.service.impl;

import cc.siyecao.uid.core.utils.StringUtils;
import com.steamgo1.csgoskinadmin.converter.RedPacketConverter;
import com.steamgo1.csgoskinadmin.dto.RedPacketAddParamDTO;
import com.steamgo1.csgoskinadmin.dto.RedPacketQueryDTO;
import com.steamgo1.csgoskinadmin.service.RedPacketService;
import com.steamgo1.csgoskinadmin.vo.RedPacketQueryParamVO;
import com.steamgo1.csgoskinadmin.vo.RedPacketVO;
import com.steamgo1.csgoskincommon.dao.RedPacketRepository;
import com.steamgo1.csgoskincommon.entity.RedPacketEntity;
import com.steamgo1.csgoskincommon.entity.enums.RedPacketMethod;
import com.steamgo1.csgoskincommon.entity.enums.RedPacketType;
import com.steamgo1.csgoskincommon.entity.enums.ThresholdType;
import com.steamgo1.csgoskincommon.enums.LanguageEnum;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class RedpacketServiceImpl implements RedPacketService {
    @Autowired
    private RedPacketRepository redPacketRepository;

    @Autowired
    private RedPacketConverter redPacketConverter;

    @Override
    public RedPacketQueryParamVO getRedPacketQueryParam() {
        List<RedPacketQueryParamVO.EnumMap> typeList = new ArrayList<>();
        for (RedPacketType type : RedPacketType.values()) {
            typeList.add(new RedPacketQueryParamVO.EnumMap() {{
                setCode(type.getCode());
                setValue(type.getValue());
            }});
        }
        List<RedPacketQueryParamVO.EnumMap> methodList = new ArrayList<>();
        for (RedPacketMethod method : RedPacketMethod.values()) {
            methodList.add(new RedPacketQueryParamVO.EnumMap() {{
                setCode(method.getCode());
                setValue(method.getValue());
            }});
        }
        List<RedPacketQueryParamVO.EnumMap> thresholdType = new ArrayList<>();
        for (ThresholdType method : ThresholdType.values()) {
            thresholdType.add(new RedPacketQueryParamVO.EnumMap() {{
                setCode(method.getCode());
                setValue(method.getValue());
            }});
        }
        return new RedPacketQueryParamVO() {{
            setTypeList(typeList);
            setMethodList(methodList);
            setThresholdTypeList(thresholdType);
        }};
    }

    @Override
    public RedPacketEntity addRedPacketQueryParam(RedPacketAddParamDTO redPacketAddParamDTO) {
        // 校验口令是否存在
        if (redPacketRepository.existsByIsDeletedIsFalseAndCode(redPacketAddParamDTO.getCode())) {
            // todo 国际化
            // throw new CsgoSkinException("口令已存在");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.redpacket.code.already.exists"));
        }
        RedPacketEntity redPacketEntity = new RedPacketEntity();
        redPacketEntity.setName(redPacketAddParamDTO.getName());
        redPacketEntity.setCode(redPacketAddParamDTO.getCode());
        redPacketEntity.setTotal(redPacketAddParamDTO.getTotal());
        redPacketEntity.setCoin(redPacketAddParamDTO.getCoin());
        redPacketEntity.setConsumeThreshold(redPacketAddParamDTO.getConsumeThreshold());
        redPacketEntity.setExpireTime(redPacketAddParamDTO.getExpireTime());
        redPacketEntity.setTotalCurrent(0);
        redPacketEntity.setType(RedPacketType.instance(redPacketAddParamDTO.getType()));
        redPacketEntity.setMethod(RedPacketMethod.instance(redPacketAddParamDTO.getMethod()));
        redPacketEntity.setThresholdType(ThresholdType.instance(redPacketAddParamDTO.getConsumeThresholdType()));
        redPacketEntity.setIsDeleted(false);
        redPacketEntity = redPacketRepository.save(redPacketEntity);
        return redPacketEntity;
    }

    @Override
    public Page<RedPacketVO> queryRedPackets(RedPacketQueryDTO redPacketQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(redPacketQueryDTO.getPage(), redPacketQueryDTO.getSize(), sort);
        Page<RedPacketEntity> redPacketEntityPage = redPacketRepository.findAll((Specification<RedPacketEntity>) (root, query, cb) -> {
            List<Predicate> ps = new ArrayList<>();
            if (StringUtils.isNotBlank(redPacketQueryDTO.getName())) {
                //ps.add(cb.like(root.get("name"), "%" + redPacketQueryDTO.getName() + "%"));
                LanguageEnum language = I18nUtils.getCurrentLanguageEnum();
                ps.add(cb.like(cb.function("JSON_EXTRACT", String.class, root.get("i18nFieldName"), cb.literal("$." + language.getCode())), "%" + redPacketQueryDTO.getName() + "%"));
            }
            if (StringUtils.isNotBlank(redPacketQueryDTO.getCode())) {
                ps.add(cb.like(root.get("code"), "%" + redPacketQueryDTO.getCode() + "%"));
            }
            if (redPacketQueryDTO.getType() != null) {
                ps.add(cb.equal(root.get("type"), redPacketQueryDTO.getType()));
            }
            if (redPacketQueryDTO.getMethod() != null) {
                ps.add(cb.equal(root.get("method"), redPacketQueryDTO.getMethod()));
            }
            if (redPacketQueryDTO.getStartTime() != null && redPacketQueryDTO.getEndTime() != null) {
                ps.add(cb.between(root.get("createTime"), redPacketQueryDTO.getStartTime(), redPacketQueryDTO.getEndTime()));
            }
            ps.add(cb.equal(root.get("isDeleted"), 0));
            return query.where(ps.toArray(new Predicate[ps.size()])).getRestriction();
        }, pageable);
        List<RedPacketVO> redPacketVOList = redPacketEntityPage.getContent().stream().map(item -> redPacketConverter.toRedPacketVO(item)).collect(Collectors.toList());
        return new PageImpl<>(redPacketVOList, redPacketEntityPage.getPageable(), redPacketEntityPage.getTotalElements());
    }
}
