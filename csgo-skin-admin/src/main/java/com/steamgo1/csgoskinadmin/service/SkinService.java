package com.steamgo1.csgoskinadmin.service;

import com.steamgo1.csgoskinadmin.dto.IdsDTO;
import com.steamgo1.csgoskinadmin.dto.SkinDTO;
import com.steamgo1.csgoskinadmin.dto.SkinQueryDTO;
import com.steamgo1.csgoskinadmin.vo.AsyncOperationVO;
import com.steamgo1.csgoskinadmin.vo.SkinQueryParamVO;
import com.steamgo1.csgoskincommon.entity.CaseSkinEntity;
import com.steamgo1.csgoskincommon.entity.SkinEntity;
import org.springframework.data.domain.Page;

import java.util.List;

public interface SkinService {
    /**
     * 获取饰品搜索条件
     */
    SkinQueryParamVO skinQueryParam();

    /**
     * 获取所有饰品信息, 分页
     */
    Page<SkinEntity> querySkins(SkinQueryDTO skinQueryDTO);

    /**
     * 添加饰品信息
     */
    SkinEntity addSkin(SkinDTO addSkinDTO);

    /**
     * 删除饰品
     *
     * @param skinId
     */
    void delSkin(Long skinId);

    /**
     * 查询单个
     *
     * @param skinId
     * @return
     */
    SkinEntity querySkinsById(Long skinId);

    /**
     * 同步所有饰品
     *
     * @return
     */
    AsyncOperationVO syncAllSkin();

    /**
     * 同步单个饰品
     *
     * @param skinId
     * @return
     */
    AsyncOperationVO syncSkin(Long skinId);

    /**
     * 检查饰品数据
     *
     * @return
     */
    void checkSkin();

    /**
     * 锁定价格
     *
     * @param idsDTO
     */
    void lockPrice(IdsDTO idsDTO);

    /**
     * 解锁价格
     *
     * @param idsDTO
     */
    void unLockPrice(IdsDTO idsDTO);

    /**
     * 标记异常
     *
     * @param idsDTO
     */
    void addAbnormal(IdsDTO idsDTO);

    /**
     * 解除异常标记
     *
     * @param idsDTO
     */
    void removeAbnormal(IdsDTO idsDTO);

    /**
     * 查询饰品关联箱子
     *
     * @param skinId
     */
    List<CaseSkinEntity> querySkinCase(Long skinId);

    /**
     * 加入在售
     *
     * @param idsDTO
     */
    void addSale(IdsDTO idsDTO);

    /**
     * 取消在售
     *
     * @param idsDTO
     */
    void removeSale(IdsDTO idsDTO);


    void uploadSkinPictureToStorage();
}
