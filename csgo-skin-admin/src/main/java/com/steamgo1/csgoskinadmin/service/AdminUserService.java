package com.steamgo1.csgoskinadmin.service;

import com.steamgo1.csgoskinadmin.dto.AdminUserDTO;
import com.steamgo1.csgoskinadmin.dto.AuthenticationDTO;
import com.steamgo1.csgoskincommon.entity.AdminUserEntity;
import com.steamgo1.csgoskincommon.vo.TokenVO;

public interface AdminUserService {
    TokenVO login(AuthenticationDTO authenticationDTO);

    void createSupperuser();

    AdminUserEntity createAdminUser(AdminUserDTO adminUserDTO);
}
