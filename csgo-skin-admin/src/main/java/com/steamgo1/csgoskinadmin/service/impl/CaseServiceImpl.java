package com.steamgo1.csgoskinadmin.service.impl;

import cc.siyecao.uid.core.utils.StringUtils;
import com.steamgo1.csgoskinadmin.converter.CaseConverter;
import com.steamgo1.csgoskinadmin.converter.CaseLevelConverter;
import com.steamgo1.csgoskinadmin.dto.*;
import com.steamgo1.csgoskinadmin.service.CaseService;
import com.steamgo1.csgoskinadmin.vo.*;
import com.steamgo1.csgoskincommon.dao.*;
import com.steamgo1.csgoskincommon.entity.*;
import com.steamgo1.csgoskincommon.entity.enums.CaseType;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import com.steamgo1.csgoskincommon.vo.CaseOpenVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CaseServiceImpl implements CaseService {
    @Value("${spring.redis.prefix.case}")
    private String redisCasePrefix;

    @Autowired
    private CaseLevelConverter caseLevelConverter;

    @Autowired
    private DataDictionaryRepository dataDictionaryRepository;

    @Autowired
    private CaseRepository caseRepository;

    @Autowired
    private SkinRepository skinRepository;

    @Autowired
    private CaseSkinRepository caseSkinRepository;

    @Autowired
    private CaseConverter caseConverter;

    @Autowired
    private CaseUserRecordRepository caseUserRecordRepository;

    @Autowired
    private UserCoinRecordRepository userCoinRecordRepository;

    @Autowired
    private CaseLevelRepository caseLevelRepository;

    @Autowired
    private SkinRarityColorRepository skinRarityColorRepository;


    /**
     * @Override public Page<CaseFullVO> allCase(CaseQueryDTO caseQueryDTO) {
     * Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
     * Pageable pageable = PageRequest.of(caseQueryDTO.getPage(), caseQueryDTO.getSize(), sort);
     * Page<CaseEntity> caseEntities = null;
     * if(caseQueryDTO.getCaseCategroyId()!=-1){
     * caseEntities = caseRepository.findByCaseCategoryEntityId(caseQueryDTO.getCaseCategroyId(), pageable);
     * }else {
     * caseEntities = caseRepository.findAll(pageable);
     * }
     * System.out.println("++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++");
     * List<CaseFullVO> caseFullVOS = this.toCaseFullVOList(caseEntities.toList());
     * int start = (int) pageable.getOffset();
     * int end = (start + pageable.getPageSize()) > caseFullVOS.size() ? caseFullVOS.size() : (start + pageable.getPageSize());
     * Page page = new PageImpl<CaseFullVO>(caseFullVOS.subList(start, end), pageable, caseFullVOS.size());
     * return page;
     * //        return new PageImpl<>(caseFullVOS);
     * }
     */
    @Override
    public CaseEntity addCase(CaseAddDTO caseAddDTO) {
        // 1.检查是否存在箱子分类
        if (dataDictionaryRepository.existsByIdAndIsDeleted(caseAddDTO.getCaseCategoryId(), false) == false) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.case.category.not.found"));
        }
        CaseEntity caseEntity = new CaseEntity();
        caseEntity.setCaseType(CaseType.instance(caseAddDTO.getType()));
        caseEntity.setCategory(caseAddDTO.getCaseCategoryId());
        caseEntity.setName(caseAddDTO.getName());
        caseEntity.setPrice(caseAddDTO.getPrice());
        caseEntity.setProbability(caseAddDTO.getProbability());
        caseEntity.setBackgroundPicture(caseAddDTO.getBackgroundPicture());
        caseEntity.setForegroundPicture(caseAddDTO.getForegroundPicture());
        caseEntity.setSkinTotal(caseAddDTO.getSkinTotal());
        caseEntity.setI18nFieldName(caseAddDTO.getI18nFieldName());
        return caseRepository.save(caseEntity);
    }

    @Override
    @Transactional
    public CaseFullVO addFullCase(CaseFullAddDTO caseFullAddDTO) {
        // 添加箱子
        // 1.检查是否存在箱子分类
        if (dataDictionaryRepository.existsByIdAndIsDeleted(caseFullAddDTO.getCategoryId(), false) == false) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.case.category.not.found"));
        }
        // 2.检查总概率是否
        StringBuffer buffer = new StringBuffer();
        List<CaseSkinDTO> caseSkinDTOS = caseFullAddDTO.getLevels().get(0).getSkins();
        BigDecimal probability = new BigDecimal("1.00");
        for (CaseLevelDTO caseLevelDTO : caseFullAddDTO.getLevels()) {
            probability = caseLevelDTO.getSkins().stream().map(CaseSkinDTO::getProbability).reduce(BigDecimal.ZERO, (p, q) -> p.add(q));
            if (probability.compareTo(BigDecimal.valueOf(1)) != 0) {
                //buffer.append("箱子" + caseFullAddDTO.getName() + "的等级：" + caseLevelDTO.getLevelNum() + ",总概率不等于1");
                buffer.append(I18nUtils.getMessage("exception.case.level.probability.invalid", new Object[]{caseFullAddDTO.getName(), caseLevelDTO.getLevelNum()}));
            }

            //查询重复
            for (CaseSkinDTO caseSkinDTO : caseLevelDTO.getSkins()) {
                if (!caseSkinDTOS.stream()
                        .map(i -> i.getSkinInfo().getId())
                        .collect(Collectors.toList())
                        .contains(caseSkinDTO.getSkinInfo().getId())) {
                    //buffer.append("箱子" + caseFullAddDTO.getName() + "的等级：" + caseLevelDTO.getLevelNum() + ",skin" + caseSkinDTO.getSkinInfo().getName() + "和0不同\n");
                    buffer.append(I18nUtils.getMessage("exception.case.skin.duplicate", new Object[]{caseFullAddDTO.getName(), caseLevelDTO.getLevelNum(), caseSkinDTO.getSkinInfo().getName()}));
                }
            }
        }
        if (buffer.length() > 0) {
            // todo 国际化
            throw new CsgoSkinException(buffer.toString());
        }

        if (caseFullAddDTO.getLevels() == null || caseFullAddDTO.getLevels().isEmpty()) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.case.no.levels"));
        }


        CaseEntity caseEntity = new CaseEntity();
        caseEntity.setCaseType(CaseType.instance(caseFullAddDTO.getType()));
        caseEntity.setCategory(caseFullAddDTO.getCategoryId());
        caseEntity.setName(caseFullAddDTO.getName());
        caseEntity.setPrice(caseFullAddDTO.getPrice());
        caseEntity.setProbability(probability);
        caseEntity.setBackgroundPicture(caseFullAddDTO.getBackgroundPicture());
        caseEntity.setForegroundPicture(caseFullAddDTO.getForegroundPicture());
        caseEntity.setSkinTotal(caseSkinDTOS.size());
        caseEntity.setIsRecommend(caseFullAddDTO.getIsRecommend());
        caseEntity.setGradle(caseFullAddDTO.getGradle());
        caseEntity.setIsSale(caseFullAddDTO.getIsSale());
        caseEntity.setI18nFieldName(caseFullAddDTO.getI18nFieldName());
        caseEntity = caseRepository.save(caseEntity);
        Integer grade = 100;
        for (CaseLevelDTO caseLevelDTO : caseFullAddDTO.getLevels()) {
            CaseLevelEntity caseLevel = new CaseLevelEntity();
            caseLevel.setLevelNum(caseLevelDTO.getLevelNum());
            caseLevel.setCaseEntity(caseEntity);
            caseLevelRepository.save(caseLevel);
            for (CaseSkinDTO caseSkinDTO : caseLevelDTO.getSkins()) {
                SkinEntity skinEntity = skinRepository.findById(caseSkinDTO.getSkinInfo().getId()).orElse(null);
                if (skinEntity == null) {
                    log.error("添加箱子提交的皮肤ID: {}不存在", caseSkinDTO.getSkinId());
                    // todo 国际化
                    throw new CsgoSkinException(I18nUtils.getMessage("exception.skin.not.found", new Object[]{String.valueOf(caseSkinDTO.getSkinId())}));
                }
                if (caseSkinDTO.getSkinRarityColorId() == null) {
                    // todo 国际化
                    throw new CsgoSkinException(I18nUtils.getMessage("exception.custom.color.required"));
                }
                SkinRarityColorEntity skinRarityColor = skinRarityColorRepository.findById(caseSkinDTO.getSkinRarityColorId()).orElse(null);
                if (skinRarityColor == null) {
                    log.error("添加箱子提交的自定义颜色: {}不存在", caseSkinDTO.getSkinRarityColorId());
                    // todo 国际化
                    throw new CsgoSkinException(I18nUtils.getMessage("exception.skin.color.not.found", new Object[]{String.valueOf(caseSkinDTO.getSkinRarityColorId())}));
                }

                CaseSkinEntity caseSkinEntity = new CaseSkinEntity();
                caseSkinEntity.setSkinRarityColor(skinRarityColor);
                caseSkinEntity.setCaseEntity(caseEntity);
                caseSkinEntity.setProbability(caseSkinDTO.getProbability());
                caseSkinEntity.setSkin(skinEntity);
                caseSkinEntity.setWinningTotal(0);
                caseSkinEntity.setLevel(caseLevel);
                caseSkinEntity.setReferencePrice(caseSkinDTO.getReferencePrice());
                log.info("{}", caseEntity.getGradle());
                caseSkinEntity.setGrade(caseSkinDTO.getGrade() == null ? grade : caseSkinDTO.getGrade());
                caseSkinRepository.save(caseSkinEntity);
                grade -= 1;
            }
        }
        return this.toCaseFullVO(caseEntity);
    }


    @Override
    @Transactional
    public CaseFullVO updateFullCase(CaseFullUpdateDTO caseFullUpdateDTO) {
        String redisKey = redisCasePrefix + ":case:" + caseFullUpdateDTO.getId();
        RedisUtils.delete(redisKey);
        // 更新箱子
        CaseEntity caseEntity = caseRepository.findById(caseFullUpdateDTO.getId());
        if (caseEntity == null) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.case.not.found"));
        }
        // 1.检查是否存在箱子分类
        if (dataDictionaryRepository.existsByIdAndIsDeleted(caseFullUpdateDTO.getCategoryId(), false) == false) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.case.category.not.found"));
        }
        // 2.检查总概率是否=1
        if (caseFullUpdateDTO.getLevels() == null || caseFullUpdateDTO.getLevels().isEmpty()) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.case.no.levels"));
        }

        StringBuffer buffer = new StringBuffer();
        List<CaseSkinDTO> caseSkinDTOS = caseFullUpdateDTO.getLevels().get(0).getSkins();
        BigDecimal probability = new BigDecimal("1.00");
        for (CaseLevelDTO caseLevelDTO : caseFullUpdateDTO.getLevels()) {
            probability = caseLevelDTO.getSkins().stream().map(CaseSkinDTO::getProbability).reduce(BigDecimal.ZERO, (p, q) -> p.add(q));
            if (probability.compareTo(BigDecimal.valueOf(1)) != 0) {
                //buffer.append("箱子" + caseFullUpdateDTO.getName() + "的等级：" + caseLevelDTO.getLevelNum() + ",总概率不等于1;" +"");
                buffer.append(I18nUtils.getMessage("exception.case.level.probability.invalid", new Object[]{caseFullUpdateDTO.getName(), caseLevelDTO.getLevelNum()}));
            }

            //查询重复
            for (CaseSkinDTO caseSkinDTO : caseLevelDTO.getSkins()) {
                if (!caseSkinDTOS.stream()
                        .map(i -> i.getSkinInfo().getId())
                        .collect(Collectors.toList())
                        .contains(caseSkinDTO.getSkinInfo().getId())) {
                    //buffer.append("箱子" + caseFullUpdateDTO.getName() + "的等级：" + caseLevelDTO.getLevelNum() + ",skin" + caseSkinDTO.getSkinInfo().getName() + "和0不同;" +"");
                    buffer.append(I18nUtils.getMessage("exception.case.skin.duplicate", new Object[]{caseFullUpdateDTO.getName(), caseLevelDTO.getLevelNum(), caseSkinDTO.getSkinInfo().getName()}));
                }
            }
        }
        if (buffer.length() > 0) {
            // todo 国际化
            throw new CsgoSkinException(buffer.toString());
        }

        caseEntity.setCaseType(CaseType.instance(caseFullUpdateDTO.getType()));
        caseEntity.setCategory(caseFullUpdateDTO.getCategoryId());
        caseEntity.setName(caseFullUpdateDTO.getName());
        caseEntity.setPrice(caseFullUpdateDTO.getPrice());
        caseEntity.setProbability(probability);
        caseEntity.setBackgroundPicture(caseFullUpdateDTO.getBackgroundPicture());
        caseEntity.setForegroundPicture(caseFullUpdateDTO.getForegroundPicture());
        caseEntity.setSkinTotal(caseSkinDTOS.size());
        caseEntity.setIsRecommend(caseFullUpdateDTO.getIsRecommend());
        caseEntity.setGradle(caseFullUpdateDTO.getGradle());
        caseEntity.setIsSale(caseFullUpdateDTO.getIsSale());
        caseEntity.setUpdateTime(new Date());
        caseEntity.setI18nFieldName(caseFullUpdateDTO.getI18nFieldName());
        caseEntity = caseRepository.save(caseEntity);


        //查询当前箱子所有Level
        List<Long> levelId = new ArrayList<>();

        // 更新及删除
        CaseLevelEntity caseLevelEntity;
        for (CaseLevelDTO caseLevelDTO : caseFullUpdateDTO.getLevels()) {
            caseLevelEntity = caseLevelRepository.getByCaseEntityAndLevelNum(caseEntity, caseLevelDTO.getLevelNum());
            if (caseLevelEntity == null) {
                caseLevelEntity = new CaseLevelEntity();
            }
            List<Long> caseSkinIds = caseLevelDTO.getSkins().stream().map(skin -> skin.getCaseSkinId()).collect(Collectors.toList());
            caseLevelEntity.setCaseEntity(caseEntity);
            caseLevelEntity.setLevelNum(caseLevelDTO.getLevelNum());
            caseLevelRepository.save(caseLevelEntity);
            levelId.add(caseLevelEntity.getId());
            for (CaseSkinEntity caseSkinEntity : caseSkinRepository.findByLevel(caseLevelEntity)) {
                // 更新原来就有的皮肤
                if (caseSkinIds.contains(caseSkinEntity.getId())) {
                    CaseSkinDTO caseSkinDTO = caseFullUpdateDTO.getCaseSkinDTOBySKinId(caseLevelDTO.getSkins(), caseSkinEntity.getId());
                    caseSkinEntity.setProbability(caseSkinDTO.getProbability());
                    SkinEntity skinEntity = skinRepository.findById(caseSkinDTO.getSkinInfo().getId()).get();
                    if (skinEntity == null) {
                        log.error("添加箱子提交的皮肤ID: {}不存在", caseSkinDTO.getSkinInfo().getId());
                        // todo 国际化
                        throw new CsgoSkinException(I18nUtils.getMessage("exception.skin.not.found", new Object[]{String.valueOf(caseSkinDTO.getSkinInfo().getId())}));
                    }
                    if (caseSkinDTO.getSkinRarityColorId() == null) {
                        // todo 国际化
                        throw new CsgoSkinException(I18nUtils.getMessage("exception.custom.color.required"));
                    }
                    SkinRarityColorEntity skinRarityColor = skinRarityColorRepository.findById(caseSkinDTO.getSkinRarityColorId()).orElse(null);
                    if (skinRarityColor == null) {
                        log.error("添加箱子提交的自定义颜色: {}不存在", caseSkinDTO.getSkinRarityColorId());
                        // todo 国际化
                        throw new CsgoSkinException(I18nUtils.getMessage("exception.skin.color.not.found", new Object[]{String.valueOf(caseSkinDTO.getSkinRarityColorId())}));
                    }
                    caseSkinEntity.setSkinRarityColor(skinRarityColor);
                    caseSkinEntity.setSkin(skinEntity);
                    caseSkinEntity.setWinningTotal(caseSkinDTO.getWinningTotal());
                    caseSkinEntity.setGrade(caseSkinDTO.getGrade());
                    caseSkinEntity.setLevel(caseLevelEntity);
                    caseSkinEntity.setReferencePrice(caseSkinDTO.getReferencePrice());
                    caseSkinRepository.save(caseSkinEntity);
                    caseSkinIds.remove(caseSkinDTO.getCaseSkinId());
                    continue;
                }
                caseSkinRepository.delete(caseSkinEntity);
            }
            // 新增
            for (int i = 0; i < caseLevelDTO.getSkins().size(); i++) {
                CaseSkinDTO caseSkinDTO = caseLevelDTO.getSkins().get(i);
                if (caseSkinDTO.getSkinInfo() == null
                        || caseSkinDTO == null
                        || caseSkinDTO.getSkinInfo().getId() == null
                        || caseSkinDTO.getCaseSkinId() != null) {
                    continue;
                }
                SkinEntity skinEntity = skinRepository.findById(caseSkinDTO.getSkinInfo().getId()).get();
                if (skinEntity == null) {
                    log.error("添加箱子提交的皮肤ID: {}不存在", caseSkinDTO.getSkinInfo().getId());
                    // todo 国际化
                    throw new CsgoSkinException(String.format("皮肤%d不存在", caseSkinDTO.getSkinInfo().getId()));
                }
                CaseSkinEntity caseSkinEntity = new CaseSkinEntity();
                caseSkinEntity.setId(caseSkinDTO.getCaseSkinId());
                if (caseSkinDTO.getSkinRarityColorId() == null) {
                    // todo 国际化
                    throw new CsgoSkinException(I18nUtils.getMessage("exception.custom.color.required"));
                }
                SkinRarityColorEntity skinRarityColor = skinRarityColorRepository.findById(caseSkinDTO.getSkinRarityColorId()).orElse(null);
                if (skinRarityColor == null) {
                    log.error("添加箱子提交的自定义颜色: {}不存在", caseSkinDTO.getSkinRarityColorId());
                    // todo 国际化
                    throw new CsgoSkinException(I18nUtils.getMessage("exception.skin.color.not.found", new Object[]{String.valueOf(caseSkinDTO.getSkinRarityColorId())}));
                }
                caseSkinEntity.setSkinRarityColor(skinRarityColor);
                caseSkinEntity.setCaseEntity(caseEntity);
                caseSkinEntity.setProbability(caseSkinDTO.getProbability());
                caseSkinEntity.setSkin(skinEntity);
                caseSkinEntity.setLevel(caseLevelEntity);
                caseSkinEntity.setWinningTotal(0);
                caseSkinEntity.setGrade(caseSkinDTO.getGrade());
                caseSkinEntity.setReferencePrice(caseSkinDTO.getReferencePrice());
                caseSkinRepository.save(caseSkinEntity);
            }
        }
        caseLevelRepository.deleteByCaseEntityAndIdNotIn(caseEntity, levelId);
        return this.toCaseFullVO(caseEntity);
    }

    @Override
    public List<CaseCategoryVO> queryCaseCategory() {
        List<CaseCategoryVO> caseCategoryVOS = new ArrayList<>();
        DataDictionaryEntity caseCategoryParent = dataDictionaryRepository.findByCode("case_category");
        if (caseCategoryParent == null) {
            return caseCategoryVOS;
        }
        for (DataDictionaryEntity caseCategory : dataDictionaryRepository.findAllByParentId(caseCategoryParent.getId())) {
            CaseCategoryVO categoryVO = new CaseCategoryVO();
            categoryVO.setId(caseCategory.getId());
            categoryVO.setName(caseCategory.getName());
            caseCategoryVOS.add(categoryVO);
        }
        return caseCategoryVOS;
    }

    @Override
    public CaseQueryParamVO caseQueryParam() {
        CaseQueryParamVO caseQueryParamVOS = new CaseQueryParamVO();
        DataDictionaryEntity caseCategoryParent = dataDictionaryRepository.findByCode("case_category");
        if (caseCategoryParent == null) {
            return caseQueryParamVOS;
        }
        List<CaseQueryParamVO.Category> categories = new ArrayList<>();
        for (DataDictionaryEntity caseCategory : dataDictionaryRepository.findAllByParentId(caseCategoryParent.getId())) {
            CaseQueryParamVO.Category categoryVO = new CaseQueryParamVO.Category();
            categoryVO.setId(caseCategory.getId());
            categoryVO.setName(caseCategory.getName());
            categories.add(categoryVO);
        }
        caseQueryParamVOS.setCategory(categories);
        List<CaseQueryParamVO.Type> types = new ArrayList<>();
        types.add(new CaseQueryParamVO.Type() {{
            setCode(1);
            setValue(I18nUtils.getMessage("case.type.beginner"));
        }});
        types.add(new CaseQueryParamVO.Type() {{
            setCode(2);
            setValue(I18nUtils.getMessage("case.type.normal"));
        }});
        caseQueryParamVOS.setType(types);
        caseQueryParamVOS.setSkinRarityColor(skinRarityColorRepository.findAll());
        return caseQueryParamVOS;
    }

    @Override
    public CaseFullVO queryCaseById(Long caseId) {
        CaseEntity caseEntity = caseRepository.findById(caseId);
        if (caseEntity != null && !caseEntity.getIsDeleted()) {
            return toCaseFullVO(caseEntity);
        }
        // todo 国际化
        throw new CsgoSkinException(I18nUtils.getMessage("exception.case.no.levels"));
    }

    @Override
    public void updateIsSaleState(CaseUpdateSaleStateDTO caseUpdateSaleStateDTO) {
        String redisKey = redisCasePrefix + ":case:" + caseUpdateSaleStateDTO.getCaseId();
        RedisUtils.delete(redisKey);
        CaseEntity caseEntity = caseRepository.findById(caseUpdateSaleStateDTO.getCaseId());
        if (caseEntity == null) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.case.not.found"));
        }
        caseEntity.setIsSale(caseUpdateSaleStateDTO.getIsSale());
        caseRepository.save(caseEntity);
    }

    @Override
    public Page<OpenCaseRecordQueryVO> queryOpenCaseRecords(OpenCaseRecordQueryDTO openCaseRecordQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(openCaseRecordQueryDTO.getPage(), openCaseRecordQueryDTO.getSize(), sort);
        Page<CaseUserRecordEntity> caseUserRecordEntityPage = caseUserRecordRepository.findAll((Specification<CaseUserRecordEntity>) (root, query, cb) -> {
            Join<CaseUserRecordEntity, UserEntity> join = root.join("user", JoinType.LEFT);
            List<Predicate> ps = new ArrayList<>();
            if (openCaseRecordQueryDTO.getUserId() != null) {
                ps.add(cb.equal(root.get("user"), openCaseRecordQueryDTO.getUserId()));
            }
            if (!StringUtils.isBlank(openCaseRecordQueryDTO.getPhone())) {
                ps.add(cb.like(join.get("phone"), "%" + openCaseRecordQueryDTO.getPhone() + "%"));
            }
            if (openCaseRecordQueryDTO.getCaseId() != null) {
                ps.add(cb.equal(root.get("box"), openCaseRecordQueryDTO.getCaseId()));
            }
            if (openCaseRecordQueryDTO.getStartTime() != null && openCaseRecordQueryDTO.getEndTime() != null) {
                ps.add(cb.between(root.get("createTime"), openCaseRecordQueryDTO.getStartTime(), openCaseRecordQueryDTO.getEndTime()));
            }
            return query.where(ps.toArray(new Predicate[ps.size()])).getRestriction();
        }, pageable);
        List<OpenCaseRecordQueryVO> openCaseRecordQueryVOList = new ArrayList<>();
        for (CaseUserRecordEntity caseUserRecordEntity : caseUserRecordEntityPage.getContent()) {
            openCaseRecordQueryVOList.add(new OpenCaseRecordQueryVO() {{
                setId(caseUserRecordEntity.getId());
                setSkin(caseUserRecordEntity.getSkin());
                setUser(caseUserRecordEntity.getUser());
                setBox(caseUserRecordEntity.getBox());
            }});
        }
        return new PageImpl<>(openCaseRecordQueryVOList, caseUserRecordEntityPage.getPageable(), caseUserRecordEntityPage.getTotalElements());
    }

    @Override
    public Page<CaseUserRecordEntity> queryCaseUserRecordEntity(OpenCaseRecordQueryDTO openCaseRecordQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(openCaseRecordQueryDTO.getPage(), openCaseRecordQueryDTO.getSize(), sort);
        Page<CaseUserRecordEntity> caseUserRecordEntityPage = caseUserRecordRepository.findAll((Specification<CaseUserRecordEntity>) (root, query, cb) -> {
            Join<CaseUserRecordEntity, UserEntity> join = root.join("user", JoinType.LEFT);
            List<Predicate> ps = new ArrayList<>();
            if (openCaseRecordQueryDTO.getUserId() != null) {
                ps.add(cb.equal(root.get("user"), openCaseRecordQueryDTO.getUserId()));
            }
            if (!StringUtils.isBlank(openCaseRecordQueryDTO.getPhone())) {
                ps.add(cb.like(join.get("phone"), "%" + openCaseRecordQueryDTO.getPhone() + "%"));
            }
            if (openCaseRecordQueryDTO.getCaseId() != null) {
                ps.add(cb.equal(root.get("box"), openCaseRecordQueryDTO.getCaseId()));
            }
            if (openCaseRecordQueryDTO.getStartTime() != null && openCaseRecordQueryDTO.getEndTime() != null) {
                ps.add(cb.between(root.get("createTime"), openCaseRecordQueryDTO.getStartTime(), openCaseRecordQueryDTO.getEndTime()));
            }
            return query.where(ps.toArray(new Predicate[ps.size()])).getRestriction();
        }, pageable);
        return caseUserRecordEntityPage;
    }

    @Override
    public CaseStatisticsVO queryCaseStatistics(Long caseId) {
        CaseEntity caseEntity = caseRepository.findById(caseId);
        if (caseEntity == null) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.case.not.found"));
        }
        CaseStatisticsVO caseStatisticsVO = new CaseStatisticsVO();
        Integer totalOpen = caseUserRecordRepository.countByBoxId(caseId);

        BigDecimal expenditure = caseUserRecordRepository.expenditureSkin(caseId);

        BigDecimal salesVolume = caseEntity.getPrice().multiply(BigDecimal.valueOf(totalOpen)).setScale(2);
        BigDecimal revenue = salesVolume.subtract(expenditure);
        caseStatisticsVO.setExpenditure(expenditure);
        caseStatisticsVO.setRevenue(revenue);
        caseStatisticsVO.setSalesVolume(salesVolume);
        caseStatisticsVO.setTotalOpen(totalOpen);
        return caseStatisticsVO;
    }

    @Override
    public CaseEntity switchCaseProtect(Boolean flag) {
        List<CaseEntity> caseEntities = caseRepository.findAll();
        if (caseEntities == null || caseEntities.isEmpty()) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.case.not.exist.create.one"));
        }
        caseEntities.forEach(caseEntity -> {
            caseEntity.setIsProtect(flag);
            caseRepository.save(caseEntity);
        });
        return caseEntities.get(0);
    }

    @Override
    public List<CaseOpenVo> statisticsOpenCase(OpenCaseDTO openCaseDTO) {
        if (openCaseDTO == null) {
            return null;
        }
        List<CaseUserRecordEntity> caseUserRecordEntities = caseUserRecordRepository.findAll((root, query, cb) -> {
            Join<CaseUserRecordEntity, UserEntity> userEntityJoin = root.join("user");
            List<Predicate> ps = new ArrayList<>();
            if (openCaseDTO.getUserType() != null) {
                ps.add(cb.equal(userEntityJoin.get("type"), openCaseDTO.getUserType()));
            }
            if (openCaseDTO.getStartDate() != null && openCaseDTO.getEndDate() != null) {
                ps.add(cb.between(root.get("createTime"), openCaseDTO.getStartDate(), openCaseDTO.getEndDate()));
            }
            return query.where(ps.toArray(new Predicate[ps.size()])).getRestriction();
        });

        Map<Long, List<CaseUserRecordEntity>> map = Optional.ofNullable(caseUserRecordEntities)
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.groupingBy(i -> i.getBox().getId()));

        List<CaseOpenVo> caseOpenVos = Optional.ofNullable(map.values()).orElse(new ArrayList<>()).stream().map(entry -> {
            List<CaseUserRecordEntity> recordEntities = Optional.ofNullable(entry).orElse(Collections.emptyList());
            CaseOpenVo caseOpenVo = new CaseOpenVo();
            if (recordEntities == null || recordEntities.isEmpty()) {
                return caseOpenVo;
            }
            //箱子id
            Long caseId = Optional.ofNullable(entry.get(0).getBox().getId()).orElse(0L);
            //箱子名称
            String caseName = Optional.ofNullable(entry.get(0).getBox().getName()).orElse("");
            //开箱数量
            Long openCount = recordEntities
                    .stream()
                    .count();
            //消耗总金币
            BigDecimal consumeCoins = recordEntities.stream()
                    .map(i -> i.getBox().getPrice())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            //物品总价值
            BigDecimal totalPrice = recordEntities.stream()
                    .map(i -> (i == null || i.getSkin() == null) ? BigDecimal.ZERO : i.getSkin().getDiamond())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            //利润
            BigDecimal caseProfit = consumeCoins.subtract(totalPrice);
            caseOpenVo.setOpenCount(openCount);
            caseOpenVo.setConsumeCoins(consumeCoins);
            caseOpenVo.setTotalPrice(totalPrice);
            caseOpenVo.setCaseProfit(caseProfit);
            caseOpenVo.setCaseId(caseId);
            caseOpenVo.setCaseName(caseName);
            return caseOpenVo;
        }).collect(Collectors.toList());

        return caseOpenVos;
    }

    public CaseStatisticsVO queryCaseStatistics(CaseEntity caseEntity) {
        if (caseEntity == null) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.case.not.found"));
        }
        CaseStatisticsVO caseStatisticsVO = new CaseStatisticsVO();
        Integer totalOpen = caseUserRecordRepository.countByBoxId(caseEntity.getId());
        totalOpen = totalOpen == null ? 0 : totalOpen;
        BigDecimal expenditure = caseUserRecordRepository.expenditureSkin(caseEntity.getId());
        expenditure = expenditure == null ? BigDecimal.ZERO : expenditure;
        BigDecimal salesVolume = caseEntity.getPrice() == null ? BigDecimal.ZERO : caseEntity.getPrice().multiply(BigDecimal.valueOf(totalOpen)).multiply(BigDecimal.valueOf(6.5));
        BigDecimal revenue = salesVolume.subtract(expenditure);
        caseStatisticsVO.setExpenditure(expenditure);
        caseStatisticsVO.setRevenue(revenue);
        caseStatisticsVO.setSalesVolume(salesVolume);
        caseStatisticsVO.setTotalOpen(totalOpen);
        return caseStatisticsVO;
    }

    @Override
    public CaseFullVO toCaseFullVO(CaseEntity caseEntity) {
        List<CaseLevelEntity> caseLevelEntities = caseLevelRepository.findByCaseEntity(caseEntity);
        CaseFullVO caseFullVO = caseConverter.toCovertCaseFullVO(caseEntity);
        List<CaseSkinEntity> caseSkinEntities;
        List<CaseFullVO.CaseSkinVO> skins;
        for (CaseLevelEntity caseLevelEntity : caseLevelEntities) {
            skins = new ArrayList<>();
            caseSkinEntities = caseSkinRepository.findByLevel(caseLevelEntity);
            CaseFullVO.CaseLevelVo caseLevelVo = caseLevelConverter.toCovertCaseLevelVo(caseLevelEntity);
            for (CaseSkinEntity caseSkinEntity : caseSkinEntities) {
                CaseFullVO.CaseSkinVO caseSkinVO = caseConverter.toCoverCaseSkin(caseSkinEntity);
                caseSkinVO.setSkinInfo(caseSkinEntity.getSkin());
                caseSkinVO.setCaseSkinId(caseSkinEntity.getId());
                skins.add(caseSkinVO);
                caseFullVO.setCaseStatistics(queryCaseStatistics(caseEntity));
            }
            caseLevelVo.setSkins(skins);
            caseFullVO.getLevels().add(caseLevelVo);
        }
        return caseFullVO;
    }

    @Override
    public List<CaseFullVO> toCaseFullVOList(List<CaseEntity> caseEntities) {
        List<CaseFullVO> caseFullVOS = new ArrayList<>();
        for (CaseEntity caseEntity : caseEntities) {
            caseFullVOS.add(caseConverter.toCovertCaseFullVO(caseEntity));
        }
        return caseFullVOS;
    }


    @Override
    public void delCaseById(Long caseId) {
        String redisKey = redisCasePrefix + ":case:" + caseId;
        RedisUtils.delete(redisKey);
        CaseEntity caseEntity = caseRepository.findById(caseId);
        if (caseEntity == null || caseEntity.getIsDeleted() == true) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.resource.not.exist"));
        }
        caseEntity.setIsDeleted(true);
        caseRepository.save(caseEntity);
    }

    @Override
    public CaseFullVO queryCase(Long caseId) {
        return null;
    }

//    @Override
//    public List<CaseFullVO> queryCases(CaseQueryDTO caseQueryDTO) {
//        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
//        Pageable pageable = PageRequest.of(caseQueryDTO.getPage(), caseQueryDTO.getSize(), sort);
//        Page<CaseEntity> caseEntities = null;
//        if(caseQueryDTO.getCaseCategroyId()!=null){
//            caseEntities = caseRepository.findAllByCategory(caseQueryDTO.getCaseCategroyId(), pageable);
//        }else {
//            caseEntities = caseRepository.findAll(pageable);
//        }
//        System.out.println("++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++");
//        List<CaseFullVO> caseFullVOS = this.toCaseFullVOList(caseEntities.toList());
//        return caseFullVOS;
//    }

    @Override
    public List<CaseFullVO> queryCaseByCategory(CaseQueryDTO caseQueryDTO) {
        List<CaseEntity> caseEntities = null;
        if (caseQueryDTO.getCaseCategoryId() != null) {
            caseEntities = caseRepository.findAllByCategoryAndIsDeleted(caseQueryDTO.getCaseCategoryId(), false);
        } else {
            caseEntities = caseRepository.findAllByIsDeleted(false);
        }
        return this.toCaseFullVOList(caseEntities);
    }

}
