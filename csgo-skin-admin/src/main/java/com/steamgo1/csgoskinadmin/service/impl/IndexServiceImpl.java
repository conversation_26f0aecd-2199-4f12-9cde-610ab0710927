package com.steamgo1.csgoskinadmin.service.impl;

import com.steamgo1.csgoskinadmin.service.IndexService;
import com.steamgo1.csgoskinadmin.vo.ReportForms1VO;
import com.steamgo1.csgoskinadmin.vo.StatisticsVO;
import com.steamgo1.csgoskincommon.dao.*;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Service
public class IndexServiceImpl implements IndexService {
    @Value("${spring.redis.prefix.user}")
    private String redisUserPrefix;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private DailyActivityRepository dailyActivityRepository;

    @Autowired
    private UserPackagePickupRepository userPackagePickupRepository;

    @Autowired
    private OrderChargeRepository orderChargeRepository;

    @Autowired
    private OcpcBaiduDataRepository ocpcBaiduDataRepository;

    @Override
    public StatisticsVO queryStatistics() {
        StatisticsVO statisticsVO = new StatisticsVO();
        Integer waitPickUp = userPackagePickupRepository.queryPickUpCount(7);
        Integer onlineUser = RedisUtils.get(redisUserPrefix + ":" + "ONLINE", Integer.class);
        statisticsVO.setWaitPickUp(waitPickUp);
        statisticsVO.setUser(new StatisticsVO.User() {{
            setTotalUsers(userRepository.totalUsers());
            setDayUsersQuantity(userRepository.dayTotalUsers());
            setWeekUser(userRepository.weekUsers());
        }});
        statisticsVO.setOnlineUser(new StatisticsVO.OnlineUser() {{
            setTotalUsers(onlineUser == null ? 0 : onlineUser);
            setDayUsersQuantity(dailyActivityRepository.dayTotalUsers());
            setWeekOnlineUser(dailyActivityRepository.weekUsers());
        }});
        statisticsVO.setPickUp(new StatisticsVO.PickUp() {{
            setTotalAmount(userPackagePickupRepository.dayAmountNet());
            setWeekPickUp(userPackagePickupRepository.weekAount());
        }});
        statisticsVO.setCharge(new StatisticsVO.Charge() {{
            setTotalAmount(orderChargeRepository.dayAmount());
            setWeekCharge(orderChargeRepository.weekAmount());
        }});
        // 使用DecimalFormat类保留两位小数
        DecimalFormat decimalFormat = new DecimalFormat("#00.00");
        Integer totalUsers = userRepository.totalUsers();
        Integer dayTotalUsers = userRepository.dayTotalUsers();
        String freeBoxRatio = decimalFormat.format(totalUsers == 0 ? 0 : ((float) userRepository.freeCase() / (float) totalUsers) * (float) 100);
        String todayBoxRatio = decimalFormat.format(dayTotalUsers == 0 ? 0 : ((float) userRepository.dayFreeCase() / (float) dayTotalUsers) * (float) 100);
        statisticsVO.setFreeBoxRatio(freeBoxRatio);
        statisticsVO.setTodayFreeBoxRatio(todayBoxRatio);
        return statisticsVO;
    }

    @Override
    public List<ReportForms1VO> queryReportForms1(Date date) {
//        日期
//        注册数
//        新用户付费数
//        老用户付费数
//        新用户付费率
//        新用户付费金额
//        老用户付费金额
//        总付费
//        arpu值
//        新用户付费占比 取回成功订单数
//        充值-取回
        List<ReportForms1VO> reportForms1VOS = new ArrayList<ReportForms1VO>();
        Integer dayTotalUsers = userRepository.dayUserRegistCount(date);
        Integer dayCharges = userRepository.dayUserChargeCount(date);
        Integer oldDayCharges = userRepository.dayOldUserChargeCount(date);
        BigDecimal allAmount = userRepository.dayAmount(date);
        BigDecimal allPackupAmount = userRepository.dayPackupAmount(date);
        reportForms1VOS.add(new ReportForms1VO() {{
            setDate(date);
            setRegisterCount(dayTotalUsers);
            setFufeiCount(dayCharges);
            setOldFufeiCount(oldDayCharges);
            setFufeiRate(dayTotalUsers == 0 ? BigDecimal.ZERO : new BigDecimal(dayCharges).divide(new BigDecimal(dayTotalUsers), 2, RoundingMode.HALF_UP));
            setFufeiRate(new BigDecimal(dayCharges).divide(new BigDecimal(dayTotalUsers), 2, RoundingMode.HALF_UP));
            setFufeiAmount(userRepository.newUserChargeAmount(date));
            setOldFufeiAmount(userRepository.oldUserChargeAmount(date));
            setAllAmount(allAmount);
            setQuhuiCount(userRepository.countPackagePickupAmount(date));
            setChognzhiquhuiCha(allAmount.subtract(allPackupAmount));
        }});
        return reportForms1VOS;
    }

    @Override
    public List<ReportForms1VO> queryReportForms1(Date startDate, Date endDate, Long ocpcChannelId) {
        // 将java.util.Date转换为java.time.LocalDate
        if (startDate == null || endDate == null) {
            return new ArrayList<>();
        }
        LocalDate start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        List<ReportForms1VO> reportForms1VOS = new ArrayList<ReportForms1VO>();
// 确保end日期不晚于today
        if (end.isAfter(LocalDate.now())) {
            end = LocalDate.now();
        }
        if (start == null) {
            start = LocalDate.now();

        }
        if (end == null) {
            end = LocalDate.now();
        }

        // 如果区间超过15天，则调整start日期
        if (start.plusDays(15).isBefore(end)) {
            start = end.minusDays(15);
        }

        while (!start.isAfter(end)) {
            // 将LocalDate转换回Date并添加到列表中
            Date date = Date.from(start.atStartOfDay(ZoneId.systemDefault()).toInstant());
            Integer dayTotalUsers;
            Integer dayCharges;
            Integer oldDayCharges;
            BigDecimal allAmount;
            if (ocpcChannelId == null) {
                dayTotalUsers = userRepository.dayUserRegistCount(date);
                dayCharges = userRepository.dayUserChargeCount(date);
                oldDayCharges = userRepository.dayOldUserChargeCount(date);
                allAmount = userRepository.dayAmount(date);
            } else {
                dayTotalUsers = userRepository.dayUserRegistCountChannel(date, ocpcChannelId);
                dayCharges = userRepository.dayUserChargeCountChannel(date, ocpcChannelId);
                oldDayCharges = userRepository.dayOldUserChargeCountChannel(date, ocpcChannelId);
                allAmount = userRepository.dayAmountChannel(date, ocpcChannelId);
            }
            if (allAmount == null) {
                allAmount = BigDecimal.ZERO;
            }
            BigDecimal allPackupAmount;
            Integer allUserChargeCount;

            if (ocpcChannelId == null) {
                allPackupAmount = userRepository.dayPackupAmount(date);
                allUserChargeCount = userRepository.allUserChargeCount(date);
            } else {
                allPackupAmount = userRepository.dayPackupAmountChannel(date, ocpcChannelId);
                allUserChargeCount = userRepository.allUserChargeCountChannel(date, ocpcChannelId);
            }

            BigDecimal finalAllAmount = allAmount;
            reportForms1VOS.add(new ReportForms1VO() {{
                setDate(date);
                setRegisterCount(dayTotalUsers);
                setFufeiCount(dayCharges);
                setOldFufeiCount(oldDayCharges);
                setFufeiRate(dayTotalUsers == 0 ? BigDecimal.ZERO : new BigDecimal(dayCharges).divide(new BigDecimal(dayTotalUsers), 2, RoundingMode.HALF_UP));
//                setFufeiRate(new BigDecimal(dayCharges).divide(new BigDecimal(dayTotalUsers), 2, RoundingMode.HALF_UP));
                if (ocpcChannelId == null) {
                    setFufeiAmount(userRepository.newUserChargeAmount(date));
                    setOldFufeiAmount(userRepository.oldUserChargeAmount(date));
                    setQuhuiCount(userRepository.countPackagePickupAmount(date));
                } else {
                    setFufeiAmount(userRepository.newUserChargeAmountChannel(date, ocpcChannelId));
                    setOldFufeiAmount(userRepository.oldUserChargeAmountChannel(date, ocpcChannelId));
                    setQuhuiCount(userRepository.countPackagePickupAmountChannel(date, ocpcChannelId));
                }
                setAllAmount(finalAllAmount);
                setArpu(allUserChargeCount == 0 ? BigDecimal.ZERO : finalAllAmount.divide(new BigDecimal(allUserChargeCount), 2, RoundingMode.HALF_UP));
                setChognzhiquhuiCha(finalAllAmount.subtract(allPackupAmount == null ? BigDecimal.ZERO : allPackupAmount));
            }});
            start = start.plusDays(1);
        }
        return reportForms1VOS;
    }
}
