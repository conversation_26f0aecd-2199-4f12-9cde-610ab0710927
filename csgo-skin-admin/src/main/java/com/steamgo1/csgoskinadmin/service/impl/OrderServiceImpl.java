package com.steamgo1.csgoskinadmin.service.impl;

import cc.siyecao.uid.core.utils.StringUtils;
import com.steamgo1.csgoskinadmin.converter.OrderChargeConverter;
import com.steamgo1.csgoskinadmin.converter.UserConverter;
import com.steamgo1.csgoskinadmin.dto.UserChargeOrderQueryDTO;
import com.steamgo1.csgoskinadmin.dto.UserPickupOrderCountDTO;
import com.steamgo1.csgoskinadmin.dto.UserPickupOrderQueryDTO;
import com.steamgo1.csgoskinadmin.service.OrderService;
import com.steamgo1.csgoskinadmin.vo.OrderChargeVO;
import com.steamgo1.csgoskinadmin.vo.OrderPickupVO;
import com.steamgo1.csgoskincommon.dao.OrderChargeRepository;
import com.steamgo1.csgoskincommon.dao.UserPackagePickupRepository;
import com.steamgo1.csgoskincommon.entity.OrderChargeEntity;
import com.steamgo1.csgoskincommon.entity.UserEntity;
import com.steamgo1.csgoskincommon.entity.UserPackagePickupEntity;
import com.steamgo1.csgoskincommon.entity.enums.PackagePickupStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class OrderServiceImpl implements OrderService {

    @Autowired
    private OrderChargeRepository orderChargeRepository;
    @Autowired
    private OrderChargeConverter orderChargeConverter;

    @Autowired
    private UserConverter userConverter;

    @Autowired
    private UserPackagePickupRepository userPackagePickupRepository;

    @Override
    public Page<OrderChargeVO> querOrderChargeVO(UserChargeOrderQueryDTO userChargeOrderQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(userChargeOrderQueryDTO.getPage(), userChargeOrderQueryDTO.getSize(), sort);
        Page<OrderChargeEntity> orderChargeEntityPage = orderChargeRepository.findAll((Specification<OrderChargeEntity>) (root, query, cb) -> {
            Join<OrderChargeEntity, UserEntity> join = root.join("user", JoinType.LEFT);
            List<Predicate> ps = new ArrayList<>();
            if (userChargeOrderQueryDTO.getUserId() != null) {
                ps.add(cb.equal(join.get("id"), userChargeOrderQueryDTO.getUserId()));
            }
            if (StringUtils.isNotBlank(userChargeOrderQueryDTO.getPhone())) {
                ps.add(cb.like(join.get("phone"), "%" + userChargeOrderQueryDTO.getPhone() + "%"));
            }
            if (StringUtils.isNotBlank(userChargeOrderQueryDTO.getOrderNo())) {
                ps.add(cb.like(root.get("orderNo"), "%" + userChargeOrderQueryDTO.getOrderNo() + "%"));
            }
            if (userChargeOrderQueryDTO.getStatus() != null) {
                ps.add(cb.equal(root.get("orderStatus"), userChargeOrderQueryDTO.getStatus() - 1));
            }
            if (userChargeOrderQueryDTO.getStartTime() != null && userChargeOrderQueryDTO.getEndTime() != null) {
                ps.add(cb.between(root.get("createTime"), userChargeOrderQueryDTO.getStartTime(), userChargeOrderQueryDTO.getEndTime()));
            }
            if (userChargeOrderQueryDTO.getRegisterStartTime() != null && userChargeOrderQueryDTO.getRegisterEndTime() != null) {
                ps.add(cb.between(join.get("createTime"), userChargeOrderQueryDTO.getRegisterStartTime(), userChargeOrderQueryDTO.getRegisterEndTime()));
            }
            if (userChargeOrderQueryDTO.getChannelId() != null) {
                ps.add(cb.equal(join.get("channel"), userChargeOrderQueryDTO.getChannelId()));
            }
            return query.where(ps.toArray(new Predicate[ps.size()])).getRestriction();
        }, pageable);
        List<OrderChargeVO> orderChargeVOList = orderChargeEntityPage.getContent().stream().map(item -> orderChargeConverter.toOrderChargeVO(item, userConverter.toUserBaseInfoVO(item.getUser()), item.getChargeGoods())).collect(Collectors.toList());
        return new PageImpl<>(orderChargeVOList, orderChargeEntityPage.getPageable(), orderChargeEntityPage.getTotalElements());
    }

    @Override
    public Page<OrderPickupVO> querOrderPickupVO(UserPickupOrderQueryDTO userPickupOrderQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(userPickupOrderQueryDTO.getPage(), userPickupOrderQueryDTO.getSize(), sort);
        Page<UserPackagePickupEntity> userPackagePickupEntityPage = userPackagePickupRepository.findAll((Specification<UserPackagePickupEntity>) (root, query, cb) -> {
            Join<OrderChargeEntity, UserEntity> join = root.join("user", JoinType.LEFT);
            List<Predicate> ps = new ArrayList<>();
            if (userPickupOrderQueryDTO.getUserId() != null) {
                ps.add(cb.equal(join.get("id"), userPickupOrderQueryDTO.getUserId()));
            }
            if (StringUtils.isNotBlank(userPickupOrderQueryDTO.getPhone())) {
                ps.add(cb.like(join.get("phone"), "%" + userPickupOrderQueryDTO.getPhone() + "%"));
            }
            if (StringUtils.isNotBlank(userPickupOrderQueryDTO.getOrderNo())) {
                ps.add(cb.like(root.get("orderNo"), "%" + userPickupOrderQueryDTO.getOrderNo() + "%"));
            }
//            if(userPickupOrderQueryDTO.getStatus()!=null){
//                ps.add(cb.equal(root.get("status"), userPickupOrderQueryDTO.getStatus()-1));
//            }
            if (userPickupOrderQueryDTO.getStatus() != null) {
                if (userPickupOrderQueryDTO.getStatus() == 1) {
                    List<PackagePickupStatus> statusValues = Arrays.asList(PackagePickupStatus.CANCEL, PackagePickupStatus.FAIL, PackagePickupStatus.FAIL_STEAM);
                    ps.add(cb.in(root.get("status")).value(statusValues));
                } else if (userPickupOrderQueryDTO.getStatus() == 2) {
                    ps.add(cb.equal(root.get("status"), 4));
                }
            }
            if (userPickupOrderQueryDTO.getStartTime() != null && userPickupOrderQueryDTO.getEndTime() != null) {
                ps.add(cb.between(root.get("createTime"), userPickupOrderQueryDTO.getStartTime(), userPickupOrderQueryDTO.getEndTime()));
            }
            return query.where(ps.toArray(new Predicate[ps.size()])).getRestriction();
        }, pageable);
        List<OrderPickupVO> orderPickupVOList = userPackagePickupEntityPage.getContent().stream().map(item -> {
            item.getUserPackage().getSkin().setDiamond(item.getUserPackage().getDiamond());
            return orderChargeConverter.toOrderPickupVO(item, userConverter.toUserBaseInfoVO(item.getUser()), item.getUserPackage().getSkin());
        }).collect(Collectors.toList());
        return new PageImpl<>(orderPickupVOList, userPackagePickupEntityPage.getPageable(), userPackagePickupEntityPage.getTotalElements());
    }

    @Override
    public Integer CountOrderPickupVO(UserPickupOrderCountDTO userPickupOrderCountDTO) {

        Integer integer = userPackagePickupRepository.queryPickUpCount(userPickupOrderCountDTO.getStatus());

        return integer;
    }

    @Override
    public Page<OrderChargeVO> querOrderChargeVOByUserId(UserChargeOrderQueryDTO userChargeOrderQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(userChargeOrderQueryDTO.getPage(), userChargeOrderQueryDTO.getSize(), sort);
        Page<OrderChargeEntity> orderChargeVOS = orderChargeRepository.findByUserId(userChargeOrderQueryDTO.getUserId(), pageable);
        List<OrderChargeVO> orderChargeVOList = orderChargeVOS.getContent().stream()
                .map(item ->
                        orderChargeConverter.toOrderChargeVO(item,
                                userConverter.toUserBaseInfoVO(item.getUser()),
                                item.getChargeGoods()))
                .collect(Collectors.toList());
        return new PageImpl<>(orderChargeVOList, orderChargeVOS.getPageable(), orderChargeVOS.getTotalElements());
    }

    @Override
    public Page<OrderPickupVO> querOrderPickupVOByUserId(UserPickupOrderQueryDTO userPickupOrderQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(userPickupOrderQueryDTO.getPage(), userPickupOrderQueryDTO.getSize(), sort);
        Page<UserPackagePickupEntity> userPackagePickupEntityPage = userPackagePickupRepository
                .findByUserId(userPickupOrderQueryDTO.getUserId(), pageable);
        List<OrderPickupVO> orderPickupVOList = userPackagePickupEntityPage.getContent()
                .stream()
                .map(i -> {
                    i.getUserPackage().getSkin().setDiamond(i.getUserPackage().getDiamond());
                    return orderChargeConverter.toOrderPickupVO(i,
                            userConverter.toUserBaseInfoVO(i.getUser()),
                            i.getUserPackage().getSkin());
                })
                .collect(Collectors.toList());
        return new PageImpl<>(orderPickupVOList, userPackagePickupEntityPage.getPageable(), orderPickupVOList.size());
    }
}
