package com.steamgo1.csgoskinadmin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinadmin.converter.RollHomeConverter;
import com.steamgo1.csgoskinadmin.dto.*;
import com.steamgo1.csgoskinadmin.schedule.producer.ProducerService;
import com.steamgo1.csgoskinadmin.service.RollHomeService;
import com.steamgo1.csgoskinadmin.utils.SecurityUtils;
import com.steamgo1.csgoskinadmin.vo.RollHomeFullVO;
import com.steamgo1.csgoskincommon.contant.CsgoContants;
import com.steamgo1.csgoskincommon.dao.*;
import com.steamgo1.csgoskincommon.entity.*;
import com.steamgo1.csgoskincommon.entity.enums.*;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.utils.*;
import com.steamgo1.csgoskincommon.enums.LanguageEnum;
import com.steamgo1.csgoskincommon.vo.RollHomeFilterParamVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
@Transactional
public class RollHomeServiceImpl implements RollHomeService {
    @Value("${spring.redis.prefix.roll-home}")
    private String redisRollHomePrefix;

    @Value("${rabbitmq.exchange.csgo}")
    private String exchageName;

    @Autowired
    private SkinRepository skinRepository;

    @Autowired
    private RollHomeRepository rollHomeRepository;

    @Autowired
    private RollHomeUserRepository rollHomeUserRepository;

    @Autowired
    private RollHomeSkinRepository rollHomeSkinRepository;

    @Autowired
    private RollHomeConverter rollHomeConverter;

    @Autowired
    private AdminUserRepository adminUserRepository;

    @Autowired
    private ProducerService producerService;

    @Autowired
    private UserRepository userRepository;

    @Value("${roll.min}")
    private Integer minRoll;

    @Value("${roll.max}")
    private Integer maxRoll;
    @Autowired
    private RollHomeService rollHomeService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private TranslationUtils translationUtils;

    /**
     * 处理多语言字段翻译
     * 如果多语言字段为空或缺少某些语言内容，且中文字段有内容，则进行翻译
     *
     * @param i18nField 多语言字段
     * @param chineseText 中文文本内容
     * @return 处理后的多语言字段
     */
    private I18nField processI18nFieldTranslation(I18nField i18nField, String chineseText) {
        // 如果中文文本为空，直接返回原多语言字段
        if (chineseText == null || chineseText.trim().isEmpty()) {
            return i18nField;
        }

        // 如果多语言字段为空，创建新的多语言字段
        if (i18nField == null) {
            log.info("多语言字段为空，使用中文内容进行翻译: {}", chineseText);
            return translationUtils.translateToAllSupportedLanguages(chineseText, LanguageEnum.CHINESE);
        }

        // 检查中文内容是否发生变化
        String existingChineseText = i18nField.get(LanguageEnum.CHINESE);
        boolean chineseContentChanged = !chineseText.equals(existingChineseText);

        if (chineseContentChanged) {
            log.info("中文内容发生变化: '{}' -> '{}'，将重新翻译所有语言", existingChineseText, chineseText);
        }

        // 检查各语言是否有内容，如果没有或中文内容变化则进行翻译
        boolean needsTranslation = false;
        Map<LanguageEnum, String> translations = new HashMap<>();

        // 首先添加中文内容
        translations.put(LanguageEnum.CHINESE, chineseText);

        // 检查其他支持的语言
        for (LanguageEnum language : LanguageEnum.getSupportedLanguages()) {
            if (language.equals(LanguageEnum.CHINESE)) {
                continue; // 跳过中文，已经处理
            }

            String existingText = i18nField.get(language);
            boolean languageContentMissing = existingText == null || existingText.trim().isEmpty();

            if (languageContentMissing || chineseContentChanged) {
                // 该语言没有内容或中文内容发生变化，需要翻译
                if (languageContentMissing) {
                    log.info("语言 {} 缺少内容，进行翻译: {} -> ?", language.getCode(), chineseText);
                } else {
                    log.info("中文内容变化，重新翻译语言 {}: {} -> ?", language.getCode(), chineseText);
                }

                String translatedText = translationUtils.translateText(chineseText, LanguageEnum.CHINESE, language);
                translations.put(language, translatedText);
                needsTranslation = true;
            } else {
                // 该语言已有内容且中文未变化，保留原内容
                translations.put(language, existingText);
            }
        }

        if (needsTranslation) {
            if (chineseContentChanged) {
                log.info("完成多语言字段重新翻译，中文内容变化导致所有语言更新");
            } else {
                log.info("完成多语言字段翻译，更新了缺失的语言内容");
            }
            return new I18nField(translations);
        } else {
            // 只更新中文内容
            i18nField.put(LanguageEnum.CHINESE, chineseText);
            return i18nField;
        }
    }

    @Override
    public RollHomeFullVO addRollHome(RollHomeAddDTO rollHomeAddDTO) {
        Long userId = SecurityUtils.getUserId();
        AdminUserEntity user = adminUserRepository.findById(userId).get();
        if (user == null) {
            log.error("管理员添加roll房失败");
            // todo 国际化
            // throw new CsgoSkinException("添加Roll房失败");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.admin.add.roll.home.failed"));
        }
        log.info("添加Roll房参数：{}", JSONObject.toJSONString(rollHomeAddDTO));

        // 处理多语言字段翻译
        I18nField processedI18nFieldName = processI18nFieldTranslation(rollHomeAddDTO.getI18nFieldName(), rollHomeAddDTO.getName());
        I18nField processedI18nFieldRemarks = processI18nFieldTranslation(rollHomeAddDTO.getI18nFieldRemarks(), rollHomeAddDTO.getRemarks());

        RollHomeEntity rollHomeEntity = new RollHomeEntity();
        rollHomeEntity.setAdminUser(user);
        rollHomeEntity.setName(rollHomeAddDTO.getName());
        rollHomeEntity.setI18nFieldName(processedI18nFieldName);
        rollHomeEntity.setMaxPeople(rollHomeAddDTO.getMaxPeople());
        rollHomeEntity.setIsRecommend(rollHomeAddDTO.getIsRecommend());
        RollHomeType rollHomeType = RollHomeType.instance(rollHomeAddDTO.getRollHomeType());
        if (rollHomeType == null) {
            log.error("管理员添加roll房失败");
            // todo 国际化
            // throw new CsgoSkinException("rollHomeType不存在");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.roll.home.type.not.exist"));
        }
        rollHomeEntity.setRollHomeType(rollHomeType);
        RollHomeLotteryMethod rollHomeLotteryMethod = RollHomeLotteryMethod.instance(rollHomeAddDTO.getRollHomeLotteryMethod());
        if (rollHomeLotteryMethod == null) {
            log.error("管理员添加roll房失败");
            // todo 国际化
            // throw new CsgoSkinException("rollHomeLotteryMethod不存在");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.roll.home.lottery.method.not.exist"));
        }
        rollHomeEntity.setRollHomeLotteryMethod(rollHomeLotteryMethod);
        rollHomeEntity.setPassword(rollHomeAddDTO.getPassword());
        rollHomeEntity.setMaxPeople(rollHomeAddDTO.getMaxPeople());
        rollHomeEntity.setLotteryTime(rollHomeAddDTO.getLotteryTime());
        rollHomeEntity.setConsumeThreshold(rollHomeAddDTO.getConsumeThreshold());
        rollHomeEntity.setSecretHash(CharUtil.getRandomString(128));
        rollHomeEntity.setSecretSalt(CharUtil.getRandomString(64));
        rollHomeEntity.setPublicHash(HashUtils.SHA256(rollHomeEntity.getSecretHash(), rollHomeEntity.getSecretHash()));
        rollHomeEntity.setClientSeed(CharUtil.getRandomString(64));
        rollHomeEntity.setRemarks(rollHomeAddDTO.getRemarks());
        rollHomeEntity.setI18nFieldRemarks(processedI18nFieldRemarks);
        rollHomeEntity.setStatus(RollHomeStatus.UNSTART);
        rollHomeEntity.setThresholdType(Optional.ofNullable(ThresholdType.instance(rollHomeAddDTO.getThresholdType())).orElse(ThresholdType.CHARGE));
        rollHomeEntity = rollHomeRepository.save(rollHomeEntity);
        for (RollHomeAddDTO.RollHomeSkinDTO rollHomeSkinDTO : rollHomeAddDTO.getRollHomeSkin()) {
            SkinEntity skin = skinRepository.findById(rollHomeSkinDTO.getSkinId()).get();
            if (skin == null) {
                log.error("饰品不存在: {}", rollHomeSkinDTO.getSkinId());
                // todo 国际化
                // throw new CsgoSkinException("饰品不存在");
                throw new CsgoSkinException(I18nUtils.getMessage("exception.skin.not.exist"));
            }
            RollHomeSkinEntity rollHomeSkin = new RollHomeSkinEntity();
            rollHomeSkin.setRollHome(rollHomeEntity);
            rollHomeSkin.setSkin(skin);
            rollHomeSkin.setGrade(rollHomeSkin.getGrade());
            rollHomeSkin.setIsDeleted(false);
            rollHomeSkinRepository.save(rollHomeSkin);

        }
//        开始添加延时任务
        if (rollHomeEntity.getRollHomeLotteryMethod().equals(RollHomeLotteryMethod.FIXTIME)) {
            producerService.addRollHomeDelayedTask(rollHomeEntity.getId());
        }
        return this.toRollHomeFullVO(rollHomeEntity);
    }


    @Override
    public RollHomeEntity addRollHomeByTask(RollHomeAddDTO rollHomeAddDTO) {
        Long userId = CsgoContants.ADMIN_ID;
        AdminUserEntity user = adminUserRepository.findById(userId).get();
        if (user == null) {
            log.error("管理员添加roll房失败");
            // todo 国际化
            // throw new CsgoSkinException("添加Roll房失败");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.admin.add.roll.home.failed"));
        }
        log.info("添加Roll房参数：{}", JSONObject.toJSONString(rollHomeAddDTO));

        // 处理多语言字段翻译
        I18nField processedI18nFieldName = processI18nFieldTranslation(rollHomeAddDTO.getI18nFieldName(), rollHomeAddDTO.getName());
        I18nField processedI18nFieldRemarks = processI18nFieldTranslation(rollHomeAddDTO.getI18nFieldRemarks(), rollHomeAddDTO.getRemarks());

        RollHomeEntity rollHomeEntity = new RollHomeEntity();
        rollHomeEntity.setAdminUser(user);
        rollHomeEntity.setName(rollHomeAddDTO.getName());
        rollHomeEntity.setI18nFieldName(processedI18nFieldName);
        rollHomeEntity.setMaxPeople(rollHomeAddDTO.getMaxPeople());
        rollHomeEntity.setIsRecommend(rollHomeAddDTO.getIsRecommend());
        RollHomeType rollHomeType = RollHomeType.instance(rollHomeAddDTO.getRollHomeType());
        if (rollHomeType == null) {
            log.error("管理员添加roll房失败");
            // todo 国际化
            // throw new CsgoSkinException("rollHomeType不存在");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.roll.home.type.not.exist"));
        }
        rollHomeEntity.setRollHomeType(rollHomeType);
        RollHomeLotteryMethod rollHomeLotteryMethod = RollHomeLotteryMethod.instance(rollHomeAddDTO.getRollHomeLotteryMethod());
        if (rollHomeLotteryMethod == null) {
            log.error("管理员添加roll房失败");
            // todo 国际化
            // throw new CsgoSkinException("rollHomeLotteryMethod不存在");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.roll.home.lottery.method.not.exist"));
        }
        rollHomeEntity.setRollHomeLotteryMethod(rollHomeLotteryMethod);
        rollHomeEntity.setPassword(rollHomeAddDTO.getPassword());
        rollHomeEntity.setMaxPeople(rollHomeAddDTO.getMaxPeople());
        rollHomeEntity.setLotteryTime(rollHomeAddDTO.getLotteryTime());
        rollHomeEntity.setConsumeThreshold(rollHomeAddDTO.getConsumeThreshold());
        rollHomeEntity.setSecretHash(CharUtil.getRandomString(128));
        rollHomeEntity.setSecretSalt(CharUtil.getRandomString(64));
        rollHomeEntity.setPublicHash(HashUtils.SHA256(rollHomeEntity.getSecretHash(), rollHomeEntity.getSecretHash()));
        rollHomeEntity.setClientSeed(CharUtil.getRandomString(64));
        rollHomeEntity.setRemarks(rollHomeAddDTO.getRemarks());
        rollHomeEntity.setI18nFieldRemarks(processedI18nFieldRemarks);
        rollHomeEntity.setStatus(RollHomeStatus.UNSTART);
        rollHomeEntity.setThresholdType(Optional.ofNullable(ThresholdType.instance(rollHomeAddDTO.getThresholdType())).orElse(ThresholdType.CHARGE));
        rollHomeEntity = rollHomeRepository.save(rollHomeEntity);
        for (RollHomeAddDTO.RollHomeSkinDTO rollHomeSkinDTO : rollHomeAddDTO.getRollHomeSkin()) {
            SkinEntity skin = skinRepository.findById(rollHomeSkinDTO.getSkinId()).get();
            if (skin == null) {
                log.error("饰品不存在: {}", rollHomeSkinDTO.getSkinId());
                // todo 国际化
                // throw new CsgoSkinException("饰品不存在");
                throw new CsgoSkinException(I18nUtils.getMessage("exception.skin.not.exist"));
            }
            RollHomeSkinEntity rollHomeSkin = new RollHomeSkinEntity();
            rollHomeSkin.setRollHome(rollHomeEntity);
            rollHomeSkin.setSkin(skin);
            rollHomeSkin.setGrade(rollHomeSkin.getGrade());
            rollHomeSkin.setIsDeleted(false);
            rollHomeSkinRepository.save(rollHomeSkin);

        }
//        开始添加延时任务
        if (rollHomeEntity.getRollHomeLotteryMethod().equals(RollHomeLotteryMethod.FIXTIME)) {
            producerService.addRollHomeDelayedTask(rollHomeEntity.getId());
        }
        return rollHomeEntity;
    }

    @Override
    public void updateRollHome(RollHomeUpdateDTO rollHomeUpdateDTO) {
        Long userId = SecurityUtils.getUserId();
        AdminUserEntity user = adminUserRepository.findById(userId).get();
        if (user == null) {
            log.error("管理员更新roll房失败");
            // todo 国际化
            // throw new CsgoSkinException("更新Roll房失败");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.admin.update.roll.home.failed"));
        }
        log.info("更新Roll房参数：{}", JSONObject.toJSONString(rollHomeUpdateDTO));
        RollHomeEntity rollHomeEntity = rollHomeRepository.findById(rollHomeUpdateDTO.getId()).orElse(null);
        if (rollHomeEntity == null) {
            log.error("更新失败，roll房不存在");
            // todo 国际化
            // throw new CsgoSkinException("更新Roll房失败");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.roll.home.not.exist"));
        }
        if (!rollHomeEntity.getStatus().equals(RollHomeStatus.UNSTART)) {
            log.error("更新失败，roll房不存在");
            // todo 国际化
            // throw new CsgoSkinException("更新Roll房失败,只支持未开始的roll房");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.roll.home.only.unstarted.can.be.updated"));
        }

        // 处理多语言字段翻译
        I18nField processedI18nFieldName = processI18nFieldTranslation(rollHomeUpdateDTO.getI18nFieldName(), rollHomeUpdateDTO.getName());
        I18nField processedI18nFieldRemarks = processI18nFieldTranslation(rollHomeUpdateDTO.getI18nFieldRemarks(), rollHomeUpdateDTO.getRemarks());

        rollHomeEntity.setAdminUser(user);
        rollHomeEntity.setName(rollHomeUpdateDTO.getName());
        rollHomeEntity.setI18nFieldName(processedI18nFieldName);
        rollHomeEntity.setMaxPeople(rollHomeUpdateDTO.getMaxPeople());
        rollHomeEntity.setIsRecommend(rollHomeUpdateDTO.getIsRecommend());
        RollHomeType rollHomeType = RollHomeType.instance(rollHomeUpdateDTO.getRollHomeType());
        if (rollHomeType == null) {
            log.error("管理员更新roll房失败");
            // todo 国际化
            // throw new CsgoSkinException("rollHomeType不存在");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.roll.home.type.not.exist"));
        }
        rollHomeEntity.setRollHomeType(rollHomeType);
        RollHomeLotteryMethod rollHomeLotteryMethod = RollHomeLotteryMethod.instance(rollHomeUpdateDTO.getRollHomeLotteryMethod());
        if (rollHomeLotteryMethod == null) {
            log.error("管理员添加roll房失败");
            // todo 国际化
            // throw new CsgoSkinException("rollHomeLotteryMethod不存在");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.roll.home.lottery.method.not.exist"));
        }

        rollHomeEntity.setRollHomeLotteryMethod(rollHomeLotteryMethod);
        rollHomeEntity.setPassword(rollHomeUpdateDTO.getPassword());
        rollHomeEntity.setMaxPeople(rollHomeUpdateDTO.getMaxPeople());
        rollHomeEntity.setLotteryTime(rollHomeUpdateDTO.getLotteryTime());
        rollHomeEntity.setConsumeThreshold(rollHomeUpdateDTO.getConsumeThreshold());
        rollHomeEntity.setRemarks(rollHomeUpdateDTO.getRemarks());
        rollHomeEntity.setI18nFieldRemarks(processedI18nFieldRemarks);
        rollHomeEntity.setStatus(RollHomeStatus.UNSTART);
        rollHomeEntity.setThresholdType(Optional.ofNullable(ThresholdType.instance(rollHomeUpdateDTO.getThresholdType())).orElse(ThresholdType.CHARGE));
        rollHomeEntity = rollHomeRepository.save(rollHomeEntity);
        List<RollHomeSkinEntity> rollHomeSkinEntityList = rollHomeSkinRepository.findByRollHome(rollHomeEntity);
        rollHomeSkinRepository.deleteAll(rollHomeSkinEntityList);
        for (RollHomeUpdateDTO.RollHomeSkinDTO rollHomeSkinDTO : rollHomeUpdateDTO.getRollHomeSkin()) {
            SkinEntity skin = skinRepository.findById(rollHomeSkinDTO.getSkinId()).orElse(null);
            if (skin == null) {
                log.error("饰品不存在: {}", rollHomeSkinDTO.getSkinId());
                // todo 国际化
                // throw new CsgoSkinException("饰品不存在");
                throw new CsgoSkinException(I18nUtils.getMessage("exception.skin.not.exist"));
            }
            RollHomeSkinEntity rollHomeSkin = rollHomeConverter.toRollHomeSkinEntityListOfUpdate(rollHomeSkinDTO);
            rollHomeSkin.setRollHome(rollHomeEntity);
            rollHomeSkin.setSkin(skin);
            rollHomeSkin.setGrade(rollHomeSkin.getGrade());

            rollHomeSkin.setRollHome(rollHomeEntity);
            rollHomeSkinRepository.save(rollHomeSkin);

        }
//        开始添加延时任务
        if (rollHomeEntity.getRollHomeLotteryMethod().equals(RollHomeLotteryMethod.FIXTIME)) {
            producerService.addRollHomeDelayedTask(rollHomeEntity.getId());
        }
    }

    @Override
    public RollHomeFullVO toRollHomeFullVO(RollHomeEntity rollHome) {
        List<RollHomeSkinEntity> rollHomeSkinEntityList = rollHomeSkinRepository.findByRollHome(rollHome);
        List<RollHomeUserEntity> rollHomeUserRepositoryList = rollHomeUserRepository.findByRollHome(rollHome);
        RollHomeFullVO rollHomeFullVO = rollHomeConverter.toRollHomeFullVO(rollHome);
        rollHomeFullVO.setRollHomeSkin(rollHomeSkinEntityList);
        rollHomeFullVO.setRollHomeUser(rollHomeUserRepositoryList);
        return rollHomeFullVO;
    }

    @Override
    public Page<RollHomeFullVO> queryRollHome(RollHomeQueryDTO rollHomeQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(rollHomeQueryDTO.getPage(), rollHomeQueryDTO.getSize(), sort);
        Page<RollHomeEntity> rollHomeEntityPage = rollHomeRepository.findAll((Specification<RollHomeEntity>) (root, query, cb) -> {
            List<Predicate> ps = new ArrayList<>();
            if (rollHomeQueryDTO.getRollHomeType() != null) {
                ps.add(cb.equal(root.get("rollHomeType"), rollHomeQueryDTO.getRollHomeType()));
            }
            if (rollHomeQueryDTO.getRollHomeLotteryMethod() != null) {
                ps.add(cb.equal(root.get("rollHomeLotteryMethod"), rollHomeQueryDTO.getRollHomeLotteryMethod()));
            }
            if (rollHomeQueryDTO.getStatus() != null) {
                ps.add(cb.equal(root.get("status"), rollHomeQueryDTO.getStatus() - 1));
            }
            return query.where(ps.toArray(new Predicate[ps.size()])).getRestriction();
        }, pageable);
        List<RollHomeFullVO> rollHomeFullVOList = new ArrayList<>();
        for (RollHomeEntity rollHome : rollHomeEntityPage.getContent()) {
            RollHomeFullVO rollHomeFullVO = rollHomeConverter.toRollHomeFullVO(rollHome);
            rollHomeFullVO.setRollHomeUser(rollHomeUserRepository.findByRollHome(rollHome));
            rollHomeFullVO.setRollHomeSkin(rollHomeSkinRepository.findByRollHome(rollHome));
            rollHomeFullVOList.add(rollHomeFullVO);
        }
        return new PageImpl<>(rollHomeFullVOList, rollHomeEntityPage.getPageable(), rollHomeEntityPage.getTotalElements());
    }

    @Override
    public RollHomeFilterParamVO getRollHomeFilterParam() {
        return new RollHomeFilterParamVO() {{
            setRollHomeType(RollHomeType.toList());
            setRollHomeLotteryMethod(RollHomeLotteryMethod.toList());
            setStatus(RollHomeStatus.toList());
            setThresholdType(ThresholdType.toList());
        }};
    }

    @Override
    public void joinRollHome(RollHomeAddUserParamDTO rollHomeAddUserParamDTO) {
        Long rollHomeId = rollHomeAddUserParamDTO.getRollHomeId();
        String redisRollHomeKey = redisRollHomePrefix + ":" + rollHomeId;
        UserEntity user = userRepository.findById(rollHomeAddUserParamDTO.getUserId());
        RollHomeEntity rollHomeEntity = rollHomeRepository.findById(rollHomeId).get();
        if (rollHomeEntity == null) {
            log.error("roll房不存在：{}", rollHomeId);
            // todo 国际化
            // throw new CsgoSkinException("roll房不存在");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.roll.home.not.exist"));
        }
        // 检查用户是否有权限参与 TODO
        // 是否新手房间
        log.info("用户: {} 参加Roll房: {} 类型： {} 模式： {}", user.getId(), rollHomeId, rollHomeEntity.getRollHomeType().getValue(), rollHomeEntity.getRollHomeLotteryMethod().getValue());
        if (user.getType().equals(UserType.ACTUAL)) {
            // todo 国际化
            // throw new CsgoSkinException("不支持的用户类型");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.user.type.not.supported"));
        }
        // 是否已结束
        if (rollHomeEntity.getStatus().equals(RollHomeStatus.END)) {
            log.error("用户: {} 已经结束：{}", user.getId(), rollHomeId);
            // todo 国际化
            // throw new CsgoSkinException("已经结束");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.roll.home.has.ended"));
        }
        // 是否已参加
        if (rollHomeUserRepository.existsByRollHomeAndUser(rollHomeEntity, user)) {
            log.error("用户: {} 已经参与过Roll房：{}", user.getId(), rollHomeId);
            // todo 国际化
            // throw new CsgoSkinException("已经参与过");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.user.already.joined.roll.home"));
        }
        // 是否已满
        if (rollHomeEntity.getMaxPeople() != null && rollHomeUserRepository.countByRollHome(rollHomeEntity) >= rollHomeEntity.getMaxPeople()) {
            log.error("用户: {} Roll房已满：{}", user.getId(), rollHomeId);
            // todo 国际化
            // throw new CsgoSkinException("Roll房已满");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.roll.home.is.full"));
        }
        // 更新参与用户的roll点
        // roll房信息入缓存

        List<RollHomeUserEntity> rollHomeUserEntityList = rollHomeUserRepository.findByRollHome(rollHomeEntity);
        Integer interval = maxRoll / (rollHomeUserEntityList.size() + 1);
        Integer roll = minRoll;
        // 修改已参与用户的roll点
        for (RollHomeUserEntity rollHomeUserEntity : rollHomeUserEntityList) {
            rollHomeUserEntity.setMinRoll(roll);
            roll += interval;
            rollHomeUserEntity.setMaxRoll(roll - 1);
        }
        // 新增的用户
        RollHomeUserEntity rollHomeUserEntity = new RollHomeUserEntity();
        rollHomeUserEntity.setUser(user);
        rollHomeUserEntity.setRollHome(rollHomeEntity);
        rollHomeUserEntity.setMinRoll(roll);
        rollHomeUserEntity.setMaxRoll(maxRoll);
        rollHomeUserRepository.saveAll(rollHomeUserEntityList);
        rollHomeUserRepository.save(rollHomeUserEntity);
        if (rollHomeEntity.getRollHomeLotteryMethod().equals(RollHomeLotteryMethod.FIXPEOPLE)) {
            if (rollHomeUserRepository.countByRollHome(rollHomeEntity) >= rollHomeEntity.getMaxPeople()) {
                rollHomeEntity.setStatus(RollHomeStatus.START);
                rollHomeRepository.save(rollHomeEntity);
            }
        }
        RedisUtils.delete(redisRollHomeKey);
    }

    @Override
    public void rollHomeBindindUser(List<RollHomeBindingUserDTO> rollHomeBindingUserDTOList) {
        for (RollHomeBindingUserDTO rollHomeBindingUserDTO : rollHomeBindingUserDTOList) {
            if (rollHomeBindingUserDTO.getUserId() == null || rollHomeBindingUserDTO.getRollHomeSkinId() == null) {
                continue;
            }
            RollHomeSkinEntity rollHomeSkin = rollHomeSkinRepository.findById(rollHomeBindingUserDTO.getRollHomeSkinId()).orElse(null);
            if (rollHomeSkin == null) {
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.invalid.parameters"));
            }
            if (!rollHomeSkin.getRollHome().getStatus().equals(RollHomeStatus.UNSTART)) {
                // todo 国际化
                // throw new CsgoSkinException("roll房已开奖不支持修改");
                throw new CsgoSkinException(I18nUtils.getMessage("exception.roll.home.can.not.be.modified.after.draw"));
            }
            UserEntity user = userRepository.findById(rollHomeBindingUserDTO.getUserId());
            if (!rollHomeUserRepository.existsByRollHomeAndUser(rollHomeSkin.getRollHome(), user)) {
                // todo 国际化
                // throw new CsgoSkinException("用户未参加roll房");
                throw new CsgoSkinException(I18nUtils.getMessage("exception.user.not.join.roll.home"));
            }
            rollHomeSkin.setUser(user);
            rollHomeSkinRepository.save(rollHomeSkin);
        }
    }

    @Override
    public List<UserEntity> createRollHomeEnoughDay(String rollHomeType,
                                                    Date drawTime,
                                                    List<UserEntity> userEntities,
                                                    Integer robotNum,
                                                    BigDecimal amount) {
        try {
            //查询相同名称的roll房
            if ((userEntities == null || userEntities.isEmpty()) && robotNum != null) {
                userEntities = userRepository.randomCheatRobot(UserType.CHEAT_ROBOT.getCode() - 1, robotNum);
            } else if ((userEntities != null && !userEntities.isEmpty()) && robotNum != null) {
                userEntities = userEntities.stream().limit(robotNum).collect(Collectors.toList());
            }
            return getUserEntities(rollHomeType, drawTime, userEntities, RollHomeType.DAY, amount);
        } catch (Exception e) {
            e.printStackTrace();
            // todo 国际化
            // throw new CsgoSkinException("创建失败！");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.create.roll.home.failed"));
        }
    }


    @Override
    public List<UserEntity> createRollHomeEnoughWeek(String rollHomeType,
                                                     Date drawTime,
                                                     BigDecimal amount) {
        try {
            return getUserEntities(rollHomeType, drawTime, null, RollHomeType.WEEK, amount);
        } catch (Exception e) {
            // todo 国际化
            // throw new CsgoSkinException("创建失败！");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.create.roll.home.failed"));
        }
    }

    @Override
    public void bindRollHomeWeek() {
        //查询所有周roll房
        List<RollHomeEntity> rollHomeEntities = rollHomeRepository.findByRollHomeTypeAndRollHomeLotteryMethodAndThresholdTypeAndStatusOrderByConsumeThreshold(RollHomeType.WEEK,
                RollHomeLotteryMethod.FIXTIME,
                ThresholdType.CHARGE,
                RollHomeStatus.UNSTART);
        //查询相同名称的roll房
        for (RollHomeEntity rollHomeEntity : rollHomeEntities) {
            String keys = redisRollHomePrefix + ":" + UserType.CHEAT_ROBOT + ":*";
            Set<String> set = RedisUtils.keys(keys);
            List<Long> ids = set.stream().map(key -> {
                        String[] parts = key.split(":"); // 最多分割成 3 部分
                        if (parts.length < 3) {
                            return null; // 冒号不足两个
                        }
                        if (!parts[2].matches("[-+]?\\d+")) {
                            return null;
                        }
                        BigDecimal charged = RedisUtils.get(key, BigDecimal.class);
                        if (charged == null) {
                            return null;
                        }
                        if (charged.compareTo(rollHomeEntity.getConsumeThreshold()) < 0) {
                            return null;
                        }
                        return Long.parseLong(parts[2]);
                    }).filter(i -> i != null)
                    .collect(Collectors.toList());
            List<UserEntity> userEntities = userRepository.findByIdIn(ids);
            bindRollHome(userEntities, RollHomeType.WEEK, rollHomeEntity.getConsumeThreshold(), rollHomeEntity);
        }
    }

    @Override
    public void createRollHomeEnoughDay() {

        // 5000 1-2
        // 2000 1-4
        // 1000 3-6
        // 500 9-12
        // 100 17-24
        // 50 21-28
        log.info("rollHomeAdd 定时任务 {}, 线程名称： {}, 线程ID: {}", LocalDateTime.now(), Thread.currentThread().getName(), Thread.currentThread().getId());
        //设置一个日期为第二天0时0分0秒的时间为开奖时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date date = calendar.getTime();
        List<UserEntity> userEntities = new ArrayList<>();
        Integer robotNum = RandomUtils.randomInt(21, 28);
        userEntities = rollHomeService.createRollHomeEnoughDay(I18nUtils.getMessage(CsgoContants.rollHomeName.ROLL_HOME_50), date, userEntities, robotNum, new BigDecimal("50"));
        robotNum = RandomUtils.randomInt(17, 24, robotNum);
        userEntities = rollHomeService.createRollHomeEnoughDay(I18nUtils.getMessage(CsgoContants.rollHomeName.ROLL_HOME_100), date, userEntities, robotNum, new BigDecimal("100"));
        robotNum = RandomUtils.randomInt(9, 12, robotNum);
        userEntities = rollHomeService.createRollHomeEnoughDay(I18nUtils.getMessage(CsgoContants.rollHomeName.ROLL_HOME_500), date, userEntities, robotNum, new BigDecimal("500"));
        robotNum = RandomUtils.randomInt(3, 6, robotNum);
        userEntities = rollHomeService.createRollHomeEnoughDay(I18nUtils.getMessage(CsgoContants.rollHomeName.ROLL_HOME_1000), date, userEntities, robotNum, new BigDecimal("1000"));
        robotNum = RandomUtils.randomInt(1, 4, robotNum);
        userEntities = rollHomeService.createRollHomeEnoughDay(I18nUtils.getMessage(CsgoContants.rollHomeName.ROLL_HOME_2000), date, userEntities, robotNum, new BigDecimal("2000"));
        robotNum = RandomUtils.randomInt(1, 2, robotNum);
        userEntities = rollHomeService.createRollHomeEnoughDay(I18nUtils.getMessage(CsgoContants.rollHomeName.ROLL_HOME_5000), date, userEntities, robotNum, new BigDecimal("5000"));
        log.info("rollHomeAdd 定时任务 {}, 线程名称： {}, 线程ID: {}", LocalDateTime.now(), Thread.currentThread().getName(), Thread.currentThread().getId());

    }

    @Override
    public void createRollHomeEnoughWeek() {

        // 5000 1-3
        // 200
        log.info("WeekRollHomeAddTask 定时任务 {}, 线程名称： {}, 线程ID: {}", LocalDateTime.now(), Thread.currentThread().getName(), Thread.currentThread().getId());

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, 7);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date date = calendar.getTime();
        rollHomeService.createRollHomeEnoughWeek(I18nUtils.getMessage(CsgoContants.weekRollHomeName.MEET_ROLL_HOME_500), date, new BigDecimal("500"));
        rollHomeService.createRollHomeEnoughWeek(I18nUtils.getMessage(CsgoContants.weekRollHomeName.MEET_ROLL_HOME_2000), date, new BigDecimal("2000"));
        rollHomeService.createRollHomeEnoughWeek(I18nUtils.getMessage(CsgoContants.weekRollHomeName.MEET_ROLL_HOME_5000), date, new BigDecimal("5000"));
        rollHomeService.createRollHomeEnoughWeek(I18nUtils.getMessage(CsgoContants.weekRollHomeName.MEET_ROLL_HOME_10000), date, new BigDecimal("10000"));
        log.info("WeekRollHomeAddTask 定时任务 {}, 线程名称： {}, 线程ID: {}", LocalDateTime.now(), Thread.currentThread().getName(), Thread.currentThread().getId());

    }

    private List<UserEntity> getUserEntities(String rollHomeType, Date drawTime, List<UserEntity> userEntities, RollHomeType rollHome, BigDecimal amount) {
        log.info("创建{}", rollHomeType);
        RollHomeEntity rollHomeEntity = rollHomeRepository.findTopByNameOrderByIdDesc(rollHomeType);
        if (rollHomeEntity == null) {
            log.info("没找到之前的roll房：{}，生成失败！", rollHomeType);
            return userEntities;
        }

        //转换成入参对象
        RollHomeAddDTO rollHomeAddDTO = ReflectFieldCopyUtil.copyToNewInstance(rollHomeEntity, RollHomeAddDTO.class);
        rollHomeAddDTO.setLotteryTime(drawTime);

        //设置roll房类型
        rollHomeAddDTO.setRollHomeType(rollHomeEntity.getRollHomeType().getCode());
        rollHomeAddDTO.setRollHomeLotteryMethod(rollHomeEntity.getRollHomeLotteryMethod().getCode());
        rollHomeAddDTO.setThresholdType(rollHomeEntity.getThresholdType().getCode());
        rollHomeAddDTO.setConsumeThreshold(rollHomeEntity.getConsumeThreshold());

        //查询roll房对应的道具
        List<RollHomeSkinEntity> rollHomeSkinEntities = rollHomeSkinRepository.findByRollHome(rollHomeEntity);
        List<RollHomeAddDTO.RollHomeSkinDTO> rollHomeSkinDTOS = Optional.of(rollHomeSkinEntities)
                .orElse(new ArrayList<>())
                .stream()
                .map(i -> {
                    RollHomeAddDTO.RollHomeSkinDTO rollHomeSkinDTO = new RollHomeAddDTO.RollHomeSkinDTO();
                    rollHomeSkinDTO.setSkinId(i.getSkin().getId());
                    rollHomeSkinDTO.setGrade(0);
                    return rollHomeSkinDTO;
                })
                .collect(Collectors.toList());
        rollHomeAddDTO.setRollHomeSkin(rollHomeSkinDTOS);

        //创建roll房
        RollHomeEntity newRollHomeEntity = addRollHomeByTask(rollHomeAddDTO);
        log.info("{},创建成功！", rollHomeType);
        //绑定roll房
        if (rollHome.equals(RollHomeType.DAY)) {
            bindRollHome(userEntities, rollHome, amount, newRollHomeEntity);
        }
        return userEntities;
    }

    @Override
    public void bindRollHome(List<UserEntity> userEntities, RollHomeType rollHome, BigDecimal amount, RollHomeEntity newRollHomeEntity) {
        if (userEntities != null) {
            userEntities.forEach(i -> {
                RollHomeAddUserParamDTO rollHomeAddUserParamDTO = new RollHomeAddUserParamDTO();
                rollHomeAddUserParamDTO.setRollHomeId(newRollHomeEntity.getId());
                rollHomeAddUserParamDTO.setUserId(i.getId());
                rollHomeAddUserParamDTO.setRollHomeType(rollHome);
                rollHomeAddUserParamDTO.setAmount(amount);
                int outTime = RandomUtils.randomInt(60 * 60 * 1000, 6 * 60 * 60 * 1000);
//                int outTime = RandomUtils.randomInt(10000, 15000);
                String routingKey = CsgoContants.rollHomeUser.ROLL_HOME_USER_1H;
                if (outTime > 60 * 60 * 1000 && outTime <= 2 * 60 * 60 * 1000) {
                    routingKey = CsgoContants.rollHomeUser.ROLL_HOME_USER_2H;
                }
                if (outTime > 2 * 60 * 60 * 1000 && outTime <= 3 * 60 * 60 * 1000) {
                    routingKey = CsgoContants.rollHomeUser.ROLL_HOME_USER_3H;
                }
                if (outTime > 3 * 60 * 60 * 1000 && outTime <= 4 * 60 * 60 * 1000) {
                    routingKey = CsgoContants.rollHomeUser.ROLL_HOME_USER_4H;
                }
                if (outTime > 4 * 60 * 60 * 1000 && outTime <= 5 * 60 * 60 * 1000) {
                    routingKey = CsgoContants.rollHomeUser.ROLL_HOME_USER_5H;
                }
                if (outTime > 5 * 60 * 60 * 1000 && outTime <= 6 * 60 * 60 * 1000) {
                    routingKey = CsgoContants.rollHomeUser.ROLL_HOME_USER_6H;
                }

                rabbitTemplate.convertAndSend(exchageName, routingKey, JSONObject.toJSONString(rollHomeAddUserParamDTO), a -> {
//                    a.getMessageProperties().setExpiration(String.valueOf(outTime));
                    a.getMessageProperties().setExpiration("10000");
                    return a;
                });
            });
        }
    }

    @Override
    public List<RollHomeAddUserParamDTO> queryRobotNowRollHome(RollHomeUpdateDTO rollHomeUpdateDTO) {
        if (rollHomeUpdateDTO == null || rollHomeUpdateDTO.getId() == null) {
            // todo 国际化
            // throw new CsgoSkinException("rollHomeId不能为空！");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.roll.home.id.cannot.be.empty"));
        }
        List<RollHomeEntity> rollHomeEntities = rollHomeRepository
                .findByRollHomeTypeAndRollHomeLotteryMethodAndThresholdTypeAndStatusOrderByConsumeThreshold(
                        RollHomeType.values()[rollHomeUpdateDTO.getRollHomeType() - 1],
                        RollHomeLotteryMethod.values()[rollHomeUpdateDTO.getRollHomeLotteryMethod() - 1],
                        ThresholdType.values()[rollHomeUpdateDTO.getThresholdType()],
                        RollHomeStatus.UNSTART);

        List<RollHomeAddUserParamDTO> rollHomeAddUserParamDTOS = new ArrayList<>();
        if (rollHomeEntities == null || rollHomeEntities.size() == 0) {
            return rollHomeAddUserParamDTOS;
        }
        RollHomeEntity rollHomeEntity = rollHomeEntities.get(0);
        List<UserEntity> userEntities = new ArrayList<>();
        if (rollHomeEntity.getId().equals(rollHomeUpdateDTO.getId())) {
            //查询所有机器人
            userEntities = userRepository.findByType(UserType.CHEAT_ROBOT);
        } else {
            rollHomeEntity = rollHomeEntities.stream()
                    .filter(i -> {
                        BigDecimal minConsume = i.getConsumeThreshold();
                        return (minConsume.compareTo(rollHomeUpdateDTO.getConsumeThreshold()) < 0);
                    }).max(Comparator.comparing(RollHomeEntity::getConsumeThreshold))
                    .orElse(null);
            if (rollHomeEntity == null) {
                return rollHomeAddUserParamDTOS;
            }
            //查询当前房间中的所有机器人用户
            List<RollHomeUserEntity> rollHomeUserEntityList = rollHomeUserRepository.findByUserTypeAndRollHome(UserType.CHEAT_ROBOT, rollHomeEntity);
            //返回机器人用户列表
            userEntities = Optional.ofNullable(rollHomeUserEntityList).orElse(new ArrayList<>())
                    .stream()
                    .map(RollHomeUserEntity::getUser)
                    .collect(Collectors.toList());
        }

        rollHomeAddUserParamDTOS = Optional.ofNullable(userEntities)
                .orElse(new ArrayList<>())
                .stream().map(i -> {
                    RollHomeAddUserParamDTO rollHomeAddUserParamDTO = new RollHomeAddUserParamDTO();
                    rollHomeAddUserParamDTO.setUserId(i.getId());
                    rollHomeAddUserParamDTO.setNickname(i.getNickname());
                    return rollHomeAddUserParamDTO;
                }).collect(Collectors.toList());

        return rollHomeAddUserParamDTOS;
    }
}
