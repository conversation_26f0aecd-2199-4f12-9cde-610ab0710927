package com.steamgo1.csgoskinadmin.service;

import com.steamgo1.csgoskinadmin.dto.BaiduDataQueryDTO;
import com.steamgo1.csgoskinadmin.dto.GoogleDataQueryDTO;
import com.steamgo1.csgoskinadmin.dto.MetaDataQueryDTO;
import com.steamgo1.csgoskinadmin.vo.OcpcBaiduDataVO;
import com.steamgo1.csgoskinadmin.vo.OcpcGoogleDataVO;
import com.steamgo1.csgoskinadmin.vo.OcpcMetaDataVO;
import com.steamgo1.csgoskincommon.entity.OcpcBaiduAccountEntity;
import com.steamgo1.csgoskincommon.entity.OcpcBaiduTokenEntity;
import com.steamgo1.csgoskincommon.entity.OcpcChannelEntity;
import com.steamgo1.csgoskincommon.entity.OcpcGoogleTokenEntity;
import com.steamgo1.csgoskincommon.entity.OcpcMetaTokenEntity;
import org.springframework.data.domain.Page;

import java.util.List;

public interface OcpcService {
    List<OcpcChannelEntity> queryChannel();

    List<OcpcBaiduTokenEntity> querBaiduToken();

    List<OcpcBaiduAccountEntity> queryBaiduAccount();

    List<OcpcGoogleTokenEntity> queryGoogleToken();

    List<OcpcMetaTokenEntity> queryMetaToken();

    Page<OcpcBaiduDataVO> queryBaiduData(BaiduDataQueryDTO baiduDataQueryDTO);

    Page<OcpcGoogleDataVO> queryGoogleData(GoogleDataQueryDTO googleDataQueryDTO);

    Page<OcpcMetaDataVO> queryMetaData(MetaDataQueryDTO metaDataQueryDTO);
}
