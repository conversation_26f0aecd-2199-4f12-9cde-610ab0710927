package com.steamgo1.csgoskinadmin.service;

import com.steamgo1.csgoskinadmin.dto.RedPacketAddParamDTO;
import com.steamgo1.csgoskinadmin.dto.RedPacketQueryDTO;
import com.steamgo1.csgoskinadmin.vo.RedPacketQueryParamVO;
import com.steamgo1.csgoskinadmin.vo.RedPacketVO;
import com.steamgo1.csgoskincommon.entity.RedPacketEntity;
import org.springframework.data.domain.Page;

public interface RedPacketService {
    RedPacketQueryParamVO getRedPacketQueryParam();

    RedPacketEntity addRedPacketQueryParam(RedPacketAddParamDTO redPacketAddParamDTO);

    Page<RedPacketVO> queryRedPackets(RedPacketQueryDTO redPacketQueryDTO);
}
