package com.steamgo1.csgoskinadmin.service;

import com.steamgo1.csgoskinadmin.dto.*;
import com.steamgo1.csgoskinadmin.vo.*;
import com.steamgo1.csgoskincommon.entity.CaseEntity;
import com.steamgo1.csgoskincommon.entity.CaseUserRecordEntity;
import com.steamgo1.csgoskincommon.vo.CaseOpenVo;
import org.springframework.data.domain.Page;

import java.util.List;

public interface CaseService {

    CaseEntity addCase(CaseAddDTO caseAddDTO);


    CaseFullVO toCaseFullVO(CaseEntity caseEntity);

    List<CaseFullVO> toCaseFullVOList(List<CaseEntity> caseEntities);


    //删除箱子，软删除
    void delCaseById(Long caseId);

    //查询单个箱子
    CaseFullVO queryCase(Long caseId);

    //查询多个箱子
//    List<CaseFullVO> queryCases(CaseQueryDTO caseQueryDTO);

    List<CaseFullVO> queryCaseByCategory(CaseQueryDTO caseQueryDTO);

    //添加箱子
    CaseFullVO addFullCase(CaseFullAddDTO caseFullAddDTO);

    //更新箱子
    CaseFullVO updateFullCase(CaseFullUpdateDTO caseFullUpdateDTO);

    /**
     * 获取箱子分类
     *
     * @return
     */
    List<CaseCategoryVO> queryCaseCategory();

    /**
     * 获取箱子搜索条件
     *
     * @return
     */
    CaseQueryParamVO caseQueryParam();

    CaseFullVO queryCaseById(Long caseId);

    /**
     * 更新上下架状态
     *
     * @param caseUpdateSaleStateDTO
     */
    void updateIsSaleState(CaseUpdateSaleStateDTO caseUpdateSaleStateDTO);

    /**
     * 查询开箱记录
     *
     * @param openCaseRecordQueryDTO
     * @return
     */
    Page<OpenCaseRecordQueryVO> queryOpenCaseRecords(OpenCaseRecordQueryDTO openCaseRecordQueryDTO);

    /**
     * 查询开箱记录
     *
     * @param openCaseRecordQueryDTO
     * @return
     */
    Page<CaseUserRecordEntity> queryCaseUserRecordEntity(OpenCaseRecordQueryDTO openCaseRecordQueryDTO);

    /**
     * 查询箱子明细
     *
     * @param caseId
     * @return
     */
    CaseStatisticsVO queryCaseStatistics(Long caseId);

    CaseEntity switchCaseProtect(Boolean flag);

    List<CaseOpenVo> statisticsOpenCase(OpenCaseDTO openCaseDTO);

}
