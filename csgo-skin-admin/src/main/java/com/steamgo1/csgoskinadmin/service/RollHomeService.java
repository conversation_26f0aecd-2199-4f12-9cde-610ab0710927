package com.steamgo1.csgoskinadmin.service;

import com.steamgo1.csgoskinadmin.dto.*;
import com.steamgo1.csgoskinadmin.vo.RollHomeFullVO;
import com.steamgo1.csgoskincommon.entity.RollHomeEntity;
import com.steamgo1.csgoskincommon.entity.UserEntity;
import com.steamgo1.csgoskincommon.entity.enums.RollHomeType;
import com.steamgo1.csgoskincommon.vo.RollHomeFilterParamVO;
import org.springframework.data.domain.Page;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface RollHomeService {
    RollHomeFullVO addRollHome(RollHomeAddDTO rollHomeAddDTO);

    RollHomeEntity addRollHomeByTask(RollHomeAddDTO rollHomeAddDTO);

    void updateRollHome(RollHomeUpdateDTO rollHomeUpdateDTO);

    RollHomeFullVO toRollHomeFullVO(RollHomeEntity rollHome);

    Page<RollHomeFullVO> queryRollHome(RollHomeQueryDTO rollHomeQueryDTO);

    RollHomeFilterParamVO getRollHomeFilterParam();

    void joinRollHome(RollHomeAddUserParamDTO rollHomeAddUserParamDTO);

    void rollHomeBindindUser(List<RollHomeBindingUserDTO> rollHomeBindingUserDTOList);

    /**
     * 自动创建日roll房
     *
     * @param rollHomeType
     * @param drawTime
     * @param userEntities
     * @param robotNum
     * @param amount
     * @return
     */
    List<UserEntity> createRollHomeEnoughDay(String rollHomeType,
                                             Date drawTime,
                                             List<UserEntity> userEntities,
                                             Integer robotNum,
                                             BigDecimal amount);

    /**
     * 自动创建周roll房
     *
     * @param rollHomeType
     * @param drawTime
     * @param amount
     * @return
     */
    List<UserEntity> createRollHomeEnoughWeek(String rollHomeType,
                                              Date drawTime,
                                              BigDecimal amount);

    /**
     * 周roll房绑定机器人
     */
    void bindRollHomeWeek();

    /**
     * 自动创建日roll房
     */
    void createRollHomeEnoughDay();

    /**
     * 自动创建周roll房
     */
    void createRollHomeEnoughWeek();

    /**
     * roll房绑定机器人
     *
     * @param userEntities
     * @param rollHome
     * @param amount
     * @param newRollHomeEntity
     */
    void bindRollHome(List<UserEntity> userEntities, RollHomeType rollHome, BigDecimal amount, RollHomeEntity newRollHomeEntity);

    List<RollHomeAddUserParamDTO> queryRobotNowRollHome(RollHomeUpdateDTO rollHomeUpdateDTO);
}
