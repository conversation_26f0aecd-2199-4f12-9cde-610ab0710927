package com.steamgo1.csgoskinadmin.service.impl;

import com.steamgo1.csgoskinadmin.converter.OcpcConverter;
import com.steamgo1.csgoskinadmin.dto.BaiduDataQueryDTO;
import com.steamgo1.csgoskinadmin.dto.GoogleDataQueryDTO;
import com.steamgo1.csgoskinadmin.dto.MetaDataQueryDTO;
import com.steamgo1.csgoskinadmin.service.OcpcService;
import com.steamgo1.csgoskinadmin.vo.OcpcBaiduDataVO;
import com.steamgo1.csgoskinadmin.vo.OcpcGoogleDataVO;
import com.steamgo1.csgoskinadmin.vo.OcpcMetaDataVO;
import com.steamgo1.csgoskincommon.dao.*;
import com.steamgo1.csgoskincommon.entity.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class OcpcServiceImpl implements OcpcService {
    @Autowired
    private OcpcChannelRepository channelRepository;

    @Autowired
    private OcpcBaiduTokenRepository baiduTokenRepository;

    @Autowired
    private OcpcBaiduAccountRepository baiduAccountRepository;

    @Autowired
    private OcpcGoogleTokenRepository googleTokenRepository;

    @Autowired
    private OcpcMetaTokenRepository metaTokenRepository;

    @Autowired
    private OcpcBaiduDataRepository baiduDataRepository;

    @Autowired
    private OcpcMetaDataRepository metaDataRepository;

    @Autowired
    private OcpcGoogleDataRepository googleDataRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private OrderChargeRepository orderChargeRepository;

    @Autowired
    private OcpcConverter ocpcConverter;

    @Override
    public List<OcpcChannelEntity> queryChannel() {
        return channelRepository.findAll();
    }

    @Override
    public List<OcpcBaiduTokenEntity> querBaiduToken() {
        return baiduTokenRepository.findAll();
    }

    @Override
    public List<OcpcBaiduAccountEntity> queryBaiduAccount() {
        return baiduAccountRepository.findAll();
    }

    @Override
    public List<OcpcGoogleTokenEntity> queryGoogleToken() {
        return googleTokenRepository.findAll();
    }

    @Override
    public List<OcpcMetaTokenEntity> queryMetaToken() {
        return metaTokenRepository.findAll();
    }

    @Override
    public Page<OcpcBaiduDataVO> queryBaiduData(BaiduDataQueryDTO baiduDataQueryDTO) {

        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(baiduDataQueryDTO.getPage(), baiduDataQueryDTO.getSize(), sort);
        Page<OcpcBaiduDataEntity> ocpcBaiduDataEntityPage = baiduDataRepository.findAll((Specification<OcpcBaiduDataEntity>) (root, query, cb) -> {

            Join<OcpcBaiduDataEntity, OcpcChannelEntity> ocpcChannelJoin = root.join("ocpcChannel");
//            Join<OcpcBaiduDataEntity, UserEntity> userJoin = root.join("user");
            Join<OcpcBaiduDataEntity, OrderChargeEntity> orderChargeJoin = root.join("orderCharge", JoinType.LEFT);
            List<Predicate> ps = new ArrayList<>();
            if (baiduDataQueryDTO.getType() != null) {
                ps.add(cb.equal(root.get("type"), baiduDataQueryDTO.getType()));
            }
            if (baiduDataQueryDTO.getIp() != null) {
                ps.add(cb.equal(root.get("ip"), baiduDataQueryDTO.getIp()));
            }
            if (baiduDataQueryDTO.getStartTime() != null && baiduDataQueryDTO.getEndTime() != null) {
                ps.add(cb.between(root.get("createTime"), baiduDataQueryDTO.getStartTime(), baiduDataQueryDTO.getEndTime()));
            }
            if (baiduDataQueryDTO.getOcpcChannelId() != null) {
                ps.add(cb.equal(ocpcChannelJoin.get("id"), baiduDataQueryDTO.getOcpcChannelId()));
            }
            if (baiduDataQueryDTO.getOrderStatus() != null) {
                ps.add(cb.equal(orderChargeJoin.get("orderStatus"), baiduDataQueryDTO.getOrderStatus()));
            }
            return ps.isEmpty()
                    ? null
                    : cb.and(ps.toArray(new Predicate[0]));
        }, pageable);
        List<OcpcBaiduDataVO> ocpcBaiduDataVOList = ocpcBaiduDataEntityPage.stream()
                .map(item -> ocpcConverter
                        .toCoverOcpcBaiduDataVO(item
                                , item.getOcpcChannel()
                                , item.getUser()
                                , item.getOrderCharge()))
                .collect(Collectors.toList());
        return new PageImpl<>(ocpcBaiduDataVOList, ocpcBaiduDataEntityPage.getPageable(), ocpcBaiduDataEntityPage.getTotalElements());
    }

    @Override
    public Page<OcpcGoogleDataVO> queryGoogleData(GoogleDataQueryDTO googleDataQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(googleDataQueryDTO.getPage(), googleDataQueryDTO.getSize(), sort);
        Page<OcpcGoogleDataEntity> ocpcGoogleDataEntityPage = googleDataRepository.findAll((Specification<OcpcGoogleDataEntity>) (root, query, cb) -> {

            Join<OcpcGoogleDataEntity, OcpcChannelEntity> ocpcChannelJoin = root.join("ocpcChannel");
            Join<OcpcGoogleDataEntity, OrderChargeEntity> orderChargeJoin = root.join("orderCharge", JoinType.LEFT);
            List<Predicate> ps = new ArrayList<>();
            if (googleDataQueryDTO.getType() != null) {
                ps.add(cb.equal(root.get("type"), googleDataQueryDTO.getType()));
            }
            if (googleDataQueryDTO.getIp() != null) {
                ps.add(cb.equal(root.get("ip"), googleDataQueryDTO.getIp()));
            }
            if (googleDataQueryDTO.getStartTime() != null && googleDataQueryDTO.getEndTime() != null) {
                ps.add(cb.between(root.get("createTime"), googleDataQueryDTO.getStartTime(), googleDataQueryDTO.getEndTime()));
            }
            if (googleDataQueryDTO.getOcpcChannelId() != null) {
                ps.add(cb.equal(ocpcChannelJoin.get("id"), googleDataQueryDTO.getOcpcChannelId()));
            }
            if (googleDataQueryDTO.getOrderStatus() != null) {
                ps.add(cb.equal(orderChargeJoin.get("orderStatus"), googleDataQueryDTO.getOrderStatus()));
            }
            return ps.isEmpty()
                    ? null
                    : cb.and(ps.toArray(new Predicate[0]));
        }, pageable);
        List<OcpcGoogleDataVO> ocpcGoogleDataVOList = ocpcGoogleDataEntityPage.stream()
                .map(item -> {
                    OcpcGoogleDataVO vo = new OcpcGoogleDataVO();
                    vo.setOcpcGoogleData(item);
                    vo.setChannel(item.getOcpcChannel());
                    vo.setUser(item.getUser());
                    vo.setOrderCharge(item.getOrderCharge());
                    return vo;
                })
                .collect(Collectors.toList());
        return new PageImpl<>(ocpcGoogleDataVOList, ocpcGoogleDataEntityPage.getPageable(), ocpcGoogleDataEntityPage.getTotalElements());
    }

    @Override
    public Page<OcpcMetaDataVO> queryMetaData(MetaDataQueryDTO metaDataQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(metaDataQueryDTO.getPage(), metaDataQueryDTO.getSize(), sort);
        Page<OcpcMetaDataEntity> ocpcMetaDataEntityPage = metaDataRepository.findAll((Specification<OcpcMetaDataEntity>) (root, query, cb) -> {

            Join<OcpcMetaDataEntity, OcpcChannelEntity> ocpcChannelJoin = root.join("ocpcChannel");
            Join<OcpcMetaDataEntity, OrderChargeEntity> orderChargeJoin = root.join("orderCharge", JoinType.LEFT);
            List<Predicate> ps = new ArrayList<>();
            if (metaDataQueryDTO.getType() != null) {
                ps.add(cb.equal(root.get("type"), metaDataQueryDTO.getType()));
            }
            if (metaDataQueryDTO.getIp() != null) {
                ps.add(cb.equal(root.get("ip"), metaDataQueryDTO.getIp()));
            }
            if (metaDataQueryDTO.getStartTime() != null && metaDataQueryDTO.getEndTime() != null) {
                ps.add(cb.between(root.get("createTime"), metaDataQueryDTO.getStartTime(), metaDataQueryDTO.getEndTime()));
            }
            if (metaDataQueryDTO.getOcpcChannelId() != null) {
                ps.add(cb.equal(ocpcChannelJoin.get("id"), metaDataQueryDTO.getOcpcChannelId()));
            }
            if (metaDataQueryDTO.getOrderStatus() != null) {
                ps.add(cb.equal(orderChargeJoin.get("orderStatus"), metaDataQueryDTO.getOrderStatus()));
            }
            if (metaDataQueryDTO.getClickId() != null) {
                ps.add(cb.like(root.get("clickId"), "%" + metaDataQueryDTO.getClickId() + "%"));
            }
            if (metaDataQueryDTO.getCampaignId() != null) {
                ps.add(cb.like(root.get("campaignId"), "%" + metaDataQueryDTO.getCampaignId() + "%"));
            }
            if (metaDataQueryDTO.getAdsetId() != null) {
                ps.add(cb.like(root.get("adsetId"), "%" + metaDataQueryDTO.getAdsetId() + "%"));
            }
            if (metaDataQueryDTO.getAdId() != null) {
                ps.add(cb.like(root.get("adId"), "%" + metaDataQueryDTO.getAdId() + "%"));
            }
            return ps.isEmpty()
                    ? null
                    : cb.and(ps.toArray(new Predicate[0]));
        }, pageable);
        List<OcpcMetaDataVO> ocpcMetaDataVOList = ocpcMetaDataEntityPage.stream()
                .map(item -> {
                    OcpcMetaDataVO vo = new OcpcMetaDataVO();
                    vo.setOcpcMetaData(item);
                    vo.setChannel(item.getOcpcChannel());
                    vo.setUser(item.getUser());
                    vo.setOrderCharge(item.getOrderCharge());
                    return vo;
                })
                .collect(Collectors.toList());
        return new PageImpl<>(ocpcMetaDataVOList, ocpcMetaDataEntityPage.getPageable(), ocpcMetaDataEntityPage.getTotalElements());
    }
}
