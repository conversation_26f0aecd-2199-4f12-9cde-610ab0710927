package com.steamgo1.csgoskinadmin.service.impl;

import com.steamgo1.csgoskinadmin.converter.ActivityConverter;
import com.steamgo1.csgoskinadmin.dto.*;
import com.steamgo1.csgoskinadmin.schedule.producer.ProducerService;
import com.steamgo1.csgoskinadmin.service.ActivityService;
import com.steamgo1.csgoskinadmin.vo.CardCollectVO;
import com.steamgo1.csgoskincommon.dao.*;
import com.steamgo1.csgoskincommon.entity.*;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@Service
public class ActivityServiceImpl implements ActivityService {
    @Autowired
    private ActivityConverter activityConverter;

    @Autowired
    private CardCollectRepository cardCollectRepository;

    @Autowired
    private CardCollectCardRepository cardCollectCardRepository;

    @Autowired
    private ProducerService producerService;

    @Autowired
    private ExtraBonuRepository extraBonuRepository;

    @Autowired
    private ExtraBonusUserRepository extraBonusUserRepository;

    @Autowired
    private UserRedPacketRecordRepository userRedPacketRecordRepository;


    @Override
    @Transactional
    public void addCardColllect(CardCollectAddDTO cardCollectAddDTO) {
        BigDecimal probability = cardCollectAddDTO.getCardList().stream().map(item -> item.getProbability()).reduce(BigDecimal.ZERO, (p, q) -> p.add(q));
        if (probability.compareTo(BigDecimal.valueOf(1)) != 0) {
            // todo 国际化
            // throw new CsgoSkinException("箱子概率不等于1");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.card.collect.probability.not.one"));
        }
        CardCollectEntity cardCollectEntity = activityConverter.toCardCollectEntity(cardCollectAddDTO);
        List<CardCollectCardEntity> cardCollectCardEntityList = activityConverter.toCardCollectCardEntity(cardCollectAddDTO.getCardList());
        cardCollectRepository.save(cardCollectEntity);
        for (CardCollectCardEntity cardCollectCardEntity : cardCollectCardEntityList) {
            cardCollectCardEntity.setCardCollect(cardCollectEntity);
            cardCollectCardRepository.save(cardCollectCardEntity);
        }
        // 添加延时任务
        producerService.addCardCollectDelayedTask(cardCollectEntity.getId());
    }

    @Override
    public void updateCardColllect(CardCollectUpdateDTO cardCollectUpdateDTO) {
        BigDecimal probability = cardCollectUpdateDTO.getCardList().stream().map(item -> item.getProbability()).reduce(BigDecimal.ZERO, (p, q) -> p.add(q));
        if (probability.compareTo(BigDecimal.valueOf(1)) != 0) {
            // todo 国际化
            // throw new CsgoSkinException("箱子概率不等于1");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.card.collect.probability.not.one"));
        }
        CardCollectEntity cardCollectEntity = activityConverter.toCardCollectEntity(cardCollectUpdateDTO);
        List<CardCollectCardEntity> cardCollectCardEntityList = activityConverter.toCardCollectCardEntityOfUpdate(cardCollectUpdateDTO.getCardList());
        cardCollectEntity = cardCollectRepository.save(cardCollectEntity);
        for (CardCollectCardEntity cardCollectCardEntity : cardCollectCardEntityList) {
            cardCollectCardEntity.setCardCollect(cardCollectEntity);
            cardCollectCardRepository.save(cardCollectCardEntity);
        }
        // 添加延时任务
        producerService.addCardCollectDelayedTask(cardCollectEntity.getId());
    }

    @Override
    public Page<CardCollectVO> queryCardCollect(CardCollectQueryDTO cardCollectQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(cardCollectQueryDTO.getPage(), cardCollectQueryDTO.getSize(), sort);
        Page<CardCollectEntity> cardCollectEntityPage = cardCollectRepository.findAll(pageable);
        List<CardCollectVO> cardCollectCardEntityList = cardCollectEntityPage.getContent().stream().map(item -> activityConverter.toCardCollectVO(item, cardCollectCardRepository.findByCardCollect(item))).collect(Collectors.toList());
        return new PageImpl<>(cardCollectCardEntityList, cardCollectEntityPage.getPageable(), cardCollectEntityPage.getTotalElements());
    }

    @Override
    public List<ExtraBonusEntity> queryExtraBonusEntity() {
        return extraBonuRepository.findAll();
    }

    @Override
    public Page<ExtraBonusUserEntity> queryExtraBonusUserEntity(QueryExtraBonusUserParamDTO queryExtraBonusUserParamDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "id");
        Pageable pageable = PageRequest.of(queryExtraBonusUserParamDTO.getPage(), queryExtraBonusUserParamDTO.getSize(), sort);
        Page<ExtraBonusUserEntity> extraBonusUserEntityPage = extraBonusUserRepository.findAll((Specification<ExtraBonusUserEntity>) (root, query, cb) -> {
            Join<ExtraBonusUserEntity, UserEntity> join = root.join("user", JoinType.LEFT);
            List<Predicate> ps = new ArrayList<>();
            if (!StringUtils.isEmpty(queryExtraBonusUserParamDTO.getUserId())) {
                ps.add(cb.equal(root.get("user"), queryExtraBonusUserParamDTO.getUserId()));
            }
            if (!StringUtils.isEmpty(queryExtraBonusUserParamDTO.getPhone())) {
                ps.add(cb.like(join.get("phone"), "%" + queryExtraBonusUserParamDTO.getPhone() + "%"));
            }
            return query.where(ps.toArray(new Predicate[ps.size()])).getRestriction();
        }, pageable);
        return extraBonusUserEntityPage;
    }

    @Override
    public void changeExtraBonusUserStatus(Long id) {
        ExtraBonusUserEntity extraBonusUserEntity = extraBonusUserRepository.findById(id).orElse(null);
        if (extraBonusUserEntity != null) {
            extraBonusUserEntity.setIsReceived(!extraBonusUserEntity.getIsReceived());
            extraBonusUserRepository.save(extraBonusUserEntity);
        }
    }

    @Override
    public void changeExtraBonusStatus(Long id, Integer type) {
        ExtraBonusEntity extraBonusEntity = extraBonuRepository.findById(id).orElse(null);
        if (extraBonusEntity != null) {
            switch (type) {
                case 0:
                    extraBonusEntity.setIsActivate(!extraBonusEntity.getIsActivate());
                    break;
                case 1:
                    extraBonusEntity.setEnableOpenCase(!extraBonusEntity.getEnableOpenCase());
                    break;
                case 2:
                    extraBonusEntity.setEnablePercentage(!extraBonusEntity.getEnablePercentage());
            }
            extraBonuRepository.save(extraBonusEntity);
        }
    }

    @Override
    public Page<UserRedPacketRecordEntity> queryRedPacketRecord(RedPacketRecordQueryDTO redPacketRecordQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(redPacketRecordQueryDTO.getPage(), redPacketRecordQueryDTO.getSize(), sort);
        return userRedPacketRecordRepository.findByRedPacketId(redPacketRecordQueryDTO.getRedPacketId(), pageable);
    }
}
