package com.steamgo1.csgoskinadmin.service;

import com.steamgo1.csgoskinadmin.dto.*;
import com.steamgo1.csgoskinadmin.vo.CardCollectVO;
import com.steamgo1.csgoskincommon.entity.ExtraBonusEntity;
import com.steamgo1.csgoskincommon.entity.ExtraBonusUserEntity;
import com.steamgo1.csgoskincommon.entity.UserRedPacketRecordEntity;
import org.springframework.data.domain.Page;

import java.util.List;

public interface ActivityService {
    void addCardColllect(CardCollectAddDTO cardCollectAddDTO);

    void updateCardColllect(CardCollectUpdateDTO cardCollectUpdateDTO);

    Page<CardCollectVO> queryCardCollect(CardCollectQueryDTO cardCollectQueryDTO);

    List<ExtraBonusEntity> queryExtraBonusEntity();

    Page<ExtraBonusUserEntity> queryExtraBonusUserEntity(QueryExtraBonusUserParamDTO queryExtraBonusUserParamDTO);

    void changeExtraBonusUserStatus(Long id);

    void changeExtraBonusStatus(Long id, Integer type);

    Page<UserRedPacketRecordEntity> queryRedPacketRecord(RedPacketRecordQueryDTO redPacketRecordQueryDTO);
}
