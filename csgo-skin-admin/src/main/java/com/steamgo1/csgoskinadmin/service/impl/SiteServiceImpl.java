package com.steamgo1.csgoskinadmin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinadmin.converter.DataDictionaryConverter;
import com.steamgo1.csgoskinadmin.dto.*;
import com.steamgo1.csgoskinadmin.service.SiteService;
import com.steamgo1.csgoskinadmin.vo.UserDisableQueryParamVO;
import com.steamgo1.csgoskincommon.converter.SiteConverter;
import com.steamgo1.csgoskincommon.dao.AnnouncementRepository;
import com.steamgo1.csgoskincommon.dao.DataDictionaryRepository;
import com.steamgo1.csgoskincommon.entity.AnnouncementEntity;
import com.steamgo1.csgoskincommon.entity.DataDictionaryEntity;
import com.steamgo1.csgoskincommon.entity.enums.UserDisableType;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageMethod;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageType;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import com.steamgo1.csgoskincommon.vo.AnnouncementVO;
import com.steamgo1.csgoskincommon.vo.websocket.MessageVO;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SiteServiceImpl implements SiteService {
    @Value("${spring.redis.prefix.data-dictionary}")
    private String redisDictionaryPrefix;
    @Value("${rabbitmq.exchange.csgo}")
    private String exchageName;
    @Autowired
    private DataDictionaryRepository dataDictionaryRepository;
    @Autowired
    private DataDictionaryConverter dataDictionaryConverter;

    @Autowired
    private AnnouncementRepository announcementRepository;

    @Autowired
    private SiteConverter siteConverter;

    @Autowired
    private RabbitTemplate rabbitTemplate;


    @Override
    public Page<DataDictionaryEntity> queryDataDictionary(DataDictionaryQueryDTO dataDictionaryQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(dataDictionaryQueryDTO.getPage(), dataDictionaryQueryDTO.getSize(), sort);
        return dataDictionaryRepository.findByIsDeletedIsFalse(pageable);
    }

    @Override
    public DataDictionaryEntity addDataDicationary(DataDictionaryDTO dataDictionaryDTO) {
        if (dataDictionaryDTO.getParentId() != null && dataDictionaryRepository.existsByIdAndIsDeleted(dataDictionaryDTO.getParentId(), false) == false) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.parent.field.not.exist.or.deleted"));
        }
        DataDictionaryEntity dataDictionaryEntity = dataDictionaryConverter.toDataDictionaryEntity(dataDictionaryDTO);
        return dataDictionaryRepository.save(dataDictionaryEntity);
    }

    @Override
    public DataDictionaryEntity updateDataDicationary(DataDictionaryUpdateDTO dataDictionaryUpdateDTO) {
        String reidsKey = redisDictionaryPrefix + ":" + dataDictionaryUpdateDTO.getId();
        RedisUtils.delete(reidsKey);
        if (dataDictionaryUpdateDTO.getId() != null && dataDictionaryRepository.existsByIdAndIsDeleted(dataDictionaryUpdateDTO.getId(), false) == false) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.field.not.exist.or.deleted"));
        }
        DataDictionaryEntity dataDictionaryEntity = dataDictionaryConverter.toDataDictionaryEntity(dataDictionaryUpdateDTO);
        if (dataDictionaryUpdateDTO.getParentId() != null && dataDictionaryRepository.existsByIdAndIsDeleted(dataDictionaryUpdateDTO.getParentId(), false) == false) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.parent.field.not.exist.or.deleted"));
        }
        return dataDictionaryRepository.save(dataDictionaryEntity);
    }

    @Override
    public void delDataDicationary(IdsDTO idsDTO) {
        for (int i = 0; i < idsDTO.getIds().size(); i++) {
            String reidsKey = redisDictionaryPrefix + ":" + idsDTO.getIds().get(i);
            RedisUtils.delete(reidsKey);
        }
        List<DataDictionaryEntity> dataDictionaryEntitys = dataDictionaryRepository.findAllByIdIn(idsDTO.getIds());
        for (DataDictionaryEntity dataDictionaryEntity : dataDictionaryEntitys) {
            dataDictionaryEntity.setIsDeleted(true);
            dataDictionaryRepository.save(dataDictionaryEntity);
        }
    }

    @Override
    public UserDisableQueryParamVO queryUserDisableQUeryParam() {
        return new UserDisableQueryParamVO() {{
            setTypeList(UserDisableType.toList());
        }};
    }

    @Override
    public AnnouncementEntity addAnnouncement(AnnouncementAddDTO announcementAddDTO) {
        AnnouncementEntity announcementEntity = new AnnouncementEntity();
        announcementEntity.setContent(announcementAddDTO.getContent());
        announcementEntity.setGrade(announcementAddDTO.getGrade());
        announcementEntity.setIsShow(announcementAddDTO.getIsShow());
        announcementEntity = announcementRepository.save(announcementEntity);
        // 放入websocket通知队列,通知客户端
        List<AnnouncementVO> announcementVOList = siteConverter.toAnnouncementVOList(announcementRepository.findByIsShowIsTrue());
        MessageVO messageVO = new MessageVO(WebSocketMessageMethod.SEND_ALL, WebSocketMessageType.ANNOUNCEMENT, announcementVOList);
        rabbitTemplate.convertAndSend(exchageName, "websocket.battleHomeJoin", JSONObject.toJSONString(messageVO));
        return announcementEntity;
    }

    @Override
    public List<AnnouncementEntity> queryAnnouncement() {
        return announcementRepository.findAll();
    }

    @Override
    public void onlineAnnouncement(Long id) {
        AnnouncementEntity announcementEntity = announcementRepository.findById(id).orElse(null);
        if (announcementEntity != null) {
            announcementEntity.setIsShow(true);
            announcementRepository.save(announcementEntity);
            // 放入websocket通知队列,通知客户端
            List<AnnouncementVO> announcementVOList = siteConverter.toAnnouncementVOList(announcementRepository.findByIsShowIsTrue());
            MessageVO messageVO = new MessageVO(WebSocketMessageMethod.SEND_ALL, WebSocketMessageType.ANNOUNCEMENT, announcementVOList);
            rabbitTemplate.convertAndSend(exchageName, "websocket.battleHomeJoin", JSONObject.toJSONString(messageVO));
        }
    }

    @Override
    public void unOnlineAnnouncement(Long id) {
        AnnouncementEntity announcementEntity = announcementRepository.findById(id).orElse(null);
        if (announcementEntity != null) {
            announcementEntity.setIsShow(false);
            announcementRepository.save(announcementEntity);
            // 放入websocket通知队列,通知客户端
            List<AnnouncementVO> announcementVOList = siteConverter.toAnnouncementVOList(announcementRepository.findByIsShowIsTrue());
            MessageVO messageVO = new MessageVO(WebSocketMessageMethod.SEND_ALL, WebSocketMessageType.ANNOUNCEMENT, announcementVOList);
            rabbitTemplate.convertAndSend(exchageName, "websocket.battleHomeJoin", JSONObject.toJSONString(messageVO));
        }
    }
}
