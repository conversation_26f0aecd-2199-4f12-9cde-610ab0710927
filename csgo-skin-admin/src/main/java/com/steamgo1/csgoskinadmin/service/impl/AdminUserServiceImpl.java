package com.steamgo1.csgoskinadmin.service.impl;

import com.steamgo1.csgoskinadmin.config.jwt.JwtProvider;
import com.steamgo1.csgoskinadmin.dto.AdminUserDTO;
import com.steamgo1.csgoskinadmin.dto.AuthenticationDTO;
import com.steamgo1.csgoskinadmin.service.AdminUserService;
import com.steamgo1.csgoskinadmin.utils.SecurityUtils;
import com.steamgo1.csgoskincommon.dao.AdminUserRepository;
import com.steamgo1.csgoskincommon.entity.AdminUserEntity;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import com.steamgo1.csgoskincommon.vo.TokenVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Objects;

@Slf4j
@Service
public class AdminUserServiceImpl implements AdminUserService {

    @Autowired
    private AdminUserRepository adminUserRepository;

    @Autowired
    private JwtProvider jwtProvider;

    @Override
    public TokenVO login(AuthenticationDTO authenticationDTO) {
        AdminUserEntity adminUser = adminUserRepository.findUserByUsername(authenticationDTO.getUsername());
        if (Objects.nonNull(adminUser) && SecurityUtils.passwordMatches(authenticationDTO.getPassword(), adminUser.getPassword())) {
            String token = jwtProvider.createToken(adminUser, null, null);
            return new TokenVO() {{
                setToken(token);
            }};
        }
        // todo 国际化
        // throw new CsgoSkinException("用户名或密码错误");
        throw new CsgoSkinException(I18nUtils.getMessage("exception.admin.login.invalid.credentials"));
    }

    @Override
    @PostConstruct
    public void createSupperuser() {
        if (adminUserRepository.findAllByIsSuperuser(true).isEmpty()) {
            AdminUserEntity adminUser = new AdminUserEntity();
            adminUser.setUsername("admin");
            adminUser.setPassword(SecurityUtils.passwordEncoder("admin2024"));
            adminUser.setIsSuperuser(true);
            adminUser.setIsDeleted(false);
            adminUserRepository.save(adminUser);
        }
        log.info("初始化超级管理员用户账号: sgadmin 密码: steamgocsgo2023");
    }

    @Override
    public AdminUserEntity createAdminUser(AdminUserDTO adminUserDTO) {
        AdminUserEntity adminUser = new AdminUserEntity();
        adminUser.setUsername(adminUserDTO.getUsername());
        adminUser.setPassword(SecurityUtils.passwordEncoder(adminUserDTO.getPassword()));
        adminUser.setPhone(adminUserDTO.getPhone());
        adminUser.setEmail(adminUserDTO.getEmail());
        return adminUserRepository.save(adminUser);
    }
}
