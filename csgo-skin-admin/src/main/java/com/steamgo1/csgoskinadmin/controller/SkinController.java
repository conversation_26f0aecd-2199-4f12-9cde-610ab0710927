package com.steamgo1.csgoskinadmin.controller;

import com.steamgo1.csgoskinadmin.dto.IdsDTO;
import com.steamgo1.csgoskinadmin.dto.SkinDTO;
import com.steamgo1.csgoskinadmin.dto.SkinQueryDTO;
import com.steamgo1.csgoskinadmin.service.SkinService;
import com.steamgo1.csgoskinadmin.vo.AsyncOperationVO;
import com.steamgo1.csgoskinadmin.vo.SkinQueryParamVO;
import com.steamgo1.csgoskincommon.dao.PrototypeCategoryRepository;
import com.steamgo1.csgoskincommon.dao.SkinRepository;
import com.steamgo1.csgoskincommon.entity.CaseSkinEntity;
import com.steamgo1.csgoskincommon.entity.SkinEntity;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RequestMapping("/skin")
@RestController
@RequiredArgsConstructor
@Api(tags = "饰品管理")
public class SkinController {
    @Autowired
    private SkinService skinService;

    @Autowired
    private SkinRepository skinRepository;

    @Autowired
    private PrototypeCategoryRepository prototypeCategoryRepository;


    @ApiOperation("获取饰品查询条件")
    @GetMapping("/filters")
    public Response<SkinQueryParamVO> skinQueryParamVOResponse() {
        return ResponseUtil.ok(skinService.skinQueryParam());
    }

    @ApiOperation("获取所有饰品")
    @GetMapping(value = "")
    public Response<Page<SkinEntity>> querySkins(SkinQueryDTO skinQueryDTO) {
        return ResponseUtil.ok(skinService.querySkins(skinQueryDTO));
    }

    @ApiOperation("获取单个饰品")
    @GetMapping(value = "/{id}")
    public Response<SkinEntity> querySkinById(@PathVariable("id") Long skinId) {
        return ResponseUtil.ok(skinService.querySkinsById(skinId));
    }

    @ApiOperation("添加饰品")
    @PostMapping(value = "")
    public Response<SkinEntity> addSkin(@Valid @RequestBody SkinDTO skinDTO) {
        return ResponseUtil.ok(skinService.addSkin(skinDTO));
    }

    @ApiOperation("删除饰品")
    @DeleteMapping(value = "/{id}")
    public Response dellSkin(@PathVariable("id") Long skinId) {
        skinService.delSkin(skinId);
        return ResponseUtil.ok();
    }


    @ApiOperation("同步所有饰品信息")
    @GetMapping(value = "/zbt/sync")
    public Response<AsyncOperationVO> SyncZbtALL() {
        return ResponseUtil.ok(skinService.syncAllSkin());
    }

    @ApiOperation("上传所有饰品图片到storage")
    @GetMapping(value = "/zbt/uploadSkinPictureToStorage")
    public Response<AsyncOperationVO> uploadSkinPictureToStorage() {
        skinService.uploadSkinPictureToStorage();
        return ResponseUtil.ok();
    }

    @ApiOperation("数据清洗")
    @GetMapping(value = "/clean")
    public Response cleanSkin() {
        skinService.checkSkin();
        return ResponseUtil.ok();
    }

    @ApiOperation("/锁定价格")
    @PostMapping("/lockPrice")
    public Response lockPriceSkin(IdsDTO idsDTO) {
        skinService.lockPrice(idsDTO);
        return ResponseUtil.ok();
    }

    @ApiOperation("/移除锁定价格")
    @PostMapping("/lockPrice/remove")
    public Response removeLockPrice(IdsDTO idsDTO) {
        skinService.unLockPrice(idsDTO);
        return ResponseUtil.ok();
    }

    @ApiOperation("/标记异常")
    @PostMapping("/abnormal")
    public Response addAbnormalIdsDTO(IdsDTO idsDTO) {
        skinService.addAbnormal(idsDTO);
        return ResponseUtil.ok();
    }

    @ApiOperation("/移除追梦随机饰品")
    @PostMapping("/abnormal/remove")
    public Response removeAbnormalIdsDTO(IdsDTO idsDTO) {
        skinService.removeAbnormal(idsDTO);
        return ResponseUtil.ok();
    }

    @ApiOperation("/查询饰品关联的箱子")
    @GetMapping("/case/{id}")
    public Response<List<CaseSkinEntity>> querySkinCase(@PathVariable Long id) {
        return ResponseUtil.ok(skinService.querySkinCase(id));
    }

    @ApiOperation("加入在售")
    @PostMapping("/sale")
    public Response addSaleSkin(IdsDTO idsDTO) {
        skinService.addSale(idsDTO);
        return ResponseUtil.ok();
    }

    @ApiOperation("/删除在售")
    @PostMapping("/sale/remove")
    public Response removeSale(IdsDTO idsDTO) {
        skinService.removeSale(idsDTO);
        return ResponseUtil.ok();
    }

}
