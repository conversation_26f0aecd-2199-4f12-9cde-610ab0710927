package com.steamgo1.csgoskinadmin.controller;

import cn.hutool.core.collection.ListUtil;
import com.steamgo1.csgoskinadmin.converter.I18nDemoConverter;
import com.steamgo1.csgoskinadmin.dto.I18nDemoDTO;
import com.steamgo1.csgoskinadmin.vo.I18nDemoVO;
import com.steamgo1.csgoskincommon.entity.I18nDemoEntity;
import com.steamgo1.csgoskincommon.enums.LanguageEnum;
import com.steamgo1.csgoskincommon.service.I18nDemoService;
import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.utils.TranslationExample;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 国际化演示控制器
 * 简化的CRUD操作，移除定制化代码
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/i18n-demo")
@Api(tags = "国际化演示管理")
public class I18nDemoController {

    @Autowired
    private I18nDemoService i18nDemoService;

    @Autowired
    private I18nDemoConverter i18nDemoConverter;

    @Autowired
    private TranslationExample translationExample;

    /**
     * 获取演示数据列表
     */
    @GetMapping
    @ApiOperation("获取演示数据列表")
    public Response<List<I18nDemoVO>> getList() {

        translationExample.runAllExamples();
        List<I18nDemoEntity> entities = i18nDemoService.findAll();
        List<I18nDemoVO> vos = i18nDemoConverter.toI18nDemoVOList(entities);
        return ResponseUtil.ok(vos);
    }

    /**
     * 分页查询
     */
    @GetMapping("/page")
    @ApiOperation("分页获取演示数据")
    public Response<Page<I18nDemoVO>> getPage(@RequestParam(defaultValue = "0") int page,
                                                  @RequestParam(defaultValue = "20") int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createTime"));
        Page<I18nDemoEntity> entityPage = i18nDemoService.findAll(pageable);
        Page<I18nDemoVO> voPage = entityPage.map(i18nDemoConverter::toI18nDemoVO);
        return ResponseUtil.ok(voPage);
    }

    /**
     * 根据ID获取详情
     */
    @GetMapping("/{id}")
    @ApiOperation("获取演示数据详情")
    public Response<I18nDemoVO> getById(@PathVariable Long id) {
        I18nDemoEntity entity = i18nDemoService.findById(id);
        if (entity == null) {
            return ResponseUtil.fail(I18nUtils.getMessage("response.exception.resource.not.found"));
        }
        I18nDemoVO vo = i18nDemoConverter.toI18nDemoVO(entity);
        return ResponseUtil.ok(vo);
    }

    /**
     * 创建演示数据
     */
    @PostMapping
    @ApiOperation("创建演示数据")
    public Response<I18nDemoVO> create(@Valid @RequestBody I18nDemoDTO dto) {
        I18nDemoEntity entity = i18nDemoConverter.fromDTO(dto);
        I18nDemoEntity saved = i18nDemoService.save(entity);
        I18nDemoVO vo = i18nDemoConverter.toI18nDemoVO(saved);
        return ResponseUtil.ok(vo);
    }

    /**
     * 更新演示数据
     */
    @PutMapping("/{id}")
    @ApiOperation("更新演示数据")
    public Response<I18nDemoVO> update(@PathVariable Long id, @Valid @RequestBody I18nDemoDTO dto) {
        I18nDemoEntity existingEntity = i18nDemoService.findById(id);
        if (existingEntity == null) {
            return ResponseUtil.fail(I18nUtils.getMessage("response.exception.resource.not.found"));
        }

        dto.setId(id);
        I18nDemoEntity entity = i18nDemoConverter.fromDTO(dto);
        I18nDemoEntity saved = i18nDemoService.save(entity);
        I18nDemoVO vo = i18nDemoConverter.toI18nDemoVO(saved);
        return ResponseUtil.ok(vo);
    }

    /**
     * 删除演示数据
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除演示数据")
    public Response<String> delete(@PathVariable Long id) {
        I18nDemoEntity entity = i18nDemoService.findById(id);
        if (entity == null) {
            return ResponseUtil.fail(I18nUtils.getMessage("response.exception.resource.not.found"));
        }

        i18nDemoService.deleteById(id);
        return ResponseUtil.ok(I18nUtils.getMessage("response.delete.success"));
    }

    /**
     * 批量创建示例数据
     */
    @PostMapping("/batch-create-samples")
    @ApiOperation("批量创建示例数据")
    public Response<String> batchCreateSamples() {
        // 创建示例数据

        List<Map<LanguageEnum, String>> sampleDataList =
                ListUtil.toList(
                        new HashMap<LanguageEnum, String>() {{
                            put(LanguageEnum.CHINESE, "欢迎使用系统");
                            put(LanguageEnum.ENGLISH, "Welcome to the system");
                        }},
                        new HashMap<LanguageEnum, String>() {{
                            put(LanguageEnum.CHINESE, "用户登录成功");
                            put(LanguageEnum.ENGLISH, "User login successful");
                        }},
                        new HashMap<LanguageEnum, String>() {{
                            put(LanguageEnum.CHINESE, "数据保存完成");
                            put(LanguageEnum.ENGLISH, "Data saved successfully");
                        }}
                );

        for (Map<LanguageEnum, String> data : sampleDataList) {
            I18nDemoEntity entity = new I18nDemoEntity();
            entity.setI18nFieldText(I18nField.fromLanguageMap(data));
            i18nDemoService.save(entity);
        }

        return ResponseUtil.ok(I18nUtils.getMessage("response.create.success"));
    }

    /**
     * 测试Jackson序列化/反序列化
     */
    @PostMapping("/test-jackson-serialization")
    @ApiOperation("测试Jackson序列化/反序列化")
    public Response<Object> testJacksonSerialization() {
        try {
            // 创建测试数据
            I18nDemoEntity entity = new I18nDemoEntity();

            // 方式1：使用LanguageEnum Map
            java.util.Map<LanguageEnum, String> languageMap = new java.util.HashMap<>();
            languageMap.put(LanguageEnum.CHINESE, "测试中文内容");
            languageMap.put(LanguageEnum.ENGLISH, "Test English content");

            I18nField field1 = new I18nField(languageMap);
            entity.setI18nFieldText(field1);

            // 保存到数据库
            entity = i18nDemoService.save(entity);

            // 从数据库读取
            I18nDemoEntity savedEntity = i18nDemoService.findById(entity.getId());

            // 测试各种获取方法
            Object result = new Object() {
                public final Long entityId = savedEntity.getId();
                public final String currentText = savedEntity.getText();
                public final java.util.Map<String, String> allLanguages = savedEntity.getI18nFieldText().getAllLanguages();
                public final java.util.Map<LanguageEnum, String> allByEnum = savedEntity.getI18nFieldText().getTranslations();
                public final String testStatus = "SUCCESS";
                public final String message = "Jackson序列化/反序列化测试成功";
            };

            return ResponseUtil.ok(result);

        } catch (Exception e) {
            Object errorResult = new Object() {
                public final String testStatus = "FAILED";
                public final String message = "Jackson序列化/反序列化测试失败";
                public final String error = e.getMessage();
                public final String stackTrace = java.util.Arrays.toString(e.getStackTrace());
            };

            return ResponseUtil.fail("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试MapStruct转换功能
     */
    @GetMapping("/test-mapstruct/{id}")
    @ApiOperation("测试MapStruct转换功能")
    public Response<Object> testMapStructConversion(@PathVariable Long id) {
        try {
            I18nDemoEntity entity = i18nDemoService.findById(id);
            if (entity == null) {
                return ResponseUtil.fail("实体不存在");
            }

            Object result = new Object() {
                public final Long entityId = id;
                public final String entityText = entity.getText();
                public final Object simpleVO = i18nDemoConverter.toI18nDemoVO(entity);
                public final Object dto = i18nDemoConverter.toDTO(entity);
                public final String testStatus = "SUCCESS";
                public final String message = "MapStruct转换测试成功";
            };

            return ResponseUtil.ok(result);

        } catch (Exception e) {
            Object errorResult = new Object() {
                public final String testStatus = "FAILED";
                public final String message = "MapStruct转换测试失败";
                public final String error = e.getMessage();
            };

            return ResponseUtil.fail("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试MapStruct更新功能
     */
    @PutMapping("/test-mapstruct-update/{id}")
    @ApiOperation("测试MapStruct更新功能")
    public Response<Object> testMapStructUpdate(@PathVariable Long id, @RequestBody I18nDemoDTO dto) {
        try {
            I18nDemoEntity entity = i18nDemoService.findById(id);
            if (entity == null) {
                return ResponseUtil.fail("实体不存在");
            }

            // 保存原始数据
            I18nDemoVO originalVO = i18nDemoConverter.toI18nDemoVO(entity);

            // 使用MapStruct更新实体
            i18nDemoConverter.updateEntityFromDTO(dto, entity);

            // 保存更新后的实体
            I18nDemoEntity updatedEntity = i18nDemoService.save(entity);
            I18nDemoVO updatedVO = i18nDemoConverter.toI18nDemoVO(updatedEntity);

            Object result = new Object() {
                public final Long entityId = id;
                public final Object originalData = originalVO;
                public final Object updateData = dto;
                public final Object updatedData = updatedVO;
                public final String testStatus = "SUCCESS";
                public final String message = "MapStruct更新测试成功";
            };

            return ResponseUtil.ok(result);

        } catch (Exception e) {
            Object errorResult = new Object() {
                public final String testStatus = "FAILED";
                public final String message = "MapStruct更新测试失败";
                public final String error = e.getMessage();
            };

            return ResponseUtil.fail("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试MapStruct批量转换
     */
    @GetMapping("/test-mapstruct-batch")
    @ApiOperation("测试MapStruct批量转换")
    public Response<Object> testMapStructBatch(@RequestParam(defaultValue = "5") int limit) {
        try {
            // 获取前N条数据
            Pageable pageable = PageRequest.of(0, limit);
            Page<I18nDemoEntity> entityPage = i18nDemoService.findAll(pageable);
            List<I18nDemoEntity> entities = entityPage.getContent();

            // 批量转换
            List<I18nDemoVO> vos = i18nDemoConverter.toI18nDemoVOList(entities);
            List<I18nDemoDTO> dtos = i18nDemoConverter.toDTOList(entities);

            Object result = new Object() {
                public final int entityCount = entities.size();
                public final int voCount = vos.size();
                public final int dtoCount = dtos.size();
                public final List<Object> sampleVOs = vos.stream().limit(3).collect(java.util.stream.Collectors.toList());
                public final String testStatus = "SUCCESS";
                public final String message = "MapStruct批量转换测试成功";
            };

            return ResponseUtil.ok(result);

        } catch (Exception e) {
            Object errorResult = new Object() {
                public final String testStatus = "FAILED";
                public final String message = "MapStruct批量转换测试失败";
                public final String error = e.getMessage();
            };

            return ResponseUtil.fail("测试失败: " + e.getMessage());
        }
    }

    /**
     * 简单搜索（当前语言）

    @GetMapping("/search/simple")
    @ApiOperation("简单关键词搜索（当前语言）")
    public Response<List<I18nDemoVO>> simpleSearch(@RequestParam String keyword) {
        List<I18nDemoEntity> entities = i18nDemoService.searchByCurrentLanguage("i18nFieldText", keyword);
        List<I18nDemoVO> vos = i18nDemoConverter.toI18nDemoVOList(entities);
        return ResponseUtil.ok(vos);
    }*/

    /**
     * 指定语言搜索

    @GetMapping("/search/by-language")
    @ApiOperation("指定语言搜索")
    public Response<List<I18nDemoVO>> searchByLanguage(@RequestParam String keyword,
                                                       @RequestParam  String language) {
        List<I18nDemoEntity> entities = i18nDemoService.searchByLanguage("i18nFieldText",keyword, LanguageEnum.fromCode(language));
        List<I18nDemoVO> vos = i18nDemoConverter.toI18nDemoVOList(entities);
        return ResponseUtil.ok(vos);
    }*/

    /**
     * 分页搜索

    @GetMapping("/search/page")
    @ApiOperation("分页搜索")
    public Response<Page<I18nDemoVO>> searchWithPagination(@RequestParam String keyword,
                                                           @RequestParam(defaultValue = "0") int page,
                                                           @RequestParam(defaultValue = "20") int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createTime"));
        Page<I18nDemoEntity> entityPage = i18nDemoService.searchWithPagination("i18nFieldText",keyword, pageable);
        Page<I18nDemoVO> voPage = entityPage.map(i18nDemoConverter::toI18nDemoVO);
        return ResponseUtil.ok(voPage);
    }*/
}
