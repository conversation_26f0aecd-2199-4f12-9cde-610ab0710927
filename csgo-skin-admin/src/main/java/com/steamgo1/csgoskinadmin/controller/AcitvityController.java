package com.steamgo1.csgoskinadmin.controller;


import com.steamgo1.csgoskinadmin.dto.*;
import com.steamgo1.csgoskinadmin.service.ActivityService;
import com.steamgo1.csgoskinadmin.service.RedPacketService;
import com.steamgo1.csgoskinadmin.vo.CardCollectVO;
import com.steamgo1.csgoskinadmin.vo.RedPacketQueryParamVO;
import com.steamgo1.csgoskinadmin.vo.RedPacketVO;
import com.steamgo1.csgoskincommon.entity.ExtraBonusEntity;
import com.steamgo1.csgoskincommon.entity.ExtraBonusUserEntity;
import com.steamgo1.csgoskincommon.entity.RedPacketEntity;
import com.steamgo1.csgoskincommon.entity.UserRedPacketRecordEntity;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RequestMapping("/activity")
@RestController
@Api(tags = "活动管理")
public class AcitvityController {

    @Autowired
    private RedPacketService redPacketService;

    @Autowired
    private ActivityService activityService;


    @GetMapping("/redpacket/filters")
    @ApiOperation("获取红包查询条件")
    public Response<RedPacketQueryParamVO> getRedPacketQueryParamVO() {
        return ResponseUtil.ok(redPacketService.getRedPacketQueryParam());
    }

    @GetMapping("/redpacket")
    @ApiOperation("查询口令红包")
    public Response<Page<RedPacketVO>> queryRedPacketQueryParamVO(RedPacketQueryDTO redPacketQueryDTO) {
        return ResponseUtil.ok(redPacketService.queryRedPackets(redPacketQueryDTO));
    }

    @PostMapping("/redpacket")
    @ApiOperation("添加口令红包")
    public Response<RedPacketEntity> addRedPacketQueryParamVO(@RequestBody RedPacketAddParamDTO redPacketAddParamDTO) {
        return ResponseUtil.ok(redPacketService.addRedPacketQueryParam(redPacketAddParamDTO));
    }

    @PostMapping("/cardCollect")
    @ApiOperation("添加集卡活动")
    public Response addCardCollect(@RequestBody CardCollectAddDTO cardCollectAddDTO) {
        activityService.addCardColllect(cardCollectAddDTO);
        return ResponseUtil.ok();
    }

    @PutMapping("/cardCollect")
    @ApiOperation("更新集卡活动")
    public Response updateCardCollect(@RequestBody CardCollectUpdateDTO cardCollectUpdateDTO) {
        activityService.updateCardColllect(cardCollectUpdateDTO);
        return ResponseUtil.ok();
    }

    @GetMapping("/cardCollect")
    @ApiOperation("查询集卡活动")
    public Response<Page<CardCollectVO>> queryCardCollect(CardCollectQueryDTO cardCollectQueryDTO) {
        return ResponseUtil.ok(activityService.queryCardCollect(cardCollectQueryDTO));
    }

    @GetMapping("/extraBonus")
    @ApiOperation("查询出金")
    public Response<List<ExtraBonusEntity>> queryExtraBonus() {
        return ResponseUtil.ok(activityService.queryExtraBonusEntity());
    }

    @GetMapping("/extraBonusUser")
    @ApiOperation("查询出金记录")
    public Response<Page<ExtraBonusUserEntity>> queryExtraBonusUser(QueryExtraBonusUserParamDTO queryExtraBonusUserParamDTO) {
        return ResponseUtil.ok(activityService.queryExtraBonusUserEntity(queryExtraBonusUserParamDTO));
    }

    @GetMapping("/extraBonusUser/changeStatus")
    @ApiOperation("修改出金记录领取状态")
    public Response<Page<ExtraBonusUserEntity>> updateExtraBonusUserStatus(Long id) {
        activityService.changeExtraBonusUserStatus(id);
        return ResponseUtil.ok();
    }

    @GetMapping("/extraBonus/changeStatus")
    @ApiOperation("修改出金活动状态")
    public Response<Page<ExtraBonusUserEntity>> changeExtraBonusUserStatus(ChangeExtraBonusUserStatusDTO changeExtraBonusUserStatusDTO) {
        activityService.changeExtraBonusStatus(changeExtraBonusUserStatusDTO.getId(), changeExtraBonusUserStatusDTO.getType());
        return ResponseUtil.ok();
    }

    @GetMapping("/redpacket/record")
    @ApiOperation("查询口令红包记录")
    public Response<Page<UserRedPacketRecordEntity>> queryRedPacketRecord(RedPacketRecordQueryDTO redPacketRecordQueryDTO) {
        return ResponseUtil.ok(activityService.queryRedPacketRecord(redPacketRecordQueryDTO));
    }
}
