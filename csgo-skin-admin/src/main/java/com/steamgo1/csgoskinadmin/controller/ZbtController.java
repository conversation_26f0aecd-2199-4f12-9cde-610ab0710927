package com.steamgo1.csgoskinadmin.controller;


import com.steamgo1.csgoskinadmin.service.SkinService;
import com.steamgo1.csgoskinadmin.vo.AsyncOperationVO;
import com.steamgo1.csgoskincommon.entity.ZbtSyncRecordEntity;
import com.steamgo1.csgoskincommon.entity.enums.ZbtSyncStatus;
import com.steamgo1.csgoskincommon.entity.enums.ZbtSyncType;
import com.steamgo1.csgoskincommon.service.ZBTService;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.utils.Utils;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequestMapping("/zbt")
@RestController
@Api(tags = "扎比特")
public class ZbtController {

    @Autowired
    private ZBTService zbtService;


    @Autowired
    private SkinService skinService;

    @ApiOperation("同步扎比特饰品查询参数数据")
    @GetMapping("/syncFilterParam")
    public Response syncFilterParam() {
        zbtService.SyncFilterParam();
        return ResponseUtil.ok();
    }

    @ApiOperation("同步扎比特饰品数据")
    @GetMapping("/syncSkinInfo")
    public Response<AsyncOperationVO> syncSkinInfo() {
        return ResponseUtil.ok(skinService.syncAllSkin());
    }
}
