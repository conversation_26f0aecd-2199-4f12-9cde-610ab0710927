package com.steamgo1.csgoskinadmin.controller;


import com.steamgo1.csgoskinadmin.dto.ReportDTO;
import com.steamgo1.csgoskinadmin.service.IndexService;
import com.steamgo1.csgoskinadmin.vo.ReportForms1VO;
import com.steamgo1.csgoskinadmin.vo.StatisticsVO;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;


@Slf4j
@RequestMapping("/index")
@RestController
@RequiredArgsConstructor
@Api(tags = "首页统计")
public class IndexController {
    @Autowired
    private IndexService indexService;


    @ApiOperation("首页统计")
    @GetMapping(value = "/statistics")
    public Response<StatisticsVO> staitistics() {
        return ResponseUtil.ok(indexService.queryStatistics());
    }


    @ApiOperation("报表1")
    @GetMapping(value = "/reportforms1/one")
    public Response<List<ReportForms1VO>> reportforms1(@RequestParam(name = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        return ResponseUtil.ok(indexService.queryReportForms1(date));
    }

    @ApiOperation("报表1")
    @GetMapping(value = "/reportforms1")
    public Response<List<ReportForms1VO>> reportforms1(ReportDTO reportDTO) {
        return ResponseUtil.ok(indexService.queryReportForms1(reportDTO.getStartDate(), reportDTO.getEndDate(), reportDTO.getOcpcChannelId()));
    }

}
