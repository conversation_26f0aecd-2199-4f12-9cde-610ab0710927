package com.steamgo1.csgoskinadmin.controller;


import com.steamgo1.csgoskinadmin.dto.*;
import com.steamgo1.csgoskinadmin.schedule.DeadLetterSender;
import com.steamgo1.csgoskinadmin.service.RollHomeService;
import com.steamgo1.csgoskinadmin.vo.RollHomeFullVO;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import com.steamgo1.csgoskincommon.vo.RollHomeFilterParamVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RequestMapping("/rollhome")
@RestController
@RequiredArgsConstructor
@Api(tags = "Roll房管理")
public class RollHomeController {
    @Autowired
    RabbitTemplate rabbitTemplate;
    @Autowired
    DeadLetterSender deadLetterSender;
    @Autowired
    private RollHomeService rollHomeService;
    @Value("${rabbitmq.exchange.csgo}")
    private String rollHomeExchageName;

    @ApiOperation("获取Roll房查询条件")
    @GetMapping(value = "/filters")
    public Response<RollHomeFilterParamVO> qeuryRollHomeFilterParam() {
        return ResponseUtil.ok(rollHomeService.getRollHomeFilterParam());
    }

    @ApiOperation("查询Roll房")
    @GetMapping(value = "")
    public Response<Page<RollHomeFullVO>> qeuryRollHome(RollHomeQueryDTO rollHomeQueryDTO) {
        return ResponseUtil.ok(rollHomeService.queryRollHome(rollHomeQueryDTO));
    }

    @ApiOperation("添加Roll房")
    @PostMapping(value = "")
    public Response<RollHomeFullVO> addRollHome(@RequestBody @Valid RollHomeAddDTO rollHomeAddDTO) {
        return ResponseUtil.ok(rollHomeService.addRollHome(rollHomeAddDTO));
    }

    @ApiOperation("更新Roll房")
    @PutMapping(value = "")
    public Response updateRollHome(@RequestBody @Valid RollHomeUpdateDTO rollHomeAddDTO) {
        rollHomeService.updateRollHome(rollHomeAddDTO);
        return ResponseUtil.ok();
    }

    @ApiOperation("Roll房绑定用户")
    @PutMapping(value = "/bindingUser")
    public Response rollHomeBindingUser(@RequestBody @Valid List<RollHomeBindingUserDTO> rollHomeBindingUserDTOList) {
        rollHomeService.rollHomeBindindUser(rollHomeBindingUserDTOList);
        return ResponseUtil.ok();
    }

    @ApiOperation("添加机器人")
    @PostMapping(value = "/addUser")
    public Response addUser(@RequestBody RollHomeAddUserParamDTO rollHomeAddUserParamDTO) {
        rollHomeService.joinRollHome(rollHomeAddUserParamDTO);
        return ResponseUtil.ok();
    }

    @ApiOperation("机器人下拉")
    @PostMapping(value = "/robotOptions")
    public Response<List<RollHomeAddUserParamDTO>> robotOptions(@RequestBody RollHomeUpdateDTO rollHomeUpdateDTO) {
        return ResponseUtil.ok(rollHomeService.queryRobotNowRollHome(rollHomeUpdateDTO));
    }


//    @ApiOperation("测试直连rabbitmq交换机")
//    @GetMapping("/sendDirectMessage")
//    public String sendDirectMessage() {
//        deadLetterSender.send1();
//        return "ok";
//    }
//
//    @ApiOperation("测试直连rabbitmq交换机 延时")
//    @GetMapping("/sendDirectMessage1")
//    public String sendDirectMessage1(String time) {
//        deadLetterSender.send2("ok", time);
//        return "ok";
//    }
//
//    @ApiOperation("测试直连rabbitmq交换机 延时2")
//    @GetMapping("/sendDirectMessage2")
//    public String sendDirectMessage2(String time) {
//        String messageId = String.valueOf(UUID.randomUUID());
//
//        RabbitRomeHomeVO rabbitRomeHomeVO = new RabbitRomeHomeVO();
//        rabbitRomeHomeVO.setRollHomeId(21212L);
//        rabbitRomeHomeVO.setMessageId(messageId);
//        rabbitRomeHomeVO.setCreateTime(new Date());
//        String routingKey =  "rollhome.test";
//        log.info("rollHomeExchageName: {}, routingkey: {}, data: {}, expiratime: {}", rollHomeExchageName, routingKey, JSONObject.toJSON(rabbitRomeHomeVO),time);
//        rabbitTemplate.convertAndSend(rollHomeExchageName, routingKey, JSONObject.toJSON(rabbitRomeHomeVO), a->{
//            a.getMessageProperties().setExpiration(time);
//            return a;
//        });
//        return "ok";
//    }


}
