package com.steamgo1.csgoskinadmin.controller;

import com.steamgo1.csgoskinadmin.dto.*;
import com.steamgo1.csgoskinadmin.service.UserService;
import com.steamgo1.csgoskinadmin.vo.*;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@RequestMapping("/user")
@RestController
@Api(tags = "用户管理")
public class UserController {
    @Autowired
    private UserService userService;

    @ApiOperation("查询用户")
    @GetMapping(value = "")
    public Response<Page<UserInfoVO>> queryUsers(UserQueryDTO userQueryDTO) {
        return ResponseUtil.ok(userService.queryUser(userQueryDTO));
    }

    @ApiOperation("查询用户背包总览")
    @GetMapping(value = "/package")
    public Response<Page<UserPackageVO>> queryUserPackage(UserQueryDTO userQueryDTO) {
        return ResponseUtil.ok(userService.queryUserPackage(userQueryDTO));
    }

    @ApiOperation("查询用户背包明细")
    @GetMapping(value = "/package/details")
    public Response<Page<UserPackageDetialsVO>> queryUserPackageDetails(UserIdPageQueryDTO userIdPageQueryDTO) {
        return ResponseUtil.ok(userService.queryUserPackageDetails(userIdPageQueryDTO));
    }

    @ApiOperation("系统充值金币")
    @PostMapping("/coin/charge")
    public Response sysChrage(@RequestBody @Valid SysUserChangeDTO sysUserChangeDTO) {
        userService.sysUserCharge(sysUserChangeDTO);
        return ResponseUtil.ok();
    }

    @ApiOperation("同步steam信息")
    @PostMapping("/steamInfo/sync")
    public Response syncSteamInfo() {
        userService.syncStreamInfo();
        return ResponseUtil.ok();
    }

    @ApiOperation("添加机器人")
    @PostMapping("/robot/add")
    public Response syncSteamInfo(@RequestBody RobotAddDTO robotAddDTO) {
        userService.addRobot(robotAddDTO.getTotal(), robotAddDTO.getType());
        return ResponseUtil.ok();
    }

    @ApiOperation("更新用户信息")
    @PutMapping("/info")
    public Response updateUserInfo(@RequestBody UserInfoUpdateVO userInfoUpdateVO) {
        userService.updateUserInfo(userInfoUpdateVO);
        return ResponseUtil.ok();
    }

    @ApiOperation("查询用户金币流水")
    @GetMapping(value = "/coinRecord")
    public Response<Page<UserCoinRecordVO>> queryUserCoinRecord(UserIdPageQueryDTO userIdPageQueryDTO) {
        return ResponseUtil.ok(userService.queryUserCoinRecord(userIdPageQueryDTO));
    }

    @ApiOperation("查询用户钻石流水")
    @GetMapping(value = "/diamondRecord")
    public Response<Page<UserDiamondRecordVO>> queryDiamondRecord(UserIdPageQueryDTO userIdPageQueryDTO) {
        return ResponseUtil.ok(userService.queryUserDiamondRecord(userIdPageQueryDTO));
    }

    /**
     * 修改用户公共hash
     *
     * @return
     */
    @GetMapping("/updateUserPublicHash")
    @ApiOperation(value = "更新用户公共hash", notes = "")
    public Response updateUserPublicHash() {
        log.info("工具方法更新用户私密hash");
        userService.updateUserPublicHash();
        return ResponseUtil.ok();
    }

    /**
     * 修改用户公共hash
     *
     * @return
     */
    @PostMapping("/distributeSkin")
    @ApiOperation(value = "给用户背包添加饰品", notes = "")
    public Response distributeSkin(@RequestBody UserAddSkinDTO userAddSkinDTO) {
        userService.addUserPackageSkin(userAddSkinDTO);
        return ResponseUtil.ok();
    }


}
