package com.steamgo1.csgoskinadmin.controller;


import com.steamgo1.csgoskinadmin.dto.*;
import com.steamgo1.csgoskinadmin.service.SiteService;
import com.steamgo1.csgoskincommon.entity.AnnouncementEntity;
import com.steamgo1.csgoskincommon.entity.DataDictionaryEntity;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RequestMapping("/site")
@RestController
@RequiredArgsConstructor
@Api(tags = "站点配置")
public class SiteController {

    @Autowired
    private SiteService siteService;


    @ApiOperation("查询字典")
    @GetMapping(value = "/dict")
    public Response<Page<DataDictionaryEntity>> queryDatadictionary(DataDictionaryQueryDTO dataDictionaryQueryDTO) {
        return ResponseUtil.ok(siteService.queryDataDictionary(dataDictionaryQueryDTO));
    }

    @ApiOperation("添加字典")
    @PostMapping(value = "/dict")
    private Response<DataDictionaryEntity> addDataDictionary(DataDictionaryDTO dataDictionaryDTO) {
        return ResponseUtil.ok(siteService.addDataDicationary(dataDictionaryDTO));
    }

    @ApiOperation("更新字典")
    @PutMapping(value = "/dict")
    private Response<DataDictionaryEntity> updateDataDictionary(DataDictionaryUpdateDTO dataDictionaryUpdateDTO) {
        return ResponseUtil.ok(siteService.updateDataDicationary(dataDictionaryUpdateDTO));
    }

    @ApiOperation("删除字典")
    @DeleteMapping(value = "/dict")
    private Response delDataDictionary(IdsDTO idsDTO) {
        siteService.delDataDicationary(idsDTO);
        return ResponseUtil.ok();
    }

    @ApiOperation("查询公告")
    @GetMapping(value = "/announcement")
    public Response<List<AnnouncementEntity>> queryAddAnnouncement() {
        return ResponseUtil.ok(siteService.queryAnnouncement());
    }

    @ApiOperation("添加公告")
    @PostMapping(value = "/announcement")
    private Response<AnnouncementEntity> addAnnouncement(AnnouncementAddDTO announcementAddDTO) {
        return ResponseUtil.ok(siteService.addAnnouncement(announcementAddDTO));
    }

    @ApiOperation("上线公告")
    @PutMapping(value = "/announcement/online/{id}")
    private Response onlineAnnouncement(@PathVariable Long id) {
        siteService.onlineAnnouncement(id);
        return ResponseUtil.ok();
    }

    @ApiOperation("下线公告")
    @PutMapping(value = "/announcement/offline/{id}")
    private Response unonlineAnnouncement(@PathVariable Long id) {
        siteService.unOnlineAnnouncement(id);
        return ResponseUtil.ok();
    }

}
