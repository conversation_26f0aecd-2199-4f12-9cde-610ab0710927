package com.steamgo1.csgoskinadmin.controller;


import com.steamgo1.csgoskinadmin.dto.IdsDTO;
import com.steamgo1.csgoskinadmin.dto.PercentageRecordQueryDTO;
import com.steamgo1.csgoskinadmin.dto.PercentageSkinQueryDTO;
import com.steamgo1.csgoskinadmin.service.PercentageService;
import com.steamgo1.csgoskinadmin.vo.PercentageSkinQueryVO;
import com.steamgo1.csgoskincommon.entity.PercentageUserRecordEntity;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequestMapping("/percentage")
@RestController
@Api(tags = "百分比管理")
public class PercentageController {
    @Autowired
    PercentageService percentageService;

    @GetMapping("/skin")
    @ApiOperation("查询追梦饰品")
    public Response<Page<PercentageSkinQueryVO>> queryPercentageSkin(PercentageSkinQueryDTO percentageSkinQueryDTO) {
        return ResponseUtil.ok(percentageService.queryPercentageSkin(percentageSkinQueryDTO));
    }

    @GetMapping("/randomSkin")
    @ApiOperation("查询随机饰品")
    public Response<Page<PercentageSkinQueryVO>> queryRandomSkin(PercentageSkinQueryDTO percentageSkinQueryDTO) {
        return ResponseUtil.ok(percentageService.queryRandomSkin(percentageSkinQueryDTO));
    }

    @ApiOperation("/添加追梦饰品")
    @PostMapping("/skin/add")
    public Response addPercentageSkin(IdsDTO idsDTO) {
        percentageService.addPercentageSkin(idsDTO);
        return ResponseUtil.ok();
    }

    @ApiOperation("/移除追梦饰品")
    @PostMapping("/skin/remove")
    public Response delPercentageSkin(IdsDTO idsDTO) {
        percentageService.delPercentageSkin(idsDTO);
        return ResponseUtil.ok();
    }

    @ApiOperation("/添加追梦随机饰品")
    @PostMapping("/randomSkin/add")
    public Response addRandomSKin(IdsDTO idsDTO) {
        percentageService.addPercentageRandomSkinAddVO(idsDTO);
        return ResponseUtil.ok();
    }

    @ApiOperation("/移除追梦随机饰品")
    @PostMapping("/randomSkin/remove")
    public Response removeRandomSkin(IdsDTO idsDTO) {
        percentageService.delPercentageRandomSkin(idsDTO);
        return ResponseUtil.ok();
    }

    @ApiOperation("获取追梦记录")
    @GetMapping(value = "/record")
    public Response<Page<PercentageUserRecordEntity>> queryPercentageUserRecord(PercentageRecordQueryDTO percentageRecordQueryDTO) {
        return ResponseUtil.ok(percentageService.querPercentageRecord(percentageRecordQueryDTO));
    }
}
