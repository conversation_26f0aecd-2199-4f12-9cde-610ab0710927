package com.steamgo1.csgoskinadmin.controller;

import com.steamgo1.csgoskinadmin.service.CaseSkinService;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;

@Slf4j
@RequestMapping("/manager")
@RestController
@RequiredArgsConstructor
@Api(tags = "管理类")
public class ManagerController {

    @Value("${spring.redis.prefix.case}")
    private String redisCasePrefix;
    @Value("${spring.redis.prefix.skin}")
    private String redisSkinPrefix;
    @Autowired
    private CaseSkinService caseSkinService;

    @GetMapping("/delCaseSession")
    @ApiOperation(value = "删除所有箱子缓存")
    public Response<String> delCaseSession() {
        String redisCaseKey = redisCasePrefix + ":case_info:level:";
        Set<String> keys = RedisUtils.keys(redisCaseKey + "*");
        for (String key : keys) {
            RedisUtils.delete(key);
        }
        // return ResponseUtil.ok();
        return ResponseUtil.ok(I18nUtils.getMessage("response.manager.case.cache.deleted"));
    }


    @GetMapping("/delSkinSession")
    @ApiOperation(value = "删除所有饰品缓存")
    public Response<Integer> delSkinSession() {
        String redisSkinInfoKey = redisSkinPrefix + ":skin_info:";
        Set<String> keys = RedisUtils.keys(redisSkinInfoKey + "*");
        for (String key : keys) {
            RedisUtils.delete(key);
        }
        return ResponseUtil.ok();
    }


    @GetMapping("/refreshCaseSkin")
    @ApiOperation(value = "根据配置刷新所有商品的caseskin关联关系")
    public Response<String> refreshCaseSkin() {
        caseSkinService.updateCaseSkinTaskByZbt();
        // return ResponseUtil.ok("调用成功！");
        return ResponseUtil.ok(I18nUtils.getMessage("response.manager.case.skin.refresh.success"));
    }
}
