package com.steamgo1.csgoskinadmin.controller;


import com.steamgo1.csgoskinadmin.dto.UserDisableQueryDTO;
import com.steamgo1.csgoskinadmin.service.SiteService;
import com.steamgo1.csgoskinadmin.service.UserService;
import com.steamgo1.csgoskinadmin.vo.UserDisableQueryParamVO;
import com.steamgo1.csgoskinadmin.vo.UserDisableQueryVO;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RequestMapping("/riskManage")
@RestController
@Api(tags = "风控管理")
public class RiskManagementController {
    @Autowired
    private UserService userService;

    @Autowired
    private SiteService siteService;

    @GetMapping("/")
    @ApiOperation("查询风控记录")
    public Response<Page<UserDisableQueryVO>> queryUserDisable(UserDisableQueryDTO userDisableQueryDTO) {
        return ResponseUtil.ok(userService.queryUserDisable(userDisableQueryDTO));
    }

    @ApiOperation("获取风控类别")
    @GetMapping(value = "/filters")
    public Response<UserDisableQueryParamVO> userDisableFilters() {
        return ResponseUtil.ok(siteService.queryUserDisableQUeryParam());
    }

    @ApiOperation("风控")
    @PutMapping(value = "/disable/{id}")
    public Response disable(@PathVariable Long id) {
        userService.userDisable(id);
        return ResponseUtil.ok();
    }

    @ApiOperation("解除风控")
    @PutMapping(value = "/enable/{id}")
    public Response enable(@PathVariable Long id) {
        userService.userEnable(id);
        return ResponseUtil.ok();
    }

    @ApiOperation("用户Ban")
    @PutMapping(value = "/ban/{id}")
    public Response userBan(@PathVariable Long id) {
        userService.disable(id);
        return ResponseUtil.ok();
    }

    @ApiOperation("用户解Ban")
    @PutMapping(value = "/leftTheBan/{id}")
    public Response userLeftBan(@PathVariable Long id) {
        userService.enable(id);
        return ResponseUtil.ok();
    }


}
