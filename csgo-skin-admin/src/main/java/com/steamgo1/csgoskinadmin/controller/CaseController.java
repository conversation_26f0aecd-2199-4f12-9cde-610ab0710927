package com.steamgo1.csgoskinadmin.controller;

import com.steamgo1.csgoskinadmin.dto.*;
import com.steamgo1.csgoskinadmin.service.CaseService;
import com.steamgo1.csgoskinadmin.vo.CaseFullVO;
import com.steamgo1.csgoskinadmin.vo.CaseQueryParamVO;
import com.steamgo1.csgoskinadmin.vo.CaseStatisticsVO;
import com.steamgo1.csgoskincommon.dao.CaseRepository;
import com.steamgo1.csgoskincommon.entity.CaseEntity;
import com.steamgo1.csgoskincommon.entity.CaseUserRecordEntity;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.CaseOpenVo;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RequestMapping("/case")
@RestController
@RequiredArgsConstructor
@Api(tags = "箱子管理")
public class CaseController {
    @Autowired
    private CaseService caseService;

    @Autowired
    private CaseRepository caseRepository;


    @ApiOperation("获取箱子查询条件")
    @GetMapping(value = "/filters")
    public Response<CaseQueryParamVO> allCaseCategory() {
        return ResponseUtil.ok(caseService.caseQueryParam());
    }


    @ApiOperation("查询箱子")
    @GetMapping(value = "")
    public Response<List<CaseFullVO>> allCase(CaseQueryDTO caseQueryDTO) {
        return ResponseUtil.ok(caseService.queryCaseByCategory(caseQueryDTO));
    }

    @ApiOperation("获取单个箱子")
    @GetMapping(value = "/{id}")
    public Response<CaseFullVO> getCase(@PathVariable Long id) {
        return ResponseUtil.ok(caseService.queryCaseById(id));
    }

    @ApiOperation("删除箱子")
    @DeleteMapping(value = "/{id}")
    public Response delCase(@PathVariable Long id) {
        caseService.delCaseById(id);
        return ResponseUtil.ok();
    }

//    @ApiOperation("添加箱子")
//    @PostMapping(value = "/")
//    public ResponseResult<CaseEntity> addCase(@RequestBody @Valid CaseAddDTO caseAddDTO) {
//        return ResponseResult.ok(caseService.addCase(caseAddDTO));
//    }

    @ApiOperation("添加箱子")
    @PostMapping(value = "")
    public Response<CaseFullVO> addFullCase(@RequestBody @Valid CaseFullAddDTO caseFullAddDTO) {
        return ResponseUtil.ok(caseService.addFullCase(caseFullAddDTO));
    }

    @ApiOperation("更新箱子")
    @PutMapping(value = "")
    public Response<CaseFullVO> updateFullCase(@RequestBody @Valid CaseFullUpdateDTO caseFullUpdateDTO) {
        return ResponseUtil.ok(caseService.updateFullCase(caseFullUpdateDTO));
    }

    @ApiOperation("上下架")
    @PutMapping(value = "/saleState")
    public Response updateIsSaleState(@RequestBody CaseUpdateSaleStateDTO caseUpdateSaleStateDTO) {
        caseService.updateIsSaleState(caseUpdateSaleStateDTO);
        return ResponseUtil.ok();
    }


    @ApiOperation("获取开箱记录")
    @GetMapping(value = "/record")
    public Response<Page<CaseUserRecordEntity>> queryCaseOpenRecord(OpenCaseRecordQueryDTO openCaseRecordQueryDTO) {
        return ResponseUtil.ok(caseService.queryCaseUserRecordEntity(openCaseRecordQueryDTO));
    }

    @ApiOperation("获取开箱记录")
    @GetMapping(value = "/statistics/{caseId}")
    public Response<CaseStatisticsVO> queryCaseOpenRecord(@PathVariable Long caseId) {
        return ResponseUtil.ok(caseService.queryCaseStatistics(caseId));
    }

    @GetMapping(value = "/switCaseProtect/{switCaseProtect}")
    public Response<CaseEntity> switCaseProtect(@PathVariable("switCaseProtect") Boolean switCaseProtect) {
        return ResponseUtil.ok(caseService.switchCaseProtect(switCaseProtect));
    }

    @PostMapping(value = "/statisticsOpenCase")
    @ApiOperation("开箱统计")
    public Response<List<CaseOpenVo>> statisticsOpenCase(@RequestBody OpenCaseDTO openCaseDTO) {
        return ResponseUtil.ok(caseService.statisticsOpenCase(openCaseDTO));
    }
}
