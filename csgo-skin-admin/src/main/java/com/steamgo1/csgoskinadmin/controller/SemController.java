package com.steamgo1.csgoskinadmin.controller;


import com.steamgo1.csgoskinadmin.dto.BaiduAppCallBackDTO;
import com.steamgo1.csgoskinadmin.dto.BaiduDataQueryDTO;
import com.steamgo1.csgoskinadmin.dto.GoogleDataQueryDTO;
import com.steamgo1.csgoskinadmin.dto.MetaDataQueryDTO;
import com.steamgo1.csgoskinadmin.service.OcpcService;
import com.steamgo1.csgoskinadmin.vo.OcpcBaiduDataVO;
import com.steamgo1.csgoskinadmin.vo.OcpcGoogleDataVO;
import com.steamgo1.csgoskinadmin.vo.OcpcMetaDataVO;
import com.steamgo1.csgoskincommon.entity.OcpcBaiduAccountEntity;
import com.steamgo1.csgoskincommon.entity.OcpcBaiduTokenEntity;
import com.steamgo1.csgoskincommon.entity.OcpcChannelEntity;
import com.steamgo1.csgoskincommon.entity.OcpcGoogleTokenEntity;
import com.steamgo1.csgoskincommon.entity.OcpcMetaTokenEntity;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RequestMapping("/ocpc")
@RestController
@Api(tags = "SEM管理")
public class SemController {
    @Autowired
    private OcpcService ocpcService;

    @ApiOperation("查询渠道")
    @GetMapping(value = "/channel")
    public Response<List<OcpcChannelEntity>> queryChannel() {
        return ResponseUtil.ok(ocpcService.queryChannel());
    }

    @ApiOperation("查询百度TOKEN")
    @GetMapping(value = "/baidu/token")
    public Response<List<OcpcBaiduTokenEntity>> queryBaiduToken() {
        return ResponseUtil.ok(ocpcService.querBaiduToken());
    }

    @ApiOperation("查询百度账号")
    @GetMapping(value = "/baidu/account")
    public Response<List<OcpcBaiduAccountEntity>> queryBaiduAccount() {
        return ResponseUtil.ok(ocpcService.queryBaiduAccount());
    }

    @ApiOperation("查询Google TOKEN")
    @GetMapping(value = "/google/token")
    public Response<List<OcpcGoogleTokenEntity>> queryGoogleToken() {
        return ResponseUtil.ok(ocpcService.queryGoogleToken());
    }

    @ApiOperation("查询Meta TOKEN")
    @GetMapping(value = "/meta/token")
    public Response<List<OcpcMetaTokenEntity>> queryMetaToken() {
        return ResponseUtil.ok(ocpcService.queryMetaToken());
    }

    @ApiOperation("查询百度数据")
    @GetMapping(value = "/baidu/data")
    public Response<Page<OcpcBaiduDataVO>> queryBaiduData(BaiduDataQueryDTO baiduDataQueryDTO) {
        return ResponseUtil.ok(ocpcService.queryBaiduData(baiduDataQueryDTO));
    }

    @ApiOperation("查询Google数据")
    @GetMapping(value = "/google/data")
    public Response<Page<OcpcGoogleDataVO>> queryGoogleData(GoogleDataQueryDTO googleDataQueryDTO) {
        return ResponseUtil.ok(ocpcService.queryGoogleData(googleDataQueryDTO));
    }

    @ApiOperation("查询Meta数据")
    @GetMapping(value = "/meta/data")
    public Response<Page<OcpcMetaDataVO>> queryMetaData(MetaDataQueryDTO metaDataQueryDTO) {
        return ResponseUtil.ok(ocpcService.queryMetaData(metaDataQueryDTO));
    }

    @ApiOperation("百度营销回调接口")
    @GetMapping(value = "/baidu/callback")
    public Response baiduCallback(BaiduAppCallBackDTO baiduAppCallBackDTO) {
        return ResponseUtil.ok();
    }

}
