package com.steamgo1.csgoskinadmin.controller;


import com.steamgo1.csgoskinadmin.dto.UserChargeOrderQueryDTO;
import com.steamgo1.csgoskinadmin.dto.UserPickupCheckDTO;
import com.steamgo1.csgoskinadmin.dto.UserPickupOrderCountDTO;
import com.steamgo1.csgoskinadmin.dto.UserPickupOrderQueryDTO;
import com.steamgo1.csgoskinadmin.service.OrderService;
import com.steamgo1.csgoskinadmin.service.UserService;
import com.steamgo1.csgoskinadmin.vo.OrderChargeVO;
import com.steamgo1.csgoskinadmin.vo.OrderPickupVO;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RequestMapping("/order")
@RestController
@Api(tags = "订单管理")
public class OrderController {

    @Autowired
    private OrderService orderService;

    @Autowired
    private UserService userService;

    @GetMapping("/charge")
    @ApiOperation("查询充值订单")
    public Response<Page<OrderChargeVO>> queryOrderCharges(UserChargeOrderQueryDTO userChargeOrderQueryDTO) {
        return ResponseUtil.ok(orderService.querOrderChargeVO(userChargeOrderQueryDTO));
    }

    @GetMapping("/pickup")
    @ApiOperation("查询取回订单")
    public Response<Page<OrderPickupVO>> queryOrderPickups(UserPickupOrderQueryDTO userPickupOrderQueryDTO) {
        return ResponseUtil.ok(orderService.querOrderPickupVO(userPickupOrderQueryDTO));
    }


    @GetMapping("/pickupCount")
    @ApiOperation("统计查询取回订单")
    public Response<Integer> CountOrderPickups(UserPickupOrderCountDTO userPickupOrderCountDTO) {
        return ResponseUtil.ok(orderService.CountOrderPickupVO(userPickupOrderCountDTO));
    }

    @PutMapping("/pickup/status")
    @ApiOperation("审核订单")
    public Response<Page<OrderPickupVO>> checkPickup(@RequestBody UserPickupCheckDTO userPickupCheckDTO) {
        userService.checkUserPickup(userPickupCheckDTO);
        return ResponseUtil.ok();
    }


    @PostMapping("/queryOrderChargesByUserId")
    @ApiOperation("查询充值订单")
    public Response<Page<OrderChargeVO>> queryOrderChargesByUserId(@RequestBody UserChargeOrderQueryDTO userChargeOrderQueryDTO) {
        return ResponseUtil.ok(orderService.querOrderChargeVOByUserId(userChargeOrderQueryDTO));
    }

    @PostMapping("/queryOrderPickupsByUserId")
    @ApiOperation("查询取回订单")
    public Response<Page<OrderPickupVO>> queryOrderPickupsByUserId(@RequestBody UserPickupOrderQueryDTO userPickupOrderQueryDTO) {
        return ResponseUtil.ok(orderService.querOrderPickupVOByUserId(userPickupOrderQueryDTO));
    }
}


