package com.steamgo1.csgoskinadmin.controller;


import com.steamgo1.csgoskinadmin.dto.BattleHomeQueryDTO;
import com.steamgo1.csgoskinadmin.service.BattleHomeService;
import com.steamgo1.csgoskinadmin.vo.BattleQueryVO;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequestMapping("/battle")
@RestController
@Api(tags = "对战管理")
public class BattleHomeController {
    @Autowired
    BattleHomeService battleHomeService;

    @GetMapping("/")
    @ApiOperation("查询对战房")
    public Response<Page<BattleQueryVO>> queryBattleVO(BattleHomeQueryDTO battleHomeQueryDTO) {
        return ResponseUtil.ok(battleHomeService.queryBattleHomes(battleHomeQueryDTO));
    }

}
