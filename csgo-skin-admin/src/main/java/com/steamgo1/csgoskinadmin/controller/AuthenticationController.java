package com.steamgo1.csgoskinadmin.controller;

import com.steamgo1.csgoskinadmin.dto.AdminUserDTO;
import com.steamgo1.csgoskinadmin.dto.AuthenticationDTO;
import com.steamgo1.csgoskinadmin.service.AdminUserService;
import com.steamgo1.csgoskincommon.entity.AdminUserEntity;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import com.steamgo1.csgoskincommon.vo.TokenVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Slf4j
@RequestMapping("/auth")
@RestController
@RequiredArgsConstructor
@Api(tags = "用户鉴权")
public class AuthenticationController {

    @Autowired
    private AdminUserService adminUserService;


    @ApiOperation("登录")
    @PostMapping(value = "/login")
    public Response<TokenVO> login(@Valid @RequestBody AuthenticationDTO authenticationDTO) {
        return ResponseUtil.ok(adminUserService.login(authenticationDTO));
    }

    @ApiOperation("注册")
    @PostMapping(value = "/register")
//    @PreAuthorize("hasRole('')")
    public Response<AdminUserEntity> register(@Valid @RequestBody AdminUserDTO adminUserDTO) {
        return ResponseUtil.ok(adminUserService.createAdminUser(adminUserDTO));
    }

//    @ApiOperation("测试")
//    @GetMapping(value = "/test")
//    public Response<String> test() {
//        return ResponseUtil.ok("ok");
//    }
}
