package com.steamgo1.csgoskinadmin.dto;

import com.steamgo1.csgoskincommon.entity.enums.OcpcGoogleDataType;
import com.steamgo1.csgoskincommon.entity.enums.OrderStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("Google数据查询DTO")
public class GoogleDataQueryDTO {
    @ApiModelProperty("页码")
    private Integer page = 0;

    @ApiModelProperty("每页大小")
    private Integer size = 10;

    @ApiModelProperty("类型")
    private OcpcGoogleDataType type;

    @ApiModelProperty("IP地址")
    private String ip;

    @ApiModelProperty("开始时间")
    private Date startTime;

    @ApiModelProperty("结束时间")
    private Date endTime;

    @ApiModelProperty("渠道ID")
    private Long ocpcChannelId;

    @ApiModelProperty("订单状态")
    private OrderStatus orderStatus;
}
