package com.steamgo1.csgoskinadmin.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class RollHomeQueryDTO {
    @ApiModelProperty("分页大小")
    @NotBlank(message = "分页大小必填")
    private int size;

    @ApiModelProperty("页数")
    @NotBlank(message = "页数必填")
    private int page;

    @ApiModelProperty("房间类型")
    private Integer rollHomeType;

    @ApiModelProperty("开奖方式")
    private Integer rollHomeLotteryMethod;

    @ApiModelProperty("状态")
    private Integer status;
}
