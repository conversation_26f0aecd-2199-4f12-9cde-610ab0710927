package com.steamgo1.csgoskinadmin.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.steamgo1.csgoskincommon.entity.enums.OcpcMetaDataType;
import com.steamgo1.csgoskincommon.entity.enums.OrderStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ApiModel("Meta数据查询DTO")
public class MetaDataQueryDTO {
    @ApiModelProperty("页码")
    private Integer page = 0;

    @ApiModelProperty("每页大小")
    private Integer size = 10;

    @ApiModelProperty("类型")
    private OcpcMetaDataType type;

    @ApiModelProperty("IP地址")
    private String ip;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty("渠道ID")
    private Long ocpcChannelId;

    @ApiModelProperty("订单状态")
    private OrderStatus orderStatus;

    @ApiModelProperty("Facebook点击ID")
    private String clickId;

    @ApiModelProperty("广告系列ID")
    private String campaignId;

    @ApiModelProperty("广告组ID")
    private String adsetId;

    @ApiModelProperty("广告ID")
    private String adId;
}
