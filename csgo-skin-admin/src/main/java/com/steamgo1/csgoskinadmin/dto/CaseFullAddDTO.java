package com.steamgo1.csgoskinadmin.dto;

import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("添加箱子全量参数")
public class CaseFullAddDTO implements Serializable {
    /**
     * 新手或者老手
     */
    @ApiModelProperty("箱子类型 1 新手箱子 2 普通箱子")
    private Integer type;

    @ApiModelProperty("箱子名（多语言）")
    private I18nField i18nFieldName;

    @ApiModelProperty("箱子名")
    private String name;

    @ApiModelProperty("箱子价格")
    private BigDecimal price;

    @ApiModelProperty("箱子前景图")
    private String foregroundPicture;

    @ApiModelProperty("箱子背景图")
    private String backgroundPicture;

    @ApiModelProperty("箱子类别")
    private Long categoryId;

    @ApiModelProperty("箱子优先级(用作排序，越大越靠前)")
    private Integer gradle;

    @ApiModelProperty("箱子是否在售")
    private Boolean isSale;

    @ApiModelProperty("箱子是否推荐")
    private Boolean isRecommend;

    @ApiModelProperty("是否battle专属")
    private Boolean onleBattle;

    @ApiModelProperty("箱子等级信息")
    private List<CaseLevelDTO> levels;

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getName() {
        return I18nUtils.getI18nFieldValue(i18nFieldName, I18nUtils.getCurrentLanguageEnum());
    }

}
