package com.steamgo1.csgoskinadmin.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.steamgo1.csgoskincommon.entity.enums.OrderStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
public class BaiduDataQueryDTO {
    @ApiModelProperty("分页大小")
    @NotBlank(message = "分页大小必填")
    private int size;

    @ApiModelProperty("页数")
    @NotBlank(message = "页数必填")
    private int page;

    @ApiModelProperty("渠道")
    private Long ocpcChannelId;

    @ApiModelProperty("type")
    private Integer type;

    @ApiModelProperty("IP")
    private String ip;

    @ApiModelProperty("订单状态")
    private OrderStatus orderStatus;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}
