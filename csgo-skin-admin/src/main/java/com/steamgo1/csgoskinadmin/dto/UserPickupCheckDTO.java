package com.steamgo1.csgoskinadmin.dto;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("用户取回审核")
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserPickupCheckDTO {
    @ApiModelProperty("取回id")
    private Long id;

    @ApiModelProperty("是否同意")
    private Boolean isAgree;
}
