package com.steamgo1.csgoskinadmin.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

@Data
@ApiModel("饰品查询参数")
public class SkinQueryDTO {
    @ApiModelProperty("分页大小")
    @NotBlank(message = "分页大小必填")
    private int size;

    @ApiModelProperty("页数")
    @NotBlank(message = "页数必填")
    private int page;

    @ApiModelProperty("饰品原型ID")
    private Long prototypeId;

    @ApiModelProperty("饰品稀有度ID")
    private Long skinRarityId;

    @ApiModelProperty("饰品外观ID")
    private Long skinExteriorId;

    @ApiModelProperty("饰品质量ID")
    private Long skinQualityId;

    @ApiModelProperty("饰品名")
    private String name;

    @ApiModelProperty("是否追梦")
    private Boolean enablePercentage;

    @ApiModelProperty("是否随机")
    private Boolean enableRandom;

    @ApiModelProperty("是否价格锁定")
    private Boolean lockPrice;

    @ApiModelProperty("是否价格异常")
    private Boolean isAbnormal;

    @ApiModelProperty("是否商品在售")
    private Boolean isSale;

    @ApiModelProperty("最低价格")
    private BigDecimal minPrice;

    @ApiModelProperty("最高价格")
    private BigDecimal maxPrice;


}
