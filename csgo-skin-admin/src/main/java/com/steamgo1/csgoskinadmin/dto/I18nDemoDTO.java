package com.steamgo1.csgoskinadmin.dto;

import com.steamgo1.csgoskincommon.utils.I18nField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 国际化演示DTO - 用于接收前端数据
 * 
 * <AUTHOR>
 */
@Data
@ApiModel("国际化演示数据传输对象")
public class I18nDemoDTO {
    
    @ApiModelProperty("ID")
    private Long id;
    
    @ApiModelProperty(value = "文本内容（多语言）", required = true)
    @NotNull(message = "文本内容不能为空")
    private I18nField i18nFieldText;
    
    @ApiModelProperty("创建时间")
    private Date createTime;
    
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("文本内容（当前语言）")
    private String text;
}
