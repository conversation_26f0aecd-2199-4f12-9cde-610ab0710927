package com.steamgo1.csgoskinadmin.dto;

import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

@Data
public class DataDictionaryUpdateDTO {
    @ApiModelProperty("ID")
    Long id;

    @ApiModelProperty("父ID")
    Long parentId;

    @ApiModelProperty("箱子名（多语言）")
    private I18nField i18nFieldName;

    @ApiModelProperty("字段名")
    @NotBlank(message = "字段名必填")
    @Length(max = 18)
    private String name;

    @ApiModelProperty("字段别名")
    @Length(max = 18)
    private String alias;

    @ApiModelProperty("优先级(用于同级别排序)")
    private Integer gradle = 0;

    @ApiModelProperty("唯一标识")
    @Length(max = 18)
    private String code;

    @ApiModelProperty("图片")
    @Length(max = 128)
    private String img;

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getName() {
        return I18nUtils.getI18nFieldValue(i18nFieldName, I18nUtils.getCurrentLanguageEnum());
    }

}
