package com.steamgo1.csgoskinadmin.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Data
@ApiModel("添加集卡活动")
public class CardCollectAddDTO {
    @ApiModelProperty("活动名")
    private String name;

    @ApiModelProperty("背景图")
    private String buttonPicture;

    @ApiModelProperty("背景图")
    private String backgroundPicture;

    @ApiModelProperty("详情")
    private String details;

    @ApiModelProperty("金币数量")
    private BigDecimal coin;

    @ApiModelProperty("开奖时间(yyyy-MM-dd HH:mm:ss)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    @ApiModelProperty("僵尸数量")
    private Integer cheatNum = 0;

    @ApiModelProperty("是否激活")
    private Boolean isActivate = false;

    @ApiModelProperty("卡片信息")
    private List<CardCollectCard> cardList;


    @Data
    public static class CardCollectCard {
        @ApiModelProperty("卡片名")
        private String name;

        @ApiModelProperty("卡片图片")
        private String picture;

        @ApiModelProperty("掉落概率")
        private BigDecimal probability;

    }
}
