package com.steamgo1.csgoskinadmin.dto;

import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("添加箱子基础数据参数")
public class CaseAddDTO {
    /**
     * 新手或者老手
     */
    @ApiModelProperty("箱子类型 1 新手箱子 2 普通箱子")
    private Integer type;

    @ApiModelProperty("箱子名（多语言）")
    private I18nField i18nFieldName;

    @ApiModelProperty("箱子名")
    private String name;

    @ApiModelProperty("箱子价格")
    private BigDecimal price;

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getName() {
        return I18nUtils.getI18nFieldValue(i18nFieldName, I18nUtils.getCurrentLanguageEnum());
    }

    /**
     * 前景图
     */
    @ApiModelProperty("箱子前景图")
    private String foregroundPicture;

    @ApiModelProperty("箱子背景图")
    private String backgroundPicture;

    @ApiModelProperty("箱子包含皮肤数量")
    private Integer skinTotal;

    @ApiModelProperty("箱子总概率")
    private BigDecimal probability;

    @ApiModelProperty("箱子类型")
    private Long caseCategoryId;

    @ApiModelProperty("箱子是否在售")
    private Boolean isSale;

    @ApiModelProperty("箱子是否删除")
    private boolean isDeleted;
}
