package com.steamgo1.csgoskinadmin.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel("用户饰品禁用")
public class UserDisableQueryDTO {
    @ApiModelProperty("分页大小")
    @NotBlank(message = "分页大小必填")
    private int size;

    @ApiModelProperty("页数")
    @NotBlank(message = "页数必填")
    private int page;

    @ApiModelProperty("用户ID")
    private String userId;

    @ApiModelProperty("用户名")
    private String name;

    @ApiModelProperty("状态")
    private Boolean isEffective;

}
