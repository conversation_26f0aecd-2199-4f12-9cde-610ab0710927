package com.steamgo1.csgoskinadmin.dto;

import com.steamgo1.csgoskincommon.utils.I18nField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("添加Roll房参数")
public class RollHomeAddDTO {
    @ApiModelProperty("房间名称")
    private String name;

    @ApiModelProperty("房间名称（多语言）")
    private I18nField i18nFieldName;

    @ApiModelProperty("房间类型")
    private Integer rollHomeType;

    @ApiModelProperty("开奖方式")
    private Integer rollHomeLotteryMethod;

    @ApiModelProperty("最高人数")
    private Integer maxPeople;

    @ApiModelProperty("开奖时间(yyyy-MM-dd HH:mm:ss)")
    private Date lotteryTime;

    @ApiModelProperty("参入阈值")
    private BigDecimal consumeThreshold;

    @ApiModelProperty("阈值类型")
    private Integer thresholdType;

    @ApiModelProperty("房间密码")
    private String password;

    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("备注（多语言）")
    private I18nField i18nFieldRemarks;

    @ApiModelProperty("是否推荐")
    private Boolean isRecommend = false;


    @ApiModelProperty("包含皮肤")
    private List<RollHomeSkinDTO> rollHomeSkin;

    @Data
    public static class RollHomeSkinDTO {
        @ApiModelProperty("皮肤ID")
        private Long skinId;

        @ApiModelProperty("排序(数字越大皮肤优先级越高)")
        private Integer grade;
    }
}
