package com.steamgo1.csgoskinadmin.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("红包添加参数")
public class RedPacketAddParamDTO {
    @ApiModelProperty("红包名称")
    @NotBlank(message = "红包名称必填")
    private String name;

    @ApiModelProperty("红包名称（多语言）")
    private I18nField i18nFieldName;

    @ApiModelProperty("口令")
    @NotBlank(message = "红包口令必填")
    private String code;

    @ApiModelProperty("金币数量")
    @NotBlank(message = "金币数量必填")
    private BigDecimal coin;

    @ApiModelProperty("到期时间")
    @NotBlank(message = "到期时间必填")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    @ApiModelProperty("总份数")
    @NotBlank(message = "红包口令必填")
    private Integer total;

    @ApiModelProperty("消费阈值")
    @NotBlank(message = "消费阈值不能为空")
    private BigDecimal consumeThreshold;

    @ApiModelProperty("阈值类型")
    @NotBlank(message = "消费阈值类型不能为空")
    private Integer consumeThresholdType;

    @ApiModelProperty("红包类型")
    @NotBlank(message = "红包类型不能为空")
    private Integer type;

    @ApiModelProperty("红包模式")
    @NotBlank(message = "红包模式不能为空")
    private Integer method;

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getName() {
        return I18nUtils.getI18nFieldValue(i18nFieldName, I18nUtils.getCurrentLanguageEnum());
    }
}