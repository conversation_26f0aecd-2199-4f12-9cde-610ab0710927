package com.steamgo1.csgoskinadmin.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Data
@ApiModel("更新用户信息")
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserInfoUpdateVO {
    @ApiModelProperty("用户ID")
    private Long id;

    @ApiModelProperty("昵称")
    @Length(min = 2, max = 10, message = "长度2到10字符")
    private String nickname;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("类型")
    private Integer type;
}
