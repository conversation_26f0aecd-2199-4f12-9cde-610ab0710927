package com.steamgo1.csgoskinadmin.dto;


import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

/**
 * 注册后台用户信息
 */
@Data
public class AdminUserDTO {
    @NotBlank(message = "用户名不能为空")
    @Length(min = 3, max = 8)
    String username;
    @NotBlank(message = "密码不能为空")
    @Length(min = 6, max = 16)
    String password;
    @Length(min = 11, max = 11)
    String phone;
    @Email
    String email;
}
