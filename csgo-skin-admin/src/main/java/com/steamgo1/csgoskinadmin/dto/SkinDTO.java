package com.steamgo1.csgoskinadmin.dto;

import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;

@Data
@ApiModel("添加饰品参数")
public class SkinDTO {
    @ApiModelProperty("饰品名")
    @Length(min = 3, max = 32)
    private String name;

    @ApiModelProperty("饰品名称（多语言）")
    private I18nField i18nFieldName;

    @ApiModelProperty("饰品唯一英文")
    private String englishName;

    @ApiModelProperty("在售数量")
    private Integer onSaleNum;

    @ApiModelProperty("饰品原型ID")
    private Long prototypeId;

    @ApiModelProperty("饰品预设稀有度ID")
    private Long skinRarityId;

    @ApiModelProperty("饰品外观级别ID")
    private Long skinExteriorId;

    @ApiModelProperty("饰品质量级别ID")
    private Long skinQualityId;

    @ApiModelProperty("饰品图片")
    private String picture;

    @ApiModelProperty("参考价格")
    private BigDecimal referencePrice;

    @ApiModelProperty("价格")
    private BigDecimal price;

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getName() {
        return I18nUtils.getI18nFieldValue(i18nFieldName, I18nUtils.getCurrentLanguageEnum());
    }
}
