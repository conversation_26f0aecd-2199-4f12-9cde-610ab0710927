package com.steamgo1.csgoskinadmin.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 国际化演示搜索DTO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel("国际化演示搜索条件")
public class I18nDemoSearchDTO {
    
    @ApiModelProperty("搜索关键词")
    private String keyword;
    
    @ApiModelProperty("搜索语言（zh/en），不指定则使用当前语言")
    private String language;
    
    @ApiModelProperty("是否在所有语言中搜索")
    private Boolean searchAllLanguages = false;
    
    @ApiModelProperty("是否精确匹配")
    private Boolean exactMatch = false;
    
    @ApiModelProperty("开始时间（格式：yyyy-MM-dd HH:mm:ss）")
    private String startTime;
    
    @ApiModelProperty("结束时间（格式：yyyy-MM-dd HH:mm:ss）")
    private String endTime;
    
    @ApiModelProperty("页码（从0开始）")
    private Integer page = 0;
    
    @ApiModelProperty("每页大小")
    private Integer size = 20;
    
    @ApiModelProperty("排序字段")
    private String sortBy = "createTime";
    
    @ApiModelProperty("排序方向（asc/desc）")
    private String sortDirection = "desc";
}
