package com.steamgo1.csgoskinadmin.dto;

import com.steamgo1.csgoskincommon.entity.SkinEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

//    @ApiModel("箱子中饰品信息")
@Data
public class CaseSkinDTO {
    @ApiModelProperty("饰品箱子Id")
    private Long caseSkinId;
    @ApiModelProperty("皮肤ID")
    private Long skinId;
    @ApiModelProperty("中奖概率")
    private BigDecimal probability;
    @ApiModelProperty("自定义饰品颜色")
    private Long skinRarityColorId;
    @ApiModelProperty("中奖数量统计")
    private Integer winningTotal;
    @ApiModelProperty("排序(数字越大皮肤优先级越高)")
    private Integer grade;
    @ApiModelProperty("参考价格")
    private BigDecimal referencePrice;
    @ApiModelProperty("参考价格")
    private SkinEntity skinInfo;
}
