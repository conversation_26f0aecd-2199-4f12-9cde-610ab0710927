package com.steamgo1.csgoskinapi.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinapi.dto.PageQueryDTO;
import com.steamgo1.csgoskinapi.dto.RollHomeJoinDTO;
import com.steamgo1.csgoskinapi.dto.RollHomeQueryDTO;
import com.steamgo1.csgoskinapi.enums.WxMsgType;
import com.steamgo1.csgoskinapi.service.AsyncTaskService;
import com.steamgo1.csgoskinapi.service.RollHomeService;
import com.steamgo1.csgoskinapi.service.SkinService;
import com.steamgo1.csgoskinapi.service.UserService;
import com.steamgo1.csgoskinapi.utils.SecurityUtils;
import com.steamgo1.csgoskinapi.vo.*;
import com.steamgo1.csgoskincommon.dao.*;
import com.steamgo1.csgoskincommon.entity.*;
import com.steamgo1.csgoskincommon.entity.enums.*;
import com.steamgo1.csgoskincommon.enums.LotterySource;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageMethod;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageType;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.service.AlgorithmService;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import com.steamgo1.csgoskincommon.vo.RollHomeFilterParamVO;
import com.steamgo1.csgoskincommon.vo.websocket.MessageVO;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.*;


@Service
@Slf4j
@Transactional
public class RollHomeServiceImpl implements RollHomeService {
    @Value("${roll.min}")
    private Integer minRoll;

    @Value("${roll.max}")
    private Integer maxRoll;

    @Value("${spring.redis.prefix.roll-home}")
    private String redisRollHomePrefix;


    @Autowired
    private RollHomeRepository rollHomeRepository;

    @Autowired
    private RollHomeUserRepository rollHomeUserRepository;

    @Autowired
    private RollHomeSkinRepository rollHomeSkinRepository;

    @Autowired
    private SkinService skinService;

    @Autowired
    private UserService userService;

    @Autowired
    private AlgorithmService algorithmService;

    @Autowired
    private UserPackageRepository userPackageRepository;

    @Autowired
    private AsyncTaskService asyncTaskService;

    @Autowired
    private UserCoinRecordRepository userCoinRecordRepository;


    @Override
    public Page<RollHomeBaseInfoVO> queryRollHomes(RollHomeQueryDTO rollHomeQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(rollHomeQueryDTO.getPage(), rollHomeQueryDTO.getSize(), sort);
        Page<RollHomeEntity> rollHomeEntities = null;
        if (rollHomeQueryDTO.getIsEnd() == null) {
            rollHomeEntities = rollHomeRepository.findAll(pageable);
        } else if (rollHomeQueryDTO.getIsEnd()) {
            rollHomeEntities = rollHomeRepository.findByStatus(RollHomeStatus.END, pageable);
        } else {
            rollHomeEntities = rollHomeRepository.findByStatusIn(RollHomeStatus.unFinishStatus(), pageable);
        }
        if (rollHomeEntities == null) {
            return null;
        }

        List<RollHomeBaseInfoVO> rollHomeBaseInfoVOList = new ArrayList<>();
        for (RollHomeEntity rollHomeEntity : rollHomeEntities.getContent()) {
            String redisRollHomeBaseInfoKey = redisRollHomePrefix + ":BASEINFO:" + rollHomeEntity.getId();
            RollHomeBaseInfoVO rollHomeBaseInfoVO = RedisUtils.get(redisRollHomeBaseInfoKey, RollHomeBaseInfoVO.class);
            if (rollHomeBaseInfoVO != null) {
                rollHomeBaseInfoVOList.add(rollHomeBaseInfoVO);
                continue;
            }
            List<RollHomeUserEntity> rollHomeUserEntityList = rollHomeUserRepository.findByRollHome(rollHomeEntity);
            List<RollHomeSkinEntity> rollHomeSkinEntityList = rollHomeSkinRepository.findByRollHome(rollHomeEntity);
            rollHomeBaseInfoVO = new RollHomeBaseInfoVO();
            rollHomeBaseInfoVO.setId(rollHomeEntity.getId());
            rollHomeBaseInfoVO.setName(rollHomeEntity.getName());
            rollHomeBaseInfoVO.setI18nFieldName(rollHomeEntity.getI18nFieldName());
            rollHomeBaseInfoVO.setStatus(rollHomeEntity.getStatus().getCode());
            rollHomeBaseInfoVO.setStatusValue(rollHomeEntity.getStatus().getValue());
            rollHomeBaseInfoVO.setLotteryTime(rollHomeEntity.getLotteryTime());
            rollHomeBaseInfoVO.setConsumeThreshold(rollHomeEntity.getConsumeThreshold());
            rollHomeBaseInfoVO.setTotalParticipants(rollHomeUserEntityList.size());
            rollHomeBaseInfoVO.setTotalSkin(rollHomeSkinEntityList.size());
            rollHomeBaseInfoVO.setRollHomeType(rollHomeEntity.getRollHomeType().getCode());
            rollHomeBaseInfoVO.setRollHomeTypeValue(rollHomeEntity.getRollHomeType().getValue());
            rollHomeBaseInfoVO.setRollHomeLotteryMethod(rollHomeEntity.getRollHomeLotteryMethod().getCode());
            rollHomeBaseInfoVO.setRollHomeLotteryMethodValue(rollHomeEntity.getRollHomeLotteryMethod().getValue());
            rollHomeBaseInfoVO.setStatus(rollHomeEntity.getStatus().getCode());
            rollHomeBaseInfoVO.setStatusValue(rollHomeEntity.getStatus().getValue());
            rollHomeBaseInfoVO.setMaxPeople(rollHomeEntity.getMaxPeople());
            rollHomeBaseInfoVO.setRemarks(rollHomeEntity.getRemarks());
            rollHomeBaseInfoVO.setI18nFieldRemarks(rollHomeEntity.getI18nFieldRemarks());
            rollHomeBaseInfoVO.setPassword(rollHomeEntity.getPassword() == null ? false : true);
            List<SkinInfoVO> skinInfoVOList = new ArrayList<>();
            Integer skinCount = 0;
            for (RollHomeSkinEntity rollHomeSkinEntity : rollHomeSkinEntityList) {
                skinInfoVOList.add(skinService.querySkinInfoById(rollHomeSkinEntity.getSkin().getId()));
                skinCount++;
                if (skinCount == 3) {
                    break;
                }
            }
            rollHomeBaseInfoVO.setSkinInfo(skinInfoVOList);
            // 写入缓存
            RedisUtils.save(redisRollHomeBaseInfoKey, rollHomeBaseInfoVO);
            rollHomeBaseInfoVOList.add(rollHomeBaseInfoVO);
        }
        return new PageImpl<>(rollHomeBaseInfoVOList, rollHomeEntities.getPageable(), rollHomeEntities.getTotalElements());
    }

    @Override
    public Page<RollHomeBaseInfoVO> queryMyRollHomes(RollHomeQueryDTO rollHomeQueryDTO) {
        Long userId = SecurityUtils.getUserId();
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(rollHomeQueryDTO.getPage(), rollHomeQueryDTO.getSize(), sort);
        Page<RollHomeUserEntity> rollHomeUserEntityPage = null;
        if (rollHomeQueryDTO.getIsEnd() == null) {
            rollHomeUserEntityPage = rollHomeUserRepository.findByUserId(userId, pageable);
        } else if (rollHomeQueryDTO.getIsEnd()) {
            rollHomeUserEntityPage = rollHomeUserRepository.findByUserIdAndRollHomeStatus(userId, RollHomeStatus.END, pageable);
        } else {
            rollHomeUserEntityPage = rollHomeUserRepository.findByUserIdAndRollHomeStatusIn(userId, RollHomeStatus.unFinishStatus(), pageable);
        }
        if (rollHomeUserEntityPage == null) {
            return null;
        }

        List<RollHomeBaseInfoVO> rollHomeBaseInfoVOList = new ArrayList<>();
        for (RollHomeUserEntity rollHomeUserEntity : rollHomeUserEntityPage.getContent()) {
            RollHomeEntity rollHomeEntity = rollHomeUserEntity.getRollHome();
            String redisRollHomeBaseInfoKey = redisRollHomePrefix + ":BASEINFO:" + rollHomeEntity.getId();
            RollHomeBaseInfoVO rollHomeBaseInfoVO = RedisUtils.get(redisRollHomeBaseInfoKey, RollHomeBaseInfoVO.class);
            if (rollHomeBaseInfoVO != null) {
                rollHomeBaseInfoVOList.add(rollHomeBaseInfoVO);
                continue;
            }
            List<RollHomeUserEntity> rollHomeUserEntityList = rollHomeUserRepository.findByRollHomeOrderById(rollHomeEntity);
            List<RollHomeSkinEntity> rollHomeSkinEntityList = rollHomeSkinRepository.findByRollHomeAndIsDeletedIsFalseOrderById(rollHomeEntity);
            rollHomeBaseInfoVO = new RollHomeBaseInfoVO();
            rollHomeBaseInfoVO.setId(rollHomeEntity.getId());
            rollHomeBaseInfoVO.setName(rollHomeEntity.getName());
            rollHomeBaseInfoVO.setI18nFieldName(rollHomeEntity.getI18nFieldName());
            rollHomeBaseInfoVO.setStatus(rollHomeEntity.getStatus().getCode());
            rollHomeBaseInfoVO.setStatusValue(rollHomeEntity.getStatus().getValue());
            rollHomeBaseInfoVO.setLotteryTime(rollHomeEntity.getLotteryTime());
            rollHomeBaseInfoVO.setConsumeThreshold(rollHomeEntity.getConsumeThreshold());
            rollHomeBaseInfoVO.setTotalParticipants(rollHomeUserEntityList.size());
            rollHomeBaseInfoVO.setTotalSkin(rollHomeSkinEntityList.size());
            rollHomeBaseInfoVO.setRemarks(rollHomeEntity.getRemarks());
            rollHomeBaseInfoVO.setI18nFieldRemarks(rollHomeEntity.getI18nFieldRemarks());
            rollHomeBaseInfoVO.setPassword(rollHomeEntity.getPassword() == null ? false : true);
            List<SkinInfoVO> skinInfoVOList = new ArrayList<>();
            Integer skinCount = 0;
            for (RollHomeSkinEntity rollHomeSkinEntity : rollHomeSkinEntityList) {
                skinInfoVOList.add(skinService.querySkinInfoById(rollHomeSkinEntity.getSkin().getId()));
                skinCount++;
                if (skinCount == 3) {
                    break;
                }
            }
//            rollHomeBaseInfoVO.setSkinCount(skinInfoVOList.size());
            rollHomeBaseInfoVO.setSkinInfo(skinInfoVOList);
            // 写入缓存
            RedisUtils.save(redisRollHomeBaseInfoKey, rollHomeBaseInfoVO);
            rollHomeBaseInfoVOList.add(rollHomeBaseInfoVO);
        }
        return new PageImpl<>(rollHomeBaseInfoVOList, rollHomeUserEntityPage.getPageable(), rollHomeUserEntityPage.getTotalElements());
    }

    @Override
    public RollHomeVO queryRollHomeById(Long rollHomeId) {
        String redisRollHomeKey = redisRollHomePrefix + ":" + rollHomeId;
        RollHomeVO rollHomeVO = RedisUtils.get(redisRollHomeKey, RollHomeVO.class);
        if (rollHomeVO != null) {
            return rollHomeVO;
        }
        RollHomeEntity rollHomeEntity = rollHomeRepository.findById(rollHomeId).get();
        if (rollHomeEntity == null) {
            log.error("roll房不存在：{}", rollHomeId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.roll_home.room_not_found"));
        }
        List<RollHomeUserEntity> rollHomeUserEntityList = rollHomeUserRepository.findByRollHomeOrderById(rollHomeEntity);
        List<RollHomeSkinEntity> rollHomeSkinEntityList = rollHomeSkinRepository.findByRollHomeAndIsDeletedIsFalseOrderById(rollHomeEntity);
        rollHomeVO = new RollHomeVO();
        rollHomeVO.setId(rollHomeId);
        rollHomeVO.setName(rollHomeEntity.getName());
        rollHomeVO.setI18nFieldName(rollHomeEntity.getI18nFieldName());
        rollHomeVO.setRollHomeType(rollHomeEntity.getRollHomeType().getCode());
        rollHomeVO.setRollHomeTypeName(rollHomeEntity.getRollHomeType().getValue());
        rollHomeVO.setRollHomeLotteryMethod(rollHomeEntity.getRollHomeLotteryMethod().getCode());
        rollHomeVO.setRollHomeLotteryMethodName(rollHomeEntity.getRollHomeLotteryMethod().getValue());
        rollHomeVO.setSecretHash(rollHomeEntity.getStatus().equals(RollHomeStatus.END) ? rollHomeEntity.getSecretHash() : I18nUtils.getMessage("roll.home.secret.after.lottery"));
        rollHomeVO.setSecretSalt(rollHomeEntity.getStatus().equals(RollHomeStatus.END) ? rollHomeEntity.getSecretSalt() : I18nUtils.getMessage("roll.home.secret.after.lottery"));
        rollHomeVO.setPublicHash(rollHomeEntity.getPublicHash());
        rollHomeVO.setClientSeed(rollHomeEntity.getClientSeed());
        rollHomeVO.setStatus(rollHomeEntity.getStatus().getCode());
        rollHomeVO.setStatusValue(rollHomeEntity.getStatus().getValue());
        rollHomeVO.setLotteryTime(rollHomeEntity.getLotteryTime());
        rollHomeVO.setMaxPeople(rollHomeEntity.getMaxPeople());
        rollHomeVO.setRemarks(rollHomeEntity.getRemarks());
        rollHomeVO.setI18nFieldRemarks(rollHomeEntity.getI18nFieldRemarks());
        rollHomeVO.setConsumeThreshold(rollHomeEntity.getConsumeThreshold());
        rollHomeVO.setPassword(rollHomeEntity.getPassword() == null ? false : true);
        List<RollHomeUserVO> rollHomeUserVOList = new ArrayList<>();
        for (RollHomeUserEntity rollHomeUserEntity : rollHomeUserEntityList) {
            RollHomeUserVO rollHomeUserVO = new RollHomeUserVO();
            rollHomeUserVO.setUserInfo(userService.queryUserPublicInfo(rollHomeUserEntity.getUser().getId()));
            rollHomeUserVO.setMinRoll(rollHomeUserEntity.getMinRoll());
            rollHomeUserVO.setMaxRoll(rollHomeUserEntity.getMaxRoll());
            rollHomeUserVOList.add(rollHomeUserVO);
        }


        List<RollHomeSkinVO> rollHomeSkinVOList = new ArrayList<>();
        for (RollHomeSkinEntity rollHomeSkinEntity : rollHomeSkinEntityList) {
            RollHomeSkinVO rollHomeSkinVO = new RollHomeSkinVO();
            rollHomeSkinVO.setRoll(rollHomeSkinEntity.getRoll());
            rollHomeSkinVO.setRounds(rollHomeSkinEntity.getRounds());
            rollHomeSkinVO.setSkinInfo(skinService.querySkinInfoById(rollHomeSkinEntity.getSkin().getId()));
            rollHomeSkinVO.setUserInfo(rollHomeSkinEntity.getUser() == null ? null : userService.queryUserPublicInfo(rollHomeSkinEntity.getUser().getId()));
            rollHomeSkinVOList.add(rollHomeSkinVO);
        }
        rollHomeVO.setRollHomeUser(rollHomeUserVOList);
        rollHomeVO.setRollHomeSkin(rollHomeSkinVOList);
        // 写入缓存
        RedisUtils.save(redisRollHomeKey, rollHomeVO);
        return rollHomeVO;
    }

    @Override
    public Page<RollHomeSkinVO> queryRollHomeSkins(Long rollHomeId, PageQueryDTO pageQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(pageQueryDTO.getPage(), pageQueryDTO.getSize(), sort);
        Page<RollHomeSkinEntity> rollHomeSkins = rollHomeSkinRepository.findByRollHomeIdAndIsDeletedIsFalseOrderById(rollHomeId, pageable);
        List<RollHomeSkinVO> rollHomeSkinVOList = new ArrayList<>();
        for (RollHomeSkinEntity rollHomeSkinEntity : rollHomeSkins.getContent()) {
            RollHomeSkinVO rollHomeSkinVO = new RollHomeSkinVO();
            rollHomeSkinVO.setRoll(rollHomeSkinEntity.getRoll());
            rollHomeSkinVO.setRounds(rollHomeSkinEntity.getRounds());
            rollHomeSkinVO.setSkinInfo(skinService.querySkinInfoById(rollHomeSkinEntity.getSkin().getId()));
            rollHomeSkinVO.setUserInfo(rollHomeSkinEntity.getUser() == null ? null : userService.queryUserPublicInfo(rollHomeSkinEntity.getUser().getId()));
            rollHomeSkinVOList.add(rollHomeSkinVO);
        }
        return new PageImpl<>(rollHomeSkinVOList, rollHomeSkins.getPageable(), rollHomeSkins.getTotalElements());
    }

    @Override
    public Page<RollHomeUserVO> queryRollHomeUsers(Long rollHomeId, PageQueryDTO pageQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(pageQueryDTO.getPage(), pageQueryDTO.getSize(), sort);
        Page<RollHomeUserEntity> rollHomeUserEntityPage = rollHomeUserRepository.findByRollHomeIdOrderById(rollHomeId, pageable);
        List<RollHomeUserVO> rollHomeUserVOList = new ArrayList<>();
        for (RollHomeUserEntity rollHomeUserEntity : rollHomeUserEntityPage.getContent()) {
            RollHomeUserVO rollHomeUserVO = new RollHomeUserVO();
            UserEntity user = rollHomeUserEntity.getUser();
            UserPubicInfoVO userPubicInfoVO = new UserPubicInfoVO();
            userPubicInfoVO.setId(user.getId());
            userPubicInfoVO.setAvatar(user.getAvatar());
            userPubicInfoVO.setNickname(user.getNickname());
            userPubicInfoVO.setIsRobot(user.getType().equals(UserType.ROBOT) ? true : false);
            userPubicInfoVO.setType(user.getType().getCode());
            rollHomeUserVO.setUserInfo(userPubicInfoVO);
            rollHomeUserVO.setMinRoll(rollHomeUserEntity.getMinRoll());
            rollHomeUserVO.setMaxRoll(rollHomeUserEntity.getMaxRoll());
            rollHomeUserVOList.add(rollHomeUserVO);
        }
        return new PageImpl<>(rollHomeUserVOList, rollHomeUserEntityPage.getPageable(), rollHomeUserEntityPage.getTotalElements());
    }

    @Override
    public RollHomeVO joinRollHome(RollHomeJoinDTO rollHomeJoinDTO) {
        Long rollHomeId = rollHomeJoinDTO.getRollHomeId();
        String redisRollHomeKey = redisRollHomePrefix + ":" + rollHomeId;
        UserEntity user = userService.getUser();
        RollHomeEntity rollHomeEntity = rollHomeRepository.findById(rollHomeId).get();
        if (rollHomeEntity == null) {
            log.error("roll房不存在：{}", rollHomeId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.roll_home.room_not_found"));
        }
        //检查密码
        if (rollHomeEntity.getPassword() != null) {
            if (rollHomeJoinDTO.getPassword() == null || !rollHomeEntity.getPassword().equals(rollHomeJoinDTO.getPassword())) {
                log.error("roll房密码不正确：{}", rollHomeId);
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.roll_home.password_incorrect"));
            }
        }
        // 检查用户是否有权限参与 TODO
        // 是否新手房间
        log.info("用户: {} 参加Roll房: {} 类型： {} 模式： {}", user.getId(), rollHomeId, rollHomeEntity.getRollHomeType().getValue(), rollHomeEntity.getRollHomeLotteryMethod().getValue());
        if (user.getType().equals(UserType.ACTUAL)) {
            switch (rollHomeEntity.getRollHomeType()) {
                case NOVICE:
                    if (rollHomeUserRepository.existsByRollHomeRollHomeTypeAndUserId(RollHomeType.NOVICE, user.getId())) {
                        log.error("用户: {} 新手房间，已参与过：{}", user.getId(), rollHomeId);
                        // todo 国际化
                        throw new CsgoSkinException(I18nUtils.getMessage("exception.roll_home.novice_already_joined"));
                    }
                    break;
                case DAY:
                    switch (rollHomeEntity.getThresholdType()) {
                        case CHARGE:
                            BigDecimal userCharge = userCoinRecordRepository.userDayCharge(user.getId());
                            userCharge = userCharge == null ? BigDecimal.ZERO : userCharge;
                            log.info("用户：{} 本日充值：{}", user.getId(), userCharge);
                            if (userCharge.compareTo(rollHomeEntity.getConsumeThreshold()) == -1) {
                                log.error("roll房 用户当日充值未满足要求：{}", rollHomeId);
                                // todo 国际化
                                throw new CsgoSkinException(I18nUtils.getMessage("exception.roll_home.insufficient_charge", new Object[]{rollHomeEntity.getConsumeThreshold().subtract(userCharge).toString()}));
                            }
                            break;
                        case CONSUME:
                            BigDecimal userConsume = userCoinRecordRepository.userDayConsume(user.getId());
                            userConsume = userConsume == null ? BigDecimal.ZERO : userConsume;
                            log.info("用户：{} 本日消费：{}", user.getId(), userConsume);
                            if (userConsume.compareTo(rollHomeEntity.getConsumeThreshold()) == -1) {
                                log.error("roll房 用户当日消费未满足要求：{}", rollHomeId);
                                // todo 国际化
                                throw new CsgoSkinException(I18nUtils.getMessage("exception.roll_home.insufficient_consume", new Object[]{rollHomeEntity.getConsumeThreshold().subtract(userConsume).toString()}));
                            }
                    }
                    break;
                case WEEK:
                    switch (rollHomeEntity.getThresholdType()) {
                        case CHARGE:
                            BigDecimal userCharge = userCoinRecordRepository.userWeekCharge(user.getId());
                            userCharge = userCharge == null ? BigDecimal.ZERO : userCharge;
                            log.info("用户：{} 本日充值：{}", user.getId(), userCharge);
                            if (userCharge.compareTo(rollHomeEntity.getConsumeThreshold()) == -1) {
                                log.error("roll房 用户本周充值未满足要求：{}", rollHomeId);
                                // todo 国际化
                                throw new CsgoSkinException(I18nUtils.getMessage("exception.roll_home.insufficient_charge", new Object[]{rollHomeEntity.getConsumeThreshold().subtract(userCharge).toString()}));
                            }
                            break;
                        case CONSUME:
                            BigDecimal userConsume = userCoinRecordRepository.userWeekConsume(user.getId());
                            userConsume = userConsume == null ? BigDecimal.ZERO : userConsume;
                            log.info("用户：{} 本周消费：{}", user.getId(), userConsume);
                            if (userConsume.compareTo(rollHomeEntity.getConsumeThreshold()) == -1) {
                                log.error("roll房 用户本周消费未满足要求：{}", rollHomeId);
                                // todo 国际化
                                throw new CsgoSkinException(I18nUtils.getMessage("exception.roll_home.insufficient_consume", new Object[]{rollHomeEntity.getConsumeThreshold().subtract(userConsume).toString()}));
                            }
                    }
                    break;
                case MONTH:
                    switch (rollHomeEntity.getThresholdType()) {
                        case CHARGE:
                            BigDecimal userCharge = userCoinRecordRepository.userMonthCharge(user.getId());
                            userCharge = userCharge == null ? BigDecimal.ZERO : userCharge;
                            log.info("用户：{} 本月充值：{}", user.getId(), userCharge);
                            if (userCharge.compareTo(rollHomeEntity.getConsumeThreshold()) == -1) {
                                log.error("roll房 用户当月充值未满足要求：{}", rollHomeId);
                                // todo 国际化
                                throw new CsgoSkinException(I18nUtils.getMessage("exception.roll_home.insufficient_charge", new Object[]{rollHomeEntity.getConsumeThreshold().subtract(userCharge).toString()}));
                            }
                            break;
                        case CONSUME:
                            BigDecimal userConsume = userCoinRecordRepository.userMonthConsume(user.getId());
                            userConsume = userConsume == null ? BigDecimal.ZERO : userConsume;
                            log.info("用户：{} 本月消费：{}", user.getId(), userConsume);
                            if (userConsume.compareTo(rollHomeEntity.getConsumeThreshold()) == -1) {
                                log.error("roll房 用户当月消费未满足要求：{}", rollHomeId);
                                // todo 国际化
                                throw new CsgoSkinException(I18nUtils.getMessage("exception.roll_home.insufficient_consume", new Object[]{rollHomeEntity.getConsumeThreshold().subtract(userConsume).toString()}));
                            }
                    }
            }
        }
        // 是否已结束
        if (rollHomeEntity.getStatus().equals(RollHomeStatus.END)) {
            log.error("用户: {} 已经结束：{}", user.getId(), rollHomeId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.roll_home.already_ended"));
        }
        // 是否已参加
        if (rollHomeUserRepository.existsByRollHomeAndUser(rollHomeEntity, user)) {
            log.error("用户: {} 已经参与过Roll房：{}", user.getId(), rollHomeId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.roll_home.user_already_joined"));
        }
        // 是否已满
        if (rollHomeEntity.getMaxPeople() != null && rollHomeUserRepository.countByRollHome(rollHomeEntity) >= rollHomeEntity.getMaxPeople()) {
            log.error("用户: {} Roll房已满：{}", user.getId(), rollHomeId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.roll_home.full"));
        }
        // 更新参与用户的roll点
        // roll房信息入缓存
        RollHomeVO rollHomeVO = new RollHomeVO();
        rollHomeVO.setId(rollHomeId);
        rollHomeVO.setName(rollHomeEntity.getName());
        rollHomeVO.setI18nFieldName(rollHomeEntity.getI18nFieldName());
        rollHomeVO.setRollHomeType(rollHomeEntity.getRollHomeType().getCode());
        rollHomeVO.setRollHomeTypeName(rollHomeEntity.getRollHomeType().getValue());
        rollHomeVO.setRollHomeLotteryMethod(rollHomeEntity.getRollHomeLotteryMethod().getCode());
        rollHomeVO.setRollHomeLotteryMethodName(rollHomeEntity.getRollHomeLotteryMethod().getValue());
        rollHomeVO.setSecretHash(I18nUtils.getMessage("roll.home.secret.after.lottery"));
        rollHomeVO.setSecretSalt(I18nUtils.getMessage("roll.home.secret.after.lottery"));
        rollHomeVO.setPublicHash(rollHomeEntity.getPublicHash());
        rollHomeVO.setClientSeed(rollHomeEntity.getClientSeed());
        rollHomeVO.setLotteryTime(rollHomeEntity.getLotteryTime());
        rollHomeVO.setMaxPeople(rollHomeEntity.getMaxPeople());
        rollHomeVO.setRemarks(rollHomeEntity.getRemarks());
        rollHomeVO.setI18nFieldRemarks(rollHomeEntity.getI18nFieldRemarks());
        rollHomeVO.setConsumeThreshold(rollHomeEntity.getConsumeThreshold());
        rollHomeVO.setPassword(rollHomeEntity.getPassword() == null ? false : true);
        List<RollHomeUserEntity> rollHomeUserEntityList = rollHomeUserRepository.findByRollHome(rollHomeEntity);
        List<RollHomeSkinEntity> rollHomeSkinEntityList = rollHomeSkinRepository.findByRollHome(rollHomeEntity);
        Integer interval = maxRoll / (rollHomeUserEntityList.size() + 1);
        Integer roll = minRoll;
        List<RollHomeUserVO> rollHomeUserVOList = new ArrayList<>();
        // 修改已参与用户的roll点
        for (RollHomeUserEntity rollHomeUserEntity : rollHomeUserEntityList) {
            rollHomeUserEntity.setMinRoll(roll);
            roll += interval;
            rollHomeUserEntity.setMaxRoll(roll - 1);
            RollHomeUserVO rollHomeUserVO = new RollHomeUserVO();
            rollHomeUserVO.setUserInfo(userService.queryUserPublicInfo(rollHomeUserEntity.getUser().getId()));
            rollHomeUserVO.setMinRoll(rollHomeUserEntity.getMinRoll());
            rollHomeUserVO.setMaxRoll(rollHomeUserEntity.getMaxRoll());
            rollHomeUserVOList.add(rollHomeUserVO);
        }
        // 新增的用户
        RollHomeUserEntity rollHomeUserEntity = new RollHomeUserEntity();
        rollHomeUserEntity.setUser(user);
        rollHomeUserEntity.setRollHome(rollHomeEntity);
        rollHomeUserEntity.setMinRoll(roll);
        rollHomeUserEntity.setMaxRoll(maxRoll);

        RollHomeUserVO rollHomeUserVO = new RollHomeUserVO();
        rollHomeUserVO.setUserInfo(userService.queryUserPublicInfo(rollHomeUserEntity.getUser().getId()));
        rollHomeUserVO.setMinRoll(rollHomeUserEntity.getMinRoll());
        rollHomeUserVO.setMaxRoll(rollHomeUserEntity.getMaxRoll());
        rollHomeUserVOList.add(rollHomeUserVO);
        rollHomeUserRepository.saveAll(rollHomeUserEntityList);
        rollHomeUserRepository.save(rollHomeUserEntity);
        if (rollHomeEntity.getRollHomeLotteryMethod().equals(RollHomeLotteryMethod.FIXPEOPLE)) {
            if (rollHomeUserRepository.countByRollHome(rollHomeEntity) >= rollHomeEntity.getMaxPeople()) {
                rollHomeEntity.setStatus(RollHomeStatus.START);
                lotteryRollHome(rollHomeEntity.getId());
                rollHomeEntity = rollHomeRepository.save(rollHomeEntity);
            }
        }
        List<RollHomeSkinVO> rollHomeSkinVOList = new ArrayList<>();
        for (RollHomeSkinEntity rollHomeSkinEntity : rollHomeSkinEntityList) {
            RollHomeSkinVO rollHomeSkinVO = new RollHomeSkinVO();
            rollHomeSkinVO.setRoll(rollHomeSkinEntity.getRoll());
            rollHomeSkinVO.setRounds(rollHomeSkinEntity.getRounds());
            rollHomeSkinVO.setSkinInfo(skinService.querySkinInfoById(rollHomeSkinEntity.getSkin().getId()));
            rollHomeSkinVO.setUserInfo(rollHomeSkinEntity.getUser() == null ? null : userService.queryUserPublicInfo(rollHomeSkinEntity.getUser().getId()));
            rollHomeSkinVOList.add(rollHomeSkinVO);
        }
        rollHomeVO.setRollHomeUser(rollHomeUserVOList);
        rollHomeVO.setRollHomeSkin(rollHomeSkinVOList);
        rollHomeVO.setStatus(rollHomeEntity.getStatus().getCode());
        rollHomeVO.setStatusValue(rollHomeEntity.getStatus().getValue());
        RedisUtils.save(redisRollHomeKey, rollHomeVO);
        // 通知用户
        MessageVO messageResultVO = new MessageVO(WebSocketMessageMethod.SEND_ALL, WebSocketMessageType.ROLL_HOME_JOIN, rollHomeVO);
        asyncTaskService.sendWebSocketMessageToAll(JSONObject.toJSONString(messageResultVO));
        return rollHomeVO;
    }

    @Async("async-executor-guava")
    @Override
    public RollHomeVO lotteryRollHome(Long rollHomeId) {
        log.debug("开始roll房抽奖+++++++++++++++++++++++++++++++++++++++++++++++++");
        String redisRollHomeKey = redisRollHomePrefix + ":" + rollHomeId;
        RollHomeEntity rollHomeEntity = rollHomeRepository.findById(rollHomeId).get();
        RollHomeVO rollHomeVO = new RollHomeVO();
        rollHomeVO.setId(rollHomeId);
        rollHomeVO.setName(rollHomeEntity.getName());
        rollHomeVO.setI18nFieldName(rollHomeEntity.getI18nFieldName());
        rollHomeVO.setRollHomeType(rollHomeEntity.getRollHomeType().getCode());
        rollHomeVO.setRollHomeTypeName(rollHomeEntity.getRollHomeType().getValue());
        rollHomeVO.setRollHomeLotteryMethod(rollHomeEntity.getRollHomeLotteryMethod().getCode());
        rollHomeVO.setRollHomeLotteryMethodName(rollHomeEntity.getRollHomeLotteryMethod().getValue());
        rollHomeVO.setSecretHash(rollHomeEntity.getSecretHash());
        rollHomeVO.setSecretSalt(rollHomeEntity.getSecretSalt());
        rollHomeVO.setPublicHash(rollHomeEntity.getPublicHash());
        rollHomeVO.setClientSeed(rollHomeEntity.getClientSeed());
        rollHomeVO.setLotteryTime(rollHomeEntity.getLotteryTime());
        rollHomeVO.setMaxPeople(rollHomeEntity.getMaxPeople());
        rollHomeVO.setPassword(rollHomeEntity.getPassword() == null ? false : true);
        rollHomeVO.setConsumeThreshold(rollHomeEntity.getConsumeThreshold());
        if (!rollHomeUserRepository.existsByRollHomeId(rollHomeId)) {
            //log.warn("roll房:{}流产，无人参加", rollHomeId);
            log.debug("roll房:{}流产，无人参加", rollHomeId);
            return null;
        }
        List<UserPackageEntity> userPackageEntityList = new ArrayList<>();
        // 先发指定饰品
        Set<Long> cheatUserIds = new HashSet<>();
        List<RollHomeSkinEntity> cheatRollHomeSkinList = rollHomeSkinRepository.findByRollHomeIdAndUserIsNotNull(rollHomeId);
        for (RollHomeSkinEntity rollHomeSkinEntity : cheatRollHomeSkinList) {
            log.info("指定中奖用户： {} 饰品： {}", rollHomeSkinEntity.getUser().getNickname(), rollHomeSkinEntity.getSkin().getName());
            cheatUserIds.add(rollHomeSkinEntity.getUser().getId());
            UserPackageEntity userPackage = new UserPackageEntity();
            userPackage.setUser(rollHomeSkinEntity.getUser());
            userPackage.setSkin(rollHomeSkinEntity.getSkin());
            userPackage.setPrice(rollHomeSkinEntity.getSkin().getPrice());
            userPackage.setDiamond(rollHomeSkinEntity.getSkin().getDiamond());
            userPackage.setSource(UserPackageSource.ROLL_HOME);
            userPackage.setIsReceived(false);
            userPackage.setIsSelled(false);
            userPackageEntityList.add(userPackage);
            if (!StringUtil.isNullOrEmpty(rollHomeSkinEntity.getUser().getWxOpenid())) {
                asyncTaskService.sendMessageToWechat(WxMsgType.ROLLHOME, rollHomeSkinEntity.getUser(), "恭喜Roll房中奖,快去官网查看吧");
            }
        }
        // 发放未指定未指定的奖品
        List<RollHomeSkinEntity> unCheatRollhomeSkinEntityList = rollHomeSkinRepository.findByRollHomeIdAndUserIsNull(rollHomeId);
        List<RollHomeUserEntity> unCheatRollHomeUserEntityList;
        if (cheatUserIds.size() == 0) {
            unCheatRollHomeUserEntityList = rollHomeUserRepository.findByRollHome(rollHomeEntity);
        } else {
            unCheatRollHomeUserEntityList = rollHomeUserRepository.findByRollHomeIdAndUserIdNotIn(rollHomeId, cheatUserIds);
        }
        log.info("cheatUserIds: {}", cheatUserIds.size());
        log.info("人数：{} 饰品数：{}", unCheatRollHomeUserEntityList.size(), unCheatRollhomeSkinEntityList.size());
        // 20250309不重复逻辑
        if (unCheatRollHomeUserEntityList.size() >= unCheatRollhomeSkinEntityList.size()) {
            Collections.shuffle(unCheatRollHomeUserEntityList);
            for (int i = 0; i < unCheatRollhomeSkinEntityList.size(); i++) {
                RollHomeSkinEntity rollHomeSkin = unCheatRollhomeSkinEntityList.get(i);
                RollHomeUserEntity rollHomeUserEntity = unCheatRollHomeUserEntityList.get(i);
                rollHomeSkin.setUser(rollHomeUserEntity.getUser());
                log.info("中奖用户： {} 饰品： {}", rollHomeUserEntity.getUser().getNickname(), rollHomeSkin.getSkin().getName());
                // 放入用户背包
                // 放入背包
                UserPackageEntity userPackage = new UserPackageEntity();
                userPackage.setUser(rollHomeUserEntity.getUser());
                userPackage.setSkin(rollHomeSkin.getSkin());
                userPackage.setPrice(rollHomeSkin.getSkin().getPrice());
                userPackage.setDiamond(rollHomeSkin.getSkin().getDiamond());
                userPackage.setSource(UserPackageSource.ROLL_HOME);
                userPackage.setIsReceived(false);
                userPackage.setIsSelled(false);
                userPackageEntityList.add(userPackage);
                if (!StringUtil.isNullOrEmpty(rollHomeUserEntity.getUser().getWxOpenid())) {
                    asyncTaskService.sendMessageToWechat(WxMsgType.ROLLHOME, rollHomeUserEntity.getUser(), "恭喜Roll房中奖,快去官网查看吧");
                }
            }
        } else {
            // 如果人数比奖品少，把奖品重新排没人发一件然后其它的删除
            Collections.shuffle(unCheatRollhomeSkinEntityList);
            for (int i = 0; i < unCheatRollhomeSkinEntityList.size(); i++) {
                RollHomeSkinEntity rollHomeSkin = unCheatRollhomeSkinEntityList.get(i);
                if (i < unCheatRollHomeUserEntityList.size()) {
                    RollHomeUserEntity rollHomeUserEntity = unCheatRollHomeUserEntityList.get(i);
                    rollHomeSkin.setUser(rollHomeUserEntity.getUser());
                    log.info("中奖用户： {} 饰品： {}", rollHomeUserEntity.getUser().getNickname(), rollHomeSkin.getSkin().getName());
                    // 放入用户背包
                    // 放入背包
                    UserPackageEntity userPackage = new UserPackageEntity();
                    userPackage.setUser(rollHomeUserEntity.getUser());
                    userPackage.setSkin(rollHomeSkin.getSkin());
                    userPackage.setPrice(rollHomeSkin.getSkin().getPrice());
                    userPackage.setDiamond(rollHomeSkin.getSkin().getDiamond());
                    userPackage.setSource(UserPackageSource.ROLL_HOME);
                    userPackage.setIsReceived(false);
                    userPackage.setIsSelled(false);
                    userPackageEntityList.add(userPackage);
                    if (!StringUtil.isNullOrEmpty(rollHomeUserEntity.getUser().getWxOpenid())) {
                        asyncTaskService.sendMessageToWechat(WxMsgType.ROLLHOME, rollHomeUserEntity.getUser(), "恭喜Roll房中奖,快去官网查看吧");
                    }
                } else {
                    // 超出的奖品删除
                    log.info("roll房奖品多余人数,删除奖品： {}", rollHomeSkin.getSkin().getName());
                    rollHomeSkin.setIsDeleted(true);
                    rollHomeSkinRepository.save(rollHomeSkin);
                }

            }
        }

        /**
         // 如果人数比饰品多, 把人重新排
         if(unCheatRollHomeUserEntityList.size() >= unCheatRollhomeSkinEntityList.size()){
         Collections.shuffle(unCheatRollHomeUserEntityList);
         for(int i=0;i<unCheatRollhomeSkinEntityList.size();i++){
         RollHomeSkinEntity rollHomeSkin = unCheatRollhomeSkinEntityList.get(i);
         RollHomeUserEntity rollHomeUserEntity = unCheatRollHomeUserEntityList.get(i);
         rollHomeSkin.setUser(rollHomeUserEntity.getUser());
         log.info("中奖用户： {} 饰品： {}", rollHomeUserEntity.getUser().getNickname(), rollHomeSkin.getSkin().getName());
         // 放入用户背包
         // 放入背包
         UserPackageEntity userPackage = new UserPackageEntity();
         userPackage.setUser(rollHomeUserEntity.getUser());
         userPackage.setSkin(rollHomeSkin.getSkin());
         userPackage.setPrice(rollHomeSkin.getSkin().getPrice());
         userPackage.setDiamond(rollHomeSkin.getSkin().getDiamond());
         userPackage.setSource(UserPackageSource.ROLL_HOME);
         userPackage.setIsReceived(false);
         userPackage.setIsSelled(false);
         userPackageEntityList.add(userPackage);
         if(!StringUtil.isNullOrEmpty(rollHomeUserEntity.getUser().getWxOpenid())){
         asyncTaskService.sendMessageToWechat(WxMsgType.ROLLHOME, rollHomeUserEntity.getUser(), "恭喜Roll房中奖,快去官网查看吧");
         }
         }
         }else {
         // 如果人数比饰品少, 把人重排一下,发一轮
         Collections.shuffle(unCheatRollHomeUserEntityList);
         for(int i=0;i<unCheatRollHomeUserEntityList.size();i++){
         RollHomeSkinEntity rollHomeSkin = unCheatRollhomeSkinEntityList.get(i);
         RollHomeUserEntity rollHomeUserEntity = unCheatRollHomeUserEntityList.get(i);
         rollHomeSkin.setUser(rollHomeUserEntity.getUser());
         log.info("中奖用户： {} 饰品： {}", rollHomeUserEntity.getUser().getNickname(), rollHomeSkin.getSkin().getName());
         // 放入用户背包
         // 放入背包
         UserPackageEntity userPackage = new UserPackageEntity();
         userPackage.setUser(rollHomeUserEntity.getUser());
         userPackage.setSkin(rollHomeSkin.getSkin());
         userPackage.setPrice(rollHomeSkin.getSkin().getPrice());
         userPackage.setDiamond(rollHomeSkin.getSkin().getDiamond());
         userPackage.setSource(UserPackageSource.ROLL_HOME);
         userPackage.setIsReceived(false);
         userPackage.setIsSelled(false);
         userPackageEntityList.add(userPackage);
         if(!StringUtil.isNullOrEmpty(rollHomeUserEntity.getUser().getWxOpenid())){
         asyncTaskService.sendMessageToWechat(WxMsgType.ROLLHOME, rollHomeUserEntity.getUser(), "恭喜Roll房中奖,快去官网查看吧");
         }
         }
         // 剩余的饰品按roll逻辑发
         Integer rounds = 0;
         for (int i=unCheatRollHomeUserEntityList.size();i<unCheatRollhomeSkinEntityList.size();i++) {
         RollHomeSkinEntity rollHomeSkin = unCheatRollhomeSkinEntityList.get(i);
         rounds += 1;
         Integer roll = algorithmService.getRoll(rollHomeEntity.getSecretHash(), rollHomeEntity.getClientSeed(), rounds);
         log.info("roll: {}", roll );
         log.info("unCheatRollHomeUserEntityList {}", unCheatRollHomeUserEntityList);
         for(RollHomeUserEntity rollHomeUserEntity: unCheatRollHomeUserEntityList){
         log.info("{}, {}",rollHomeUserEntity.getMaxRoll(), rollHomeUserEntity.getMaxRoll());
         }
         RollHomeUserEntity rollHomeUserEntity = unCheatRollHomeUserEntityList.stream().filter(item -> item.getMinRoll() <= roll && item.getMaxRoll() >= roll).findFirst().orElse(unCheatRollHomeUserEntityList.get(0));
         rollHomeSkin.setUser(rollHomeUserEntity.getUser());
         rollHomeSkin.setRounds(rounds);
         rollHomeSkin.setRoll(roll);
         log.info("中奖用户： {} 饰品： {}", rollHomeUserEntity.getUser().getNickname(), rollHomeSkin.getSkin().getName());
         // 放入用户背包
         // 放入背包
         UserPackageEntity userPackage = new UserPackageEntity();
         userPackage.setUser(rollHomeUserEntity.getUser());
         userPackage.setSkin(rollHomeSkin.getSkin());
         userPackage.setPrice(rollHomeSkin.getSkin().getPrice());
         userPackage.setDiamond(rollHomeSkin.getSkin().getDiamond());
         userPackage.setSource(UserPackageSource.ROLL_HOME);
         userPackage.setIsReceived(false);
         userPackage.setIsSelled(false);
         userPackageEntityList.add(userPackage);
         if(!StringUtil.isNullOrEmpty(rollHomeUserEntity.getUser().getWxOpenid())){
         asyncTaskService.sendMessageToWechat(WxMsgType.ROLLHOME, rollHomeUserEntity.getUser(), "恭喜Roll房中奖,快去官网查看吧");
         }
         }

         }**/
        /**roll点开奖逻辑 20230814
         Integer rounds = 0;
         for (RollHomeSkinEntity rollHomeSkin : rollHomeSkinEntityList) {
         rounds += 1;
         Integer roll = algorithmService.getRoll(rollHomeEntity.getSecretHash(), rollHomeEntity.getClientSeed(), rounds);
         RollHomeUserEntity rollHomeUserEntity = rollHomeUserEntityList.stream().filter(item -> item.getMinRoll() <= roll && item.getMaxRoll() >= roll).findFirst().get();
         rollHomeSkin.setUser(rollHomeUserEntity.getUser());
         rollHomeSkin.setRounds(rounds);
         rollHomeSkin.setRoll(roll);
         log.info("中奖用户： {} 饰品： {}", rollHomeUserEntity.getUser().getNickname(), rollHomeSkin.getSkin().getName());
         // 放入用户背包
         // 放入背包
         UserPackageEntity userPackage = new UserPackageEntity();
         userPackage.setUser(rollHomeUserEntity.getUser());
         userPackage.setSkin(rollHomeSkin.getSkin());
         userPackage.setPrice(rollHomeSkin.getSkin().getPrice());
         userPackage.setSource(UserPackageSource.ROLL_HOME);
         userPackage.setIsReceived(false);
         userPackage.setIsSelled(false);
         userPackageEntityList.add(userPackage);
         }
         **/
        // 保存到数据库
        rollHomeEntity.setStatus(RollHomeStatus.END);
        rollHomeRepository.save(rollHomeEntity);
        rollHomeSkinRepository.saveAll(unCheatRollhomeSkinEntityList);
        rollHomeUserRepository.saveAll(unCheatRollHomeUserEntityList);
        userPackageRepository.saveAll(userPackageEntityList);

        List<RollHomeSkinVO> rollHomeSkinVOList = new ArrayList<>();
        List<RollHomeSkinEntity> rollHomeSkinEntityList = rollHomeSkinRepository.findByRollHomeAndIsDeletedIsFalseOrderById(rollHomeEntity);
        List<RollHomeUserEntity> rollHomeUserEntityList = rollHomeUserRepository.findByRollHome(rollHomeEntity);
        for (RollHomeSkinEntity rollHomeSkinEntity : rollHomeSkinEntityList) {
            RollHomeSkinVO rollHomeSkinVO = new RollHomeSkinVO();
            rollHomeSkinVO.setRoll(rollHomeSkinEntity.getRoll());
            rollHomeSkinVO.setRounds(rollHomeSkinEntity.getRounds());
            rollHomeSkinVO.setSkinInfo(skinService.querySkinInfoById(rollHomeSkinEntity.getSkin().getId()));
            rollHomeSkinVO.setUserInfo(rollHomeSkinEntity.getUser() == null ? null : userService.queryUserPublicInfo(rollHomeSkinEntity.getUser().getId()));
            rollHomeSkinVOList.add(rollHomeSkinVO);
            // 全局通知
            if (rollHomeSkinEntity.getUser() != null) {
                UserLotteryResultOfWebsocketVO userLotteryResultOfWebsocketVO = new UserLotteryResultOfWebsocketVO() {{
                    setUserInfo(userService.queryUserPublicInfo(rollHomeSkinEntity.getUser().getId()));
                    setSkinInfoVO(skinService.querySkinInfoById(rollHomeSkinEntity.getSkin().getId()));
                    setSource(LotterySource.ROLL_HOME.getCode());
                    setSourceValue(LotterySource.ROLL_HOME.getValue());
                }};
                MessageVO messageResultVO = new MessageVO(WebSocketMessageMethod.SEND_ALL, WebSocketMessageType.LOTTERY_RESULT, userLotteryResultOfWebsocketVO);
                asyncTaskService.sendWebSocketMessageToAll(JSONObject.toJSONString(messageResultVO));
            }
        }

        List<RollHomeUserVO> rollHomeUserVOList = new ArrayList<>();
        for (RollHomeUserEntity rollHomeUserEntity : rollHomeUserEntityList) {
            RollHomeUserVO rollHomeUserVO = new RollHomeUserVO();
            rollHomeUserVO.setUserInfo(userService.queryUserPublicInfo(rollHomeUserEntity.getUser().getId()));
            rollHomeUserVO.setMinRoll(rollHomeUserEntity.getMinRoll());
            rollHomeUserVO.setMaxRoll(rollHomeUserEntity.getMaxRoll());
            rollHomeUserVOList.add(rollHomeUserVO);
        }
        rollHomeVO.setRollHomeSkin(rollHomeSkinVOList);
        rollHomeVO.setRollHomeUser(rollHomeUserVOList);
        rollHomeVO.setStatus(rollHomeEntity.getStatus().getCode());
        rollHomeVO.setStatusValue(rollHomeEntity.getStatus().getValue());
        RedisUtils.save(redisRollHomeKey, rollHomeVO);
        MessageVO messageResultVO = new MessageVO(WebSocketMessageMethod.SEND_ALL, WebSocketMessageType.ROLL_HOME_RESULT, rollHomeVO);
        asyncTaskService.sendWebSocketMessageToAll(JSONObject.toJSONString(messageResultVO));
        return rollHomeVO;
    }

    @Override
    @Async("async-executor-guava")
    public void removeRollHomeRobotSession(RollHomeEntity rollHomeEntity) {
        try {
            if (rollHomeEntity == null || rollHomeEntity.getId() == null) {
                return;
            }
            switch (rollHomeEntity.getRollHomeType()) {
                case DAY:
                    return;
                case WEEK:
                    //如果是周roll房，就查询roll房中的机器人
                    List<RollHomeUserEntity> robots = rollHomeUserRepository.findByRollHomeAndUserType(rollHomeEntity, UserType.CHEAT_ROBOT);
                    if (robots == null || robots.isEmpty()) {
                        return;
                    }
                    robots.forEach(rollHomeUserEntity -> {
                        String key = redisRollHomePrefix + ":" + UserType.CHEAT_ROBOT + ":" + rollHomeUserEntity.getUser().getId();
                        RedisUtils.delete(key);
                    });
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error("removeRollHomeRobotSession删除失败:{}", e.getMessage(), e);
        }
    }

    @Override
    public RollHomeFilterParamVO getRollHomeFilterParam() {
        return new RollHomeFilterParamVO() {{
            setRollHomeType(RollHomeType.toList());
            setRollHomeLotteryMethod(RollHomeLotteryMethod.toList());
            setStatus(RollHomeStatus.toList());
        }};
    }

    @Override
    public List<UserLotteryResultVO> queryRollHomeUserLotteryResult() {
        Sort sort = Sort.by(Sort.Direction.DESC, "updateTime");
        Pageable pageable = PageRequest.of(0, 10, sort);
        Page<RollHomeSkinEntity> rollHomeSkinEntityList = rollHomeSkinRepository.findByUserIsNotNull(pageable);
        List<UserLotteryResultVO> userLotteryResultVOList = new ArrayList<>();
        for (RollHomeSkinEntity rollHomeSkinEntity : rollHomeSkinEntityList.getContent()) {
            userLotteryResultVOList.add(new UserLotteryResultVO() {{
                setUserInfo(userService.queryUserPublicInfo(rollHomeSkinEntity.getUser().getId()));
                setSkinInfoVO(skinService.querySkinInfoById(rollHomeSkinEntity.getSkin().getId()));
                setSource(UserPackageSource.ROLL_HOME.getCode());
            }});
        }
        return userLotteryResultVOList;
    }

    @Override
    public List<RollHomeBaseInfoVO> queryRecommendRollHome() {
        Sort sort = Sort.by(Sort.Direction.DESC, "updateTime");
        Pageable pageable = PageRequest.of(0, 10, sort);
        Page<RollHomeEntity> rollHomeEntityPage = rollHomeRepository.findByStatusInAndIsRecommendIsTrue(RollHomeStatus.unFinishStatus(), pageable);

        List<RollHomeBaseInfoVO> rollHomeBaseInfoVOList = new ArrayList<>();
        for (RollHomeEntity rollHomeEntity : rollHomeEntityPage.getContent()) {
            String redisRollHomeBaseInfoKey = redisRollHomePrefix + ":BASEINFO:" + rollHomeEntity.getId();
            RollHomeBaseInfoVO rollHomeBaseInfoVO = RedisUtils.get(redisRollHomeBaseInfoKey, RollHomeBaseInfoVO.class);
            if (rollHomeBaseInfoVO != null) {
                rollHomeBaseInfoVOList.add(rollHomeBaseInfoVO);
                continue;
            }
            List<RollHomeUserEntity> rollHomeUserEntityList = rollHomeUserRepository.findByRollHome(rollHomeEntity);
            List<RollHomeSkinEntity> rollHomeSkinEntityList = rollHomeSkinRepository.findByRollHome(rollHomeEntity);
            rollHomeBaseInfoVO = new RollHomeBaseInfoVO();
            rollHomeBaseInfoVO.setId(rollHomeEntity.getId());
            rollHomeBaseInfoVO.setName(rollHomeEntity.getName());
            rollHomeBaseInfoVO.setI18nFieldName(rollHomeEntity.getI18nFieldName());
            rollHomeBaseInfoVO.setStatus(rollHomeEntity.getStatus().getCode());
            rollHomeBaseInfoVO.setStatusValue(rollHomeEntity.getStatus().getValue());
            rollHomeBaseInfoVO.setLotteryTime(rollHomeEntity.getLotteryTime());
            rollHomeBaseInfoVO.setConsumeThreshold(rollHomeEntity.getConsumeThreshold());
            rollHomeBaseInfoVO.setTotalParticipants(rollHomeUserEntityList.size());
            rollHomeBaseInfoVO.setTotalSkin(rollHomeSkinEntityList.size());
            rollHomeBaseInfoVO.setRollHomeType(rollHomeEntity.getRollHomeType().getCode());
            rollHomeBaseInfoVO.setRollHomeTypeValue(rollHomeEntity.getRollHomeType().getValue());
            rollHomeBaseInfoVO.setRollHomeLotteryMethod(rollHomeEntity.getRollHomeLotteryMethod().getCode());
            rollHomeBaseInfoVO.setRollHomeLotteryMethodValue(rollHomeEntity.getRollHomeLotteryMethod().getValue());
            rollHomeBaseInfoVO.setStatus(rollHomeEntity.getStatus().getCode());
            rollHomeBaseInfoVO.setStatusValue(rollHomeEntity.getStatus().getValue());
            List<SkinInfoVO> skinInfoVOList = new ArrayList<>();
            for (RollHomeSkinEntity rollHomeSkinEntity : rollHomeSkinEntityList) {
                skinInfoVOList.add(skinService.querySkinInfoById(rollHomeSkinEntity.getSkin().getId()));
            }
            rollHomeBaseInfoVO.setSkinInfo(skinInfoVOList);
            // 写入缓存
            RedisUtils.save(redisRollHomeBaseInfoKey, rollHomeBaseInfoVO);
            rollHomeBaseInfoVOList.add(rollHomeBaseInfoVO);
        }
        return rollHomeBaseInfoVOList;
    }

}
