package com.steamgo1.csgoskinapi.service;

import com.steamgo1.csgoskinapi.dto.BattleHomeCreateDTO;
import com.steamgo1.csgoskinapi.dto.BattleHomeQueryDTO;
import com.steamgo1.csgoskinapi.vo.BattleHomeBaseInfoVO;
import com.steamgo1.csgoskinapi.vo.BattleHomeQueryParamVO;
import com.steamgo1.csgoskinapi.vo.BattleHomeVO;
import com.steamgo1.csgoskinapi.vo.BattleTop10VO;
import org.springframework.data.domain.Page;

import java.util.List;

public interface BattleHomeService {
    BattleHomeQueryParamVO getBattleHomequeryParam();

    void joinBattleHome(Long battleHomeId);

    void robotJoinBattleHome(Long battleHomeId);

    BattleHomeVO createBattleHome(BattleHomeCreateDTO battleHomeCreateDTO);

    Page<BattleHomeBaseInfoVO> queryBattleHomeBaseInfo(BattleHomeQueryDTO battleHomeQueryDTO);

    Page<BattleHomeBaseInfoVO> queryMyBattleHomeBaseInfo(BattleHomeQueryDTO battleHomeQueryDTO);

    BattleHomeVO queryBattleHomeBaseInfoById(Long battleHomeId);

    /**
     * 查询昨日top10
     *
     * @return
     */
    List<BattleTop10VO> queryBattleTop10VO();

    /**
     * 机器人对战
     *
     * @param userId
     */
    void robotBattle(Long userId);
}
