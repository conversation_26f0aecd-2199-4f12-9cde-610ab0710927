package com.steamgo1.csgoskinapi.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinapi.dto.LotteryResultDTO;
import com.steamgo1.csgoskinapi.dto.PageQueryDTO;
import com.steamgo1.csgoskinapi.dto.UserLotteryPercentageDTO;
import com.steamgo1.csgoskinapi.dto.UserPercentageLotteryRecordQueryDTO;
import com.steamgo1.csgoskinapi.service.*;
import com.steamgo1.csgoskinapi.utils.SecurityUtils;
import com.steamgo1.csgoskinapi.vo.*;
import com.steamgo1.csgoskincommon.dao.*;
import com.steamgo1.csgoskincommon.entity.*;
import com.steamgo1.csgoskincommon.entity.enums.*;
import com.steamgo1.csgoskincommon.enums.LotterySource;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageMethod;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageType;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import com.steamgo1.csgoskincommon.utils.RateLimiterUtils;
import com.steamgo1.csgoskincommon.vo.websocket.MessageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

@Slf4j
@Service
@Transactional
public class PercentageServiceImpl implements PercentageService {
    private Integer SEED_MIN_ROLL = 1;
    private Integer SEED_MAX_ROLL = 1000000;
    private Integer SEED_TIMES = 10000;

    @Autowired
    private LotteryService lotteryService;

    @Autowired
    private SkinRepository skinRepository;

    @Autowired
    private SkinService skinService;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private UserPackageRepository userPackageRepository;

    @Autowired
    private RecordPercentageRepository recordPercentageRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private SiteService siteService;

    @Autowired
    private UserCoinRecordRepository userCoinRecordRepository;

    @Autowired
    private AsyncTaskService asyncTaskService;

    @Autowired
    private ActivityService activityService;


    @Override
    public PercentageResultVO percentageLottery(UserLotteryPercentageDTO userLotteryPercentageDTO) {
        Long userId = SecurityUtils.getUserId();
        if (!RateLimiterUtils.tryAcquire(userId)) {
            log.warn("用户追梦被限流: {}", userId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.operation.too.frequent"));
        }
        UserProfileEntity userProfile = userProfileRepository.findByUserId(userId);
        if (userProfile.getUser().getType().equals(UserType.ACTUAL) && userProfile.getUser().getIsFirstCharge()) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.percentage.need.charge"));
        }
        SkinEntity skin = skinRepository.findById(userLotteryPercentageDTO.getSkinId()).get();
        if (!skin.getEnablePercentage()) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.percentage.skin.not.supported"));
        }
        if (userLotteryPercentageDTO.getMax() <= userLotteryPercentageDTO.getMin()) {
            log.error("百分比抽奖错误userId: {} 最大值小于最小值", userId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.invalid.parameters"));
        }
        // 百分比抽奖限制只能从一边拉取
        if (userLotteryPercentageDTO.getMax() != 100 && userLotteryPercentageDTO.getMin() != 0) {
            log.error("百分比抽奖错误userId: {} 最大值小于最小值", userId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.invalid.parameters"));
        }
        if (skin == null) {
            log.error("百分比抽奖错误skinId： {} userId: {}", userLotteryPercentageDTO.getSkinId(), userId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.skin.not.exist.or.offline"));
        }
        Integer minRoll = userLotteryPercentageDTO.getMin() * SEED_TIMES;
        Integer maxRoll = userLotteryPercentageDTO.getMax() * SEED_TIMES;
        // 计算当前需要的金币
        BigDecimal consumeCoin = skin.getDiamond().multiply(BigDecimal.valueOf(userLotteryPercentageDTO.getMax() - userLotteryPercentageDTO.getMin())).multiply(siteService.getExchangeRate().getPercentagePremium()).divide(BigDecimal.valueOf(100).multiply(siteService.getExchangeRate().getCoinToDiamond()), 2, BigDecimal.ROUND_CEILING);
//        BigDecimal coin = consumeCoin.multiply(percentage).setScale(2, BigDecimal.ROUND_UP);
        log.info("饰品钻石：{}, 最小 {} 最大 {}需要消耗金币：{}, 用户余额：{}", skin.getDiamond(), userLotteryPercentageDTO.getMin(), userLotteryPercentageDTO.getMax(), consumeCoin, userProfile.getCoin());
        // 金币消耗 TODOq
        if (consumeCoin.compareTo(BigDecimal.valueOf(3)) == -1) {
            log.info("金币消耗不小与3金币");
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.percentage.min.coin.requirement"));
        }
        if (userProfile.getCoin().compareTo(consumeCoin) == -1) {
            log.info("金币不足");
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.insufficient.coins"));
        }
        userProfile.setCoin(userProfile.getCoin().subtract(consumeCoin));
        // 经验 TODO
        userProfile.setExperience(userProfile.getExperience() + 10);
        userProfileRepository.save(userProfile);
        // 记录算法开箱绑定记录 TODO
        PercentageResultVO percentageResultVO = new PercentageResultVO();
        LotteryResultDTO lotteryResultDTO = lotteryService.lottery();
        PercentageUserRecordEntity recordPercentage = new PercentageUserRecordEntity();
        recordPercentage.setUser(userProfile.getUser());
        recordPercentage.setRoll(lotteryResultDTO.getRoll());
        recordPercentage.setRounds(lotteryResultDTO.getAlgorithmData().getRounds() - 1);
        recordPercentage.setSkin(skin);
        recordPercentage.setMaxRoll(maxRoll);
        recordPercentage.setMinRoll(minRoll);
        recordPercentage.setConsumeCoin(consumeCoin);
        recordPercentage.setAlgorithmData(lotteryResultDTO.getAlgorithmData());
        if (minRoll <= lotteryResultDTO.getRoll() && maxRoll >= lotteryResultDTO.getRoll()) {
            percentageResultVO.setIsWin(true);
            recordPercentage.setIsWin(true);
            // 放入背包
            UserPackageEntity userPackage = new UserPackageEntity();
            userPackage.setUser(userProfile.getUser());
            userPackage.setSkin(skin);
            userPackage.setPrice(skin.getPrice());
            userPackage.setDiamond(skin.getDiamond());
            userPackage.setSource(UserPackageSource.PERCENTAGE);
            userPackage.setIsReceived(false);
            userPackage.setIsSelled(false);
            userPackage = userPackageRepository.save(userPackage);
            percentageResultVO.setPackageId(userPackage.getId());
            percentageResultVO.setSkinInfo(skinService.querySkinInfoById(skin.getId()));
        } else {
            // 放入背包一个随机物品
            SkinEntity randomSkin = skinRepository.findRandomSkin();
            if (randomSkin != null) {
                UserPackageEntity userPackage = new UserPackageEntity();
                userPackage.setUser(userProfile.getUser());
                userPackage.setSkin(randomSkin);
                userPackage.setDiamond(randomSkin.getDiamond());
                userPackage.setPrice(randomSkin.getPrice());
                userPackage.setSource(UserPackageSource.PERCENTAGE);
                userPackage.setIsReceived(false);
                userPackage = userPackageRepository.save(userPackage);
                percentageResultVO.setPackageId(userPackage.getId());
                percentageResultVO.setSkinInfo(skinService.querySkinInfoById(randomSkin.getId()));
            }
        }
        recordPercentage = recordPercentageRepository.save(recordPercentage);
        // 记录金币流水
        UserCoinRecordEntity userCoinRecordEntity = new UserCoinRecordEntity();
        userCoinRecordEntity.setUser(userProfile.getUser());
        userCoinRecordEntity.setSource(UserCoinChangeSource.PERCENTAGE);
        userCoinRecordEntity.setSourceId(recordPercentage.getId());
        userCoinRecordEntity.setAmount(consumeCoin);
        userCoinRecordEntity.setAfterAmount(userProfile.getCoin());
        userCoinRecordEntity.setIsPositive(false);
        userCoinRecordRepository.save(userCoinRecordEntity);
        percentageResultVO.setRoll(lotteryResultDTO.getRoll());
        CardVO cardVO = activityService.joinCardCollect(userId, lotteryResultDTO.getRoll(), CardCollectSource.PERCENTAGE);
        if (cardVO == null) {
            cardVO = activityService.joinExtraBonus(userId, CardCollectSource.PERCENTAGE, consumeCoin);
        }
        percentageResultVO.setCard(cardVO);
        // 全局通知
        UserLotteryResultOfWebsocketVO userLotteryResultOfWebsocketVO = new UserLotteryResultOfWebsocketVO() {{
            setUserInfo(userService.queryUserPublicInfo(userId));
            setSkinInfoVO(percentageResultVO.getSkinInfo());
            setSource(LotterySource.PERCENTAGE.getCode());
            setSourceValue(LotterySource.PERCENTAGE.getValue());
        }};
        MessageVO messageResultVO = new MessageVO(WebSocketMessageMethod.SEND_ALL, WebSocketMessageType.LOTTERY_RESULT, userLotteryResultOfWebsocketVO);
        asyncTaskService.sendWebSocketMessageToAll(JSONObject.toJSONString(messageResultVO));
        userService.DailyActivity(userId, DailyActivityType.PERCENTAGE);
        activityService.queryUserConsumePlan(userId, consumeCoin);
        return percentageResultVO;
    }

    @Override
    public Page<PercetageUserLotteryRecordVO> queryPercetageUserLotteryRecord(UserPercentageLotteryRecordQueryDTO userPercentageLotteryRecordQueryDTO, Long userId) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(userPercentageLotteryRecordQueryDTO.getPage(), userPercentageLotteryRecordQueryDTO.getSize(), sort);
        Page<PercentageUserRecordEntity> percentageUserRecordEntityPage = recordPercentageRepository.findByUserId(userId, pageable);
        List<PercetageUserLotteryRecordVO> percetageUserLotteryRecordVOList = new ArrayList<>();
        for (PercentageUserRecordEntity percentageUserRecordEntity : percentageUserRecordEntityPage.getContent()) {
            PercetageUserLotteryRecordVO percetageUserLotteryRecordVO = new PercetageUserLotteryRecordVO();
            percetageUserLotteryRecordVO.setIsWin(percentageUserRecordEntity.getIsWin());
            percetageUserLotteryRecordVO.setRounds(percentageUserRecordEntity.getRounds());
            percetageUserLotteryRecordVO.setRoll(percentageUserRecordEntity.getRoll());
            percetageUserLotteryRecordVO.setMaxRoll(percentageUserRecordEntity.getMaxRoll());
            percetageUserLotteryRecordVO.setMinRoll(percentageUserRecordEntity.getMinRoll());
            AlgorithmDataEntity algorithmDataEntity = percentageUserRecordEntity.getAlgorithmData();
            UserAlgorithmDataFullVO userAlgorithmDataFullVO = new UserAlgorithmDataFullVO();
            if (!algorithmDataEntity.getIsUsed()) {
                userAlgorithmDataFullVO.setSecretHash(algorithmDataEntity.getSecretHash());
                userAlgorithmDataFullVO.setSecretSalt(algorithmDataEntity.getSecretSalt());
            }
            userAlgorithmDataFullVO.setPublicHash(algorithmDataEntity.getPublicHash());
            userAlgorithmDataFullVO.setClientSeed(algorithmDataEntity.getClientSeed());
            userAlgorithmDataFullVO.setRounds(algorithmDataEntity.getRounds());
            percetageUserLotteryRecordVO.setUserAlgorithmData(userAlgorithmDataFullVO);
            SkinInfoVO skinInfoVO = skinService.querySkinInfoById(percentageUserRecordEntity.getSkin().getId());
            percetageUserLotteryRecordVO.setSkinInfo(skinInfoVO);
            percetageUserLotteryRecordVOList.add(percetageUserLotteryRecordVO);
        }
        return new PageImpl<>(percetageUserLotteryRecordVOList, percentageUserRecordEntityPage.getPageable(), percentageUserRecordEntityPage.getTotalPages());
    }

    @Override
    public Page<PercentageLotteryRecordVO> queryPercentageLotteryRecord(PageQueryDTO pageQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(pageQueryDTO.getPage(), pageQueryDTO.getSize(), sort);
        Page<PercentageUserRecordEntity> percentageUserRecordEntityPage = recordPercentageRepository.findAll(pageable);
        List<PercentageLotteryRecordVO> percentageLotteryRecordVOList = new ArrayList<>();
        for (PercentageUserRecordEntity percentageUserRecordEntity : percentageUserRecordEntityPage.getContent()) {
            PercentageLotteryRecordVO percentageLotteryRecordVO = new PercentageLotteryRecordVO();
            percentageLotteryRecordVO.setCreateTime(percentageUserRecordEntity.getCreateTime());
            percentageLotteryRecordVO.setSkinInfo(skinService.querySkinInfoById(percentageUserRecordEntity.getSkin().getId()));
            percentageLotteryRecordVO.setMaxRoll(percentageUserRecordEntity.getMaxRoll());
            percentageLotteryRecordVO.setMinRoll(percentageUserRecordEntity.getMinRoll());
            percentageLotteryRecordVO.setRoll(percentageUserRecordEntity.getRoll());
            percentageLotteryRecordVO.setUserInfo(userService.queryUserPublicInfo(percentageUserRecordEntity.getUser().getId()));
            percentageLotteryRecordVO.setIsWin(percentageUserRecordEntity.getIsWin());
            percentageLotteryRecordVOList.add(percentageLotteryRecordVO);
        }
        return new PageImpl<>(percentageLotteryRecordVOList, percentageUserRecordEntityPage.getPageable(), percentageUserRecordEntityPage.getTotalPages());
    }

    @Override
    public List<UserLotteryResultVO> queryPercentageUserLotteryResult() {
        Sort sort = Sort.by(Sort.Direction.DESC, "updateTime");
        Pageable pageable = PageRequest.of(0, 10, sort);
        Page<PercentageUserRecordEntity> percentageUserRecordEntityPage = recordPercentageRepository.findByIsWinIsTrue(pageable);
        List<UserLotteryResultVO> userLotteryResultVOList = new ArrayList<>();
        for (PercentageUserRecordEntity percentageUserRecordEntity : percentageUserRecordEntityPage.getContent()) {
            userLotteryResultVOList.add(new UserLotteryResultVO() {{
                setSkinInfoVO(skinService.querySkinInfoById(percentageUserRecordEntity.getSkin().getId()));
                setUserInfo(userService.queryUserPublicInfo(percentageUserRecordEntity.getUser().getId()));
                setSource(UserPackageSource.PERCENTAGE.getCode());
            }});
        }
        return userLotteryResultVOList;
    }

    @Override
    public void robotPercentage(Long userId) {
        Random r = new Random();
        UserProfileEntity userProfile = userProfileRepository.findByUserId(userId);
        SkinEntity skin = skinRepository.randomPercentageSkin();
        Integer minRandom = r.nextInt(20);
        Integer maxRandom = r.nextInt(50) + 20;
        Integer minRoll = minRandom * SEED_TIMES;
        Integer maxRoll = maxRandom * SEED_TIMES;
        // 计算当前需要的金币
        BigDecimal consumeCoin = skin.getDiamond().multiply(BigDecimal.valueOf(maxRandom - maxRandom)).multiply(siteService.getExchangeRate().getPercentagePremium()).divide(BigDecimal.valueOf(100).multiply(siteService.getExchangeRate().getCoinToDiamond()), 2, BigDecimal.ROUND_CEILING);
//        BigDecimal coin = consumeCoin.multiply(percentage).setScale(2, BigDecimal.ROUND_UP);
        log.info("机器人, 饰品钻石：{}, 最小 {} 最大 {}需要消耗金币：{}, 用户余额：{}", skin.getDiamond(), minRandom, maxRandom, consumeCoin, userProfile.getCoin());
        // 金币消耗 TODOq
        if (userProfile.getCoin().compareTo(consumeCoin) == -1) {
            log.info("金币不足");
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.insufficient.coins"));
        }
        userProfile.setCoin(userProfile.getCoin().subtract(consumeCoin));
        // 经验 TODO
        userProfile.setExperience(userProfile.getExperience() + 10);
        userProfileRepository.save(userProfile);
        // 记录算法开箱绑定记录 TODO
        PercentageResultVO percentageResultVO = new PercentageResultVO();
        LotteryResultDTO lotteryResultDTO = lotteryService.lottery(userProfile.getUser());
        PercentageUserRecordEntity recordPercentage = new PercentageUserRecordEntity();
        recordPercentage.setUser(userProfile.getUser());
        recordPercentage.setRoll(lotteryResultDTO.getRoll());
        recordPercentage.setRounds(lotteryResultDTO.getAlgorithmData().getRounds() - 1);
        recordPercentage.setSkin(skin);
        recordPercentage.setMaxRoll(maxRoll);
        recordPercentage.setMinRoll(minRoll);
        recordPercentage.setAlgorithmData(lotteryResultDTO.getAlgorithmData());
        if (minRoll <= lotteryResultDTO.getRoll() && maxRoll >= lotteryResultDTO.getRoll()) {
            percentageResultVO.setIsWin(true);
            recordPercentage.setIsWin(true);
            // 放入背包
            UserPackageEntity userPackage = new UserPackageEntity();
            userPackage.setUser(userProfile.getUser());
            userPackage.setSkin(skin);
            userPackage.setPrice(skin.getPrice());
            userPackage.setDiamond(skin.getDiamond());
            userPackage.setSource(UserPackageSource.PERCENTAGE);
            userPackage.setIsReceived(false);
            userPackage.setIsSelled(false);
            userPackage = userPackageRepository.save(userPackage);
            percentageResultVO.setPackageId(userPackage.getId());
            percentageResultVO.setSkinInfo(skinService.querySkinInfoById(skin.getId()));
        } else {
            // 放入背包一个随机物品
            SkinEntity randomSkin = skinRepository.findRandomSkin();
            if (randomSkin != null) {
                UserPackageEntity userPackage = new UserPackageEntity();
                userPackage.setUser(userProfile.getUser());
                userPackage.setSkin(randomSkin);
                userPackage.setDiamond(skin.getDiamond());
                userPackage.setPrice(randomSkin.getPrice());
                userPackage.setSource(UserPackageSource.PERCENTAGE);
                userPackage.setIsReceived(false);
                userPackage = userPackageRepository.save(userPackage);
                percentageResultVO.setPackageId(userPackage.getId());
                percentageResultVO.setSkinInfo(skinService.querySkinInfoById(randomSkin.getId()));
            }
        }
        recordPercentage = recordPercentageRepository.save(recordPercentage);
        // 记录金币流水
        UserCoinRecordEntity userCoinRecordEntity = new UserCoinRecordEntity();
        userCoinRecordEntity.setUser(userProfile.getUser());
        userCoinRecordEntity.setSource(UserCoinChangeSource.PERCENTAGE);
        userCoinRecordEntity.setSourceId(recordPercentage.getId());
        userCoinRecordEntity.setAmount(consumeCoin);
        userCoinRecordEntity.setAfterAmount(userProfile.getCoin());
        userCoinRecordEntity.setIsPositive(false);
        userCoinRecordRepository.save(userCoinRecordEntity);
        percentageResultVO.setRoll(lotteryResultDTO.getRoll());
        percentageResultVO.setCard(activityService.joinCardCollect(userId, lotteryResultDTO.getRoll(), CardCollectSource.PERCENTAGE));
        // 全局通知
        UserLotteryResultOfWebsocketVO userLotteryResultOfWebsocketVO = new UserLotteryResultOfWebsocketVO() {{
            setUserInfo(userService.queryUserPublicInfo(userId));
            setSkinInfoVO(percentageResultVO.getSkinInfo());
            setSource(LotterySource.PERCENTAGE.getCode());
            setSourceValue(LotterySource.PERCENTAGE.getValue());
        }};
        MessageVO messageResultVO = new MessageVO(WebSocketMessageMethod.SEND_ALL, WebSocketMessageType.LOTTERY_RESULT, userLotteryResultOfWebsocketVO);
        asyncTaskService.sendWebSocketMessageToAll(JSONObject.toJSONString(messageResultVO));
        userService.DailyActivity(userId, DailyActivityType.PERCENTAGE);
    }
}
