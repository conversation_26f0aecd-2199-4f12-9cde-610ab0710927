package com.steamgo1.csgoskinapi.service;

import com.steamgo1.csgoskinapi.vo.*;
import com.steamgo1.csgoskincommon.entity.enums.CardCollectSource;

import java.math.BigDecimal;
import java.util.List;

public interface ActivityService {
    WalletVO receiveRedPicket(String key);

    CardCollectVO queryCardCollect();

    //    参加集卡活动
    CardVO joinCardCollect(Long userId, Integer roll, CardCollectSource cardCollectSource);

    List<CardCollectUserVO> queryUserCard(Long cardCollectId);

    void startCardCollect(Long id, String messageId);

    // 参加出金活动
    CardVO joinExtraBonus(Long userId, CardCollectSource cardCollectSource, BigDecimal amount);


    /**
     * 用户查询每日红包详情
     */
    UserDailyRedPacketVO queryUserDailyRedPacket(Long userId);

    /**
     * 用户查询日常签到详情
     */
    UserCheckInRedPacketVO queryUserCheckInRedPacket(Long userId);

    WalletVO receiveUserDailyRedPacket(Long userId, Long id);

    WalletVO receiveUserCheckInRedPacket(Long userId, Long id);

    void userCheckIn(Long userId);

    // 查询用户消费是否满足消费计划送箱子, 返回相关提示语
    void queryUserConsumePlan(Long userId, BigDecimal consume);

}
