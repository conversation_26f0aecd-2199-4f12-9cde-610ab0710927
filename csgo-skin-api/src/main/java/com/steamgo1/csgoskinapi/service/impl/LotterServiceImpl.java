package com.steamgo1.csgoskinapi.service.impl;

import com.steamgo1.csgoskinapi.dto.LotteryResultDTO;
import com.steamgo1.csgoskinapi.dto.LotteryVerifyDTO;
import com.steamgo1.csgoskinapi.service.LotteryService;
import com.steamgo1.csgoskinapi.service.UserService;
import com.steamgo1.csgoskinapi.utils.SecurityUtils;
import com.steamgo1.csgoskinapi.vo.LotteryTestResultVO;
import com.steamgo1.csgoskinapi.vo.LotteryVerifyResult;
import com.steamgo1.csgoskincommon.dao.AlgorithmDataRepository;
import com.steamgo1.csgoskincommon.entity.AlgorithmDataEntity;
import com.steamgo1.csgoskincommon.entity.UserEntity;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.service.AlgorithmService;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class LotterServiceImpl implements LotteryService {

    @Autowired
    AlgorithmDataRepository algorithmDataRepository;

    @Autowired
    UserService userService;

    @Autowired
    AlgorithmService algorithmService;


    @Override
    public LotteryTestResultVO testLettery() {
        Long userId = SecurityUtils.getUserId();
        List<AlgorithmDataEntity> algorithmDataEntitys = algorithmDataRepository.findByUserIdAndIsUsed(userId, true);
        if (algorithmDataEntitys.size() != 1) {
            log.info("用户：{} 算法数据异常 size {}", userId, algorithmDataEntitys.size());
            // throw new CsgoSkinException("用户算法数据异常,尝试重置后在试");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.algorithm.data.abnormal.please.reset"));
        }
        String secretHash = algorithmDataEntitys.get(0).getSecretHash();
        String clientSeed = algorithmDataEntitys.get(0).getClientSeed();
        Integer rounds = algorithmDataEntitys.get(0).getRounds();

        Integer roll = algorithmService.getRoll(secretHash, clientSeed, rounds);
        log.info("用户ID: {}, 私密哈希：{} 用户种子: {} 局数: {}", userId, secretHash, clientSeed, rounds);
        algorithmDataEntitys.get(0).setRounds(rounds + 1);
        algorithmDataRepository.save(algorithmDataEntitys.get(0));
        return new LotteryTestResultVO() {{
            setPublicHash(algorithmDataEntitys.get(0).getPublicHash());
            setClientSeed(clientSeed);
            setRounds(rounds);
            setRoll(roll);
        }};
    }

    @Override
    public LotteryResultDTO lottery() {
        Long userId = SecurityUtils.getUserId();
        List<AlgorithmDataEntity> algorithmDataEntitys = algorithmDataRepository.findByUserIdAndIsUsed(userId, true);
        if (algorithmDataEntitys.size() != 1) {
            log.info("用户：{} 算法数据异常 size {}", userId, algorithmDataEntitys.size());
            // throw new CsgoSkinException("用户算法数据异常,尝试重置后在试");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.algorithm.data.abnormal.please.reset"));
        }
        String secretHash = algorithmDataEntitys.get(0).getSecretHash();
        String clientSeed = algorithmDataEntitys.get(0).getClientSeed();
        Integer rounds = algorithmDataEntitys.get(0).getRounds();

        Integer roll = algorithmService.getRoll(secretHash, clientSeed, rounds);
        log.info("用户ID: {}, 私密哈希：{} 用户种子: {} 局数: {}", userId, secretHash, clientSeed, rounds);
        algorithmDataEntitys.get(0).setRounds(rounds + 1);
        algorithmDataRepository.save(algorithmDataEntitys.get(0));
        return new LotteryResultDTO() {{
            setAlgorithmData(algorithmDataEntitys.get(0));
            setRoll(roll);
        }};
    }

    @Override
    public LotteryResultDTO lottery(UserEntity user) {
        List<AlgorithmDataEntity> algorithmDataEntitys = algorithmDataRepository.findByUserIdAndIsUsed(user.getId(), true);
        if (algorithmDataEntitys.size() != 1) {
            log.info("用户：{} 算法数据异常 size {}", user.getId(), algorithmDataEntitys.size());
            // throw new CsgoSkinException("用户算法数据异常,尝试重置后在试");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.algorithm.data.abnormal.please.reset"));
        }
        AlgorithmDataEntity algorithmDataEntity = algorithmDataEntitys.get(0);
        String secretHash = algorithmDataEntity.getSecretHash();
        String clientSeed = algorithmDataEntity.getClientSeed();
        Integer rounds = algorithmDataEntity.getRounds();

        Integer roll = algorithmService.getRoll(secretHash, clientSeed, rounds);
        log.info("用户ID: {}, 私密哈希：{} 用户种子: {} 局数: {}", user.getId(), secretHash, clientSeed, rounds);
        algorithmDataEntity.setRounds(rounds + 1);
        algorithmDataRepository.save(algorithmDataEntity);
        AlgorithmDataEntity finalAlgorithmDataEntity = algorithmDataEntity;
        return new LotteryResultDTO() {{
            setAlgorithmData(finalAlgorithmDataEntity);
            setRoll(roll);
        }};
    }

    @Override
    public LotteryVerifyResult lottery(LotteryVerifyDTO lotteryVerifyDTO) {
        String secretHash = lotteryVerifyDTO.getSecretHash();
        String clientSeed = lotteryVerifyDTO.getClientSeed();
        if (!lotteryVerifyDTO.getPublicHash().equals(algorithmService.getPublicHash(secretHash, lotteryVerifyDTO.getSecretSalt()))) {
            // todo 国际化
            // throw new CsgoSkinException("算法数据异常,公共哈希与私密哈希不匹配");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.public.hash.mismatch.secret.hash"));
        }
        Integer rounds = lotteryVerifyDTO.getRounds();
        Integer roll = algorithmService.getRoll(secretHash, clientSeed, rounds);
        return new LotteryVerifyResult() {{
            setRoll(roll);
        }};
    }
}
