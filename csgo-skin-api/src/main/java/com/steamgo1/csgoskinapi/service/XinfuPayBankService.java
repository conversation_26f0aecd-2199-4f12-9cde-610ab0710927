package com.steamgo1.csgoskinapi.service;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinapi.dto.xinfupaybank.*;
import com.steamgo1.csgoskinapi.utils.XinfuPayBankSignUtils;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Data
@Slf4j
public class XinfuPayBankService {
    
    private String baseUrl = "http://pay.xinfupaybank.com";
    private String machId;
    private String machAppId;
    private String paySecret;
    private String withdrawalSecret;
    private String payReturnUrl;
    private String withdrawalReturnUrl;

    /**
     * 请求收款
     */
    public <T extends PayOrderResponse> T createPayOrder(PayOrderRequest request, Class<T> responseClass) {
        try {
            // 构建签名参数
            request.setMachId(machId);
            request.setReturnUrl(payReturnUrl);
            Map<String, String> signParams = request.toSignMapIgnoreNullValue();

            // 生成签名
            Map<String, String> signedParams = XinfuPayBankSignUtils.signMap(signParams, paySecret);
            request.setSign(signedParams.get("sign"));

            // 发送请求
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            if(!request.validate()) {
                throw new CsgoSkinException(I18nUtils.getMessage("exception.invalid.parameters"));
            }
            HttpEntity<PayOrderRequest> entity = new HttpEntity<>(request, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                baseUrl + "/pay/payOrder/" + request.paySubPath() + "/pay",
                HttpMethod.POST, 
                entity, 
                String.class
            );

            log.info("XinfuPayBank创建订单响应: {}", response.getBody());
            return JSONObject.parseObject(response.getBody(), responseClass);

        } catch (Exception e) {
            log.error("XinfuPayBank创建订单失败", e);
            throw new RuntimeException("创建支付订单失败", e);
        }
    }

    /**
     * 请求代付
     */
    public WithdrawalResponse createWithdrawal(WithdrawalRequest request) {
        try {
            // 构建签名参数
            Map<String, String> signParams = new HashMap<>();
            signParams.put("machId", machId);
            signParams.put("amount", String.valueOf(request.getAmount()));
            signParams.put("name", request.getName());
            signParams.put("account", request.getAccount());
            signParams.put("type", request.getType());
            signParams.put("returnUrl", this.withdrawalReturnUrl);
            if (request.getIdCode() != null) signParams.put("idCode", request.getIdCode());
            signParams.put("merchantOrderNo", request.getMerchantOrderNo());

            // 生成签名
            Map<String, String> signedParams = XinfuPayBankSignUtils.signMap(signParams, withdrawalSecret);
            request.setSign(signedParams.get("sign"));

            // 发送请求
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<WithdrawalRequest> entity = new HttpEntity<>(request, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    baseUrl + "/pay/payOrder/bx/withdrawal",
                    HttpMethod.POST,
                    entity,
                    String.class
            );

            log.info("XinfuPayBank代付响应: {}", response.getBody());
            return JSONObject.parseObject(response.getBody(), WithdrawalResponse.class);

        } catch (Exception e) {
            log.error("XinfuPayBank代付失败", e);
            throw new RuntimeException("代付请求失败", e);
        }
    }

    /**
     * 查询收款订单
     */
    public PayOrderQueryResponse queryPayOrder(String merchantOrderNo) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("merchantOrderNo", merchantOrderNo);
            params.put("machId", machId);
            params.put("appId", machAppId);

            Map<String, String> signedParams = XinfuPayBankSignUtils.signMap(params, paySecret);

            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, String>> entity = new HttpEntity<>(signedParams, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    baseUrl + "/pay/payOrder/orderOne",
                    HttpMethod.POST,
                    entity,
                    String.class
            );

            log.info("XinfuPayBank查询订单响应: {}", response.getBody());
            return JSONObject.parseObject(response.getBody(), PayOrderQueryResponse.class);

        } catch (Exception e) {
            log.error("查询收款订单失败", e);
            throw new RuntimeException("查询订单失败", e);
        }
    }

    /**
     * 验证回调签名
     */
    public boolean verifyNotifySign(PayNotifyRequest notify) {
        Map<String, String> params = new HashMap<>();
        params.put("merchantOrderNo", notify.getMerchantOrderNo());
        params.put("payMoney", notify.getPayMoney());
        params.put("payTime", notify.getPayTime());
        params.put("status", notify.getStatus());

        String expectedSignString = XinfuPayBankSignUtils.generateSignString(params, paySecret);
        String expectedSign = DigestUtils.md5DigestAsHex(expectedSignString.getBytes());
        String actualSign = notify.getSign();
        
        return expectedSign.equals(actualSign);
    }

    /**
     * 验证代付回调签名
     */
    public boolean verifyWithdrawalNotifySign(WithdrawalNotifyRequest notify) {
        Map<String, String> params = new HashMap<>();
        params.put("merchantOrderNo", notify.getMerchantOrderNo());
        params.put("withdrawalMoney", notify.getWithdrawalMoney());
        params.put("withdrawalTime", notify.getWithdrawalTime());
        params.put("status", notify.getStatus());

        String expectedSignString = XinfuPayBankSignUtils.generateSignString(params, withdrawalSecret);
        String expectedSign = DigestUtils.md5DigestAsHex(expectedSignString.getBytes());
        String actualSign = notify.getSign();
        
        return expectedSign.equals(actualSign);
    }

    /**
     * 查询代付订单
     */
    public WithdrawalQueryResponse queryWithdrawalOrder(String merchantOrderNo) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("merchantOrderNo", merchantOrderNo);
            params.put("machId", machId);
            params.put("appId", machAppId);

            Map<String, String> signedParams = XinfuPayBankSignUtils.signMap(params, withdrawalSecret);

            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, String>> entity = new HttpEntity<>(signedParams, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                baseUrl + "/pay/pay/orderOne",
                HttpMethod.POST,
                entity,
                String.class
            );

            log.info("XinfuPayBank查询代付订单响应: {}", response.getBody());
            return JSONObject.parseObject(response.getBody(), WithdrawalQueryResponse.class);

        } catch (Exception e) {
            log.error("查询代付订单失败", e);
            throw new RuntimeException("查询代付订单失败", e);
        }
    }

    /**
     * 查询账户余额
     */
    public AccountBalanceResponse queryAccountBalance() {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("appId", machAppId);
            params.put("machId", machId);

            Map<String, String> signedParams = XinfuPayBankSignUtils.signMap(params, paySecret);

            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, String>> entity = new HttpEntity<>(signedParams, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    baseUrl + "/pay/pay/myAccount",
                    HttpMethod.POST,
                    entity,
                    String.class
            );

            log.info("XinfuPayBank查询账户余额响应: {}", response.getBody());
            return JSONObject.parseObject(response.getBody(), AccountBalanceResponse.class);

        } catch (Exception e) {
            log.error("查询账户余额失败", e);
            throw new RuntimeException("查询账户余额失败", e);
        }
    }
}
