package com.steamgo1.csgoskinapi.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.steamgo1.csgoskinapi.enums.SmsType;
import com.steamgo1.csgoskinapi.service.SmsService;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import com.steamgo1.csgoskincommon.utils.Json;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class SmsServiceImpl implements SmsService {
    /**
     * 产品名称:云通信短信API产品,开发者无需替换
     */
    private static final String PRODUCT = "Dysmsapi";
    /**
     * 产品域名,开发者无需替换
     */
    private static final String DOMAIN = "dysmsapi.aliyuncs.com";
    /**
     * 短信发送成功的标志
     */
    private static final String SEND_SMS_SUCCESS_FLAG = "OK";
    @Value("${aliyun.sms.accessKeyId}")
    private String accessKeyId;
    @Value("${aliyun.sms.accesskeySecret}")
    private String accesskeySecret;
    @Value("${aliyun.sms.signName}")
    private String signName;
    @Value("${spring.redis.prefix.login-captcha}")
    private String redisLoginPrefix;
    @Value("${spring.redis.expire.login-captcha}")
    private Long redisLoginExpire;
    // 旦米短信
    @Value("${danmi.sms.accountSid}")
    private String accountSid;
    @Value("${danmi.sms.authtoken}")
    private String authtoken;
    @Value("${danmi.sms.accountId}")
    private String accountId;
    @Value("${danmi.sms.templateid}")
    private String templateid;
    @Autowired
    private RestTemplate restTemplate;

    public static void main(String[] args) {
        SmsService smsService = new SmsServiceImpl();
        smsService.sendSms(SmsType.VALID, "***********", new HashMap<String, String>() {{
            put("code", "123456");
        }});
    }

    @Override
    public void sendSms(SmsType smsType, String mobile, Map<String, String> params) {
        try {
            this.sendSms(mobile, smsType.getTemplateCode(), params);
        } catch (ClientException e) {
            // todo 国际化
            //throw new CsgoSkinException("发送短信失败，请稍后再试");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.sms.send.fail.retry.later"));
        }
    }

    @Override
    public String sendLoginSms(String mobile) {
        String key = String.format("%s:%s", redisLoginPrefix, mobile);
        if (RedisUtils.hasKey(key)) {
            // todo 国际化
            //throw new CsgoSkinException("验证码未到期,稍后重试");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.captcha.not.expired.retry.later"));

        }
        String code = RandomUtil.randomNumbers(6);
        sendSms(SmsType.VALID, mobile, new HashMap<String, String>() {
            {
                put("code", code);
            }
        });
        RedisUtils.save(key, code, redisLoginExpire);
        return code;
    }

    @Override
    public String sendLoginSmsBydanmi(String mobile) {
        String redisKey = String.format("%s:%s", redisLoginPrefix, mobile);
        if (RedisUtils.hasKey(redisKey)) {
            // todo 国际化
            //throw new CsgoSkinException("验证码未到期,稍后重试");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.captcha.not.expired.retry.later"));

        }
        String code = RandomUtil.randomNumbers(6);
        // 构造请求体参数
        JSONObject requestBody = new JSONObject();
        String timestamp = String.valueOf(System.currentTimeMillis());
        requestBody.put("accountSid", accountSid);
        requestBody.put("to", mobile);
        requestBody.put("templateid", templateid);
        requestBody.put("timestamp", timestamp);
        requestBody.put("sig", DigestUtils.md5Hex(accountSid + authtoken + timestamp));
        requestBody.put("accountId", accountId);
        requestBody.put("templateId", templateid);
        requestBody.put("param", code);

        // 将 JSON 对象转换为 URL 编码格式的字符串
        StringBuilder requestParams = new StringBuilder();
        for (String key : requestBody.keySet()) {
            if (requestParams.length() != 0) {
                requestParams.append('&');
            }
            requestParams.append(key)
                    .append('=')
                    .append(requestBody.getString(key));
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        // 封装 HTTP 请求
        HttpEntity<String> requestEntity = new HttpEntity<>(requestParams.toString(), headers);

        // 发起 POST 请求
        ResponseEntity<String> responseEntity = restTemplate.exchange("https://openapi.danmi.com/textSMS/sendSMS/V1", HttpMethod.POST, requestEntity, String.class);

        // 检查响应状态码并处理响应体
        if (responseEntity.getStatusCode().is2xxSuccessful()) {
            // 响应体转换为 JSON 对象
            JSONObject responseJson = JSONObject.parseObject(responseEntity.getBody());
            if (responseJson.getString("respCode").equals("0000")) {
                RedisUtils.save(redisKey, code, redisLoginExpire);
                return code;
            }
            log.error("发送短信失败: {}", responseJson.toJSONString());
            // todo 国际化
            //throw new CsgoSkinException("发送短信失败，请稍后再试");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.sms.send.fail.retry.later"));
        } else {
            // todo 国际化
            // throw new CsgoSkinException("发送短信失败，请稍后再试");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.sms.send.fail.retry.later"));
        }
    }

    private void sendSms(String mobile, String templateCode, Map<String, String> params) throws ClientException {

        //可自助调整超时时间
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");

        //初始化acsClient,暂不支持region化
        IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", accessKeyId, accesskeySecret);
        DefaultProfile.addEndpoint("cn-hangzhou", PRODUCT, DOMAIN);
        IAcsClient acsClient = new DefaultAcsClient(profile);

        //组装请求对象-具体描述见控制台-文档部分内容
        SendSmsRequest request = new SendSmsRequest();
        //必填:待发送手机号
        request.setPhoneNumbers(mobile);
        //必填:短信签名-可在短信控制台中找到
        request.setSignName(signName);
        //必填:短信模板-可在短信控制台中找到
        request.setTemplateCode(templateCode);
        request.setTemplateParam(Json.toJsonString(params));


        //hint 此处可能会抛出异常，注意catch
        SendSmsResponse sendSmsResponse = acsClient.getAcsResponse(request);
        System.out.println(sendSmsResponse);
        log.debug(Json.toJsonString(sendSmsResponse));
        if (sendSmsResponse.getCode() == null || !SEND_SMS_SUCCESS_FLAG.equals(sendSmsResponse.getCode())) {
            // todo 国际化
            //throw new CsgoSkinException("发送短信失败，请稍后再试:" + sendSmsResponse.getMessage());
            throw new CsgoSkinException(I18nUtils.getMessage("exception.sms.send.fail.retry.later") + sendSmsResponse.getMessage());
        }
    }

    private String formatContent(SmsType smsType, Map<String, String> params) {
        if (CollectionUtil.isEmpty(params)) {
            return smsType.getContent();
        }
        String content = smsType.getContent();
        for (Map.Entry<String, String> element : params.entrySet()) {
            content = content.replace("${" + element.getKey() + "}", element.getValue());
        }
        return content;
    }
}
