package com.steamgo1.csgoskinapi.service;

import com.steamgo1.csgoskinapi.dto.SkinQueryDTO;
import com.steamgo1.csgoskinapi.vo.SkinInfoVO;
import com.steamgo1.csgoskinapi.vo.SkinQueryParamVO;
import org.springframework.data.domain.Page;

public interface SkinService {
    /**
     * 获取饰品搜索条件
     */
    SkinQueryParamVO skinQueryParam();

    /**
     * 获取所有饰品信息, 分页
     */
    Page<SkinInfoVO> querySkins(SkinQueryDTO skinQueryDTO);


    /**
     * 获取随机奖励饰品信息, 分页
     */
    Page<SkinInfoVO> queryRandomSkins(SkinQueryDTO skinQueryDTO);

    /**
     * 查询单个
     *
     * @param skinId
     * @return
     */
    SkinInfoVO querySkinInfoById(Long skinId);


}
