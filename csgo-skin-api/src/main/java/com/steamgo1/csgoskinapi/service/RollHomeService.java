package com.steamgo1.csgoskinapi.service;

import com.steamgo1.csgoskinapi.dto.PageQueryDTO;
import com.steamgo1.csgoskinapi.dto.RollHomeJoinDTO;
import com.steamgo1.csgoskinapi.dto.RollHomeQueryDTO;
import com.steamgo1.csgoskinapi.vo.*;
import com.steamgo1.csgoskincommon.entity.RollHomeEntity;
import com.steamgo1.csgoskincommon.vo.RollHomeFilterParamVO;
import org.springframework.data.domain.Page;

import java.util.List;

public interface RollHomeService {
    /**
     * 查询roll房
     *
     * @param rollHomeQueryDTO
     * @return
     */
    Page<RollHomeBaseInfoVO> queryRollHomes(RollHomeQueryDTO rollHomeQueryDTO);

    /**
     * 用戶查询roll房
     *
     * @param rollHomeQueryDTO
     * @return
     */
    Page<RollHomeBaseInfoVO> queryMyRollHomes(RollHomeQueryDTO rollHomeQueryDTO);

    RollHomeVO queryRollHomeById(Long rollHomeId);

    Page<RollHomeSkinVO> queryRollHomeSkins(Long rollHomeId, PageQueryDTO pageQueryDTO);

    Page<RollHomeUserVO> queryRollHomeUsers(Long rollHomeId, PageQueryDTO pageQueryDTO);

    /**
     * 参入roll房
     */
    RollHomeVO joinRollHome(RollHomeJoinDTO rollHomeJoinDTO);


    /**
     * Roll房开奖
     *
     * @param rollHomeId
     * @return
     */
    RollHomeVO lotteryRollHome(Long rollHomeId);

    /**
     * 移除roll房中机器人累计
     *
     * @param rollHomeEntity
     */
    void removeRollHomeRobotSession(RollHomeEntity rollHomeEntity);

    /**
     * 获取Roll房查询条件
     *
     * @return
     */
    RollHomeFilterParamVO getRollHomeFilterParam();

    /**
     * 获取Roll房最新10条中奖记录
     */
    List<UserLotteryResultVO> queryRollHomeUserLotteryResult();

    /**
     * 获取推荐Roll房
     *
     * @return
     */
    List<RollHomeBaseInfoVO> queryRecommendRollHome();


}
