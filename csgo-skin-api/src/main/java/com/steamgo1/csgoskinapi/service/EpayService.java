package com.steamgo1.csgoskinapi.service;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinapi.dto.epay.CreateOrderParmas;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.MessageDigest;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.*;


@Slf4j
@Service
public class EpayService {
//    private String pid="1004"; // 商户ID
//    private String merchantPrivateKey="MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDfFfzqNrkA99kx2nVETTJVFEPYq/i043gZsIgQmXAvgG/S8R8QVGMYdCxncsPuFe95OZalKSh7iSZullPn5CSAqMIzdJBaVp7i6EclzQ2ApX+/qQn0yYIWoGHq4EKlnQr1T7ziUtHZOnXjbDm0xlmhwkqV4JsWqsqyGZzb09Ql6IldnLEgcI2WXWcEISRuaPBa9+8X7O57ShTks4wNF1dB73Tsfxmj7y9x8+ZGmFCGS9j7Prn4nTwL7yHP/v/TsCIaRdgkKXzPMyNW68yc+X+BxsgKK8NKiFlC0GY6we3lpE8/7HsGXwAW2gdpcLgGj9iOuU8uHwpgo8MFjEgQNRXjAgMBAAECggEAVU6OMW4Nns4oMwUr1JuEhL5EF4XjjCBz27cCwP47xR5jJJHp5A+w+Tfrhv9+5vtiYMPh1HSBs4PFu4ImwcUfJEfkQ1LupGz9Qx25nz3jTO98g2j7kqy/3zEjYHKzaas89oOwYgdzkZLmY7oN8w/qVI38gYWrKKbU7fgXYISHWprzdrOG017qW86ge3k20vMoAXtCBgef1rzdUJGm6WWV9y7LMmLhRXIQqT2McnyXxrqhBqe/kSr7EaBfvwrND7l3xm6MlxCCy07ueB+/xiYaejbhTmkN0qlo10fAZ/+yLs42mt2qYaZM8hhbOaQ4EWhg3rIPwjlgQY59+RwiVdok+QKBgQD7BsPX/2zDRkOhasYTgQEgGSi44Y76T7G1o4QFHZzD7CD9PZGHPGomy8VmWfNRJREMREgAPbYfMZOvqQwcirLtKN+T/iwMB+JtRaNwR2QE93MhqjyOb7Z6bG4jFJ2bcz9D1g1s6R3tzhGqXgB+Arowt+1IWnqVFPKj7axygNnoRQKBgQDjgYFcYi3uI9bCDarsrr8XcH1XzotphxBjctdqytzm8qvyZ8d7vneJ4H9TFw6eZ6Fhx8e0B+s6GkyPCT96unu+/ZKRmmEpW0stSX519GtbA2w+E2t4cMuQCANJJdWaU0FO3OaHsN2CXC2SNtPWN66Hb6K49ExzTe56Xng6dvKMBwKBgDt3Jcu0SWqU6Y6Cgbu7Eq4s55WscTdgy6NQwgSIOEu9Uy9Kd4AG0sqv+Oo/IsG1Oy2hu0C7XbpT/z1cmW7leG9wAE7JpluPPpdOKOJ/NvVqPQ7big7f7eVgbFLJHnDlwzXprjJ/osRjzZGDRTCUqtDBsWczb9iDJtuogPJ7DaPVAoGABM0L1M5Bl9/rvUaaj8oUQENgccTrDjwKbX7T6u4DAWCnnkLwY70eOquhJDi9VzxqnGyh2zu7jAtYCECytT1QNTSWrjnqyPujN35wi76vsUj+NMUmJHGE6jIxF48T1eDh5O8mckCaPBED6Sz9BW1BYpxJe8uz/U7stGpf8SPndGsCgYEA2mpjjx1Vghefch0NO/+LWivCQhTeVfuYRt/Xg3TPv2eYpmZrKvrDcjDWubFpxLWMyX784HGnUMjZGVYj88JsBhafOgIaqRUpQZMvBNyIBtT9YdhTd2oG8l6suyPrmMWUU+7TGEG/obR+OL0QrkEruppp/d/5/QDnNFJSO7ajHjw="; // 商户私钥
//    private String signType = "RSA"; // 签名类型
//    private String md5Key = "p952jw12h5P9Wj322Nq9V8PP19njpPVz";
//    private String baseUrl = "http://epay.wuai.net";

    //    =================================================== 20250327替换
    private String pid = "1001"; // 商户ID
    private String merchantPrivateKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDVenWJN/3LvMV+uBr8TmdekNSUuWP+lGKz7U/plaJE99rGAjCN61blv9hJ+wm3JMzybtXG0qbi8qy4zvRDfni59vgJO6bivN0GJAJPv8VVnE1hx0jg27EQA3+TkNXcYp/R2vMvkfLu2YKHNadXvj6uzhAq0DxaFIG5uPfw4Uo5OXEUe/OHz2R/Ex4EMRI4EHqI224LF7h4Bzol/lwWYSHYZdrvMktMOUbX8ht7QsYhoPWBub84/toXdtKD+cIx/Antz812XSknTX0Btiwf3256h2/P3oQsX5EjfEBGOCGydM3PEWuzx19JY6alAlsiwZLw6uB6snQ4hQ7LoQkcIqXFAgMBAAECggEAT0PPDIo7tEB9XFXjvzclbpl8NVS46jgShhhhiavDurQFCWyatrdhZqFbH+0UaGpDpui8e7yPtsjAlUZSsBTvoq0u387EYoAZ2UPZj9722JjWqq2iQ8uO7RE/d31ZyyDbksIndcxNVZ5xtt6XKh87ixB8NElpf1EJ8fnaJhigxKn/t0q9l2jSGx3wZ1YX0rL3uxlOF2z+5cA61ypIbeUwHmHd0yKhW+jDsfxFZktCe5YdGgmTPP3gNYo5EoPHZN+sfBQZ0LGj/sL5ujmmh82LtQ9+nJksJeYH3ciLpPne41ncj5RTEM9mYQUCOhV7ba6NuOZ0QkHwqEVwltCxWEbIqQKBgQDyo/FXSefpqgaIIBalJSseddGf/XF1nv8C6ThldCklJcPCRtMgQAJmRc4Tnl3Bk8G9uG9zIB4XH1eXyLzMfEu0DwgmuX1HjeR4sx5mtiS5H38vRYGH+rF8ufnOcVDYkVJVhk3NoGy62FCxLBxQ4pQcjm43Aacqmona6VY0CN0UGwKBgQDhO3jyshtOxibpiOMpVgclbnLOPCiajs9xNdw1H0Q7h4ASVcMK1DloADQ9Bry88cQcSOTTkjT5Ytsix48U+BTaTUdOJhqZHEWkfzP3z5CMYiGcAK0kQ9YJxIFmDdK7AkgHMoPWPeoFXvbw2lcd7rAWl81Bi9TIcnfr/F57hEILnwKBgQClHQTdRnCnZEk/lr5yMQHPn5M/kSMFTGXyYCw9dbeODM3SxYlkF8rKN9RNK2ZXlSmw5fIJYKC5UV207yfy6t5FMDl2yKDE8Xy42gLlGCuTQii1TVIg62qeGpwwbJH8XZ+ujU8ij3mOCCeY+RBnxlfNuuqrSdesuS74JfLARAvVYwKBgC+9TlaKv9UDlQ3c8OE7FcquTXE/ORABgyCo3OL5nJnNj6zTNsMd1okfufLRccHOsNWZ8b6Sd94AQRhvGfnE146p4JpE41BeuLg0Kgy1lxraVeteDVTpd/2cBB1ggrEWu7Oi1mTbvLWkuky0ytBSXkp7pIrnG5PdB2QimtgtYV5bAoGBAJX5dg3L5F9BHw1uZiD0xzUF76k8zBY7bvfABkv0w27Igx/5jKaGPp1W0SAM00CRuV3vM2lFCRFBw7+gdNygHG+mPQ3X2XJmbNCYxmr/g1MViutPe0QX1P6JHXcLAVa66DH5T9KjJ39QyRJGqfbWY3a/h7xVDsXK8ZgbZctZLsGR"; // 商户私钥
    private String signType = "RSA"; // 签名类型
    private String md5Key = "2o2Clq8Sc1Y88cW2elbeb08K020qRsYy";
    private String baseUrl = "https://apis.710o.com";

    /**
     * 根据给定的JSONObject和商户密钥生成签名
     *
     * @param params 请求参数的JSONObject
     * @param key    商户密钥
     * @return 签名
     * @throws Exception 抛出异常
     */
    private static String generateSign(JSONObject params, String key) throws Exception {
        TreeMap<String, Object> sortedParams = new TreeMap<>();
        Iterator<Map.Entry<String, Object>> iterator = params.entrySet().iterator();

        while (iterator.hasNext()) {
            Map.Entry<String, Object> entry = iterator.next();
            String paramName = entry.getKey();
            Object paramValue = entry.getValue();
            if (paramValue != null && !paramName.equals("sign") && !paramName.equals("sign_type")) {
                sortedParams.put(paramName, paramValue);
            }
        }

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : sortedParams.entrySet()) {
            sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        sb.setLength(sb.length() - 1); // 移除最后一个&

        // 添加商户密钥
        sb.append(key);

        // MD5加密
        return md5(sb.toString());
    }

    /**
     * 使用MD5算法加密给定的字符串
     *
     * @param input 输入字符串
     * @return 加密后的字符串
     * @throws Exception 抛出异常
     */
    private static String md5(String input) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] messageDigest = md.digest(input.getBytes());
        StringBuilder hexString = new StringBuilder();
        for (byte b : messageDigest) {
            String h = Integer.toHexString(0xFF & b);
            if (h.length() == 1) {
                hexString.append('0');
            }
            hexString.append(h);
        }
        return hexString.toString();
    }

    public static void main(String[] args) {
        EpayService epayService = new EpayService();
        CreateOrderParmas createOrderParmas = new CreateOrderParmas();
        createOrderParmas.setType("web");
        createOrderParmas.setOutTradeNo("2016-06111");
        createOrderParmas.setType("alipay");
        createOrderParmas.setName("test");
        createOrderParmas.setMoney("100.00");
        createOrderParmas.setClientip("***********");
        epayService.payV1(createOrderParmas);
    }

    /**
     * 生成签名
     *
     * @param params 请求参数
     * @return 签名
     */
    private String getSign(Map<String, String> params) {
        String signContent = getSignContent(params);
        System.out.println(signContent);
        return rsaPrivateSign(signContent);
    }

    /**
     * 获取待签名字符串
     *
     * @param params 请求参数
     * @return 待签名字符串
     */
    private String getSignContent(Map<String, String> params) {
        // 过滤空值、sign和sign_type字段
        Map<String, String> filteredParams = new HashMap<>();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            if (!isEmpty(value) && !"sign".equals(key) && !"sign_type".equals(key)) {
                filteredParams.put(key, value);
            }
        }

        // 按照键的ASCII码升序排序
        List<String> sortedKeys = new ArrayList<>(filteredParams.keySet());
        Collections.sort(sortedKeys);

        // 生成待签名字符串
        StringBuilder signContent = new StringBuilder();
        for (String key : sortedKeys) {
            if (signContent.length() > 0) {
                signContent.append("&");
            }
            signContent.append(key).append("=").append(filteredParams.get(key));
        }

        return signContent.toString();
    }

    /**
     * 判断字符串是否为空
     *
     * @param value 字符串
     * @return 是否为空
     */
    private boolean isEmpty(String value) {
        return value == null || value.trim().isEmpty();
    }

    /**
     * 使用商户私钥进行RSA签名
     *
     * @param data 待签名字符串
     * @return 签名（Base64编码）
     */
    private String rsaPrivateSign(String data) {
        try {
            // 将Base64编码的私钥解码为字节数组
            byte[] keyBytes = Base64.getDecoder().decode(this.merchantPrivateKey);

            // 创建PKCS8EncodedKeySpec对象
            PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);

            // 获取KeyFactory实例，生成PrivateKey
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey privateKey = keyFactory.generatePrivate(pkcs8KeySpec);

            // 使用SHA256WithRSA算法签名
            Signature signature = Signature.getInstance("SHA256withRSA");
            signature.initSign(privateKey);
            signature.update(data.getBytes(StandardCharsets.UTF_8));

            // 对签名结果进行Base64编码
            byte[] signed = signature.sign();
            return Base64.getEncoder().encodeToString(signed);
        } catch (Exception e) {
            throw new RuntimeException("签名失败，商户私钥错误", e);
        }
    }

    public void pay(CreateOrderParmas createOrderParmas) {
        try {
            // 请求地址
            URL url = new URL(baseUrl + "/api/pay/create");
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            conn.setDoOutput(true);

            // 请求参数
            Map<String, String> parameters = new HashMap<>();
            parameters.put("pid", pid);
            parameters.put("sign_type", signType);
            parameters.put("method", "web");
            parameters.put("device", "pc");
            parameters.put("timestamp", String.valueOf(System.currentTimeMillis() / 1000));
            parameters.put("type", createOrderParmas.getType());
            parameters.put("out_trade_no", createOrderParmas.getOutTradeNo());
            parameters.put("notify_url", "http://www.pay.com/notify_url.php");
            parameters.put("return_url", "http://www.pay.com/return_url.php");
            parameters.put("name", createOrderParmas.getName());
            parameters.put("money", createOrderParmas.getMoney());
            parameters.put("clientip", createOrderParmas.getClientip());
            parameters.put("sign", ""); // 你需要根据签名规则生成sign

            // 1. 获取待签名字符串
            String sign = getSign(parameters);
            System.out.println("生成的签名: " + sign);
            parameters.put("sign", sign);

            // 构建请求体
            StringBuilder postData = new StringBuilder();
            for (Map.Entry<String, String> param : parameters.entrySet()) {
                if (postData.length() != 0) postData.append('&');
                postData.append(param.getKey());
                postData.append('=');
                postData.append(param.getValue());
            }
            byte[] postDataBytes = postData.toString().getBytes(StandardCharsets.UTF_8);

            // 发送请求
            try (OutputStream os = conn.getOutputStream()) {
                os.write(postDataBytes);
            }

            // 获取响应
            int responseCode = conn.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();

                // 输出响应
                System.out.println("Response: " + response.toString());
            } else {
                System.out.println("POST request not worked: " + responseCode);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Map<String, String> payV1(CreateOrderParmas createOrderParmas) {
        log.info("开始创建订单payV1");
        RestTemplate restTemplate = new RestTemplate();
        // 构建请求参数
        JSONObject params = new JSONObject();
        params.put("pid", pid);
        params.put("type", createOrderParmas.getType());
        params.put("out_trade_no", createOrderParmas.getOutTradeNo());
        params.put("notify_url", "http://www.kk8skins.com/api/csgo/api/order/epay/notify");
        params.put("name", createOrderParmas.getName());
        params.put("money", createOrderParmas.getMoney());
        params.put("clientip", createOrderParmas.getClientip());


        try {
            params.put("sign", generateSign(params, md5Key));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
//        params.put("sign_type", "MD5");
        log.info("创建订单请求参数: {}", params);

        // 设置请求头
//        HttpHeaders headers = new HttpHeaders();
//        headers.set("Content-Type", "application/json; charset=UTF-8");

        // 创建HttpEntity对象，包含请求体和请求头
//        HttpEntity<String> requestEntity = new HttpEntity<>(params.toString(), headers);

        try {
            // 将params转换为MultiValueMap以适应form-url-encoded格式
            MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
            for (Map.Entry<String, Object> param : params.entrySet()) {
                formData.add(param.getKey(), param.getValue().toString());
            }
            log.info("请求参数: {}", formData);
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/x-www-form-urlencoded");
            // 创建HttpEntity对象，包含请求体和请求头
            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(formData, headers);
            // 发送POST请求并接收响应
            ResponseEntity<String> responseEntity = restTemplate.exchange(baseUrl + "/mapi.php", HttpMethod.POST, requestEntity, String.class);
            String responseBody = responseEntity.getBody();
            log.info("请求响应： {}", responseBody);
            JSONObject res = JSONObject.parseObject(responseBody);
            if (res.getInteger("code").equals(1)) {
                Map<String, String> m = new HashMap<>();
                m.put("trade_no", res.getString("trade_no"));
                if (res.containsKey("payurl")) {
                    m.put("url", res.getString("payurl"));
                } else if (res.containsKey("qrcode")) {
                    m.put("url", res.getString("qrcode"));
                } else {
                    log.info("创建订单异常");
                    return null;
                }

                return m;
            }
            return null;
        } catch (Exception e) {
            log.info("创建订单异常：{}", e.getMessage());
            return null;
        }
    }
}
