package com.steamgo1.csgoskinapi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.steamgo1.csgoskinapi.converter.ChargeGoodsConverter;
import com.steamgo1.csgoskinapi.service.GoodsService;
import com.steamgo1.csgoskinapi.vo.ChargeGoodsVO;
import com.steamgo1.csgoskincommon.dao.ChargeGoodsRepository;
import com.steamgo1.csgoskincommon.dao.PayChannelRepository;
import com.steamgo1.csgoskincommon.entity.ChargeGoodsEntity;
import com.steamgo1.csgoskincommon.entity.PayChannelEntity;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Locale;

@Service
public class GoodsServiceImpl implements GoodsService {

    @Autowired
    private ChargeGoodsRepository chargeGoodsRepository;

    @Autowired
    private ChargeGoodsConverter chargeGoodsConverter;
    @Autowired
    private PayChannelRepository payChannelRepository;

    @Override
    public List<ChargeGoodsVO> queryChargeGoods() {
        List<ChargeGoodsEntity> chargeGoodsVOS = chargeGoodsRepository.findByIsSell(true);
        Locale locale = I18nUtils.getCurrentLocale();
        PayChannelEntity payChannelEntity = payChannelRepository.findByCountry(locale.getCountry());
        if(ObjectUtil.isNull(payChannelEntity)) {
            throw new RuntimeException(I18nUtils.getMessage("response.exception.pay.channel.not.found"));
        }
        chargeGoodsVOS.forEach(chargeGoodsEntity -> {   
            chargeGoodsEntity.setPrice(
                    BigDecimal.valueOf(chargeGoodsEntity.getPrice().doubleValue() * payChannelEntity.getExchangeRate().doubleValue())
                            .setScale(2, RoundingMode.HALF_UP)
            );
        });
        return chargeGoodsConverter.toChargesList(chargeGoodsVOS);
    }
}
