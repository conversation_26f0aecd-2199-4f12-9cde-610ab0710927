package com.steamgo1.csgoskinapi.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinapi.config.websocket.WebSocket;
import com.steamgo1.csgoskinapi.config.wechat.WxMpProperties;
import com.steamgo1.csgoskinapi.converter.CaseConverter;
import com.steamgo1.csgoskinapi.dto.LotteryResultDTO;
import com.steamgo1.csgoskinapi.enums.WxMsgType;
import com.steamgo1.csgoskinapi.service.*;
import com.steamgo1.csgoskinapi.vo.BattleHomeVO;
import com.steamgo1.csgoskinapi.vo.BattleLotterResultVO;
import com.steamgo1.csgoskinapi.vo.CaseFullInfoVO;
import com.steamgo1.csgoskincommon.contant.CsgoContants;
import com.steamgo1.csgoskincommon.dao.*;
import com.steamgo1.csgoskincommon.entity.*;
import com.steamgo1.csgoskincommon.entity.enums.BattleHomeCaseStatus;
import com.steamgo1.csgoskincommon.entity.enums.BattleHomeStatus;
import com.steamgo1.csgoskincommon.entity.enums.UserPackageSource;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageMethod;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageType;
import com.steamgo1.csgoskincommon.vo.websocket.MessageVO;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
public class AsyncTaskServiceImpl implements AsyncTaskService {

    @Value("${rabbitmq.exchange.csgo}")
    private String exchageName;

    @Value("${wx.mp.msgTemplate.pickUp}")
    private String pickUpTemplateId;

    @Value("${wx.mp.msgTemplate.rollHome}")
    private String rollhomeTemplateId;


    @Autowired
    private BattleHomeReposiotry battleHomeReposiotry;

    @Autowired
    private BattleHomeUserReposiotry battleHomeUserReposiotry;

    @Autowired
    private BattleHomeCaseReposiotry battleHomeCaseReposiotry;


    @Autowired
    private UserService userService;

    @Autowired
    private CaseService caseService;

    @Autowired
    private CaseConverter caseConverter;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private BattleHomeCaseUserRecordReposiotry battleHomeCaseUserRecordReposiotry;

    @Autowired
    private SkinService skinService;

    @Autowired
    private WebSocket webSocket;

    @Autowired
    private LotteryService lotteryService;

    @Autowired
    private SkinRepository skinRepository;

    @Autowired
    private UserPackageRepository userPackageRepository;

    @Autowired
    private BattleHomeService battleHomeService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private BattleHomeCaseUserSkinReposiotry battleHomeCaseUserSkinReposiotry;

    @Autowired
    private WxMpService wxService;

    @Autowired
    private WxMpProperties properties;

    // battle开始游戏
    @Async("async-executor-guava")
    public void startBattleHome(BattleHomeEntity battleHome) {
        battleHome.setRounds(battleHome.getRounds() + 1);
        log.info("battleHome{} 第{}局开始", battleHome.getId(), battleHome.getRounds());
        BattleHomeCaseEntity battleHomeCase = battleHomeCaseReposiotry.findFirstByBattleHomeAndStatusOrderById(battleHome, BattleHomeCaseStatus.WAIT);
        List<BattleHomeUserEntity> battleHomeUserEntityList = battleHomeUserReposiotry.findByBattleHome(battleHome);
//        Map<Long, BigDecimal> userScoreM = new HashMap<>();
//        Map<Long, List<Long>> userSkinM = new HashMap<>();
        Map<Long, UserEntity> userEntityM = new HashMap<>();
        Map<Long, SkinEntity> skinEntityM = new HashMap<>();
        BattleLotterResultVO battleLotterResultVO = new BattleLotterResultVO();
        battleLotterResultVO.setBattleHomeId(battleHome.getId());
        battleLotterResultVO.setRounds(battleHome.getRounds());
        battleLotterResultVO.setRoundsTotal(battleHome.getTotalRounds());
        battleLotterResultVO.setCaseInfo(caseConverter.toCaseInfoVO(battleHomeCase.getBox()));
        List<BattleLotterResultVO.LotteryResult> lotteryResultList = new ArrayList<>();
        for (BattleHomeUserEntity battleHomeUser : battleHomeUserEntityList) {
            Long userId = battleHomeUser.getUser().getId();
//            if(!userScoreM.containsKey(userId)){
//                userScoreM.put(userId, BigDecimal.ZERO);
//                userSkinM.put(userId, new ArrayList<>());
//                userEntityM.put(userId, battleHomeUser.getUser());
//            }
            userEntityM.put(userId, battleHomeUser.getUser());
            // 记录算法开箱绑定记录 TODO
            LotteryResultDTO lotteryResultDTO = lotteryService.lottery(battleHomeUser.getUser());
            BattleHomeCaseUserRecordEntity battleHomeCaseUserRecord = new BattleHomeCaseUserRecordEntity();
            battleHomeCaseUserRecord.setBattleHome(battleHome);
            battleHomeCaseUserRecord.setBattleHomeCase(battleHomeCase);
            battleHomeCaseUserRecord.setUser(battleHomeUser.getUser());
            battleHomeCaseUserRecord.setAlgorithmData(lotteryResultDTO.getAlgorithmData());
            battleHomeCaseUserRecord.setRoll(lotteryResultDTO.getRoll());
            battleHomeCaseUserRecord.setRounds(lotteryResultDTO.getAlgorithmData().getRounds() - 1);
            BattleLotterResultVO.LotteryResult lotteryResult = new BattleLotterResultVO.LotteryResult();
            // 用户结果
            lotteryResult.setRoll(lotteryResultDTO.getRoll());
            lotteryResult.setUser(userService.queryUserPublicInfo(battleHomeUser.getUser().getId()));
            for (CaseFullInfoVO.CaseSkinVO skinVO : caseService.getCaseFullInfo(battleHomeCase.getBox().getId(), CsgoContants.levelNumber.LEVEL_1).getSkins()) {
                if (skinVO.getMinRoll() <= lotteryResultDTO.getRoll() && skinVO.getMaxRoll() >= lotteryResultDTO.getRoll()) {
                    SkinEntity skin = skinRepository.findById(skinVO.getSkinInfo().getId()).get();
                    battleHomeCaseUserRecord.setSkin(skin);
                    lotteryResult.setSkinInfo(skinService.querySkinInfoById(skin.getId()));
//                    userScoreM.put(userId, userScoreM.get(userId).add(skin.getPrice()));
//                    userSkinM.get(userId).add(skin.getId());
                    skinEntityM.put(skin.getId(), skin);
                    break;
                }
            }
            battleHomeCaseUserRecordReposiotry.save(battleHomeCaseUserRecord);
            lotteryResultList.add(lotteryResult);
        }
        battleHomeCase.setStatus(BattleHomeCaseStatus.OPEN);
        battleLotterResultVO.setLotteryResult(lotteryResultList);
        battleHomeCaseReposiotry.save(battleHomeCase);
        // 通知用户
        // 通知大厅用户
        MessageVO messageResultVO = new MessageVO(WebSocketMessageMethod.SEND_ALL, WebSocketMessageType.BATTLE_RESULT, battleLotterResultVO);
        rabbitTemplate.convertAndSend(exchageName, "websocket.battleHomeLottery", JSONObject.toJSONString(messageResultVO));
        // 结束了
        log.info("battleHome 第{}局 总局数量：{}", battleHome.getRounds(), battleHome.getTotalRounds());
        if (battleHome.getRounds().equals(battleHome.getTotalRounds())) {
            battleHome.setBattleHomeStatus(BattleHomeStatus.FINISH);
            Map<Long, BigDecimal> userScoreM = new HashMap<>();
            for (UserEntity userEntity : userEntityM.values()) {
                BigDecimal score = battleHomeCaseUserRecordReposiotry.findByUserAndBattleHome(userEntity, battleHome).stream().map(item -> item.getSkin().getPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
                userScoreM.put(userEntity.getId(), score);
                log.info("用户ID: {} 用户名： {} 最终分数：{}", userEntity.getId(), userEntity.getNickname(), score);
            }
            BigDecimal maxScore = Collections.max(userScoreM.values());
            log.info("竞技最大分数：{}", maxScore);
            List<Long> winUserIds = userScoreM.keySet().stream().filter(userId -> userScoreM.get(userId).compareTo(maxScore) > -1).collect(Collectors.toList());
            List<Long> loserUserIds = userScoreM.keySet().stream().filter(userId -> userScoreM.get(userId).compareTo(maxScore) == -1).collect(Collectors.toList());
            log.info("胜利者ID：{}", JSONObject.toJSONString(winUserIds));
            log.info("失败者ID：{}", JSONObject.toJSONString(loserUserIds));
            for (Long loserUserId : loserUserIds) {
                BattleHomeCaseUserSkinEntity battleHomeCaseUserSkinEntity = new BattleHomeCaseUserSkinEntity();
                battleHomeCaseUserSkinEntity.setBattleHome(battleHome);
                battleHomeCaseUserSkinEntity.setUser(userEntityM.get(loserUserId));
                SkinEntity randomSkin = skinRepository.findRandomSkin();
                if (randomSkin != null) {
                    battleHomeCaseUserSkinEntity.setSkin(randomSkin);
                    UserPackageEntity userPackage = new UserPackageEntity();
                    userPackage.setUser(userEntityM.get(loserUserId));
                    userPackage.setSkin(randomSkin);
                    userPackage.setPrice(randomSkin.getPrice());
                    userPackage.setDiamond(randomSkin.getDiamond());
                    userPackage.setSource(UserPackageSource.BATTLE);
                    userPackage.setIsReceived(false);
                    userPackageRepository.save(userPackage);
                }
                battleHomeCaseUserSkinReposiotry.save(battleHomeCaseUserSkinEntity);
                BattleHomeUserEntity battleHomeUserEntity = battleHomeUserReposiotry.findByBattleHomeAndUser(battleHome, userEntityM.get(loserUserId));
                battleHomeUserEntity.setIsWin(false);
                battleHomeUserReposiotry.save(battleHomeUserEntity);
            }
            if (winUserIds.size() > 1) {
                log.info("平局情况, 各自获取自己的奖品");
                List<UserPackageEntity> userPackageEntityList = new ArrayList<>();
                for (Long userId : winUserIds) {
                    // 放入背包
                    for (BattleHomeCaseUserRecordEntity battleHomeCaseUserRecord : battleHomeCaseUserRecordReposiotry.findByUserAndBattleHome(userEntityM.get(userId), battleHome)) {
                        UserPackageEntity userPackage = new UserPackageEntity();
                        userPackage.setUser(userEntityM.get(userId));
                        userPackage.setSkin(battleHomeCaseUserRecord.getSkin());
                        userPackage.setSource(UserPackageSource.BATTLE);
                        userPackage.setCaseId(battleHomeCaseUserRecord.getBattleHomeCase().getBox().getId());
                        userPackage.setIsReceived(false);
                        userPackage.setDiamond(battleHomeCaseUserRecord.getSkin().getDiamond());
                        userPackage.setPrice(battleHomeCaseUserRecord.getSkin().getPrice());
                        userPackageEntityList.add(userPackage);
                        BattleHomeCaseUserSkinEntity battleHomeCaseUserSkinEntity = new BattleHomeCaseUserSkinEntity();
                        battleHomeCaseUserSkinEntity.setBattleHome(battleHome);
                        battleHomeCaseUserSkinEntity.setUser(userEntityM.get(userId));
                        battleHomeCaseUserSkinEntity.setSkin(battleHomeCaseUserRecord.getSkin());
                        battleHomeCaseUserSkinReposiotry.save(battleHomeCaseUserSkinEntity);
                    }
                    BattleHomeUserEntity battleHomeUserEntity = battleHomeUserReposiotry.findByBattleHomeAndUser(battleHome, userEntityM.get(userId));
                    battleHomeUserEntity.setIsWin(true);
                    battleHomeUserReposiotry.save(battleHomeUserEntity);
                }
                userPackageRepository.saveAll(userPackageEntityList);
            }
            if (winUserIds.size() == 1) {
                List<UserPackageEntity> userPackageEntityList = new ArrayList<>();
                Long userId = winUserIds.get(0);
                log.info("非平局情况, {}获取所有的奖品", userId);
                for (BattleHomeCaseUserRecordEntity battleHomeCaseUserRecord : battleHomeCaseUserRecordReposiotry.findByBattleHome(battleHome)) {
                    UserPackageEntity userPackage = new UserPackageEntity();
                    userPackage.setUser(userEntityM.get(userId));
                    userPackage.setSkin(battleHomeCaseUserRecord.getSkin());
                    userPackage.setPrice(battleHomeCaseUserRecord.getSkin().getPrice());
                    userPackage.setDiamond(battleHomeCaseUserRecord.getSkin().getDiamond());
                    userPackage.setIsReceived(false);
                    userPackage.setSource(UserPackageSource.BATTLE);
                    userPackage.setCaseId(battleHomeCaseUserRecord.getBattleHomeCase().getBox().getId());
                    userPackageEntityList.add(userPackage);
                    BattleHomeCaseUserSkinEntity battleHomeCaseUserSkinEntity = new BattleHomeCaseUserSkinEntity();
                    battleHomeCaseUserSkinEntity.setBattleHome(battleHome);
                    battleHomeCaseUserSkinEntity.setUser(userEntityM.get(userId));
                    battleHomeCaseUserSkinEntity.setSkin(battleHomeCaseUserRecord.getSkin());
                    battleHomeCaseUserSkinReposiotry.save(battleHomeCaseUserSkinEntity);
                }
                BattleHomeUserEntity battleHomeUserEntity = battleHomeUserReposiotry.findByBattleHomeAndUser(battleHome, userEntityM.get(userId));
                battleHomeUserEntity.setIsWin(true);
                battleHomeUserReposiotry.save(battleHomeUserEntity);
                userPackageRepository.saveAll(userPackageEntityList);
            }
            battleHomeReposiotry.save(battleHome);
            BattleHomeVO battleHomeVO = battleHomeService.queryBattleHomeBaseInfoById(battleHome.getId());
            MessageVO messageVO = new MessageVO(WebSocketMessageMethod.SEND_ALL, WebSocketMessageType.BATTLE_UPDATE, battleHomeVO);
            rabbitTemplate.convertAndSend(exchageName, "websocket.battleHomeLottery", JSONObject.toJSONString(messageVO));
        }
        battleHomeReposiotry.save(battleHome);
    }

    @Override
    @Async("async-executor-guava")
    public void sendWebSocketMessageToAll(String data) {
        webSocket.sendAllMessage(data);
    }

    @Override
    @Async("async-executor-guava")
    public void sendWebSocketMessageToOne(Long userId, String data) {
        webSocket.sendOneMessage(userId, data);
    }

    @Override
    @Async("async-executor-guava")
    public void sendMessageToWechat(WxMsgType wxMsgType, UserEntity user, String... message) {
        if (!wxService.switchover(properties.getConfigs().get(0).getAppId())) {
            log.error("微信公众号配置错误");
            return;
        }
        WxMpTemplateMessage templateMessage;
        switch (wxMsgType) {
            case PICKUP:
                templateMessage = WxMpTemplateMessage.builder()
                        .toUser(user.getWxOpenid())
                        .templateId(pickUpTemplateId)
                        .url("")
                        .build();
                templateMessage.addData(new WxMpTemplateData("thing4", message[0], "#FF00FF"));
                templateMessage.addData(new WxMpTemplateData("character_string5", message[1], "#FF00FF"));
                try {
                    wxService.getTemplateMsgService().sendTemplateMsg(templateMessage);
                } catch (WxErrorException e) {
                    log.error("推送公众号失败： {}", e.getMessage());
                }
                break;
            case ROLLHOME:
                templateMessage = WxMpTemplateMessage.builder()
                        .toUser(user.getWxOpenid())
                        .templateId(rollhomeTemplateId)
                        .url("")
                        .build();
                templateMessage.addData(new WxMpTemplateData("thing4", message[0], "#FF00FF"));
                try {
                    wxService.getTemplateMsgService().sendTemplateMsg(templateMessage);
                } catch (WxErrorException e) {
                    log.error("推送公众号失败： {}", e.getMessage());
                }
                break;
        }
    }
}

