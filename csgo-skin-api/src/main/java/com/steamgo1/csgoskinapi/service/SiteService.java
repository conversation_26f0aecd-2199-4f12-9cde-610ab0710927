package com.steamgo1.csgoskinapi.service;

import com.steamgo1.csgoskinapi.vo.*;
import com.steamgo1.csgoskincommon.entity.DataDictionaryEntity;
import com.steamgo1.csgoskincommon.entity.SysUserBenefitsEntity;
import com.steamgo1.csgoskincommon.vo.AnnouncementVO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface SiteService {
    /**
     * 从字典中获取值
     *
     * @param dataDictionaryId
     * @return
     */
    String getValueByKeyID(Long dataDictionaryId);

    /**
     * 从字典中获取级别
     *
     * @param dataDictionaryId
     * @return
     */
    Integer getGradleByKeyID(Long dataDictionaryId);

    /**
     * 获取汇率
     *
     * @return
     */
    SysExchangeRateVO getExchangeRate();

    /**
     * 获取配置的联系方式
     *
     * @return
     */
    ContactInfomationVO getContactInfomationVO();

    /**
     * 首充福利
     */
    SysUserBenefitsEntity getSysUserBenefits();

    /**
     * 获取公告
     */
    List<AnnouncementVO> queryAnnountVO();

    /**
     * 初始化配置
     */
    void initSysConfig();

    /**
     * 查询首页banner
     *
     * @return
     */
    List<IndexBannerVO> queryIndexBanner();

    /**
     * 查询历史最高价值饰品
     *
     * @return
     */
    List<WinSkinTop3VO> queryWinSKinTop3(HttpServletRequest request);

    StatisticsInfoVO getStatistics();

    /**
     * 递归向上查询到指定父级代码的数据字典实体
     * 
     * @param dataDictionaryId 起始数据字典ID
     * @param parentCode 目标父级代码
     * @return 找到的父级数据字典实体，未找到返回null
     */
    DataDictionaryEntity findParentByCode(Long dataDictionaryId, String parentCode);

    DataDictionaryEntity findDictionaryById(Long dataDictionaryId);
}
