package com.steamgo1.csgoskinapi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinapi.converter.IndexBannerConverter;
import com.steamgo1.csgoskinapi.converter.SysExchangeRateConverter;
import com.steamgo1.csgoskinapi.service.SiteService;
import com.steamgo1.csgoskinapi.service.SkinService;
import com.steamgo1.csgoskinapi.utils.IPUtils;
import com.steamgo1.csgoskinapi.vo.*;
import com.steamgo1.csgoskincommon.bo.OcpcTaskBO;
import com.steamgo1.csgoskincommon.converter.SiteConverter;
import com.steamgo1.csgoskincommon.dao.*;
import com.steamgo1.csgoskincommon.entity.*;
import com.steamgo1.csgoskincommon.entity.enums.UserType;
import com.steamgo1.csgoskincommon.enums.OcpcRabbitmqType;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import com.steamgo1.csgoskincommon.vo.AnnouncementVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@Slf4j
@RequiredArgsConstructor
public class SiteServiceImpl implements SiteService {
    @Value("${spring.redis.prefix.data-dictionary}")
    private String redisDictionaryPrefix;

    @Value("${spring.redis.prefix.sys}")
    private String redisSysPrefix;

    @Value("${spring.redis.prefix.user}")
    private String redisUserPrefix;

    @Autowired
    private DataDictionaryRepository dataDictionaryRepository;

    @Autowired
    private SysExchangeRateRepository sysExchangeRateRepository;

    @Autowired
    private SysExchangeRateConverter sysExchangeRateConverter;

    @Autowired
    private SysContactInformationRepository sysContactInformationRepository;

    @Autowired
    private SysUserBenefitsRepository sysUserBenefitsRepository;

    @Autowired
    private AnnouncementRepository announcementRepository;

    @Autowired
    private SiteConverter siteConverter;

    @Autowired
    private SysIndexBannerRepository sysIndexBannerRepository;

    @Autowired
    private IndexBannerConverter indexBannerConverter;

    @Autowired
    private UserPackageRepository userPackageRepository;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private CaseUserRecordRepository caseUserRecordRepository;
    @Autowired
    private BattleHomeReposiotry battleHomeReposiotry;

    @Autowired
    private RecordPercentageRepository recordPercentageRepository;
//
//    @Autowired
//    private UserService userService;

    @Autowired
    private SkinService skinService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Value("${rabbitmq.exchange.csgo}")
    private String exchageName;
    @Autowired
    private UserRepository userRepository;


    @Override
    @PostConstruct
    public void initSysConfig() {
        log.info("初始化配置++++++++++++++++");
        if (!sysExchangeRateRepository.existsByIdNotNull()) {
            SysExchangeRateEntity sysExchangeRateEntity = new SysExchangeRateEntity();
            sysExchangeRateEntity.setCoinToDiamond(BigDecimal.valueOf(6.5));
            sysExchangeRateEntity.setCoinTocny(BigDecimal.valueOf(6.5));
            sysExchangeRateEntity.setPercentagePremium(BigDecimal.valueOf(1.098));
            sysExchangeRateEntity.setZbtToCny(BigDecimal.valueOf(6.380));
            sysExchangeRateEntity.setZbtToCnyPremium(BigDecimal.valueOf(1.050));
            sysExchangeRateRepository.save(sysExchangeRateEntity);
            log.info("+++++++++++++++++++++++初始化汇率");
        }
        if (!sysUserBenefitsRepository.existsByIdNotNull()) {
            SysUserBenefitsEntity sysUserBenefitsEntity = new SysUserBenefitsEntity();
            sysUserBenefitsEntity.setFreeCoin(BigDecimal.valueOf(0));
            sysUserBenefitsEntity.setFirstChargePercentage(BigDecimal.valueOf(0));
            sysUserBenefitsEntity.setInviteEncoragePercentage(BigDecimal.valueOf(0.01));
            sysUserBenefitsEntity.setInviteFirstChargeFreeCoin(BigDecimal.valueOf(5));
            sysUserBenefitsRepository.save(sysUserBenefitsEntity);
            log.info("+++++++++++++++++++++++初始化用户福利");
        }
//        if(!sysContactInformationRepository.existsByIdNotNull()){
//            SysContactInformationEntity sysContactInformationEntity = new SysContactInformationEntity(){{
//               setQqGroup();
//               setWechatUrl();
//            }};
//            sysContactInformationRepository.save(sysContactInformationEntity);
//        }

    }

    @Override
    public List<IndexBannerVO> queryIndexBanner() {
        return indexBannerConverter.toIndexBannerVO(sysIndexBannerRepository.findByIsDeletedIsFalseOrderByGradleDesc());
    }

    public UserPubicInfoVO queryUserPublicInfo(Long userId) {
        String userInfoRedisKey = redisUserPrefix + ":" + userId;
        UserInfoVO userInfoVO = RedisUtils.get(userInfoRedisKey, UserInfoVO.class);
        UserPubicInfoVO userPubicInfoVO = new UserPubicInfoVO();
        if (userInfoVO != null) {
            userPubicInfoVO.setAvatar(userInfoVO.getAvatar());
            userPubicInfoVO.setNickname(userInfoVO.getNickname());
            userPubicInfoVO.setId(userInfoVO.getId());
            userPubicInfoVO.setType(userInfoVO.getType());
            userPubicInfoVO.setIsRobot(userInfoVO.getType().equals(UserType.ROBOT) ? true : false);
            return userPubicInfoVO;
        }
        log.warn("用户信息缓存未命中：{}", userId);
//        UserProfileEntity userProfile = userProfileRepository.findByUserId(userId);
        UserEntity user = userRepository.findById(userId);
//        UserEntity user = userProfile.getUser();
        userPubicInfoVO.setId(userId);
        userPubicInfoVO.setAvatar(user.getAvatar());
        userPubicInfoVO.setNickname(user.getNickname());
        userPubicInfoVO.setIsRobot(user.getType().equals(UserType.ROBOT) ? true : false);
        userPubicInfoVO.setType(user.getType().getCode());
        return userPubicInfoVO;
    }

    @Override
    public List<WinSkinTop3VO> queryWinSKinTop3(HttpServletRequest request) {
        List<Map<String, Object>> winSkinTop3 = userPackageRepository.queryWinSkinTop3();
        List<WinSkinTop3VO> winSkinTop3VOList = new ArrayList<>();
        for (Map<String, Object> map : winSkinTop3) {
            winSkinTop3VOList.add(new WinSkinTop3VO() {{
                setSkinInfoVO(skinService.querySkinInfoById(Long.valueOf(map.get("skinId").toString())));
                setUserInfo(queryUserPublicInfo(Long.valueOf(map.get("userId").toString())));
                setSource(Integer.valueOf(map.get("source").toString()) + 1);
                if (map.get("case_id") != null) {
                    setBoxId(Long.valueOf(map.get("case_id").toString()));
                }
            }});
        }
        String logidUrl = request.getHeader("X-HTTP-REFERER");
        log.info("======================================{}", logidUrl);
        if (StrUtil.isNotEmpty(logidUrl) && logidUrl.contains("bd_vid")) {
            OcpcTaskBO ocpcTaskBO = new OcpcTaskBO() {{
                setIp(IPUtils.getIpAddr(request));
                setType(OcpcRabbitmqType.CLICK);
            }};
            rabbitTemplate.convertAndSend(exchageName, "ocpc.baidu.click", JSONObject.toJSON(ocpcTaskBO));
        }
        return winSkinTop3VOList;
    }

    @Override
    public StatisticsInfoVO getStatistics() {
        Integer onlineUser = RedisUtils.get(redisUserPrefix + ":" + "ONLINE", Integer.class);
        long openCaseCount = caseUserRecordRepository.count();
        long battleHomeCount = battleHomeReposiotry.count();
        long percentageCount = recordPercentageRepository.count();
        return StatisticsInfoVO.builder().onlineCount(onlineUser)
                .battleCount(battleHomeCount).openCaseCount(openCaseCount).percentageCount(percentageCount)
                .build();
    }

    @Override
    public String getValueByKeyID(Long dataDictionaryId) {
        if (dataDictionaryId == null) {
            return "";
        }
        String reidsKey = redisDictionaryPrefix + ":" + dataDictionaryId;
         DataDictionaryEntity dataDictionaryEntity = RedisUtils.get(reidsKey, DataDictionaryEntity.class);
        if (dataDictionaryEntity != null) {
            return dataDictionaryEntity.getName();
        }
        Optional<DataDictionaryEntity> optionalDataDictionaryEntity = dataDictionaryRepository.findById(dataDictionaryId);
        dataDictionaryEntity = optionalDataDictionaryEntity.orElse(null);
        if (dataDictionaryEntity == null) {
            log.error("ID查字典值错误 ID: {}", dataDictionaryId);
            // todo 国际化
            // throw new CsgoSkinException("所选类别错误");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.dictionary.id.not.exist"));
        }
        RedisUtils.save(reidsKey, dataDictionaryEntity);
        return dataDictionaryEntity.getName();
    }



    @Override
    public Integer getGradleByKeyID(Long dataDictionaryId) {
        if (dataDictionaryId == null) {
            return 0;
        }
        String reidsKey = redisDictionaryPrefix + ":" + dataDictionaryId;
        DataDictionaryEntity dataDictionaryEntity = RedisUtils.get(reidsKey, DataDictionaryEntity.class);
        if (dataDictionaryEntity != null) {
            return dataDictionaryEntity.getGradle();
        }
        Optional<DataDictionaryEntity> optionalDataDictionaryEntity = dataDictionaryRepository.findById(dataDictionaryId);
        dataDictionaryEntity = optionalDataDictionaryEntity.orElse(null);
        if (dataDictionaryEntity == null) {
            log.error("ID查字典值错误 ID: {}", dataDictionaryId);
            // todo 国际化
            // throw new CsgoSkinException("所选类别错误");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.dictionary.id.not.exist"));
        }
        RedisUtils.save(reidsKey, dataDictionaryEntity);
        return dataDictionaryEntity.getGradle();
    }

    @Override
    public SysExchangeRateVO getExchangeRate() {
        String reidsKey = redisSysPrefix + ":" + "EXCHANGE_RATE";
        SysExchangeRateVO sysExchangeRateVO = RedisUtils.get(reidsKey, SysExchangeRateVO.class);
        if (sysExchangeRateVO != null) {
            return sysExchangeRateVO;
        }
        SysExchangeRateEntity sysExchangeRateEntity = sysExchangeRateRepository.findFirstByOrderById();
        if (sysExchangeRateEntity == null) {
            log.error("未配置汇率");
            // todo 国际化
            // throw new CsgoSkinException("后台未配置");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.exchange.rate.not.configured"));
        }
        sysExchangeRateVO = sysExchangeRateConverter.toSysExchangeVO(sysExchangeRateEntity);
        RedisUtils.save(reidsKey, sysExchangeRateVO);
        return sysExchangeRateVO;
    }

    @Override
    public ContactInfomationVO getContactInfomationVO() {
//        String reidsKey = redisSysPrefix + ":" + "CONTAC";
//        ContactInfomationVO contactInfomationVO = RedisUtils.get(reidsKey, ContactInfomationVO.class);
//        if(contactInfomationVO!=null){
//            return contactInfomationVO;
//        }
        SysContactInformationEntity sysContactInformationEntity = sysContactInformationRepository.findFirstByOrderById();
        if (sysContactInformationEntity == null) {
            log.error("未配置联系方式");
            // todo 国际化
            // throw new CsgoSkinException("后台未配置");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.contact.info.not.configured"));
        }
        ContactInfomationVO contactInfomationVO = new ContactInfomationVO() {{
            setQqGroup(sysContactInformationEntity.getQqGroup());
        }};
//        RedisUtils.save(reidsKey, contactInfomationVO);
        return contactInfomationVO;
    }

    @Override
    public SysUserBenefitsEntity getSysUserBenefits() {
        SysUserBenefitsEntity sysExchangeRateEntity = sysUserBenefitsRepository.findFirstByOrderById();
        if (sysExchangeRateEntity == null) {
            log.error("未配置联系方式");
            // todo 国际化
            // throw new CsgoSkinException("后台未配置");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.user.benefit.not.configured"));
        }
        return sysExchangeRateEntity;
    }

    @Override
    public List<AnnouncementVO> queryAnnountVO() {
        return siteConverter.toAnnouncementVOList(announcementRepository.findByIsShowIsTrue());
    }

    public UserProfileRepository getUserProfileRepository() {
        return userProfileRepository;
    }

    public void setUserProfileRepository(UserProfileRepository userProfileRepository) {
        this.userProfileRepository = userProfileRepository;
    }

    /**
     * 递归向上查询到指定父级代码的数据字典实体
     * 
     * @param dataDictionaryId 起始数据字典ID
     * @param parentCode 目标父级代码
     * @return 找到的父级数据字典实体，未找到返回null
     */
    @Override
    public DataDictionaryEntity findParentByCode(Long dataDictionaryId, String parentCode) {
        if (dataDictionaryId == null || parentCode == null) {
            return null;
        }
        
        DataDictionaryEntity currentEntity = this.findDictionaryById(dataDictionaryId);
        if (ObjectUtil.isNull(currentEntity)) {
            return null;
        }
        
        // 如果当前实体的code就是目标code，直接返回
        if (parentCode.equals(currentEntity.getCode())) {
            return currentEntity;
        }
        
        // 如果没有父级ID，说明已经到顶级，未找到目标
        if (currentEntity.getParentId() == null) {
            return null;
        }
        
        // 递归查询父级
        return findParentByCode(currentEntity.getParentId(), parentCode);
    }

    @Override
    public DataDictionaryEntity findDictionaryById(Long dataDictionaryId) {
        if (dataDictionaryId == null) {
            return null;
        }
        String reidsKey = redisDictionaryPrefix + ":" + dataDictionaryId;
        DataDictionaryEntity dataDictionaryEntity = RedisUtils.get(reidsKey, DataDictionaryEntity.class);
        if (dataDictionaryEntity != null) {
            return dataDictionaryEntity;
        }
        Optional<DataDictionaryEntity> optionalDataDictionaryEntity = dataDictionaryRepository.findById(dataDictionaryId);
        dataDictionaryEntity = optionalDataDictionaryEntity.orElse(null);
        if (dataDictionaryEntity == null) {
            return null;
        }
        RedisUtils.save(reidsKey, dataDictionaryEntity);
        return dataDictionaryEntity;
    }
}
