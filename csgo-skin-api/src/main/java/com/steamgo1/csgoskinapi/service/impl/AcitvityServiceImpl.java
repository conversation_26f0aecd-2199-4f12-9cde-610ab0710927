package com.steamgo1.csgoskinapi.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinapi.config.websocket.WebSocket;
import com.steamgo1.csgoskinapi.converter.ActivityConverter;
import com.steamgo1.csgoskinapi.service.ActivityService;
import com.steamgo1.csgoskinapi.utils.SecurityUtils;
import com.steamgo1.csgoskinapi.vo.*;
import com.steamgo1.csgoskincommon.dao.*;
import com.steamgo1.csgoskincommon.entity.*;
import com.steamgo1.csgoskincommon.entity.enums.CardCollectSource;
import com.steamgo1.csgoskincommon.entity.enums.DailyActivityType;
import com.steamgo1.csgoskincommon.entity.enums.RedPacketMethod;
import com.steamgo1.csgoskincommon.entity.enums.UserCoinChangeSource;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageMethod;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageType;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import com.steamgo1.csgoskincommon.utils.RateLimiterUtils;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import com.steamgo1.csgoskincommon.vo.websocket.MessageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;


@Slf4j
@Service
@Transactional
public class AcitvityServiceImpl implements ActivityService {
    @Value("${site.roll.min}")
    private Integer SEED_MIN_ROLL;
    @Value("${site.roll.max}")
    private Integer SEED_MAX_ROLL;

    @Value("${spring.redis.prefix.activity}")
    private String redisActivityPrefix;

    @Autowired
    private RedPacketRepository redPacketRepository;

    @Autowired
    private UserRedPacketRecordRepository userRedPacketRecordRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private UserCoinRecordRepository userCoinRecordRepository;

    @Autowired
    private CardCollectRepository cardCollectRepository;

    @Autowired
    private CardCollectCardRepository cardCollectCardRepository;

    @Autowired
    private ActivityConverter activityConverter;

    @Autowired
    private CardCollectUserRepository cardCollectUserRepository;

    @Autowired
    private ExtraBonuRepository extraBonuRepository;

    @Autowired
    private ExtraBonusUserRepository extraBonusUserRepository;

    @Autowired
    private DailyRedPacketRepository dailyRedPacketRepository;

    @Autowired
    private CheckInRedPacketRepository checkInRedPacketRepository;

    @Autowired
    private DailyActivityRepository dailyActivityRepository;

    @Autowired
    private DailyRedPacketRecordRepository dailyRedPacketRecordRepository;

    @Autowired
    private CheckInRedPacketRecordRepository checkInRedPacketRecordRepository;

    @Autowired
    private CheckInRedPacketConfigRepository checkInRedPacketConfigRepository;

    @Autowired
    private ConsumePlanUserRecordRepository consumePlanUserRecordRepository;
    @Autowired
    private ConsumePlanRepository consumePlanRepository;

    @Autowired
    private WebSocket webSocket;

    @Override
    public WalletVO receiveRedPicket(String key) {
        Long userId = SecurityUtils.getUserId();
        if (!RateLimiterUtils.tryAcquire(userId)) {
            log.warn("用户取回限流: {}", userId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.operation.too.frequent"));
        }
        UserEntity user = userRepository.findById(userId);
        UserProfileEntity userProfile = userProfileRepository.findByUser(user);
        RedPacketEntity redPacketEntity = redPacketRepository.findByIsDeletedIsFalseAndCode(key);
        if (redPacketEntity == null) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.red.packet.not.exist"));
        }
        if (redPacketEntity.getExpireTime().getTime() - System.currentTimeMillis() < 0) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.red.packet.expired"));
        }
        if (redPacketEntity.getTotal() <= redPacketEntity.getTotalCurrent()) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.red.packet.out.of.stock"));
        }
        if (userRedPacketRecordRepository.existsByUserIdAndRedPacketId(userId, redPacketEntity.getId())) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.red.packet.already.received"));
        }
        if (redPacketEntity.getMethod().equals(RedPacketMethod.NOVICE) && userRedPacketRecordRepository.existsByUserIdAndRedPacketMethod(userId, RedPacketMethod.NOVICE)) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.red.packet.novice.only.once"));
        }
        log.info("红包类型: {}", redPacketEntity.getType());
        switch (redPacketEntity.getType()) {
            case DAY:
                switch (redPacketEntity.getThresholdType()) {
                    case CHARGE:
                        BigDecimal userCharge = userCoinRecordRepository.userDayCharge(userId);
                        userCharge = userCharge == null ? BigDecimal.ZERO : userCharge;
                        if (userCharge.compareTo(redPacketEntity.getConsumeThreshold()) == -1) {
                            // todo 国际化
                            throw new CsgoSkinException(I18nUtils.getMessage("exception.need.charge.to.receive", redPacketEntity.getConsumeThreshold().subtract(userCharge).toString()));
                            // throw new CsgoSkinException(String.format("再充值 %s 金币即可领取", redPacketEntity.getConsumeThreshold().subtract(userCharge)));
                        }
                        break;
                    case CONSUME:
                        BigDecimal userConsume = userCoinRecordRepository.userDayConsume(userId);
                        userConsume = userConsume == null ? BigDecimal.ZERO : userConsume;
                        if (userConsume.compareTo(redPacketEntity.getConsumeThreshold()) == -1) {
                            // todo 国际化
                            throw new CsgoSkinException(I18nUtils.getMessage("exception.need.consume.to.receive", redPacketEntity.getConsumeThreshold().subtract(userConsume).toString()));
                            // throw new CsgoSkinException(String.format("再消费 %s 金币即可领取", redPacketEntity.getConsumeThreshold().subtract(userConsume)));
                        }
                }
                break;

            case WEEK:
                switch (redPacketEntity.getThresholdType()) {
                    case CHARGE:
                        BigDecimal userCharge = userCoinRecordRepository.userWeekCharge(userId);
                        userCharge = userCharge == null ? BigDecimal.ZERO : userCharge;
                        if (userCharge.compareTo(redPacketEntity.getConsumeThreshold()) == -1) {
                            // todo 国际化
                            throw new CsgoSkinException(I18nUtils.getMessage("exception.need.charge.to.receive", redPacketEntity.getConsumeThreshold().subtract(userCharge).toString()));
                            // throw new CsgoSkinException(String.format("再充值 %s 金币即可领取", redPacketEntity.getConsumeThreshold().subtract(userCharge)));
                        }
                        break;
                    case CONSUME:
                        BigDecimal userConsume = userCoinRecordRepository.userWeekConsume(userId);
                        userConsume = userConsume == null ? BigDecimal.ZERO : userConsume;
                        if (userConsume.compareTo(redPacketEntity.getConsumeThreshold()) == -1) {
                            // todo 国际化
                            throw new CsgoSkinException(I18nUtils.getMessage("exception.need.consume.to.receive", redPacketEntity.getConsumeThreshold().subtract(userConsume).toString()));
                            // throw new CsgoSkinException(String.format("再消费 %s 金币即可领取", redPacketEntity.getConsumeThreshold().subtract(userConsume)));
                        }
                }
                break;
            case MONTH:
                switch (redPacketEntity.getThresholdType()) {
                    case CHARGE:
                        BigDecimal userCharge = userCoinRecordRepository.userMonthCharge(userId);
                        userCharge = userCharge == null ? BigDecimal.ZERO : userCharge;
                        if (userCharge.compareTo(redPacketEntity.getConsumeThreshold()) == -1) {
                            // todo 国际化
                            throw new CsgoSkinException(I18nUtils.getMessage("exception.need.charge.to.receive", redPacketEntity.getConsumeThreshold().subtract(userCharge).toString()));
                            // throw new CsgoSkinException(String.format("再充值 %s 金币即可领取", redPacketEntity.getConsumeThreshold().subtract(userCharge)));
                        }
                        break;
                    case CONSUME:
                        BigDecimal userConsume = userCoinRecordRepository.userMonthConsume(userId);
                        userConsume = userConsume == null ? BigDecimal.ZERO : userConsume;
                        if (userConsume.compareTo(redPacketEntity.getConsumeThreshold()) == -1) {
                            // todo 国际化
                            throw new CsgoSkinException(I18nUtils.getMessage("exception.need.consume.to.receive", redPacketEntity.getConsumeThreshold().subtract(userConsume).toString()));
                            // throw new CsgoSkinException(String.format("再消费 %s 金币即可领取", redPacketEntity.getConsumeThreshold().subtract(userConsume)));
                        }
                }
        }
        // 领取记录
        UserRedPacketRecordEntity userRedPacketRecordEntity = new UserRedPacketRecordEntity();
        userRedPacketRecordEntity.setRedPacket(redPacketEntity);
        userRedPacketRecordEntity.setUser(user);
        userRedPacketRecordRepository.save(userRedPacketRecordEntity);
        //        金币变动
        userProfile.setCoin(userProfile.getCoin().add(redPacketEntity.getCoin()));
        userProfile = userProfileRepository.save(userProfile);
        // 金币记录
        UserCoinRecordEntity userCoinRecordEntity = new UserCoinRecordEntity();
        userCoinRecordEntity.setUser(user);
        userCoinRecordEntity.setSource(UserCoinChangeSource.RED_PACKET);
        userCoinRecordEntity.setAmount(redPacketEntity.getCoin());
        userCoinRecordEntity.setAfterAmount(userProfile.getCoin());
        userCoinRecordEntity.setIsPositive(true);
        userCoinRecordRepository.save(userCoinRecordEntity);
        UserProfileEntity finalUserProfile = userProfile;
        redPacketEntity.setTotalCurrent(redPacketEntity.getTotalCurrent() + 1);
        redPacketRepository.save(redPacketEntity);
        return new WalletVO() {{
            setCoin(finalUserProfile.getCoin());
            setDiamond(finalUserProfile.getDiamond());
        }};
    }

    @Override
    public CardCollectVO queryCardCollect() {
        String redisCardCollect = redisActivityPrefix + ":CARD_COLLECT";
        String redisCardFullCollect = redisActivityPrefix + ":CARD_COLLECT_FULL";
        CardCollectVO cardCollectVO = RedisUtils.get(redisCardCollect, CardCollectVO.class);
        if (cardCollectVO != null) {
            return cardCollectVO;
        }
        CardCollectEntity cardCollectEntity = cardCollectRepository.findTopByIsActivateTrueAndIsDeletedIsFalseOrderByIdDesc();
        if (cardCollectEntity == null) {
            return null;
        }
        List<CardCollectCardEntity> cardCollectCardEntityList = cardCollectCardRepository.findByCardCollectOrderById(cardCollectEntity);
        cardCollectVO = activityConverter.toCardCollectVO(cardCollectEntity, cardCollectCardEntityList);
        Integer roll = SEED_MIN_ROLL;
        for (CardCollectVO.CardCollectCard cardCollectCard : cardCollectVO.getCardList()) {
            cardCollectCard.setMinRoll(roll);
            roll += cardCollectCard.getProbability().multiply(BigDecimal.valueOf(SEED_MAX_ROLL)).intValue();
            cardCollectCard.setMaxRoll(roll - 1);
        }
        List<Long> userJoinIds = cardCollectRepository.querJoinUserId(cardCollectEntity.getId());
        List<List<Long>> cardUserIds = new ArrayList<>();
        for (CardCollectCardEntity cardCollectCardEntity : cardCollectCardEntityList) {
            List<Long> cardUserIdList = cardCollectUserRepository.queryUserIds(cardCollectCardEntity.getId());
            cardUserIds.add(cardUserIdList);
        }
        Integer totalPeople = cardCollectEntity.getCheatNum();
        for (Long userId : userJoinIds) {
            boolean is_collect = true;
            for (List<Long> cardUserIdsL : cardUserIds) {
                if (!cardUserIdsL.contains(userId)) {
                    is_collect = false;
                    break;
                }
            }
            if (is_collect) {
                totalPeople += 1;
            }
        }
        cardCollectVO.setTotalPeople(totalPeople);
        if (cardCollectVO != null) {
            RedisUtils.save(redisCardCollect, cardCollectVO);
            RedisUtils.save(redisCardFullCollect, activityConverter.toCardCollectFullVO(cardCollectVO));
        }
        return cardCollectVO;
    }

    public CardCollectFullVO queryCardCollectFull() {
        String redisCardCollect = redisActivityPrefix + ":CARD_COLLECT";
        String redisCardFullCollect = redisActivityPrefix + ":CARD_COLLECT_FULL";
        CardCollectFullVO cardCollectFullVO = RedisUtils.get(redisCardFullCollect, CardCollectFullVO.class);
        if (cardCollectFullVO != null) {
            return cardCollectFullVO;
        }
        CardCollectEntity cardCollectEntity = cardCollectRepository.findTopByIsActivateTrueAndIsDeletedIsFalseOrderByIdDesc();
        if (cardCollectEntity == null) {
            return null;
        }
        List<CardCollectCardEntity> cardCollectCardEntityList = cardCollectCardRepository.findByCardCollectOrderById(cardCollectEntity);
        CardCollectVO cardCollectVO = activityConverter.toCardCollectVO(cardCollectEntity, cardCollectCardEntityList);
        Integer roll = SEED_MIN_ROLL;
        for (CardCollectVO.CardCollectCard cardCollectCard : cardCollectVO.getCardList()) {
            cardCollectCard.setMinRoll(roll);
            roll += cardCollectCard.getProbability().multiply(BigDecimal.valueOf(SEED_MAX_ROLL)).intValue();
            cardCollectCard.setMaxRoll(roll - 1);
        }
        List<Long> userJoinIds = cardCollectRepository.querJoinUserId(cardCollectEntity.getId());
        List<List<Long>> cardUserIds = new ArrayList<>();
        for (CardCollectCardEntity cardCollectCardEntity : cardCollectCardEntityList) {
            List<Long> cardUserIdList = cardCollectUserRepository.queryUserIds(cardCollectCardEntity.getId());
            cardUserIds.add(cardUserIdList);
        }
        Integer totalPeople = 0;
        for (Long userId : userJoinIds) {
            boolean is_collect = true;
            for (List<Long> cardUserIdsL : cardUserIds) {
                if (!cardUserIdsL.contains(userId)) {
                    is_collect = false;
                    break;
                }
            }
            if (is_collect) {
                totalPeople += 1;
            }
        }
        cardCollectVO.setTotalPeople(totalPeople);
        cardCollectFullVO = activityConverter.toCardCollectFullVO(cardCollectVO);
        if (cardCollectVO != null) {
            RedisUtils.save(redisCardCollect, cardCollectVO);
            RedisUtils.save(redisCardFullCollect, cardCollectFullVO);
        }
        return cardCollectFullVO;
    }

    @Override
    public CardVO joinCardCollect(Long userId, Integer roll, CardCollectSource cardCollectSource) {
        CardCollectFullVO cardCollectVO = queryCardCollectFull();
        if (cardCollectVO == null) {
            return null;
        }
        CardCollectEntity cardCollectEntity = cardCollectRepository.findById(cardCollectVO.getId()).orElse(null);
        if (cardCollectEntity == null || !cardCollectEntity.getIsActivate() || cardCollectEntity.getIsDeleted()) {
            return null;
        }
        for (CardCollectFullVO.CardCollectCard cardCollectCard : cardCollectVO.getCardList()) {
            if (cardCollectCard.getMinRoll() <= roll && cardCollectCard.getMaxRoll() >= roll) {
                // 记录中奖卡片
                CardCollectCardEntity cardCollectCardEntity = cardCollectCardRepository.findById(cardCollectCard.getId()).orElse(null);
                if (cardCollectEntity == null) {
                    return null;
                }
                UserEntity user = userRepository.findById(userId);
                CardCollectUserEntity cardCollectUserEntity = new CardCollectUserEntity();
                cardCollectUserEntity.setCardCollectCard(cardCollectCardEntity);
                cardCollectUserEntity.setSource(cardCollectSource);
                cardCollectUserEntity.setUser(user);
                cardCollectUserRepository.save(cardCollectUserEntity);
                // 删除缓存
                String redisCardCollect = redisActivityPrefix + ":CARD_COLLECT";
                String redisCardFullCollect = redisActivityPrefix + ":CARD_COLLECT_FULL";
                RedisUtils.delete(redisCardCollect);
                RedisUtils.delete(redisCardFullCollect);
                return activityConverter.toCardVO(cardCollectCard);
            }
        }
        return null;
    }

    @Override
    public List<CardCollectUserVO> queryUserCard(Long cardCollectId) {
        Long userId = SecurityUtils.getUserId();
        if (!cardCollectRepository.existsByIsActivateTrueAndIsDeletedIsFalseAndId(cardCollectId)) {
            log.error("用户: {} 传了错误的id: {}", userId, cardCollectId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.activity.not.exist.or.ended"));
        }
        List<CardCollectCardEntity> cardCollectCardEntityList = cardCollectCardRepository.findByCardCollectId(cardCollectId);
        List<CardCollectUserVO> cardCollectUserVOList = new ArrayList<>();
        for (CardCollectCardEntity cardCollectCardEntity : cardCollectCardEntityList) {
            cardCollectUserVOList.add(new CardCollectUserVO() {{
                setId(cardCollectCardEntity.getId());
                setName(cardCollectCardEntity.getName());
                setPicture(cardCollectCardEntity.getPicture());
                setTotal(cardCollectUserRepository.countByUserIdAndCardCollectCardId(userId, cardCollectCardEntity.getId()));
            }});
        }
        return cardCollectUserVOList;
    }

    @Override
    public void startCardCollect(Long id, String messageId) {
        CardCollectEntity cardCollectEntity = cardCollectRepository.findById(id).orElse(null);
        if (cardCollectEntity == null || cardCollectEntity.getIsDeleted()) {
            log.warn("集卡活动已删除： {}", id);
            return;
        }
        if (!messageId.equals(cardCollectEntity.getRabbitMessageId())) {
            log.warn("集卡活动messageID不一致： {}", id);
            return;
        }
        List<CardCollectCardEntity> cardCollectCardEntityList = cardCollectCardRepository.findByCardCollectOrderById(cardCollectEntity);
        List<Long> userJoinIds = cardCollectRepository.querJoinUserId(cardCollectEntity.getId());
        List<List<Long>> cardUserIds = new ArrayList<>();
        for (CardCollectCardEntity cardCollectCardEntity : cardCollectCardEntityList) {
            List<Long> cardUserIdList = cardCollectUserRepository.queryUserIds(cardCollectCardEntity.getId());
            cardUserIds.add(cardUserIdList);
        }
        Integer totalPeople = cardCollectEntity.getCheatNum();
        List<Long> successUserIds = new ArrayList<>();
        for (Long userId : userJoinIds) {
            boolean is_collect = true;
            for (List<Long> cardUserIdsL : cardUserIds) {
                if (!cardUserIdsL.contains(userId)) {
                    is_collect = false;
                    break;
                }
            }
            if (is_collect) {
                totalPeople += 1;
                successUserIds.add(userId);
            }
        }
        if (totalPeople == 0) {
            log.warn("集卡活动无人集齐： {}", id);
            cardCollectEntity.setIsActivate(false);
            cardCollectRepository.save(cardCollectEntity);
            return;
        }
        BigDecimal coin = cardCollectEntity.getCoin().divide(BigDecimal.valueOf(totalPeople), 2, RoundingMode.DOWN);
        for (Long userId : successUserIds) {
            UserProfileEntity userProfile = userProfileRepository.findByUserId(userId);
            if (userProfile == null) {
                continue;
            }
            // 分金币
            userProfile.setCoin(userProfile.getCoin().add(coin));
            userProfileRepository.save(userProfile);
            // 金币记录
            UserCoinRecordEntity userCoinRecordEntity = new UserCoinRecordEntity();
            userCoinRecordEntity.setUser(userProfile.getUser());
            userCoinRecordEntity.setSource(UserCoinChangeSource.ACTIVITY_CARD);
            userCoinRecordEntity.setAmount(coin);
            userCoinRecordEntity.setAfterAmount(userProfile.getCoin());
            userCoinRecordEntity.setIsPositive(true);
            userCoinRecordEntity.setSourceId(cardCollectEntity.getId());
            userCoinRecordRepository.save(userCoinRecordEntity);
        }
        cardCollectEntity.setIsActivate(false);
        cardCollectRepository.save(cardCollectEntity);
    }

    @Override
    public CardVO joinExtraBonus(Long userId, CardCollectSource cardCollectSource, BigDecimal amount) {
        ExtraBonusEntity extraBonusEntity = extraBonuRepository.findTopByIsActivateTrue();
        if (extraBonusEntity == null) {
            return null;
        }
        if (extraBonusEntity.getConsumeThreshold().compareTo(amount) != -1) {
            return null;
        }
        switch (cardCollectSource.getCode()) {
            case 0:
                if (!extraBonusEntity.getEnableOpenCase()) {
                    return null;
                }
                break;
            case 1:
                if (!extraBonusEntity.getEnablePercentage()) {
                    return null;
                }
        }
        Random r = new Random();
        if (BigDecimal.valueOf(r.nextDouble()).compareTo(extraBonusEntity.getProbability()) == -1) {
//            记录
            ExtraBonusUserEntity extraBonusUserEntity = new ExtraBonusUserEntity();
            extraBonusUserEntity.setExtraBonus(extraBonusEntity);
            extraBonusUserEntity.setUser(userRepository.findById(userId));
            extraBonusUserEntity.setSource(cardCollectSource);
            extraBonusUserEntity.setIsReceived(false);
            extraBonusUserRepository.save(extraBonusUserEntity);
            return new CardVO() {{
                setName(extraBonusEntity.getName());
                setPicture(extraBonusEntity.getPicture());
            }};
        }
        return null;
    }

    @Override
    public UserDailyRedPacketVO queryUserDailyRedPacket(Long userId) {
        BigDecimal userCharge = userCoinRecordRepository.userDayCharge(userId);
        userCharge = userCharge == null ? BigDecimal.ZERO : userCharge;
        UserDailyRedPacketVO userDailyRedPacketVO = new UserDailyRedPacketVO();
        userDailyRedPacketVO.setCurrent(userCharge);
        List<UserDailyRedPacketVO.DailyRedPacketDetail> dailyRedPacketDetailList = new ArrayList<>();
        for (DailyRedPacketEntity dailyRedPacketEntity : dailyRedPacketRepository.findByIsDeletedIsFalse()) {
            UserDailyRedPacketVO.DailyRedPacketDetail dailyRedPacketDetail = new UserDailyRedPacketVO.DailyRedPacketDetail();
            dailyRedPacketDetail.setId(dailyRedPacketEntity.getId());
            dailyRedPacketDetail.setName(dailyRedPacketEntity.getName());
            dailyRedPacketDetail.setI18nFieldName(dailyRedPacketEntity.getI18nFieldName());
            dailyRedPacketDetail.setCoin(dailyRedPacketEntity.getCoin());
            dailyRedPacketDetail.setThreshold(dailyRedPacketEntity.getThreshold());
            if (userCharge.compareTo(dailyRedPacketEntity.getThreshold()) < 0) {
                dailyRedPacketDetail.setStatus(0);
            } else {
                Integer current = dailyRedPacketRecordRepository.dayDailyRedPacketRecordCount(userId, dailyRedPacketEntity.getId());
                current = current == null ? 0 : current;
                if (current >= 1) {
                    dailyRedPacketDetail.setStatus(2);
                } else {
                    dailyRedPacketDetail.setStatus(1);
                }
            }
            dailyRedPacketDetailList.add(dailyRedPacketDetail);
        }
        userDailyRedPacketVO.setDailyRedPacketDetailList(dailyRedPacketDetailList);
        return userDailyRedPacketVO;
    }

    @Override
    public UserCheckInRedPacketVO queryUserCheckInRedPacket(Long userId) {
        UserCheckInRedPacketVO userCheckInRedPacketVO = new UserCheckInRedPacketVO();
        Integer current = dailyActivityRepository.monthUserActivatyCount(userId, DailyActivityType.CHECK_IN.getCode() - 1);
        current = current == null ? 0 : current;
        userCheckInRedPacketVO.setCurrent(current);
        Integer todayCurrent = dailyActivityRepository.dayUserActivatyCount(userId, DailyActivityType.CHECK_IN.getCode() - 1);
        todayCurrent = todayCurrent == null ? 0 : todayCurrent;
        if (todayCurrent > 0) {
            userCheckInRedPacketVO.setIsCheckIn(true);
        } else {
            userCheckInRedPacketVO.setIsCheckIn(false);
        }
        List<UserCheckInRedPacketVO.CheckInRedPacketDetail> checkInRedPacketDetailList = new ArrayList<>();
        for (CheckInRedPacketEntity checkInRedPacketEntity : checkInRedPacketRepository.findByIsDeletedIsFalse()) {
            UserCheckInRedPacketVO.CheckInRedPacketDetail checkInRedPacketDetail = new UserCheckInRedPacketVO.CheckInRedPacketDetail();
            checkInRedPacketDetail.setId(checkInRedPacketEntity.getId());

            checkInRedPacketDetail.setName(checkInRedPacketEntity.getName());
            checkInRedPacketDetail.setThreshold(checkInRedPacketEntity.getThreshold());
            Integer monthCurrent = dailyActivityRepository.monthUserActivatyCount(userId, DailyActivityType.CHECK_IN.getCode() - 1);
            monthCurrent = monthCurrent == null ? 0 : monthCurrent;
            if (monthCurrent < checkInRedPacketEntity.getThreshold()) {
                checkInRedPacketDetail.setStatus(0);
            } else {
                Integer currentRcourdCOunt = checkInRedPacketRecordRepository.monthCheckInRedPacketRecordCount(userId, checkInRedPacketEntity.getId());
                currentRcourdCOunt = currentRcourdCOunt == null ? 0 : currentRcourdCOunt;
                if (currentRcourdCOunt > 0) {
                    checkInRedPacketDetail.setStatus(2);
                } else {
                    checkInRedPacketDetail.setStatus(1);
                }
            }
            checkInRedPacketDetailList.add(checkInRedPacketDetail);
        }
        userCheckInRedPacketVO.setCheckInRedPacketDetailList(checkInRedPacketDetailList);
        return userCheckInRedPacketVO;
    }

    @Override
    public WalletVO receiveUserDailyRedPacket(Long userId, Long id) {
        if (!RateLimiterUtils.tryAcquire(userId)) {
            log.warn("用户红包限流: {}", userId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.operation.too.frequent"));
        }
        DailyRedPacketEntity dailyRedPacketEntity = dailyRedPacketRepository.findById(id).orElse(null);
        if (dailyRedPacketEntity == null || dailyRedPacketEntity.getIsDeleted() == true) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.activity.not.exist.or.ended"));
        }
        BigDecimal userCharge = userCoinRecordRepository.userDayCharge(userId);
        userCharge = userCharge == null ? BigDecimal.ZERO : userCharge;
        if (userCharge.compareTo(dailyRedPacketEntity.getThreshold()) == -1) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.need.charge.to.receive", dailyRedPacketEntity.getThreshold().subtract(userCharge).toString()));
            // throw new CsgoSkinException(String.format("再充值 %s 金币即可领取", dailyRedPacketEntity.getThreshold().subtract(userCharge)));
        }
        Integer current = dailyRedPacketRecordRepository.dayDailyRedPacketRecordCount(userId, id);
        current = current == null ? 0 : current;
        if (current >= 1) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.red.packet.already.received"));
        }
        UserProfileEntity userProfile = userProfileRepository.findByUserId(userId);
        if (userProfile == null) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.user.info.error"));
        }
        // 领取记录
        DailyRedPacketRecordEntity dailyRedPacketRecordEntity = new DailyRedPacketRecordEntity();
        dailyRedPacketRecordEntity.setUser(userProfile.getUser());
        dailyRedPacketRecordEntity.setDailyRedPacket(dailyRedPacketEntity);
        dailyRedPacketRecordRepository.save(dailyRedPacketRecordEntity);
        // 发金币
        userProfile.setCoin(userProfile.getCoin().add(dailyRedPacketEntity.getCoin()));
        userProfileRepository.save(userProfile);
        // 金币记录
        UserCoinRecordEntity userCoinRecordEntity = new UserCoinRecordEntity();
        userCoinRecordEntity.setUser(userProfile.getUser());
        userCoinRecordEntity.setSource(UserCoinChangeSource.DAY_CHANGE_RED_PACKET);
        userCoinRecordEntity.setAmount(dailyRedPacketEntity.getCoin());
        userCoinRecordEntity.setAfterAmount(userProfile.getCoin());
        userCoinRecordEntity.setIsPositive(true);
        userCoinRecordEntity.setSourceId(dailyRedPacketRecordEntity.getId());
        userCoinRecordRepository.save(userCoinRecordEntity);
        return new WalletVO() {{
            setCoin(userProfile.getCoin());
            setDiamond(userProfile.getDiamond());
        }};
    }

    @Override
    public WalletVO receiveUserCheckInRedPacket(Long userId, Long id) {
        if (!RateLimiterUtils.tryAcquire(userId)) {
            log.warn("用户签到限流: {}", userId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.operation.too.frequent"));
        }
        CheckInRedPacketEntity checkInRedPacketEntity = checkInRedPacketRepository.findById(id).orElse(null);
        if (checkInRedPacketEntity == null || checkInRedPacketEntity.getIsDeleted() == true) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.activity.not.exist.or.ended"));
        }
        Integer current = dailyActivityRepository.monthUserActivatyCount(userId, DailyActivityType.CHECK_IN.getCode() - 1);
        current = current == null ? 0 : current;
        if (current < checkInRedPacketEntity.getThreshold()) {
            // todo 国际化
            throw new CsgoSkinException(String.format("再签到 %s 天即可领取", checkInRedPacketEntity.getThreshold() - current));
        }
        Integer monthCurrent = checkInRedPacketRecordRepository.monthCheckInRedPacketRecordCount(userId, checkInRedPacketEntity.getId());
        monthCurrent = monthCurrent == null ? 0 : monthCurrent;
        if (monthCurrent >= 1) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.checkin.reward.already.claimed"));
        }
        UserProfileEntity userProfile = userProfileRepository.findByUserId(userId);
        if (userProfile == null) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.user.info.error"));
        }
        // 领取记录
        CheckInRedPacketRecordEntity checkInRedPacketRecordEntity = new CheckInRedPacketRecordEntity();
        checkInRedPacketRecordEntity.setUser(userProfile.getUser());
        checkInRedPacketRecordEntity.setCheckInRedPacket(checkInRedPacketEntity);
        checkInRedPacketRecordRepository.save(checkInRedPacketRecordEntity);
        // 发金币
        Random rand = new Random();
        Integer probability = Integer.valueOf(rand.nextInt(100));
        CheckInRedPacketConfigEntity finalCheckInRedPacketConfigEntity = null;
        Integer finalProbability = 0;
        List<CheckInRedPacketConfigEntity> checkInRedPacketConfigEntityList = checkInRedPacketConfigRepository.findByCheckInRedPacketId(checkInRedPacketEntity.getId());
        for (CheckInRedPacketConfigEntity checkInRedPacketConfigEntity : checkInRedPacketConfigEntityList) {
            finalProbability += checkInRedPacketConfigEntity.getProbability();
            if (finalProbability >= probability) {
                finalCheckInRedPacketConfigEntity = checkInRedPacketConfigEntity;
                break;
            }
        }
        BigDecimal coin = finalCheckInRedPacketConfigEntity.getMinCoin().add(new BigDecimal(rand.nextDouble()).multiply(finalCheckInRedPacketConfigEntity.getMaxCoin().subtract(finalCheckInRedPacketConfigEntity.getMinCoin()))).setScale(2, BigDecimal.ROUND_HALF_UP);
        userProfile.setCoin(userProfile.getCoin().add(coin));
        userProfileRepository.save(userProfile);
        // 金币记录
        UserCoinRecordEntity userCoinRecordEntity = new UserCoinRecordEntity();
        userCoinRecordEntity.setUser(userProfile.getUser());
        userCoinRecordEntity.setSource(UserCoinChangeSource.CHECK_IN);
        userCoinRecordEntity.setAmount(coin);
        userCoinRecordEntity.setAfterAmount(userProfile.getCoin());
        userCoinRecordEntity.setIsPositive(true);
        userCoinRecordEntity.setSourceId(checkInRedPacketRecordEntity.getId());
        userCoinRecordRepository.save(userCoinRecordEntity);
        return new WalletVO() {{
            setCoin(userProfile.getCoin());
            setDiamond(userProfile.getDiamond());
        }};
    }

    @Override
    public void userCheckIn(Long userId) {
        if (!RateLimiterUtils.tryAcquire(userId)) {
            log.warn("用户签到限流: {}", userId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.operation.too.frequent"));
        }
        Integer current = dailyActivityRepository.dayUserActivatyCount(userId, DailyActivityType.CHECK_IN.getCode() - 1);
        current = current == null ? 0 : current;
        if (current >= 1) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.checkin.today.completed"));
        }
        DailyActivityEntity dailyActivityEntity = new DailyActivityEntity();
        dailyActivityEntity.setType(DailyActivityType.CHECK_IN);
        dailyActivityEntity.setUserId(userId);
        dailyActivityRepository.save(dailyActivityEntity);
    }

    @Override
    @Async("async-executor-guava")
    public void queryUserConsumePlan(Long userId, BigDecimal consume) {
        ConsumePlanMessageVO consumePlanMessageVO = new ConsumePlanMessageVO();
        BigDecimal totalConsume = userCoinRecordRepository.userDayConsume(userId);
        totalConsume = totalConsume == null ? BigDecimal.ZERO : totalConsume;
        totalConsume = totalConsume.add(consume);
        List<ConsumePlanEntity> consumePlanEntityList = consumePlanRepository.findAll();
        UserEntity user = userRepository.findById(userId);
        log.info("用户当前总消费： {}, 本次消费 {}", totalConsume, consume);
        for (ConsumePlanEntity consumePlan : consumePlanEntityList) {
            if (totalConsume.compareTo(consumePlan.getThreshold()) == -1) {
                log.info("总消费不达标: {}", consumePlan.getName());
                if (consumePlan.getThreshold().multiply(BigDecimal.valueOf(0.75)).compareTo(totalConsume) <= 0 && consumePlan.getThreshold().multiply(BigDecimal.valueOf(0.75)).compareTo(totalConsume.subtract(consume)) > 0) {
                    consumePlanMessageVO.setReConsume(consumePlan.getThreshold().subtract(totalConsume));
                    consumePlanMessageVO.setReTotal(consumePlan.getTotal());
                }
                break;
            } else {
                if (consumePlanUserRecordRepository.existsDayByUserIdAndConsumePlanId(userId, consumePlan.getId()) == null) {
                    log.info("总消费达标: {}", consumePlan.getName());
                    consumePlanMessageVO.setTotal(consumePlanMessageVO.getTotal() + consumePlan.getTotal());
                    // 记录
                    ConsumePlanUserRecordEntity consumePlanUserRecordEntity = new ConsumePlanUserRecordEntity();
                    consumePlanUserRecordEntity.setUser(user);
                    consumePlanUserRecordEntity.setConsumePlan(consumePlan);
                    consumePlanUserRecordRepository.save(consumePlanUserRecordEntity);
                }
            }
        }
        // 通知用户
        log.info("消费计划: {}", consumePlanMessageVO);
        MessageVO messageResultVO = new MessageVO(WebSocketMessageMethod.SEND_ONE, WebSocketMessageType.CONSUME_PLAN, consumePlanMessageVO);
//            asyncTaskService.sendWebSocketMessageToAll(JSONObject.toJSONString(messageResultVO));
        webSocket.sendOneMessage(userId, JSONObject.toJSONString(messageResultVO));
    }
}
