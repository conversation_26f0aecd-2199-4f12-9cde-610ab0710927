package com.steamgo1.csgoskinapi.service.impl;

import com.steamgo1.csgoskinapi.converter.PayChannelConverter;
import com.steamgo1.csgoskinapi.service.PayChannelService;
import com.steamgo1.csgoskinapi.vo.PayChannelVO;
import com.steamgo1.csgoskincommon.dao.PayChannelRepository;
import com.steamgo1.csgoskincommon.entity.PayChannelEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PayChannelServiceImpl implements PayChannelService {
    
    @Autowired
    private PayChannelRepository payChannelRepository;
    
    @Autowired
    private PayChannelConverter payChannelConverter;
    
    @Override
    public List<PayChannelVO> getAvailablePayChannels() {
        List<PayChannelEntity> availableChannels = payChannelRepository.findByStatus(1);
        return payChannelConverter.toPayChannelVOList(availableChannels);
    }
}