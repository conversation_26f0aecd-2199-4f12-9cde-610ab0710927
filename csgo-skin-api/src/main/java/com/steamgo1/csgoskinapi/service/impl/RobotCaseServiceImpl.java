package com.steamgo1.csgoskinapi.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinapi.dto.LotteryResultDTO;
import com.steamgo1.csgoskinapi.service.*;
import com.steamgo1.csgoskinapi.vo.CaseFullInfoVO;
import com.steamgo1.csgoskinapi.vo.CaseLotterResultVO;
import com.steamgo1.csgoskinapi.vo.UserInfoVO;
import com.steamgo1.csgoskinapi.vo.UserLotteryResultOfWebsocketVO;
import com.steamgo1.csgoskincommon.dao.*;
import com.steamgo1.csgoskincommon.entity.*;
import com.steamgo1.csgoskincommon.entity.enums.CardCollectSource;
import com.steamgo1.csgoskincommon.entity.enums.DailyActivityType;
import com.steamgo1.csgoskincommon.entity.enums.UserCoinChangeSource;
import com.steamgo1.csgoskincommon.entity.enums.UserPackageSource;
import com.steamgo1.csgoskincommon.enums.LotterySource;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageMethod;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageType;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.utils.CsvUtils;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import com.steamgo1.csgoskincommon.vo.websocket.MessageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
@Transactional
public class RobotCaseServiceImpl implements RobotCaseService {

    @Value("${spring.redis.prefix.user}")
    private String redisUserPrefix;
    @Autowired
    private CaseService caseService;


    @Autowired
    private LotteryService lotteryService;


    @Autowired
    private CaseRepository caseRepository;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private SkinRepository skinRepository;


    @Autowired
    private CaseUserRecordRepository caseUserRecordRepository;

    @Autowired
    private UserPackageRepository userPackageRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private UserCoinRecordRepository userCoinRecordRepository;

    @Autowired
    private AsyncTaskService asyncTaskService;

    @Autowired
    private ActivityService activityService;


    @Override
    @Async("async-executor-spring")
    public void rorbotOpenCaseById(Long caseId, Long userId, BigDecimal zhyk) {
        CaseEntity caseEntity = caseRepository.findByIdAndIsDeletedAndIsSale(caseId, false, true);
        UserProfileEntity userProfile = userProfileRepository.findByUserId(userId);
        if (caseEntity == null) {
            log.error("开箱错误caseId： {} userId: {}", caseId, userId);
            // todo 国际化
            //throw new CsgoSkinException("箱子不存在或已下架");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.case.not.exist.or.offline"));
        }
        if (caseEntity.getDisableOpenCase()) {
            // todo 国际化
            //throw new CsgoSkinException("非开箱箱子");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.case.not.openable"));
        }
        Integer rounds = 100;
        BigDecimal amount = caseEntity.getPrice().multiply(new BigDecimal(rounds)); // 总消耗
        BigDecimal initAmount = new BigDecimal("5000.00");
        if (userProfile.getCoin().compareTo(amount) == -1) {
            log.info("金币不足");
            // todo 国际化
            // throw new CsgoSkinException(I18nUtils.getMessage("exception.insufficient.coins"));
            throw new CsgoSkinException(I18nUtils.getMessage("exception.insufficient.coin"));
        }
        List<CaseLotterResultVO> caseLotterResultVOList = new ArrayList<>();
        for (int i = 0; i < rounds; i++) {
            int level = caseService.getLevel(caseId, userId, caseEntity);
            log.info("本次箱子为：{}，等级为：{}", caseEntity.getName(), level);
//            int level = 0;
            CaseLotterResultVO caseLotterResultVO = new CaseLotterResultVO();
            CaseFullInfoVO caseFullInfoVO = caseService.getCaseFullInfo(caseEntity.getId(), level);
            // 金币消耗 TODO
            if (userProfile.getCoin().compareTo(caseEntity.getPrice()) == -1) {
                log.info("金币不足");
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.insufficient.coins"));
            }
            userProfile.setCoin(userProfile.getCoin().subtract(caseEntity.getPrice()));
            // 经验 TODO
            userProfile.setExperience(userProfile.getExperience() + 10);
            userProfileRepository.save(userProfile);
            // 记录算法开箱绑定记录 TODO
            LotteryResultDTO lotteryResultDTO = lotteryService.lottery(userProfile.getUser());
            CaseUserRecordEntity recordOpenCase = new CaseUserRecordEntity();
            recordOpenCase.setUser(userProfile.getUser());
            recordOpenCase.setBox(caseEntity);
            recordOpenCase.setAlgorithmData(lotteryResultDTO.getAlgorithmData());
            recordOpenCase.setRoll(lotteryResultDTO.getRoll());
            recordOpenCase.setRounds(lotteryResultDTO.getAlgorithmData().getRounds() - 1);
            for (CaseFullInfoVO.CaseSkinVO skinVO : caseFullInfoVO.getSkins()) {
                if (skinVO.getMinRoll() <= lotteryResultDTO.getRoll() && skinVO.getMaxRoll() >= lotteryResultDTO.getRoll()) {
                    caseLotterResultVO.setSkinInfoVO(skinVO.getSkinInfo());
                    SkinEntity skin = skinRepository.findById(skinVO.getSkinInfo().getId()).get();
                    recordOpenCase.setSkin(skin);
                    // 放入背包
                    UserPackageEntity userPackage = new UserPackageEntity();
                    userPackage.setUser(userProfile.getUser());
                    userPackage.setSkin(skin);
                    userPackage.setPrice(skin.getPrice());
                    userPackage.setPrice(skin.getDiamond());
                    userPackage.setSource(UserPackageSource.OPEN_CASE);
                    userPackage.setCaseId(caseEntity.getId());
                    userPackage.setIsReceived(false);
                    userPackage.setIsSelled(false);
                    userPackage = userPackageRepository.save(userPackage);
                    caseLotterResultVO.setPackageId(userPackage.getId());
                }
            }
            recordOpenCase = caseUserRecordRepository.save(recordOpenCase);
            caseRepository.save(caseEntity);
            // 记录金币流水
            UserCoinRecordEntity userCoinRecordEntity = new UserCoinRecordEntity();
            userCoinRecordEntity.setUser(userProfile.getUser());
            userCoinRecordEntity.setSource(UserCoinChangeSource.OPEN_CASE);
            userCoinRecordEntity.setAmount(caseEntity.getPrice());
            userCoinRecordEntity.setAfterAmount(userProfile.getCoin().subtract(amount));
            userCoinRecordEntity.setIsPositive(false);
            userCoinRecordEntity.setSourceId(recordOpenCase.getId());
            userCoinRecordRepository.save(userCoinRecordEntity);
            caseLotterResultVO.setRoll(lotteryResultDTO.getRoll());
            String userInfoRedisKey = redisUserPrefix + ":" + userId;
            UserInfoVO userInfoVO = RedisUtils.get(userInfoRedisKey, UserInfoVO.class);
            if (userInfoVO != null) {
                userInfoVO.setCoin(userProfile.getCoin());
                userInfoVO.setExperience(userProfile.getExperience());
                RedisUtils.save(userInfoRedisKey, userInfoVO);
            }
            caseLotterResultVO.setCard(activityService.joinCardCollect(userId, lotteryResultDTO.getRoll(), CardCollectSource.OPEN_CASE));
            caseLotterResultVOList.add(caseLotterResultVO);
            zhyk = zhyk.add(((caseLotterResultVO.getSkinInfoVO() == null || caseLotterResultVO.getSkinInfoVO().getDiamond() == null
                    ? new BigDecimal("0.00")
                    : caseLotterResultVO.getSkinInfoVO().getDiamond())
                    .subtract(caseEntity.getPrice())));
            CsvUtils.writeRecord(new String[]{
                            ((caseLotterResultVO == null || caseLotterResultVO.getSkinInfoVO() == null) ? "" : caseLotterResultVO.getSkinInfoVO().getName())
                            , ((caseLotterResultVO == null || caseLotterResultVO.getSkinInfoVO() == null || caseLotterResultVO.getSkinInfoVO().getDiamond() == null) ? new BigDecimal("0.00") : caseLotterResultVO.getSkinInfoVO().getDiamond()).toString()
                            , String.valueOf(level)
                            , String.valueOf(userId)
                            , String.valueOf(caseEntity.getPrice())
                            , (((caseLotterResultVO == null || caseLotterResultVO.getSkinInfoVO() == null || caseLotterResultVO.getSkinInfoVO().getDiamond() == null) ? new BigDecimal("0.00") : caseLotterResultVO.getSkinInfoVO().getDiamond())
                            .subtract(caseEntity.getPrice())).toString()
                            , initAmount.add(zhyk).toString()
                            , String.valueOf((i / 10) + 1)}
                    , "data");
        }
        for (CaseLotterResultVO caseLotterResultVO : caseLotterResultVOList) {
            UserLotteryResultOfWebsocketVO userLotteryResultOfWebsocketVO = new UserLotteryResultOfWebsocketVO() {{
                setUserInfo(userService.queryUserPublicInfo(userId));
                setSkinInfoVO(caseLotterResultVO.getSkinInfoVO());
                setSource(LotterySource.OPEN_CASE.getCode());
                setSourceValue(LotterySource.OPEN_CASE.getValue());
                setBoxId(caseEntity.getId());
            }};
            MessageVO messageResultVO = new MessageVO(WebSocketMessageMethod.SEND_ALL, WebSocketMessageType.LOTTERY_RESULT, userLotteryResultOfWebsocketVO);
            asyncTaskService.sendWebSocketMessageToAll(JSONObject.toJSONString(messageResultVO));
        }
        userService.DailyActivity(userId, DailyActivityType.OPEN_CASE);
    }

}
