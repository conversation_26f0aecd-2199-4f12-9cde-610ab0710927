package com.steamgo1.csgoskinapi.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinapi.converter.CaseConverter;
import com.steamgo1.csgoskinapi.dto.*;
import com.steamgo1.csgoskinapi.utils.SecurityUtils;
import com.steamgo1.csgoskincommon.contant.CsgoContants;
import com.steamgo1.csgoskincommon.entity.enums.*;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageMethod;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageType;
import com.steamgo1.csgoskinapi.service.*;
import com.steamgo1.csgoskinapi.vo.*;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import com.steamgo1.csgoskincommon.utils.RateLimiterUtils;
import com.steamgo1.csgoskincommon.vo.websocket.MessageVO;
import com.steamgo1.csgoskincommon.dao.*;
import com.steamgo1.csgoskincommon.entity.*;
import com.steamgo1.csgoskincommon.enums.LotterySource;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
@Transactional
public class CaseServiceImpl implements CaseService {
    @Value("${site.roll.min}")
    private  Integer SEED_MIN_ROLL;
    @Value("${site.roll.max}")
    private  Integer SEED_MAX_ROLL;

    @Value("${spring.redis.prefix.case}")
    private String redisCasePrefix;
    @Value("${spring.redis.prefix.skin}")
    private String redisSkinPrefix;

    @Value("${spring.redis.prefix.user}")
    private String redisUserPrefix;
    @Autowired
    private UserRepository userRepository;


    @Autowired
    private DataDictionaryRepository dataDictionaryRepository;
    @Autowired
    private RobotCaseService robotCaseService;

    @Autowired
    private CaseLevelRepository caseLevelRepository;

    @Autowired
    private CaseRepository caseRepository;

    @Autowired
    private CaseConverter caseConverter;

    @Autowired
    private SiteService siteService;

    @Autowired
    private CaseSkinRepository caseSkinRepository;

    @Autowired
    private LotteryService lotteryService;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private SkinRepository skinRepository;

    @Autowired
    private CaseUserRecordRepository caseUserRecordRepository;

    @Autowired
    private UserPackageRepository userPackageRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private SkinService skinService;

    @Autowired
    private UserCoinRecordRepository userCoinRecordRepository;

    @Autowired
    private CaseService caseService;

    @Autowired
    private AsyncTaskService asyncTaskService;

    @Autowired
    private ActivityService activityService;

    @Autowired
    private SkinRarityColorRepository skinRarityColorRepository;

    @Autowired
    private ConsumePlanUserRecordRepository consumePlanUserRecordRepository;

    @Autowired
    private DailyTasksRepository dailyTasksRepository;

    private static final int SAFE_PAGE_SIZE = 5;

    private static final int SAFE_OFFSET = 0;

    @Override
    public List<CaseCategoryVO> getCaseCategory() {
        String redisKey = redisCasePrefix + ":case_category";
        List<CaseCategoryVO> caseCategoryVOS = RedisUtils.get(redisKey, ArrayList.class);
        if(caseCategoryVOS!=null){
            return caseCategoryVOS;
        }
        caseCategoryVOS = new ArrayList<>();
        DataDictionaryEntity caseCategoryParent = dataDictionaryRepository.findByCode("case_category");
        if(caseCategoryParent == null){
            return null;
        }
        for(DataDictionaryEntity caseCategory: dataDictionaryRepository.findAllByParentId(caseCategoryParent.getId())){
            CaseCategoryVO caseCategoryVO = new CaseCategoryVO();
            caseCategoryVO.setId(caseCategory.getId());
            caseCategoryVO.setName(caseCategory.getName());
            caseCategoryVOS.add(caseCategoryVO);
        }
       if(!caseCategoryVOS.isEmpty()){
           RedisUtils.save(redisKey, caseCategoryVOS);
       }
       return caseCategoryVOS;
    }

    @Override
    public List<CaseInfoOfCategoryVO> getCaseInfoOfCategory(CaseQueryParamDTO caseQueryParamDTO) {
        CaseQueryParamDTO.Type type = CaseQueryParamDTO.Type.instance(caseQueryParamDTO.getType());
        List<CaseInfoOfCategoryVO> caseInfoOfCategoryVOS = new ArrayList<>();
        if(type!=null && type.equals(CaseQueryParamDTO.Type.OPEN_CASE)){
            String redisKey = redisCasePrefix + ":case_of_category:open_case";
            caseInfoOfCategoryVOS = RedisUtils.get(redisKey, ArrayList.class);
            if(caseInfoOfCategoryVOS!=null){
                return caseInfoOfCategoryVOS;
            }
        }
        if(type!=null && type.equals(CaseQueryParamDTO.Type.BATTLE)){
            String redisKey = redisCasePrefix + ":case_of_category:battle";
            caseInfoOfCategoryVOS = RedisUtils.get(redisKey, ArrayList.class);
            if(caseInfoOfCategoryVOS!=null){
                return caseInfoOfCategoryVOS;
            }
        }
        caseInfoOfCategoryVOS = new ArrayList<>();
        DataDictionaryEntity caseCategoryParent = dataDictionaryRepository.findByCodeOrderByGradleDesc("case_category");
        if(caseCategoryParent == null){
            return caseInfoOfCategoryVOS;
        }
        for(DataDictionaryEntity caseCategory: dataDictionaryRepository.findAllByParentIdOrderByGradleDesc(caseCategoryParent.getId())){
            CaseInfoOfCategoryVO caseInfoOfCategoryVO = new CaseInfoOfCategoryVO();
            caseInfoOfCategoryVO.setCategoryId(caseCategory.getId());
            caseInfoOfCategoryVO.setName(caseCategory.getName());
            caseInfoOfCategoryVO.setI18nFieldName(caseCategory.getI18nFieldName());
            List<CaseEntity> caseEntities = new ArrayList<>();
            if(caseCategory.getName().equals(I18nUtils.getMessage("case.category.treasure.box"))){
                continue;
            }
            if(caseCategory.getName().equals(I18nUtils.getMessage("case.category.daily.task.box"))){
                continue;
            }
            if(type!=null && type.equals(CaseQueryParamDTO.Type.OPEN_CASE)){
                if(caseCategory.getName().equals(I18nUtils.getMessage("case.category.battle.exclusive"))){
                    continue;
                }
                caseEntities = caseRepository.findAllByCategoryAndIsDeletedIsFalseAndIsSaleIsTrueAndDisableOpenCaseIsFalse(caseCategory.getId());
            }
            if(type!=null && type.equals(CaseQueryParamDTO.Type.BATTLE)){
                if(caseCategory.getName().equals(I18nUtils.getMessage("case.category.newbie.welfare"))){
                    continue;
                }
                caseEntities = caseRepository.findAllByCategoryAndIsDeletedIsFalseAndIsSaleIsTrueAndDisableBattleIsFalse(caseCategory.getId());
            }
            List<CaseInfoVO> caseInfoVOList = caseConverter.toCaseInfoVOS(caseEntities);

            for(CaseInfoVO caseInfoVO: caseInfoVOList){
                List<BigDecimal> probabilityOverview = new ArrayList<>();
                probabilityOverview.add(BigDecimal.ZERO);
                probabilityOverview.add(BigDecimal.ZERO);
                probabilityOverview.add(BigDecimal.ZERO);
                probabilityOverview.add(BigDecimal.ZERO);
                List<CaseSkinEntity> caseSkinEntityList = caseSkinRepository.findByCaseEntityId(caseInfoVO.getId());
                for(CaseSkinEntity caseSkinEntity: caseSkinEntityList){
                    SkinEntity skin = caseSkinEntity.getSkin();
                    if(siteService.getGradleByKeyID(skin.getQuality()).equals(1)){
                        probabilityOverview.set(0, probabilityOverview.get(0).add(caseSkinEntity.getProbability()));
                    } else if (siteService.getGradleByKeyID(skin.getQuality()).equals(2)) {
                        probabilityOverview.set(1, probabilityOverview.get(1).add(caseSkinEntity.getProbability()));

                    } else if (siteService.getGradleByKeyID(skin.getQuality()).equals(3)) {
                        probabilityOverview.set(2, probabilityOverview.get(2).add(caseSkinEntity.getProbability()));

                    }else if (siteService.getGradleByKeyID(skin.getQuality()).equals(4)) {
                        probabilityOverview.set(3, probabilityOverview.get(3).add(caseSkinEntity.getProbability()));

                    }else {
                        probabilityOverview.set(0, probabilityOverview.get(0).add(caseSkinEntity.getProbability()));
                    }
                }
                caseInfoVO.setProbabilityOverview(probabilityOverview);
            }
            caseInfoOfCategoryVO.setCaseInfo(caseInfoVOList);
            caseInfoOfCategoryVOS.add(caseInfoOfCategoryVO);
        }
        if(!caseInfoOfCategoryVOS.isEmpty()){
            if(type!=null && type.equals(CaseQueryParamDTO.Type.OPEN_CASE)){
                String redisKey = redisCasePrefix + ":case_of_category:open_case";
                RedisUtils.save(redisKey, caseInfoOfCategoryVOS);
            }
            if(type!=null && type.equals(CaseQueryParamDTO.Type.BATTLE)){
                String redisKey = redisCasePrefix + ":case_of_category:battle";
                RedisUtils.save(redisKey, caseInfoOfCategoryVOS);
            }
        }
        return caseInfoOfCategoryVOS;
    }

    @Override
    public CaseFullInfoVO getCaseFullInfo(Long caseId, int level) {
        String redisCaseKey = redisCasePrefix + ":case_info:level:" + caseId+level;
        CaseFullInfoVO caseFullInfoVO = RedisUtils.get(redisCaseKey, CaseFullInfoVO.class);
        if(caseFullInfoVO != null){
            return caseFullInfoVO;
        }
        CaseEntity caseEntity = caseRepository.findByIdAndIsDeletedAndIsSale(caseId, false, true);
        if(caseEntity == null){
            throw new CsgoSkinException(I18nUtils.getMessage("exception.case.not.exist.or.offline"));
        }
        caseFullInfoVO = new CaseFullInfoVO();
        caseFullInfoVO.setId(caseEntity.getId());
        caseFullInfoVO.setName(caseEntity.getName());
        caseFullInfoVO.setCategoryId(caseEntity.getCategory());
        caseFullInfoVO.setCategoryName(siteService.getValueByKeyID(caseEntity.getCategory()));
        caseFullInfoVO.setType(caseEntity.getCaseType().getCode());
        caseFullInfoVO.setTypeName(caseEntity.getCaseType().getType());
        caseFullInfoVO.setPrice(caseEntity.getPrice());
        caseFullInfoVO.setBackgroundPicture(caseEntity.getBackgroundPicture());
        caseFullInfoVO.setForegroundPicture(caseEntity.getForegroundPicture());
        caseFullInfoVO.setGradle(caseEntity.getGradle());
        caseFullInfoVO.setSkinTotal(caseEntity.getSkinTotal());
        caseFullInfoVO.setProbability(caseEntity.getProbability());

        List<CaseFullInfoVO.CaseSkinVO>  skins = new ArrayList<>();
        CaseLevelEntity caseLevel = caseLevelRepository.getByCaseEntityAndLevelNum(caseEntity, level);
        Integer roll = SEED_MIN_ROLL;

        List<CaseSkinEntity> caseSkinEntitys = new ArrayList<>();
        if (caseLevel == null) {
            caseLevel = caseLevelRepository.getByCaseEntityAndLevelNum(caseEntity, CsgoContants.levelNumber.LEVEL_1);
        }
        if(caseLevel == null){
            // throw new CsgoSkinException("箱子不存在或已下架。。。");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.case.level.not.exist.or.offline"));
        }
        caseSkinEntitys = caseSkinRepository.findByLevel(caseLevel);
        if(caseSkinEntitys == null || caseSkinEntitys.isEmpty()){
            // throw new CsgoSkinException("箱子不存在或已下架！！！");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.case.skins.not.exist.or.offline"));
        }
        for(CaseSkinEntity caseSkinEntity: caseSkinEntitys){
            CaseFullInfoVO.CaseSkinVO caseSkinVO = new CaseFullInfoVO.CaseSkinVO();
            caseSkinVO.setCaseSkinId(caseSkinEntity.getId());
            String redisSkinInfoKey = redisSkinPrefix + ":skin_info:" + caseSkinEntity.getSkin().getId();

            SkinInfoVO skinInfoVO = RedisUtils.get(redisSkinInfoKey, SkinInfoVO.class);
            if(skinInfoVO==null){
                skinInfoVO = new SkinInfoVO();
                skinInfoVO.setId(caseSkinEntity.getSkin().getId());
                skinInfoVO.setI18nFieldName(caseSkinEntity.getSkin().getI18nFieldName());
                skinInfoVO.setName(caseSkinEntity.getSkin().getName());
                skinInfoVO.setEnglishName(caseSkinEntity.getSkin().getEnglishName());
                skinInfoVO.setI18nFieldShortName(caseSkinEntity.getSkin().getI18nFieldShortName());
                skinInfoVO.setShortName(caseSkinEntity.getSkin().getShortName());
                skinInfoVO.setPicture(caseSkinEntity.getSkin().getPicture());
                skinInfoVO.setPrototype(caseSkinEntity.getSkin().getPrototype());
                skinInfoVO.setPrototypeName(siteService.getValueByKeyID(caseSkinEntity.getSkin().getPrototype()));
                skinInfoVO.setQuality(caseSkinEntity.getSkin().getQuality());
                skinInfoVO.setQualityName(siteService.getValueByKeyID(caseSkinEntity.getSkin().getQuality()));
                skinInfoVO.setQualityColor(caseSkinEntity.getSkin().getQualityColor());
                skinInfoVO.setExterior(caseSkinEntity.getSkin().getExterior());
                skinInfoVO.setExteriorName(siteService.getValueByKeyID(caseSkinEntity.getSkin().getExterior()));
                skinInfoVO.setRarity(caseSkinEntity.getSkin().getRarity());
                skinInfoVO.setRarityName(siteService.getValueByKeyID(caseSkinEntity.getSkin().getRarity()));
                skinInfoVO.setRarityColor(caseSkinEntity.getSkin().getRarityColor());
                skinInfoVO.setPrice(caseSkinEntity.getSkin().getPrice());
                skinInfoVO.setQuantity(caseSkinEntity.getSkin().getQuantity());
                skinInfoVO.setAutoDeliverQuantity(caseSkinEntity.getSkin().getAutoDeliverQuantity());
                skinInfoVO.setAutoDeliverPrice(caseSkinEntity.getSkin().getAutoDeliverPrice());
                skinInfoVO.setManualQuantity(caseSkinEntity.getSkin().getManualQuantity());
                skinInfoVO.setManualDeliverPrice(caseSkinEntity.getSkin().getManualDeliverPrice());
                skinInfoVO.setDiamond(caseSkinEntity.getSkin().getDiamond());
                RedisUtils.save(redisSkinInfoKey, skinInfoVO);
            }
            caseSkinVO.setSkinInfo(skinInfoVO);
            caseSkinVO.setProbability(caseSkinEntity.getProbability());
            caseSkinVO.setMinRoll(roll);
            roll += caseSkinEntity.getProbability().multiply(BigDecimal.valueOf(SEED_MAX_ROLL)).intValue();
            caseSkinVO.setMaxRoll(roll-1);
            caseSkinVO.setColor(caseSkinEntity.getSkinRarityColor().getCode());
            skins.add(caseSkinVO);
        }
        caseFullInfoVO.setSkins(skins);
        if(!skins.isEmpty()){
            RedisUtils.save(redisCaseKey, caseFullInfoVO);
        }
        return caseFullInfoVO;
    }


//
//    @Override
//    public CaseFullInfoVO getCaseFullInfoSafety(Long caseId, Long userId) {
//        String redisCaseKey = redisCasePrefix + ":case_info:" + caseId;
//        CaseFullInfoVO caseFullInfoVO = RedisUtils.get(redisCaseKey, CaseFullInfoVO.class);
////        if(caseFullInfoVO != null){
////            return caseFullInfoVO;
////        }
//        CaseEntity caseEntity = caseRepository.findByIdAndIsDeletedAndIsSale(caseId, false, true);
//        if(caseEntity == null){
//            throw new CsgoSkinException(I18nUtils.getMessage("exception.case.not.exist.or.offline"));
//        }
//        caseFullInfoVO = new CaseFullInfoVO();
//        caseFullInfoVO.setId(caseEntity.getId());
//        caseFullInfoVO.setName(caseEntity.getName());
//        caseFullInfoVO.setCategoryId(caseEntity.getCategory());
//        caseFullInfoVO.setCategoryName(siteService.getValueByKeyID(caseEntity.getCategory()));
//        caseFullInfoVO.setType(caseEntity.getCaseType().getCode());
//        caseFullInfoVO.setTypeName(caseEntity.getCaseType().getType());
//        caseFullInfoVO.setPrice(caseEntity.getPrice());
//        caseFullInfoVO.setBackgroundPicture(caseEntity.getBackgroundPicture());
//        caseFullInfoVO.setForegroundPicture(caseEntity.getForegroundPicture());
//        caseFullInfoVO.setGradle(caseEntity.getGradle());
//        caseFullInfoVO.setSkinTotal(caseEntity.getSkinTotal());
//        caseFullInfoVO.setProbability(caseEntity.getProbability());
//
//        List<CaseFullInfoVO.CaseSkinVO>  skins = new ArrayList<>();
//        CaseLevelEntity caseLevel = caseLevelRepository.getByCaseEntityAndLevelNum(caseEntity, level);
//        Integer roll = SEED_MIN_ROLL;
//
//        List<CaseSkinEntity> caseSkinEntitys = new ArrayList<>();
//        if (caseLevel != null) {
//            caseSkinEntitys = caseSkinRepository.findByLevelOrderByProbability(caseLevel);
//        }else{
//            caseSkinEntitys = caseSkinRepository.findByCaseEntityOrderByProbability(caseEntity);
//        }
//        for(CaseSkinEntity caseSkinEntity: caseSkinEntitys){
//            CaseFullInfoVO.CaseSkinVO caseSkinVO = new CaseFullInfoVO.CaseSkinVO();
//            caseSkinVO.setCaseSkinId(caseSkinEntity.getId());
//            String redisSkinInfoKey = redisSkinPrefix + ":skin_info:" + caseSkinEntity.getSkin().getId();
//
//            SkinInfoVO skinInfoVO = RedisUtils.get(redisSkinInfoKey, SkinInfoVO.class);
//            if(skinInfoVO==null){
//                skinInfoVO = new SkinInfoVO();
//                skinInfoVO.setId(caseSkinEntity.getSkin().getId());
//                skinInfoVO.setName(caseSkinEntity.getSkin().getName());
//                skinInfoVO.setEnglishName(caseSkinEntity.getSkin().getEnglishName());
//                skinInfoVO.setShortName(caseSkinEntity.getSkin().getShortName());
//                skinInfoVO.setPicture(caseSkinEntity.getSkin().getPicture());
//                skinInfoVO.setPrototype(caseSkinEntity.getSkin().getPrototype());
//                skinInfoVO.setPrototypeName(siteService.getValueByKeyID(caseSkinEntity.getSkin().getPrototype()));
//                skinInfoVO.setQuality(caseSkinEntity.getSkin().getQuality());
//                skinInfoVO.setQualityName(siteService.getValueByKeyID(caseSkinEntity.getSkin().getQuality()));
//                skinInfoVO.setQualityColor(caseSkinEntity.getSkin().getQualityColor());
//                skinInfoVO.setExterior(caseSkinEntity.getSkin().getExterior());
//                skinInfoVO.setExteriorName(siteService.getValueByKeyID(caseSkinEntity.getSkin().getExterior()));
//                skinInfoVO.setRarity(caseSkinEntity.getSkin().getRarity());
//                skinInfoVO.setRarityName(siteService.getValueByKeyID(caseSkinEntity.getSkin().getRarity()));
//                skinInfoVO.setRarityColor(caseSkinEntity.getSkin().getRarityColor());
//                skinInfoVO.setPrice(caseSkinEntity.getSkin().getPrice());
//                skinInfoVO.setQuantity(caseSkinEntity.getSkin().getQuantity());
//                skinInfoVO.setAutoDeliverQuantity(caseSkinEntity.getSkin().getAutoDeliverQuantity());
//                skinInfoVO.setAutoDeliverPrice(caseSkinEntity.getSkin().getAutoDeliverPrice());
//                skinInfoVO.setManualQuantity(caseSkinEntity.getSkin().getManualQuantity());
//                skinInfoVO.setManualDeliverPrice(caseSkinEntity.getSkin().getManualDeliverPrice());
//                skinInfoVO.setDiamond(caseSkinEntity.getSkin().getDiamond());
//                RedisUtils.save(redisSkinInfoKey, skinInfoVO);
//            }
//            caseSkinVO.setSkinInfo(skinInfoVO);
//            caseSkinVO.setProbability(caseSkinEntity.getProbability());
//            caseSkinVO.setMinRoll(roll);
//            roll += caseSkinEntity.getProbability().multiply(BigDecimal.valueOf(SEED_MAX_ROLL)).intValue();
//            caseSkinVO.setMaxRoll(roll-1);
//            caseSkinVO.setColor(caseSkinEntity.getSkinRarityColor().getCode());
//            skins.add(caseSkinVO);
//        }
//        caseFullInfoVO.setSkins(skins);
//        if(!skins.isEmpty()){
//            RedisUtils.save(redisCaseKey, caseFullInfoVO);
//        }
//        return caseFullInfoVO;
//    }


    @Override
    public List<CaseLotterResultVO> getCaseLotterResult(Long caseId, Integer rounds, Long userId) {
//        限流
        if(!RateLimiterUtils.tryAcquire(userId)){
            log.warn("用户开箱被限流: {}", userId);
            throw new CsgoSkinException(I18nUtils.getMessage("exception.operation.too.frequent"));
        }
        CaseEntity caseEntity = caseRepository.findByIdAndIsDeletedAndIsSale(caseId, false, true);
        UserProfileEntity userProfile = userProfileRepository.findByUserId(userId);
        if(caseEntity==null){
            log.error("开箱错误caseId： {} userId: {}", caseId, userId);
            throw new CsgoSkinException(I18nUtils.getMessage("exception.case.not.exist.or.offline"));
        }
        if(caseEntity.getDisableOpenCase()){
            throw new CsgoSkinException(I18nUtils.getMessage("exception.case.not.openable"));
        }
        // 校验是否可以开这个箱子(新手or老手) TODO
        if(caseEntity.getCaseType().equals(CaseType.NOVICE)){
            String userInfoRedisKey = redisUserPrefix + ":" + userId;
            log.info("删除用户信息缓存,开了免费箱子 {}, {}", userId, userInfoRedisKey);
            if(RedisUtils.hasKey(userInfoRedisKey)){
                RedisUtils.delete(userInfoRedisKey);
            }
            Integer totalFreeCase = 5 + userService.queryFreeCase(userId);
            Integer totalDone = caseUserRecordRepository.countByUserIdAndBoxCaseType(userId, CaseType.NOVICE);
            if(totalFreeCase-rounds-totalDone<0){
                if(totalFreeCase-totalDone<=0){
                    log.error("用户:{} 已抽 {}次", userId, totalDone);
                    throw new CsgoSkinException(I18nUtils.getMessage("exception.novice.case.no.remaining.chances"));
                }
                log.error("用户:{} 不足{} 次开箱机会", userId, rounds);
                throw new CsgoSkinException(I18nUtils.getMessage("exception.novice.case.remaining.chances", new Object[]{String.valueOf(totalFreeCase - totalDone)}));
            }
        }
        // 是否是消费计划送的箱子
        if(caseEntity.getCaseType().equals(CaseType.CONSUMEPLAN)){
            List<ConsumePlanUserRecordEntity> consumePlanUserRecordEntityList = consumePlanUserRecordRepository.dayUserConsumePlan(userId);
            Integer totalConsumePlanCase = consumePlanUserRecordEntityList.stream().map(item->item.getConsumePlan().getTotal()).reduce(0, Integer::sum);
            Integer totalConsumePlanCaseDone = caseUserRecordRepository.countDayByUserIdAndBoxCaseType(userId, CaseType.CONSUMEPLAN.getCode()-1);
            if(totalConsumePlanCase-rounds-totalConsumePlanCaseDone<0){
                if(totalConsumePlanCase-totalConsumePlanCaseDone<=0){
                    log.error("用户:{} 已抽 {}次", userId, totalConsumePlanCaseDone);
                    throw new CsgoSkinException(I18nUtils.getMessage("exception.consumeplan.case.no.remaining.chances"));
                }
                log.error("用户:{} 不足{} 次开箱机会", userId, rounds);
                throw new CsgoSkinException(I18nUtils.getMessage("exception.consumeplan.case.remaining.chances", new Object[]{String.valueOf(totalConsumePlanCase - totalConsumePlanCaseDone)}));
            }
        }
        // 判断如果是日常任务箱子
        if(caseEntity.getCaseType().equals(CaseType.DAILY_TASKS)){
            // 查箱子关联的配置
            DailyTasksEntity dailyTasks = dailyTasksRepository.findTopByBox(caseEntity);
            if(dailyTasks==null){
                log.error("日常任务箱子未配置： {}", caseEntity.getId());
                throw new CsgoSkinException(I18nUtils.getMessage("exception.daily.task.config.missing"));
            }
            // 用户本月充值
            BigDecimal userMonthCharge = userCoinRecordRepository.userMonthCharge(userId);
            userMonthCharge = userMonthCharge==null?BigDecimal.ZERO:userMonthCharge;
            // 用户本月消费
            if(userMonthCharge.compareTo(dailyTasks.getChargeThreshold()) < 0){
                // 充值不达标
                throw new CsgoSkinException(I18nUtils.getMessage("exception.daily.task.insufficient.charge", new Object[]{dailyTasks.getChargeThreshold().subtract(userMonthCharge).toString()}));
            }
            BigDecimal userMonthConsume = userCoinRecordRepository.userMonthConsume(userId);
            userMonthConsume = userMonthConsume==null?BigDecimal.ZERO:userMonthConsume;
            if(userMonthConsume.compareTo(dailyTasks.getConsumThreshold()) < 0){
                // 消费不达标
                throw new CsgoSkinException(I18nUtils.getMessage("exception.daily.task.insufficient.consume", new Object[]{dailyTasks.getConsumThreshold().subtract(userMonthConsume).toString()}));
            }
            Integer totalTodayOpen = caseUserRecordRepository.totalUserOpenDailyTasksCase(userId, caseId);
            if(totalTodayOpen>0){
                // 用户今日已经开过此箱子
                throw new CsgoSkinException(I18nUtils.getMessage("exception.daily.task.already.opened.today", new Object[]{dailyTasks.getName()}));
            }
            rounds = 1;
        }
        BigDecimal amount = caseEntity.getPrice().multiply(new BigDecimal(rounds)); // 总消耗
        if(userProfile.getCoin().compareTo(amount)==-1){
            log.info("金币不足");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.insufficient.coins"));
        }
        List<CaseLotterResultVO> caseLotterResultVOList = new ArrayList<>();
        for(int i=0;i<rounds;i++){
            int level = getLevel(caseId, userId, caseEntity);
            CaseLotterResultVO caseLotterResultVO = new CaseLotterResultVO();
            log.info("本次箱子为：{}，等级为：{}", caseEntity.getName(), level);
            CaseFullInfoVO caseFullInfoVO = getCaseFullInfo(caseId, level);
            // 金币消耗 TODO
            if(userProfile.getCoin().compareTo(caseEntity.getPrice())==-1){
                log.info("金币不足");
                throw new CsgoSkinException(I18nUtils.getMessage("exception.insufficient.coins"));
            }
            userProfile.setCoin(userProfile.getCoin().subtract(caseEntity.getPrice()));
            // 经验 TODO
            userProfile.setExperience(userProfile.getExperience() + 10);
            userProfileRepository.save(userProfile);
            // 记录算法开箱绑定记录 TODO
            LotteryResultDTO lotteryResultDTO = lotteryService.lottery();
            CaseUserRecordEntity recordOpenCase = new CaseUserRecordEntity();
            recordOpenCase.setUser(userProfile.getUser());
            recordOpenCase.setBox(caseEntity);
            recordOpenCase.setAlgorithmData(lotteryResultDTO.getAlgorithmData());
            recordOpenCase.setRoll(lotteryResultDTO.getRoll());
            recordOpenCase.setRounds(lotteryResultDTO.getAlgorithmData().getRounds() - 1);
            for(CaseFullInfoVO.CaseSkinVO skinVO: caseFullInfoVO.getSkins()){
                if(skinVO.getMinRoll() <= lotteryResultDTO.getRoll() && skinVO.getMaxRoll() >= lotteryResultDTO.getRoll()){
                    caseLotterResultVO.setSkinInfoVO(skinVO.getSkinInfo());
                    SkinEntity skin = skinRepository.findById(skinVO.getSkinInfo().getId()).get();
                    recordOpenCase.setSkin(skin);
                    // 放入背包
                    UserPackageEntity userPackage = new UserPackageEntity();
                    userPackage.setUser(userProfile.getUser());
                    userPackage.setSkin(skin);
                    userPackage.setPrice(skin.getPrice());
                    userPackage.setDiamond(skin.getDiamond());
                    userPackage.setSource(UserPackageSource.OPEN_CASE);
                    userPackage.setCaseId(caseId);
                    userPackage.setIsReceived(false);
                    userPackage.setIsSelled(false);
                    userPackage = userPackageRepository.save(userPackage);
                    caseLotterResultVO.setPackageId(userPackage.getId());
                }
            }
            recordOpenCase = caseUserRecordRepository.save(recordOpenCase);
            caseRepository.save(caseEntity);
            // 记录金币流水
            UserCoinRecordEntity userCoinRecordEntity = new UserCoinRecordEntity();
            userCoinRecordEntity.setUser(userProfile.getUser());
            userCoinRecordEntity.setSource(UserCoinChangeSource.OPEN_CASE);
            userCoinRecordEntity.setAmount(caseEntity.getPrice());
            userCoinRecordEntity.setAfterAmount(userProfile.getCoin());
            userCoinRecordEntity.setIsPositive(false);
            userCoinRecordEntity.setSourceId(recordOpenCase.getId());
            userCoinRecordRepository.save(userCoinRecordEntity);
            caseLotterResultVO.setRoll(lotteryResultDTO.getRoll());
            String userInfoRedisKey = redisUserPrefix + ":" + userId;
            UserInfoVO userInfoVO = RedisUtils.get(userInfoRedisKey, UserInfoVO.class);
            if(userInfoVO != null){
                userInfoVO.setCoin(userProfile.getCoin());
                userInfoVO.setExperience(userProfile.getExperience());
                RedisUtils.save(userInfoRedisKey, userInfoVO);
            }
            CardVO cardVO = activityService.joinCardCollect(userId, lotteryResultDTO.getRoll(), CardCollectSource.OPEN_CASE);
            if(cardVO==null){
                cardVO = activityService.joinExtraBonus(userId, CardCollectSource.OPEN_CASE, caseEntity.getPrice());
            }
            caseLotterResultVO.setCard(cardVO);
            caseLotterResultVOList.add(caseLotterResultVO);
        }
        for(CaseLotterResultVO caseLotterResultVO: caseLotterResultVOList){
            UserLotteryResultOfWebsocketVO userLotteryResultOfWebsocketVO = new UserLotteryResultOfWebsocketVO(){{
                setUserInfo(userService.queryUserPublicInfo(userId));
                setSkinInfoVO(caseLotterResultVO.getSkinInfoVO());
                setSource(LotterySource.OPEN_CASE.getCode());
                setSourceValue(LotterySource.OPEN_CASE.getValue());
                setBoxId(caseId);
            }};
            MessageVO messageResultVO = new MessageVO(WebSocketMessageMethod.SEND_ALL, WebSocketMessageType.LOTTERY_RESULT, userLotteryResultOfWebsocketVO);
            asyncTaskService.sendWebSocketMessageToAll(JSONObject.toJSONString(messageResultVO));
        }
        userService.DailyActivity(userId, DailyActivityType.OPEN_CASE);
        activityService.queryUserConsumePlan(userId, amount);
        return caseLotterResultVOList;
    }

    @Override
    public int getLevel(Long caseId, Long userId, CaseEntity caseEntity) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(SAFE_OFFSET, SAFE_PAGE_SIZE, sort);
        Page<CaseUserRecordEntity> userRecordEntityPage = caseUserRecordRepository.findByUserIdAndBoxId(userId, caseId, pageable);

        //确定箱子等级
        int level = 0;
        if (!caseEntity.getIsProtect()) {
            return level;
        }
        Integer maxLevel = caseLevelRepository.findMaxLevelNumByCaseEntity(caseEntity.getId());
        if (maxLevel == null) {
            maxLevel = 0;
        }
        List<CaseUserRecordEntity> CaseUserRecordEntitys = userRecordEntityPage.getContent();
        BigDecimal boxPrice;
        BigDecimal skinPrice;
        for(CaseUserRecordEntity caseUserRecordEntity : CaseUserRecordEntitys){
            //获取箱子的价格
            boxPrice = (caseUserRecordEntity == null || caseUserRecordEntity.getBox() == null) ? new BigDecimal(0.00) : caseUserRecordEntity.getBox().getPrice();
            //获取开到物品的价格
            skinPrice = (caseUserRecordEntity == null || caseUserRecordEntity.getSkin() == null) ? new BigDecimal(0.00) : caseUserRecordEntity.getSkin().getDiamond();
            int result = boxPrice.compareTo(skinPrice);
            if (result > 0) {
                //不允许超过最高级别
                if(level < maxLevel){
                    level++;
                }
            } else {
                break;
            }
        }
        return level;
    }

    @Override
    public Page<CaseUserLotteryRecordVO> getUserLotterRecord(UserCaseLotteryRecordQueryDTO userCaseLotteryRecordQueryDTO, Long userId) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(userCaseLotteryRecordQueryDTO.getPage(), userCaseLotteryRecordQueryDTO.getSize(), sort);
        Page<CaseUserRecordEntity> userRecordEntityPage = caseUserRecordRepository.findByUserId(userId, pageable);
        List<CaseUserLotteryRecordVO> caseUserLotteryRecordVOList = new ArrayList<>();
        for(CaseUserRecordEntity caseUserRecordEntity: userRecordEntityPage.getContent()){
            CaseUserLotteryRecordVO caseUserLotteryRecordVO = new CaseUserLotteryRecordVO();
            CaseFullInfoVO caseFullInfoVO = getCaseFullInfo(caseUserRecordEntity.getBox().getId(), CsgoContants.levelNumber.LEVEL_1);
            caseUserLotteryRecordVO.setCaseInfo(caseFullInfoVO);
            if(caseUserRecordEntity.getSkin()!=null){
                caseUserLotteryRecordVO.setSkinInfo(caseFullInfoVO.getSkins().stream().filter(item->item.getSkinInfo().getId().equals(caseUserRecordEntity.getSkin().getId())).findAny().get().getSkinInfo());
            }
            UserAlgorithmDataFullVO userAlgorithmDataFullVO = new UserAlgorithmDataFullVO();
            AlgorithmDataEntity algorithmDataEntity = caseUserRecordEntity.getAlgorithmData();
            if(!algorithmDataEntity.getIsUsed()) {
                userAlgorithmDataFullVO.setSecretHash(algorithmDataEntity.getSecretHash());
                userAlgorithmDataFullVO.setSecretSalt(algorithmDataEntity.getSecretSalt());
            }
            userAlgorithmDataFullVO.setPublicHash(algorithmDataEntity.getPublicHash());
            userAlgorithmDataFullVO.setClientSeed(algorithmDataEntity.getClientSeed());
            userAlgorithmDataFullVO.setRounds(algorithmDataEntity.getRounds());
            caseUserLotteryRecordVO.setUserAlgorithmData(userAlgorithmDataFullVO);
            caseUserLotteryRecordVO.setRoll(caseUserRecordEntity.getRoll());
            caseUserLotteryRecordVO.setRounds(caseUserRecordEntity.getRounds());
            caseUserLotteryRecordVOList.add(caseUserLotteryRecordVO);
        }
        return new PageImpl<>(caseUserLotteryRecordVOList, userRecordEntityPage.getPageable(), userRecordEntityPage.getTotalPages());
    }

    @Override
    public Page<CaseUserLotteryRecordOfCaseVO> queryCaseUserLotteryRecordOfCase(PageQueryDTO pageQueryDTO, Long caseId) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(pageQueryDTO.getPage(), pageQueryDTO.getSize(), sort);
        Page<CaseUserRecordEntity> userRecordEntityPage = caseUserRecordRepository.findByBoxId(caseId, pageable);
        List<CaseUserLotteryRecordOfCaseVO> caseUserLotteryRecordOfCaseVOList = new ArrayList<>();
        for(CaseUserRecordEntity caseUserRecordEntity: userRecordEntityPage.getContent()){
            if(caseUserRecordEntity.getSkin()==null){
                log.info("倒霉鬼：{} 抽中了个寂寞", caseUserRecordEntity.getUser().getNickname());
                continue;
            }
            CaseUserLotteryRecordOfCaseVO caseUserLotteryRecordOfCaseVO = new CaseUserLotteryRecordOfCaseVO();
            caseUserLotteryRecordOfCaseVO.setCreateTime(caseUserRecordEntity.getCreateTime());
            caseUserLotteryRecordOfCaseVO.setUserInfo(userService.queryUserPublicInfo(caseUserRecordEntity.getUser().getId()));
            caseUserLotteryRecordOfCaseVO.setSkinInfo(skinService.querySkinInfoById(caseUserRecordEntity.getSkin().getId()));
            caseUserLotteryRecordOfCaseVO.setRoll(caseUserRecordEntity.getRoll());
            caseUserLotteryRecordOfCaseVOList.add(caseUserLotteryRecordOfCaseVO);
        }
        return  new PageImpl<>(caseUserLotteryRecordOfCaseVOList, userRecordEntityPage.getPageable(), userRecordEntityPage.getTotalPages());
    }

    @Override
    public List<CaseFullInfoVO> queryRecommendofRandom() {
        String redisRecommendCaseKey = redisCasePrefix + ":RECOMMEND";
        List<CaseFullInfoVO> caseFullInfoVOList = RedisUtils.get(redisRecommendCaseKey, ArrayList.class);
        if(caseFullInfoVOList!=null){
            return caseFullInfoVOList;
        }
        caseFullInfoVOList = new ArrayList<>();
        List<CaseEntity> caseEntityList = caseRepository.findByIsRecommendIsTrueAndIsDeletedIsFalseAndIsSaleIsTrue();
        for(CaseEntity caseEntity: caseEntityList){
            caseFullInfoVOList.add(caseService.getCaseFullInfo(caseEntity.getId(), CsgoContants.levelNumber.LEVEL_1));
            if(caseFullInfoVOList.size()==3){
                RedisUtils.save(redisRecommendCaseKey, caseFullInfoVOList);
                break;
            }
        }
        return caseFullInfoVOList;
    }

    @Override
    public List<UserLotteryResultVO> queryOpenCaseUserLotteryResult() {
        Sort sort = Sort.by(Sort.Direction.DESC, "updateTime");
        Pageable pageable = PageRequest.of(0, 20, sort);
        Page<CaseUserRecordEntity> caseUserRecordEntityPage = caseUserRecordRepository.findAll(pageable);
        List<UserLotteryResultVO> userLotteryResultVOList = new ArrayList<>();
        for(CaseUserRecordEntity caseUserRecordEntity: caseUserRecordEntityPage.getContent()){
            SkinEntity skinEntity = caseUserRecordEntity.getSkin();
            if(skinEntity==null){
                continue;
            }
            userLotteryResultVOList.add(new UserLotteryResultVO(){{
                setSkinInfoVO(skinService.querySkinInfoById(caseUserRecordEntity.getSkin().getId()));
                setUserInfo(userService.queryUserPublicInfo(caseUserRecordEntity.getUser().getId()));
                setSource(UserPackageSource.OPEN_CASE.getCode());
                setBoxId(caseUserRecordEntity.getBox().getId());
            }});
        }
        return userLotteryResultVOList;
    }

    @Override
    public void rorbotOpenCase(Long userId) {
        List<CaseEntity> caseEntityList = caseRepository.randomOpenCase(1);
        if(caseEntityList.isEmpty()){
            return;
        }
        Integer rounds = 1;
        CaseEntity caseEntity = caseEntityList.get(0);
        UserProfileEntity userProfile = userProfileRepository.findByUserId(userId);
        BigDecimal amount = caseEntity.getPrice().multiply(new BigDecimal(rounds)); // 总消耗
        if(userProfile.getCoin().compareTo(amount)==-1){
            log.info("金币不足");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.insufficient.coins"));
        }
        List<CaseLotterResultVO> caseLotterResultVOList = new ArrayList<>();
        for(int i=0;i<rounds;i++){
            CaseLotterResultVO caseLotterResultVO = new CaseLotterResultVO();
            CaseFullInfoVO caseFullInfoVO = getCaseFullInfo(caseEntity.getId(), CsgoContants.levelNumber.LEVEL_1);
            // 金币消耗 TODO
            if(userProfile.getCoin().compareTo(caseEntity.getPrice())==-1){
                log.info("金币不足");
                throw new CsgoSkinException(I18nUtils.getMessage("exception.insufficient.coins"));
            }
            userProfile.setCoin(userProfile.getCoin().subtract(caseEntity.getPrice()));
            // 经验 TODO
            userProfile.setExperience(userProfile.getExperience() + 10);
            userProfileRepository.save(userProfile);
            // 记录算法开箱绑定记录 TODO
            LotteryResultDTO lotteryResultDTO = lotteryService.lottery(userProfile.getUser());
            CaseUserRecordEntity recordOpenCase = new CaseUserRecordEntity();
            recordOpenCase.setUser(userProfile.getUser());
            recordOpenCase.setBox(caseEntity);
            recordOpenCase.setAlgorithmData(lotteryResultDTO.getAlgorithmData());
            recordOpenCase.setRoll(lotteryResultDTO.getRoll());
            recordOpenCase.setRounds(lotteryResultDTO.getAlgorithmData().getRounds() - 1);
            for(CaseFullInfoVO.CaseSkinVO skinVO: caseFullInfoVO.getSkins()){
                if(skinVO.getMinRoll() <= lotteryResultDTO.getRoll() && skinVO.getMaxRoll() >= lotteryResultDTO.getRoll()){
                    caseLotterResultVO.setSkinInfoVO(skinVO.getSkinInfo());
                    SkinEntity skin = skinRepository.findById(skinVO.getSkinInfo().getId()).get();
                    recordOpenCase.setSkin(skin);
                    // 放入背包
                    UserPackageEntity userPackage = new UserPackageEntity();
                    userPackage.setUser(userProfile.getUser());
                    userPackage.setSkin(skin);
                    userPackage.setPrice(skin.getPrice());
                    userPackage.setPrice(skin.getDiamond());
                    userPackage.setSource(UserPackageSource.OPEN_CASE);
                    userPackage.setCaseId(caseEntity.getId());
                    userPackage.setIsReceived(false);
                    userPackage.setIsSelled(false);
                    userPackage = userPackageRepository.save(userPackage);
                    caseLotterResultVO.setPackageId(userPackage.getId());
                }
            }
            recordOpenCase = caseUserRecordRepository.save(recordOpenCase);
            caseRepository.save(caseEntity);
            // 记录金币流水
            UserCoinRecordEntity userCoinRecordEntity = new UserCoinRecordEntity();
            userCoinRecordEntity.setUser(userProfile.getUser());
            userCoinRecordEntity.setSource(UserCoinChangeSource.OPEN_CASE);
            userCoinRecordEntity.setAmount(caseEntity.getPrice());
            userCoinRecordEntity.setAfterAmount(userProfile.getCoin().subtract(amount));
            userCoinRecordEntity.setIsPositive(false);
            userCoinRecordEntity.setSourceId(recordOpenCase.getId());
            userCoinRecordRepository.save(userCoinRecordEntity);
            caseLotterResultVO.setRoll(lotteryResultDTO.getRoll());
            String userInfoRedisKey = redisUserPrefix + ":" + userId;
            UserInfoVO userInfoVO = RedisUtils.get(userInfoRedisKey, UserInfoVO.class);
            if(userInfoVO != null){
                userInfoVO.setCoin(userProfile.getCoin());
                userInfoVO.setExperience(userProfile.getExperience());
                RedisUtils.save(userInfoRedisKey, userInfoVO);
            }
            caseLotterResultVO.setCard(activityService.joinCardCollect(userId, lotteryResultDTO.getRoll(), CardCollectSource.OPEN_CASE));
            caseLotterResultVOList.add(caseLotterResultVO);
        }
        for(CaseLotterResultVO caseLotterResultVO: caseLotterResultVOList){
            UserLotteryResultOfWebsocketVO userLotteryResultOfWebsocketVO = new UserLotteryResultOfWebsocketVO(){{
                setUserInfo(userService.queryUserPublicInfo(userId));
                setSkinInfoVO(caseLotterResultVO.getSkinInfoVO());
                setSource(LotterySource.OPEN_CASE.getCode());
                setSourceValue(LotterySource.OPEN_CASE.getValue());
                setBoxId(caseEntity.getId());
            }};
            MessageVO messageResultVO = new MessageVO(WebSocketMessageMethod.SEND_ALL, WebSocketMessageType.LOTTERY_RESULT, userLotteryResultOfWebsocketVO);
            asyncTaskService.sendWebSocketMessageToAll(JSONObject.toJSONString(messageResultVO));
        }
        userService.DailyActivity(userId, DailyActivityType.OPEN_CASE);
    }



    @Override
    public void rorbotOpenCaseByRound(Integer robotNum, Long caseId) {
        List<UserEntity> userEntityList = userRepository.randomCheatRobot(UserType.CHEAT_ROBOT.getCode() - 1, robotNum);
        BigDecimal shyk = new BigDecimal(0.00);
        for (UserEntity user : userEntityList) {
            log.info("rorbotOpenCaseByRound：全职机器人： {}, 开箱子", user.getId());
            robotCaseService.rorbotOpenCaseById(caseId, user.getId(), shyk);
        }
    }

    @Override
    public List<SkinRarityColorFilterVO> querySkinRarityColorFilterVO() {
        return caseConverter.toSkinRarityColorFilterVO(skinRarityColorRepository.findAll());
    }

    @Override
    public ConsumeCaseVO queryConsumeCase() {
        Long userId = SecurityUtils.getUserId();
        CaseEntity caseEntity = caseRepository.findTopByIsDeletedIsFalseAndCaseType(CaseType.CONSUMEPLAN);
        if(caseEntity == null){
            throw new CsgoSkinException(I18nUtils.getMessage("exception.case.not.exist.or.offline"));
        }
        CaseFullInfoVO caseFullInfoVO = getCaseFullInfo(caseEntity.getId(), CsgoContants.levelNumber.LEVEL_1);
        List<ConsumePlanUserRecordEntity> consumePlanUserRecordEntityList = consumePlanUserRecordRepository.dayUserConsumePlan(userId);
        Integer totalConsumePlanCase = consumePlanUserRecordEntityList.stream().map(item->item.getConsumePlan().getTotal()).reduce(0, Integer::sum);
        Integer totalDoneConsumePlanCase = caseUserRecordRepository.countDayByUserIdAndBoxCaseType(userId, CaseType.CONSUMEPLAN.getCode()-1);
        ConsumeCaseVO consumeCaseVO = new ConsumeCaseVO(){{
            setCaseInfo(caseFullInfoVO);
            setTotal(totalConsumePlanCase-totalDoneConsumePlanCase);
        }};
        return consumeCaseVO;
    }

    @Override
    public List<DailyTaskCaseVO> queryDailyTaskCase() {
        Long userId = SecurityUtils.getUserId();
        List<CaseEntity> caseEntityList = caseRepository.findByIsDeletedIsFalseAndCaseTypeOrderByGradle(CaseType.DAILY_TASKS);
        List<DailyTaskCaseVO> dailyTaskCaseVOList = new ArrayList<>();
        for(CaseEntity caseEntity: caseEntityList){
            DailyTaskCaseVO dailyTaskCaseVO = new DailyTaskCaseVO();
            CaseFullInfoVO caseFullInfoVO = getCaseFullInfo(caseEntity.getId(), CsgoContants.levelNumber.LEVEL_1);
            // 查箱子关联的配置
            DailyTasksEntity dailyTasks = dailyTasksRepository.findTopByBox(caseEntity);
            if(dailyTasks==null){
                log.error("日常任务箱子未配置： {}", caseEntity.getId());
//                dailyTaskCaseVO.setCaseInfo(caseFullInfoVO);
//                dailyTaskCaseVO.setStatus(DailyTaskCaseVO.Status.SUBSTANDARD.getCode());
//                dailyTaskCaseVOList.add(dailyTaskCaseVO);
                continue;
            }
            dailyTaskCaseVO.setName(dailyTasks.getName());
            // 用户本月充值
            BigDecimal userMonthCharge = userCoinRecordRepository.userMonthCharge(userId);
            userMonthCharge = userMonthCharge==null?BigDecimal.ZERO:userMonthCharge;
            // 用户本月消费
            if(userMonthCharge.compareTo(dailyTasks.getChargeThreshold()) < 0){
                // 充值不达标
                dailyTaskCaseVO.setCaseInfo(caseFullInfoVO);
                dailyTaskCaseVO.setStatus(DailyTaskCaseVO.Status.CHARGE_SUBSTANDARD.getCode());
                dailyTaskCaseVO.setShortfall(dailyTasks.getChargeThreshold().subtract(userMonthCharge));
                dailyTaskCaseVOList.add(dailyTaskCaseVO);
                continue;
            }
            BigDecimal userMonthConsume = userCoinRecordRepository.userMonthConsume(userId);
            userMonthConsume = userMonthConsume==null?BigDecimal.ZERO:userMonthConsume;
            if(userMonthConsume.compareTo(dailyTasks.getConsumThreshold()) < 0){
                // 消费不达标
                dailyTaskCaseVO.setCaseInfo(caseFullInfoVO);
                dailyTaskCaseVO.setStatus(DailyTaskCaseVO.Status.CONSUME_SUBSTANDARD.getCode());
                dailyTaskCaseVO.setShortfall(dailyTasks.getConsumThreshold().subtract(userMonthConsume));
                dailyTaskCaseVOList.add(dailyTaskCaseVO);
                continue;
            }
            Integer totalTodayOpen = caseUserRecordRepository.totalUserOpenDailyTasksCase(userId, caseEntity.getId());
            if(totalTodayOpen>0){
                dailyTaskCaseVO.setCaseInfo(caseFullInfoVO);
                dailyTaskCaseVO.setStatus(DailyTaskCaseVO.Status.OPENED.getCode());
                dailyTaskCaseVOList.add(dailyTaskCaseVO);
                continue;
            }
            dailyTaskCaseVO.setCaseInfo(caseFullInfoVO);
            dailyTaskCaseVO.setStatus(DailyTaskCaseVO.Status.UNOPENED.getCode());
            dailyTaskCaseVOList.add(dailyTaskCaseVO);
        }
        return dailyTaskCaseVOList;
    }
}
