package com.steamgo1.csgoskinapi.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.steamgo1.csgoskinapi.service.EmailService;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;

/**
 * @Description: 邮件服务实现类
 * @Author: caoxiaobo
 * @CreateDate: 2025/06/27
 */
@Service
@Slf4j
public class EmailServiceImpl implements EmailService {

    @Value("${spring.redis.prefix.login-captcha}")
    private String redisLoginPrefix;
    @Value("${spring.redis.expire.login-captcha}")
    private Long redisLoginExpire;
    @Resource
    private JavaMailSender mailSender;
    @Value("${spring.mail.username}")
    private String emailFrom;


    /**
     * 发送邮件
     *
     * @param email   邮箱
     * @param subject 主题
     * @param content 内容
     * @return
     */
    @Override
    public Boolean sendEmail(String email, String subject, String content) {
        try {
            log.info("发送邮件开始");
            SimpleMailMessage message = new SimpleMailMessage();
            message.setTo(email);
            message.setFrom(emailFrom);
            message.setSubject(subject);
            message.setText(content);
            mailSender.send(message);
            return true;
        } catch (Exception e) {
            log.error("发送邮件失败", e);
            return false;
        }
    }

    /**
     * 发送登录验证码
     *
     * @param email 邮箱
     * @return 验证码
     */
    @Override
    public Boolean sendLoginCode(String email) {

        String key = String.format("%s:%s", redisLoginPrefix, email);
        if (RedisUtils.hasKey(key)) {
            throw new CsgoSkinException(I18nUtils.getMessage("exception.captcha.not.expired.retry.later"));
        }
        String code = RandomUtil.randomNumbers(6);
        final String content = I18nUtils.getMessage("email.login.verify.template", new Object[]{code});
        String subject = I18nUtils.getMessage("email.login.subject");
        Boolean result = sendEmail(email, subject, content);
        if(result) {
            RedisUtils.save(key, code, redisLoginExpire);
        }

        return result;
    }
}
