package com.steamgo1.csgoskinapi.service;

import com.github.binarywang.wxpay.exception.WxPayException;
import com.steamgo1.csgoskinapi.vo.OrderChargeCreateVO;
import com.steamgo1.csgoskincommon.entity.enums.PayType;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

public interface OrderService {
    /**
     * 创建充值订单
     *
     * @param chargeGoodsId
     * @return
     */
    OrderChargeCreateVO createOrderCharge(Long chargeGoodsId, PayType payType, HttpServletRequest request);

    /**
     * 更新订单状态
     *
     * @param orderNo
     */
    void updateOrderStatus(String orderNo);

    /**
     * 更新订单状态
     *
     * @param orderNo
     */
    void updateOrderStatusAdmin(String orderNo);

    /**
     * 微信支付回调
     */
    void wxPayNotify(String xmlData) throws WxPayException;

    /**
     * 支付宝回调
     */
    void aliPayNotify(Map<String, String[]> data);

    /**
     * 盒子回调
     */
    void boxPayNotify(String orderNo);

    /**
     * 信付支付回调
     */
    void xinfuPayNotify(String orderNo);
}
