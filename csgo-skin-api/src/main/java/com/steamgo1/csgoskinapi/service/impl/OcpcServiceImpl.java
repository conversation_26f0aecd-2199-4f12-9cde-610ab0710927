package com.steamgo1.csgoskinapi.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.steamgo1.csgoskinapi.dto.ocpc.ConversionType;
import com.steamgo1.csgoskinapi.service.OcpcService;
import com.steamgo1.csgoskinapi.utils.JsonUtils;
import com.steamgo1.csgoskinapi.vo.OcpcBaiduKeyWordResponse;
import com.steamgo1.csgoskincommon.contant.CsgoContants;
import com.steamgo1.csgoskincommon.dao.*;
import com.steamgo1.csgoskincommon.entity.*;
import com.steamgo1.csgoskincommon.entity.enums.OcpcBaiduDataType;
import com.steamgo1.csgoskincommon.entity.enums.OcpcMetaDataType;
import com.steamgo1.csgoskincommon.entity.enums.OcpcGoogleDataType;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import com.steamgo1.csgoskincommon.utils.Utils;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.nio.charset.Charset;
import java.time.Instant;
import java.util.*;

@Slf4j
@Service
public class OcpcServiceImpl implements OcpcService {
    public static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    @Value("${ocpc.baidu.BAIDU_OCPC_URL}")
    private String BAIDU_OCPC_URL;
    @Value("${ocpc.qihu.BASE_OCPC_URL}")
    private String OCPC_URL_360;
    @Value("${ocpc.meta.BASE_OCPC_URL}")
    private String OCPC_URL_META;
    @Value("${ocpc.baidu.RETRY_TIMES}")
    private int RETRY_TIMES;
    @Value("${ocpc.qihu.RETRY_TIMES}")
    private int RETRY_TIMES_360;
    @Value("${ocpc.meta.RETRY_TIMES}")
    private int RETRY_TIMES_META;
    @Value("${ocpc.google.BASE_OCPC_URL}")
    private String OCPC_URL_GOOGLE;
    @Value("${ocpc.google.RETRY_TIMES}")
    private int RETRY_TIMES_GOOGLE;
    @Value("${spring.redis.prefix.ocpc}")
    private String redisPrefixOcpc;
    @Value("${spring.redis.expire.ocpc-baidu-keyword}")
    private Long redisExpireOcpcBaiduKeyWord;
    @Autowired
    private OcpcBaiduTokenRepository ocpcBaiduTokenRepository;
    @Autowired
    private Ocpc360TokenRepository ocpc360TokenRepository;
    @Autowired
    private OcpcMetaTokenRepository ocpcMetaTokenRepository;
    @Autowired
    private OcpcGoogleTokenRepository ocpcGoogleTokenRepository;
    @Autowired
    private OcpcBaiduAccountRepository ocpcBaiduAccountRepository;
    @Autowired
    private OcpcChannelRepository ocpcChannelRepository;
    @Autowired
    private OcpcBaiduDataRepository ocpcBaiduDataRepository;
    @Autowired
    private OcpcMetaDataRepository ocpcMetaDataRepository;
    @Autowired
    private OcpcGoogleDataRepository ocpcGoogleDataRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private OrderChargeRepository orderChargeRepository;
    @Autowired
    private RestTemplate restTemplate;
    private OkHttpClient client = new OkHttpClient();


    //    http://down.gelanrui.cn/detail?appid=1568590&amp;bd_vid=7144544721397678364

    private String post(String url, String json) throws IOException {
        RequestBody body = RequestBody.create(json, JSON);
        Request request = new Request.Builder().url(url).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            return response.body().string();
        }
    }

    public OcpcBaiduKeyWordResponse queryBaidukeyWord(String keyWordId, String userName, String accessToken) throws IOException {
        log.info("String keyWordId={}, String userName={}, String accessToken={}", keyWordId, userName, accessToken);
        String keyWordRedisKey = redisPrefixOcpc + ":BAiDUKEYWORD:" + keyWordId;
        if (RedisUtils.hasKey(keyWordRedisKey)) {
            return RedisUtils.get(keyWordRedisKey, OcpcBaiduKeyWordResponse.class);
        }
        String json = "{\"header\":{\"userName\":\"%s\",\"accessToken\":\"%s\"},\"body\":{\"wordFields\":[\"campaignId\",\"keyword\",\"price\"],\"ids\":[%s],\"idType\":11,\"getTemp\":\"0\"}}";
        String url = "https://api.baidu.com/json/sms/service/KeywordService/getWord";
        String result = this.post(url, String.format(json, userName, accessToken, keyWordId));
        log.info("result={}", result);
        JSONObject data = JSONObject.parseObject(result);
        List<JSONObject> dataList = data.getJSONObject("body").getObject("data", List.class);
        if (!dataList.isEmpty()) {
            OcpcBaiduKeyWordResponse ocpcBaiduKeyWordResponse = JSONObject.parseObject(String.valueOf(dataList.get(0)), OcpcBaiduKeyWordResponse.class);
            RedisUtils.save(keyWordRedisKey, ocpcBaiduKeyWordResponse, redisExpireOcpcBaiduKeyWord);
            return ocpcBaiduKeyWordResponse;
        }
        return null;
    }

    @Override
    public void sendRegisterTo360(String ip) {
        String key = redisPrefixOcpc + ":logidUrl:" + ip;
//        if(!RedisUtils.hasKey(key)){
//            log.info("用户注册OCPC未记录  ip:{}", ip);
//            return;
//        }
        String logidUrl = RedisUtils.get(key, String.class);
//        if(StringUtil.isNullOrEmpty(logidUrl)){
//            log.info("用户注册OCPC未记录  ip:{}", ip);
//            return;
//        }
        if (!logidUrl.contains("qhclickid=")) {
            log.info("非360OCPC  ip:{}", ip);
            return;
        }
        // 正则表达式匹配模式，用于匹配&qhclickid=或?qhclickid=后面跟随的字符直到下一个&或字符串结束
        String regex = "[\\?&]qhclickid=([^&]*)";

        // 编译正则表达式
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
        java.util.regex.Matcher matcher = pattern.matcher(logidUrl);

        // 查找匹配的部分
        if (!matcher.find()) {
            log.info("360OCPC  正则未匹配:{}", logidUrl);
            return;
        }
        String qhclickid = matcher.group(1);
        String relamName = Utils.regexrelamName(logidUrl);
        Ocpc360TokenEntity ocpc360TokenEntity = ocpc360TokenRepository.findTopByRelamNameContains(relamName);
        if (ocpc360TokenEntity == null) {
            log.info("360Token后台未配置, logidUrl:{}", logidUrl);
            return;
        }
        // 创建用于存放postData的Map
        Map<String, Object> postDataMap = new HashMap<>();
        Map<String, Object> dataDetailMap = new HashMap<>();
//        Map<String, Object> eventData = new HashMap<>();

//        eventData.put("value", 2345);
//        dataDetailMap.put("impression_id", "移动推广");
        dataDetailMap.put("qhclickid", qhclickid);
        dataDetailMap.put("event", "REGISTERED");
        dataDetailMap.put("event_time", Instant.now().getEpochSecond());
//        dataDetailMap.put("event_param", eventData);

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("request_time", Instant.now().getEpochSecond());
        dataMap.put("data_industry", "ocpc_ps_convert");
        dataMap.put("data_detail", dataDetailMap);

        postDataMap.put("data", dataMap);
        log.info("360上报请求: {}", postDataMap);

        // 向360发送数据
        sendWithRetry360(ocpc360TokenEntity.getAppKey(), ocpc360TokenEntity.getSecret(), postDataMap);
    }

    @Override
    public void sendRegisterToBaidu(String ip) {
        String key = redisPrefixOcpc + ":logidUrl:" + ip;
//        if(!RedisUtils.hasKey(key)){
//            log.info("用户注册OCPC未记录  ip:{}", ip);
//            return;
//        }
        String logidUrl = RedisUtils.get(key, String.class);
//        if(StringUtil.isNullOrEmpty(logidUrl)){
//            log.info("用户注册OCPC未记录  ip:{}", ip);
//            return;
//        }
//        if(!logidUrl.contains("bd_vid")){
//            log.info("非百度OCPC  ip:{}", ip);
//            return;
//        }
        String relamName = Utils.regexrelamName(logidUrl);
        OcpcBaiduTokenEntity ocpcBaiduToken = ocpcBaiduTokenRepository.findTopByRelamNameContains(relamName);
        if (ocpcBaiduToken == null) {
            log.info("百度Token后台未配置, logidUrl:{}", logidUrl);
            return;
        }
        List<ConversionType> conversionTypeList = new ArrayList<>();
        conversionTypeList.add(new ConversionType() {{
            setNewType(3);
//                setNewType(25);
            setLogidUrl(logidUrl);
        }});
        log.info("-----------------------{}", conversionTypeList.toString());
        Map<String, Object> data = new HashMap<>();
        data.put("token", ocpcBaiduToken.getToken());
        data.put("conversionTypes", conversionTypeList);
        // 发送的完整请求数据
        // do some log
        log.info("req data: " + JSONObject.toJSONString(data));
        // 向百度发送数据
        sendWithRetry(JSONObject.toJSONString(data));
    }

    @Override
    public void addClickBaiduData(String ip) {
        log.info("后台添加点击数据：{}", ip);
        String key = redisPrefixOcpc + ":logidUrl:" + ip;
        if (!RedisUtils.hasKey(key)) {
            log.info("用户注册OCPC未记录  ip:{}", ip);
            return;
        }
        String logidUrl = RedisUtils.get(key, String.class);
        if (StringUtil.isNullOrEmpty(logidUrl)) {
            log.info("用户注册OCPC未记录  ip:{}", ip);
            return;
        }
        if (!logidUrl.contains("bd_vid")) {
            log.info("非百度OCPC  ip:{}", ip);
            return;
        }
        String keyWordId = Utils.regexBaiduWorkId(logidUrl);
        String plan = Utils.extractParam(logidUrl, CsgoContants.baiduColumnName.BAIDU_PLAN);
        String unit = Utils.extractParam(logidUrl, CsgoContants.baiduColumnName.BAIDU_UNIT);
        if (keyWordId == null) {
            log.info("落地页没有找到keywordId  ip:{}", ip);
            return;
        }
        String relamName = Utils.regexrelamName(logidUrl);
        OcpcBaiduAccountEntity ocpcBaiduAccount = ocpcBaiduAccountRepository.findTopByRelamNameContains(relamName);
        if (ocpcBaiduAccount == null) {
            log.error("后台未配置百度账号, {}, 域名：{}", logidUrl, relamName);
            return;
        }
        OcpcChannelEntity ocpcChannel = ocpcChannelRepository.findTopByRelamName(relamName);
        try {
            OcpcBaiduKeyWordResponse ocpcBaiduKeyWordResponse = this.queryBaidukeyWord(keyWordId, ocpcBaiduAccount.getAccount(), ocpcBaiduAccount.getAccessToken());
            OcpcBaiduDataEntity ocpcBaiduDataEntity = new OcpcBaiduDataEntity();
            ocpcBaiduDataEntity.setType(OcpcBaiduDataType.CLICK);
            ocpcBaiduDataEntity.setOcpcChannel(ocpcChannel);
            ocpcBaiduDataEntity.setPrice(ocpcBaiduKeyWordResponse == null || ocpcBaiduKeyWordResponse.getPrice() == null ? null : ocpcBaiduKeyWordResponse.getPrice());
            ocpcBaiduDataEntity.setKeyWord(ocpcBaiduKeyWordResponse == null || ocpcBaiduKeyWordResponse.getKeyword() == null ? null : ocpcBaiduKeyWordResponse.getKeyword());
            ocpcBaiduDataEntity.setCreativeId(ocpcBaiduKeyWordResponse == null || ocpcBaiduKeyWordResponse.getCampaignId() == null ? null : ocpcBaiduKeyWordResponse.getCampaignId());
            ocpcBaiduDataEntity.setIp(ip);
            ocpcBaiduDataEntity.setPlan(plan);
            ocpcBaiduDataEntity.setUnit(unit);
            ocpcBaiduDataRepository.save(ocpcBaiduDataEntity);
        } catch (Exception e) {
            log.error("请求百度错误：{}", e.getMessage());
            return;
        }


    }

    @Override
    public void addRegisterBaiduData(String ip, Long userId) {
        log.info("后台添加注册数据：{}", ip);
        String key = redisPrefixOcpc + ":logidUrl:" + ip;
        if (!RedisUtils.hasKey(key)) {
            log.info("用户注册OCPC未记录  ip:{}", ip);
            return;
        }
        String logidUrl = RedisUtils.get(key, String.class);
        if (StringUtil.isNullOrEmpty(logidUrl)) {
            log.info("用户注册OCPC未记录  ip:{}", ip);
            return;
        }
        if (!logidUrl.contains("bd_vid")) {
            log.info("非百度OCPC  ip:{}", ip);
            return;
        }
        String keyWordId = Utils.regexBaiduWorkId(logidUrl);
        String plan = Utils.extractParam(logidUrl, CsgoContants.baiduColumnName.BAIDU_PLAN);
        String unit = Utils.extractParam(logidUrl, CsgoContants.baiduColumnName.BAIDU_UNIT);
        if (keyWordId == null) {
            log.info("落地页没有找到keywordId  ip:{}", ip);
            return;
        }
        String relamName = Utils.regexrelamName(logidUrl);
        OcpcBaiduAccountEntity ocpcBaiduAccount = ocpcBaiduAccountRepository.findTopByRelamNameContains(relamName);
        if (ocpcBaiduAccount == null) {
            log.error("后台未配置百度账号, {}", logidUrl);
            return;
        }
        OcpcChannelEntity ocpcChannel = ocpcChannelRepository.findTopByRelamName(relamName);
        UserEntity user = userRepository.findById(userId);

        log.info("data1：{}", JsonUtils.toJson(ocpcChannel));
        try {
            OcpcBaiduKeyWordResponse ocpcBaiduKeyWordResponse = this.queryBaidukeyWord(keyWordId, ocpcBaiduAccount.getAccount(), ocpcBaiduAccount.getAccessToken());
            OcpcBaiduDataEntity ocpcBaiduDataEntity = new OcpcBaiduDataEntity();
            ocpcBaiduDataEntity.setType(OcpcBaiduDataType.REGISTER);
            ocpcBaiduDataEntity.setOcpcChannel(ocpcChannel);
            ocpcBaiduDataEntity.setUser(user);
            ocpcBaiduDataEntity.setPrice(ocpcBaiduKeyWordResponse == null || ocpcBaiduKeyWordResponse.getPrice() == null ? null : ocpcBaiduKeyWordResponse.getPrice());
            ocpcBaiduDataEntity.setKeyWord(ocpcBaiduKeyWordResponse == null || ocpcBaiduKeyWordResponse.getKeyword() == null ? null : ocpcBaiduKeyWordResponse.getKeyword());
            ocpcBaiduDataEntity.setCreativeId(ocpcBaiduKeyWordResponse == null || ocpcBaiduKeyWordResponse.getCampaignId() == null ? null : ocpcBaiduKeyWordResponse.getCampaignId());
            ocpcBaiduDataEntity.setIp(ip);
            ocpcBaiduDataEntity.setPlan(plan);
            ocpcBaiduDataEntity.setUnit(unit);
            ocpcBaiduDataRepository.save(ocpcBaiduDataEntity);

        } catch (Exception e) {
            log.info("请求百度错误：{}", e.getMessage());
        }

    }

    @Override
    public void addChangeBaiduData(String ip, Long userId, Long orderChargeId) {
        log.info("后台添加订单数据：{}", ip);
        String key = redisPrefixOcpc + ":logidUrl:" + ip;
        if (!RedisUtils.hasKey(key)) {
            log.info("用户注册OCPC未记录  ip:{}", ip);
            return;
        }
        String logidUrl = RedisUtils.get(key, String.class);
        if (StringUtil.isNullOrEmpty(logidUrl)) {
            log.info("用户注册OCPC未记录  ip:{}", ip);
            return;
        }
        if (!logidUrl.contains("bd_vid")) {
            log.info("非百度OCPC  ip:{}", ip);
            return;
        }
        String keyWordId = Utils.regexBaiduWorkId(logidUrl);
        String plan = Utils.extractParam(logidUrl, CsgoContants.baiduColumnName.BAIDU_PLAN);
        String unit = Utils.extractParam(logidUrl, CsgoContants.baiduColumnName.BAIDU_UNIT);
        if (keyWordId == null) {
            log.info("落地页没有找到keywordId  ip:{}", ip);
            return;
        }
        String relamName = Utils.regexrelamName(logidUrl);
        OcpcBaiduAccountEntity ocpcBaiduAccount = ocpcBaiduAccountRepository.findTopByRelamNameContains(relamName);
        if (ocpcBaiduAccount == null) {
            log.error("后台未配置百度账号, {}", logidUrl);
            return;
        }
        OcpcChannelEntity ocpcChannel = ocpcChannelRepository.findTopByRelamName(relamName);
        UserEntity user = userRepository.findById(userId);
        OrderChargeEntity orderCharge = orderChargeRepository.findById(orderChargeId).get();
        try {
            OcpcBaiduKeyWordResponse ocpcBaiduKeyWordResponse = this.queryBaidukeyWord(keyWordId, ocpcBaiduAccount.getAccount(), ocpcBaiduAccount.getAccessToken());
            OcpcBaiduDataEntity ocpcBaiduDataEntity = new OcpcBaiduDataEntity();
            ocpcBaiduDataEntity.setType(OcpcBaiduDataType.CHARGE);
            ocpcBaiduDataEntity.setOcpcChannel(ocpcChannel);
            ocpcBaiduDataEntity.setUser(user);
            ocpcBaiduDataEntity.setOrderCharge(orderCharge);
            ocpcBaiduDataEntity.setPrice(ocpcBaiduKeyWordResponse == null || ocpcBaiduKeyWordResponse.getPrice() == null ? null : ocpcBaiduKeyWordResponse.getPrice());
            ocpcBaiduDataEntity.setKeyWord(ocpcBaiduKeyWordResponse == null || ocpcBaiduKeyWordResponse.getKeyword() == null ? null : ocpcBaiduKeyWordResponse.getKeyword());
            ocpcBaiduDataEntity.setCreativeId(ocpcBaiduKeyWordResponse == null || ocpcBaiduKeyWordResponse.getCampaignId() == null ? null : ocpcBaiduKeyWordResponse.getCampaignId());
            ocpcBaiduDataEntity.setPlan(plan);
            ocpcBaiduDataEntity.setUnit(unit);
            ocpcBaiduDataEntity.setIp(ip);
            ocpcBaiduDataRepository.save(ocpcBaiduDataEntity);
        } catch (Exception e) {
            log.error("请求百度错误：{}", e.getMessage());
        }
    }

    @Async("async-executor-guava")
    public void sendWithRetry(String msg) {
        // 发送请求
        CloseableHttpClient client = HttpClients.createDefault();
        log.info("BAIDU_OCPC_URL: {}", BAIDU_OCPC_URL);
        HttpPost post = new HttpPost(BAIDU_OCPC_URL);
        post.setHeader("Content-type", "application/json; charset=UTF-8");
        StringEntity entity = new StringEntity(msg, Charset.forName("UTF-8"));
        entity.setContentEncoding("UTF-8");
        post.setEntity(entity);
        // 添加失败重试
        for (int i = 0; i < RETRY_TIMES; i++) {
            try {
                HttpResponse response = client.execute(post);
                // 检验状态码，如果成功接收数据
                int code = response.getStatusLine().getStatusCode();
                if (code == HttpStatus.SC_OK) {
                    String res = EntityUtils.toString(response.getEntity());
                    JsonObject returnData = new JsonParser().parse(res).getAsJsonObject();
                    // 打印返回结果
                    // do some log
                    log.info("retry times :" + i + ", res data: " + res);

                    int status = returnData.getAsJsonObject("header").get("status").getAsInt();
                    // status为4，代表服务端异常，可添加重试
                    if (status != 4) {
                        log.info("上传百度OPCC成功, {}", msg);
                        return;
                    }
                }
            } catch (IOException e) {
                log.error("上传百度OPCC失败, {} , {}", msg, e.getMessage());
                // do some log
            }
        }
    }

    @Async("async-executor-guava")
    public void sendWithRetry360(String appKey, String secret, Map<String, Object> postData) {
        String postDataStr = "";
        try {
            // 将Map转换为JSON字符串
            ObjectMapper objectMapper = new ObjectMapper();
            postDataStr = objectMapper.writeValueAsString(postData);
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 发送请求
        log.info("OCPC_URL_360: {}", OCPC_URL_360);
//        post.setHeader("Content-Type", "application/json;charset=utf-8");
        // 计算App-Sign
        String appSign = DigestUtils.md5Hex(secret + postDataStr);
        log.info("appKey: {}, appSign: {}", appKey, appSign);
        HttpHeaders headers = new HttpHeaders();
        // 添加其他需要的header
        headers.set("App-Key", appKey);
        headers.set("App-Sign", appSign);
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(postData, headers);
        // 发送POST请求，并接收响应
        ResponseEntity<String> responseEntity = restTemplate.exchange(OCPC_URL_360, HttpMethod.POST, requestEntity, String.class);
        // 输出响应结果
        log.info("360响应: {}", responseEntity.getBody());

    }

    @Override
    public void sendRegisterToMeta(String ip) {
        String key = redisPrefixOcpc + ":logidUrl:" + ip;
        String logidUrl = RedisUtils.get(key, String.class);
        if (StringUtil.isNullOrEmpty(logidUrl)) {
            log.info("用户注册OCPC未记录  ip:{}", ip);
            return;
        }
        if (!logidUrl.contains("fbclid=")) {
            log.info("非Meta OCPC  ip:{}", ip);
            return;
        }
        
        String relamName = Utils.regexrelamName(logidUrl);
        OcpcMetaTokenEntity ocpcMetaToken = ocpcMetaTokenRepository.findTopByRelamNameContains(relamName);
        if (ocpcMetaToken == null) {
            log.info("Meta Token后台未配置, logidUrl:{}", logidUrl);
            return;
        }

        String fbclid = Utils.extractParam(logidUrl, "fbclid");
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("event_name", "CompleteRegistration");
        eventData.put("event_time", Instant.now().getEpochSecond());
        eventData.put("event_id", java.util.UUID.randomUUID().toString());
        eventData.put("event_source_url", logidUrl);
        eventData.put("action_source", "website");
        
        Map<String, Object> userData = new HashMap<>();
        userData.put("client_ip_address", ip);
        userData.put("client_user_agent", "");
        userData.put("fbc", fbclid);
        eventData.put("user_data", userData);

        Map<String, Object> postData = new HashMap<>();
        postData.put("data", Collections.singletonList(eventData));
        postData.put("access_token", ocpcMetaToken.getAccessToken());

        sendWithRetryMeta(ocpcMetaToken.getPixelId(), postData);
    }

    @Override
    public void addClickMetaData(String ip) {
        log.info("后台添加Meta点击数据：{}", ip);
        String key = redisPrefixOcpc + ":logidUrl:" + ip;
        if (!RedisUtils.hasKey(key)) {
            log.info("用户OCPC未记录  ip:{}", ip);
            return;
        }
        String logidUrl = RedisUtils.get(key, String.class);
        if (StringUtil.isNullOrEmpty(logidUrl)) {
            log.info("用户OCPC未记录  ip:{}", ip);
            return;
        }
        if (!logidUrl.contains("fbclid=")) {
            log.info("非Meta OCPC  ip:{}", ip);
            return;
        }

        String relamName = Utils.regexrelamName(logidUrl);
        OcpcChannelEntity ocpcChannel = ocpcChannelRepository.findTopByRelamName(relamName);
        
        OcpcMetaDataEntity ocpcMetaDataEntity = new OcpcMetaDataEntity();
        ocpcMetaDataEntity.setType(OcpcMetaDataType.CLICK);
        ocpcMetaDataEntity.setOcpcChannel(ocpcChannel);
        ocpcMetaDataEntity.setIp(ip);
        ocpcMetaDataEntity.setClickId(Utils.extractParam(logidUrl, "fbclid"));
        ocpcMetaDataRepository.save(ocpcMetaDataEntity);
    }

    @Override
    public void addRegisterMetaData(String ip, Long userId) {
        log.info("后台添加Meta注册数据：{}", ip);
        String key = redisPrefixOcpc + ":logidUrl:" + ip;
        if (!RedisUtils.hasKey(key)) {
            log.info("用户OCPC未记录  ip:{}", ip);
            return;
        }
        String logidUrl = RedisUtils.get(key, String.class);
        if (StringUtil.isNullOrEmpty(logidUrl)) {
            log.info("用户OCPC未记录  ip:{}", ip);
            return;
        }
        if (!logidUrl.contains("fbclid=")) {
            log.info("非Meta OCPC  ip:{}", ip);
            return;
        }

        String relamName = Utils.regexrelamName(logidUrl);
        OcpcChannelEntity ocpcChannel = ocpcChannelRepository.findTopByRelamName(relamName);
        UserEntity user = userRepository.findById(userId);

        OcpcMetaDataEntity ocpcMetaDataEntity = new OcpcMetaDataEntity();
        ocpcMetaDataEntity.setType(OcpcMetaDataType.REGISTER);
        ocpcMetaDataEntity.setOcpcChannel(ocpcChannel);
        ocpcMetaDataEntity.setUser(user);
        ocpcMetaDataEntity.setIp(ip);
        ocpcMetaDataEntity.setClickId(Utils.extractParam(logidUrl, "fbclid"));
        ocpcMetaDataRepository.save(ocpcMetaDataEntity);
    }

    @Override
    public void addChangeMetaData(String ip, Long userId, Long orderChargeId) {
        log.info("后台添加Meta订单数据：{}", ip);
        String key = redisPrefixOcpc + ":logidUrl:" + ip;
        if (!RedisUtils.hasKey(key)) {
            log.info("用户OCPC未记录  ip:{}", ip);
            return;
        }
        String logidUrl = RedisUtils.get(key, String.class);
        if (StringUtil.isNullOrEmpty(logidUrl)) {
            log.info("用户OCPC未记录  ip:{}", ip);
            return;
        }
        if (!logidUrl.contains("fbclid=")) {
            log.info("非Meta OCPC  ip:{}", ip);
            return;
        }

        String relamName = Utils.regexrelamName(logidUrl);
        OcpcChannelEntity ocpcChannel = ocpcChannelRepository.findTopByRelamName(relamName);
        UserEntity user = userRepository.findById(userId);
        OrderChargeEntity orderCharge = orderChargeRepository.findById(orderChargeId).get();

        OcpcMetaDataEntity ocpcMetaDataEntity = new OcpcMetaDataEntity();
        ocpcMetaDataEntity.setType(OcpcMetaDataType.CHARGE);
        ocpcMetaDataEntity.setOcpcChannel(ocpcChannel);
        ocpcMetaDataEntity.setUser(user);
        ocpcMetaDataEntity.setOrderCharge(orderCharge);
        ocpcMetaDataEntity.setIp(ip);
        ocpcMetaDataEntity.setClickId(Utils.extractParam(logidUrl, "fbclid"));
        ocpcMetaDataEntity.setEventValue(orderCharge.getActualAmount());
        ocpcMetaDataRepository.save(ocpcMetaDataEntity);
    }

    @Async("async-executor-guava")
    public void sendWithRetryMeta(String pixelId, Map<String, Object> postData) {
        String url = String.format(OCPC_URL_META, pixelId);
        log.info("Meta OCPC URL: {}", url);
        
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String postDataStr = objectMapper.writeValueAsString(postData);
            
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            HttpEntity<String> requestEntity = new HttpEntity<>(postDataStr, headers);
            
            for (int i = 0; i < RETRY_TIMES_META; i++) {
                try {
                    ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
                    log.info("Meta响应: {}", responseEntity.getBody());
                    
                    if (responseEntity.getStatusCode().is2xxSuccessful()) {
                        log.info("上传Meta OCPC成功, {}", postDataStr);
                        return;
                    }
                } catch (Exception e) {
                    log.error("上传Meta OCPC失败, 重试次数: {}, 错误: {}", i, e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("Meta OCPC数据序列化失败: {}", e.getMessage());
        }
    }

    @Override
    public void sendRegisterToGoogle(String ip) {
        String key = redisPrefixOcpc + ":logidUrl:" + ip;
        String logidUrl = RedisUtils.get(key, String.class);
        if (StringUtil.isNullOrEmpty(logidUrl)) {
            log.info("用户注册OCPC未记录  ip:{}", ip);
            return;
        }
        if (!logidUrl.contains("gclid=")) {
            log.info("非Google OCPC  ip:{}", ip);
            return;
        }

        String relamName = Utils.regexrelamName(logidUrl);
        OcpcGoogleTokenEntity ocpcGoogleToken = ocpcGoogleTokenRepository.findTopByRelamNameContains(relamName);
        if (ocpcGoogleToken == null) {
            log.info("Google Token后台未配置, logidUrl:{}", logidUrl);
            return;
        }

        // 构建Google Analytics 4 Measurement Protocol数据
        Map<String, Object> postData = new HashMap<>();
        postData.put("client_id", Utils.extractParam(logidUrl, "gclid"));

        Map<String, Object> event = new HashMap<>();
        event.put("name", "conversion");
        Map<String, Object> params = new HashMap<>();
        params.put("send_to", ocpcGoogleToken.getConversionId() + "/" + ocpcGoogleToken.getConversionLabel());
        event.put("params", params);

        postData.put("events", new Object[]{event});

        sendWithRetryGoogle(ocpcGoogleToken.getMeasurementId(), ocpcGoogleToken.getApiSecret(), postData);
    }

    @Override
    public void addClickGoogleData(String ip) {
        log.info("后台添加Google点击数据：{}", ip);
        String key = redisPrefixOcpc + ":logidUrl:" + ip;
        if (!RedisUtils.hasKey(key)) {
            log.info("用户OCPC未记录  ip:{}", ip);
            return;
        }
        String logidUrl = RedisUtils.get(key, String.class);
        if (StringUtil.isNullOrEmpty(logidUrl)) {
            log.info("用户OCPC未记录  ip:{}", ip);
            return;
        }
        if (!logidUrl.contains("gclid=")) {
            log.info("非Google OCPC  ip:{}", ip);
            return;
        }

        String relamName = Utils.regexrelamName(logidUrl);
        OcpcChannelEntity ocpcChannel = ocpcChannelRepository.findTopByRelamName(relamName);

        OcpcGoogleDataEntity ocpcGoogleDataEntity = new OcpcGoogleDataEntity();
        ocpcGoogleDataEntity.setType(OcpcGoogleDataType.CLICK);
        ocpcGoogleDataEntity.setOcpcChannel(ocpcChannel);
        ocpcGoogleDataEntity.setIp(ip);
        ocpcGoogleDataEntity.setGclid(Utils.extractParam(logidUrl, "gclid"));
        ocpcGoogleDataRepository.save(ocpcGoogleDataEntity);
    }

    @Override
    public void addRegisterGoogleData(String ip, Long userId) {
        log.info("后台添加Google注册数据：{}", ip);
        String key = redisPrefixOcpc + ":logidUrl:" + ip;
        if (!RedisUtils.hasKey(key)) {
            log.info("用户OCPC未记录  ip:{}", ip);
            return;
        }
        String logidUrl = RedisUtils.get(key, String.class);
        if (StringUtil.isNullOrEmpty(logidUrl)) {
            log.info("用户OCPC未记录  ip:{}", ip);
            return;
        }
        if (!logidUrl.contains("gclid=")) {
            log.info("非Google OCPC  ip:{}", ip);
            return;
        }

        String relamName = Utils.regexrelamName(logidUrl);
        OcpcChannelEntity ocpcChannel = ocpcChannelRepository.findTopByRelamName(relamName);
        UserEntity user = userRepository.findById(userId);

        OcpcGoogleDataEntity ocpcGoogleDataEntity = new OcpcGoogleDataEntity();
        ocpcGoogleDataEntity.setType(OcpcGoogleDataType.REGISTER);
        ocpcGoogleDataEntity.setOcpcChannel(ocpcChannel);
        ocpcGoogleDataEntity.setUser(user);
        ocpcGoogleDataEntity.setIp(ip);
        ocpcGoogleDataEntity.setGclid(Utils.extractParam(logidUrl, "gclid"));
        ocpcGoogleDataRepository.save(ocpcGoogleDataEntity);
    }

    @Override
    public void addChangeGoogleData(String ip, Long userId, Long orderChargeId) {
        log.info("后台添加Google订单数据：{}", ip);
        String key = redisPrefixOcpc + ":logidUrl:" + ip;
        if (!RedisUtils.hasKey(key)) {
            log.info("用户OCPC未记录  ip:{}", ip);
            return;
        }
        String logidUrl = RedisUtils.get(key, String.class);
        if (StringUtil.isNullOrEmpty(logidUrl)) {
            log.info("用户OCPC未记录  ip:{}", ip);
            return;
        }
        if (!logidUrl.contains("gclid=")) {
            log.info("非Google OCPC  ip:{}", ip);
            return;
        }

        String relamName = Utils.regexrelamName(logidUrl);
        OcpcChannelEntity ocpcChannel = ocpcChannelRepository.findTopByRelamName(relamName);
        UserEntity user = userRepository.findById(userId);
        OrderChargeEntity orderCharge = orderChargeRepository.findById(orderChargeId).get();

        OcpcGoogleDataEntity ocpcGoogleDataEntity = new OcpcGoogleDataEntity();
        ocpcGoogleDataEntity.setType(OcpcGoogleDataType.CHARGE);
        ocpcGoogleDataEntity.setOcpcChannel(ocpcChannel);
        ocpcGoogleDataEntity.setUser(user);
        ocpcGoogleDataEntity.setOrderCharge(orderCharge);
        ocpcGoogleDataEntity.setIp(ip);
        ocpcGoogleDataEntity.setGclid(Utils.extractParam(logidUrl, "gclid"));
        ocpcGoogleDataEntity.setEventValue(orderCharge.getActualAmount());
        ocpcGoogleDataRepository.save(ocpcGoogleDataEntity);
    }

    @Async("async-executor-guava")
    public void sendWithRetryGoogle(String measurementId, String apiSecret, Map<String, Object> postData) {
        String url = String.format(OCPC_URL_GOOGLE, measurementId, apiSecret);
        log.info("Google OCPC URL: {}", url);

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String postDataStr = objectMapper.writeValueAsString(postData);

            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            HttpEntity<String> requestEntity = new HttpEntity<>(postDataStr, headers);

            for (int i = 0; i < RETRY_TIMES_GOOGLE; i++) {
                try {
                    ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
                    log.info("Google响应: {}", responseEntity.getBody());

                    if (responseEntity.getStatusCode().is2xxSuccessful()) {
                        log.info("上传Google OCPC成功, {}", postDataStr);
                        return;
                    }
                } catch (Exception e) {
                    log.error("上传Google OCPC失败, 重试次数: {}, 错误: {}", i, e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("Google OCPC数据序列化失败: {}", e.getMessage());
        }
    }
}
