package com.steamgo1.csgoskinapi.service;

import com.paypal.api.payments.*;
import com.paypal.base.rest.APIContext;
import com.paypal.base.rest.PayPalRESTException;
import com.steamgo1.csgoskinapi.config.pay.paypal.PayPalConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

@Service
public class PayPalService {

    @Resource
    @Lazy
    private APIContext apiContext;
    @Resource
    @Lazy
    private PayPalConfiguration payPalConfiguration;

    public Payment createPayment(
            Double total,
            String currency,
            String method,
            String intent,
            String description,
            String custom) throws PayPalRESTException {

        Amount amount = new Amount();
        amount.setCurrency(currency);
        total = new BigDecimal(total).setScale(2, RoundingMode.HALF_UP).doubleValue();
        amount.setTotal(String.format("%.2f", total));

        Transaction transaction = new Transaction();
        transaction.setDescription(description);
        transaction.setAmount(amount);

        List<Transaction> transactions = new ArrayList<>();
        transactions.add(transaction);

        Payer payer = new Payer();
        payer.setPaymentMethod(method);

        Payment payment = new Payment();
        payment.setIntent(intent);
        payment.setPayer(payer);
        payment.setTransactions(transactions);

        RedirectUrls redirectUrls = new RedirectUrls();
        redirectUrls.setCancelUrl(payPalConfiguration.getProperties().getCancelUrl());
        redirectUrls.setReturnUrl(payPalConfiguration.getProperties().getReturnUrl());
        payment.setRedirectUrls(redirectUrls);

        return payment.create(apiContext);
    }

    public Payment executePayment(String paymentId, String payerId) throws PayPalRESTException {
        Payment payment = new Payment();
        payment.setId(paymentId);
        PaymentExecution paymentExecute = new PaymentExecution();
        paymentExecute.setPayerId(payerId);
        return payment.execute(apiContext, paymentExecute);
    }

    public Refund refundPayment(String saleId) throws PayPalRESTException {
        Sale sale = new Sale();
        sale.setId(saleId);

        Amount amount = new Amount();
        amount.setCurrency("USD");
        amount.setTotal("10.00"); // 退款金额

        RefundRequest refundRequest = new RefundRequest();
        refundRequest.setAmount(amount);

        return sale.refund(apiContext, refundRequest);
    }
}
