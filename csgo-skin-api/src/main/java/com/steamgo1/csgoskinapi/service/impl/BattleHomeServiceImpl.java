package com.steamgo1.csgoskinapi.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinapi.converter.CaseConverter;
import com.steamgo1.csgoskinapi.dto.BattleHomeCreateDTO;
import com.steamgo1.csgoskinapi.dto.BattleHomeQueryDTO;
import com.steamgo1.csgoskinapi.service.*;
import com.steamgo1.csgoskinapi.utils.SecurityUtils;
import com.steamgo1.csgoskinapi.vo.*;
import com.steamgo1.csgoskincommon.contant.CsgoContants;
import com.steamgo1.csgoskincommon.dao.*;
import com.steamgo1.csgoskincommon.entity.*;
import com.steamgo1.csgoskincommon.entity.enums.*;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageMethod;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageType;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import com.steamgo1.csgoskincommon.utils.RateLimiterUtils;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import com.steamgo1.csgoskincommon.vo.websocket.MessageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Transactional
@Service
public class BattleHomeServiceImpl implements BattleHomeService {
    @Value("${rabbitmq.exchange.csgo}")
    private String exchageName;

    @Value("${spring.redis.prefix.battle-home}")
    private String redisBattleHomePrefix;
    @Autowired
    private BattleHomeReposiotry battleHomeReposiotry;

    @Autowired
    private BattleHomeUserReposiotry battleHomeUserReposiotry;

    @Autowired
    private BattleHomeCaseReposiotry battleHomeCaseReposiotry;


    @Autowired
    private CaseRepository caseRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private CaseService caseService;

    @Autowired
    private CaseConverter caseConverter;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private BattleHomeCaseUserRecordReposiotry battleHomeCaseUserRecordReposiotry;

    @Autowired
    private SkinService skinService;

    @Autowired
    private AsyncTaskService asyncTaskService;

    @Autowired
    private UserCoinRecordRepository userCoinRecordRepository;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ActivityService activityService;

    @Autowired
    private BattleHomeCaseUserSkinReposiotry battleHomeCaseUserSkinReposiotry;

    @Override
    public BattleHomeQueryParamVO getBattleHomequeryParam() {
        List<BattleHomeQueryParamVO.Status> statusList = new ArrayList<>();
        for (BattleHomeStatus battleHomeStatus : BattleHomeStatus.values()) {
            statusList.add(new BattleHomeQueryParamVO.Status() {{
                setCode(battleHomeStatus.getCode());
                setValue(battleHomeStatus.getValue());
            }});
        }
        List<BattleHomeQueryParamVO.Method> methodList = new ArrayList<>();
        for (BattleHomeMethod battleHomeMethod : BattleHomeMethod.values()) {
            methodList.add(new BattleHomeQueryParamVO.Method() {{
                setCode(battleHomeMethod.getCode());
                setValue(battleHomeMethod.getValue());
            }});
        }
        return new BattleHomeQueryParamVO() {{
            setStatusList(statusList);
            setMethodList(methodList);
        }};
    }

    @Override
    public void joinBattleHome(Long battleHomeId) {
        UserEntity user = userService.getUser();
        UserProfileEntity userProfile = userProfileRepository.findByUser(user);
        BattleHomeEntity battleHome = battleHomeReposiotry.findById(battleHomeId).get();
        if (battleHome == null || !battleHome.getBattleHomeStatus().equals(BattleHomeStatus.WAITING)) {
            log.error("参加竞技房失败：{}", battleHomeId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.battle.home.cannot.join"));
        }
        // 判断是否参与过竞技房
        BattleHomeUserEntity battleHomeUserEntity = battleHomeUserReposiotry.findByBattleHomeAndUser(battleHome, user);
        if (battleHomeUserEntity != null) {
            log.error("参加竞技房失败,已参与：{}", battleHomeId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.battle.home.already.joined"));
        }
        BattleHomeUserEntity battleHomeUser = new BattleHomeUserEntity();
        battleHomeUser.setBattleHome(battleHome);
        battleHomeUser.setIsWin(false);
        battleHomeUser.setUser(user);
        battleHomeUser.setIsOwner(false);
        battleHomeUserReposiotry.save(battleHomeUser);
        Integer battleHomeUserTotal = battleHomeUserReposiotry.findByBattleHome(battleHome).size();
        if (battleHomeUserTotal > battleHome.getTotalPlayer()) {
            log.error("参加箱子失败,房间已满：{}", battleHomeId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.battle.home.room.full"));
        }
        if (userProfile.getCoin().compareTo(battleHome.getAmount()) == -1) {
            log.info("金币不足");
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.insufficient.coins"));
        }
        // 记录金币流水
        UserCoinRecordEntity userCoinRecordEntity = new UserCoinRecordEntity();
        userCoinRecordEntity.setUser(userProfile.getUser());
        userCoinRecordEntity.setSource(UserCoinChangeSource.BATTLE);
        userCoinRecordEntity.setSourceId(battleHomeId);
        userCoinRecordEntity.setAmount(battleHome.getAmount());
        userCoinRecordEntity.setAfterAmount(userProfile.getCoin().subtract(battleHome.getAmount()));
        userCoinRecordEntity.setIsPositive(false);
        userCoinRecordRepository.save(userCoinRecordEntity);
        userProfile.setCoin(userProfile.getCoin().subtract(battleHome.getAmount()));
        userProfileRepository.save(userProfile);
        if (battleHomeUserTotal == battleHome.getTotalPlayer()) {
            battleHome.setBattleHomeStatus(BattleHomeStatus.RUNNING);
            battleHome = battleHomeReposiotry.save(battleHome);
//            asyncTaskService.startBattleHome(battleHome);
        }
        // 放入websocket通知队列,通知客户端
        BattleHomeVO battleHomeVO = this.queryBattleHomeBaseInfoById(battleHome.getId());
        MessageVO messageVO = new MessageVO(WebSocketMessageMethod.SEND_ALL, WebSocketMessageType.BATTLE_UPDATE, battleHomeVO);
        rabbitTemplate.convertAndSend(exchageName, "websocket.battleHomeJoin", JSONObject.toJSONString(messageVO));
//        webSocket.sendAllMessage(new MessageVO(WebSocketMessageMethod.SEND_ALL, WebSocketMessageType.BATTLE, battleHomeVO));
        activityService.queryUserConsumePlan(user.getId(), battleHome.getAmount());
        userService.DailyActivity(user.getId(), DailyActivityType.BATTLE);

    }

    //    @Override
    public void robotJoinBattleHome_old(Long battleHomeId) {

        BattleHomeEntity battleHome = battleHomeReposiotry.findById(battleHomeId).get();
        List<UserEntity> robotUserList = null;
        if (battleHome.getBattleHomeMethod().equals(BattleHomeMethod.PVP)) {
            robotUserList = userRepository.randomCheatRobot(UserType.CHEAT_ROBOT.getCode() - 1, 1);
        } else {
            robotUserList = userRepository.randomCheatRobot(UserType.ROBOT.getCode() - 1, 1);
        }
        UserEntity lotteryRobot = null;
        UserProfileEntity lotteryRobotProfile = null;
        for (UserEntity robotUser : robotUserList) {
            // 判断是否参与过竞技房
            BattleHomeUserEntity battleHomeUserEntity = battleHomeUserReposiotry.findByBattleHomeAndUser(battleHome, robotUser);
            if (battleHomeUserEntity != null) {
                continue;
            }
            UserProfileEntity userProfile = userProfileRepository.findByUser(robotUser);
            lotteryRobot = robotUser;
            lotteryRobotProfile = userProfile;
            // robot 不再校验金币
//            if(userProfile.getCoin().compareTo(battleHome.getAmount())!=-1){
//                lotteryRobot = robotUser;
//                lotteryRobotProfile = userProfile;
//                break;
//            }
        }
//        for (int i = 1;i<battleHome.getTotalPlayer();i++){
//
//        }
        if (lotteryRobot == null) {
            log.warn("没有就绪的机器人可以加入游戏");
            return;
        }
        if (battleHome == null || !battleHome.getBattleHomeStatus().equals(BattleHomeStatus.WAITING)) {
            log.error("参加竞技房失败：{}", battleHomeId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.battle.home.cannot.join"));
        }
        BattleHomeUserEntity battleHomeUser = new BattleHomeUserEntity();
        battleHomeUser.setBattleHome(battleHome);
        battleHomeUser.setIsWin(false);
        battleHomeUser.setUser(lotteryRobot);
        battleHomeUser.setIsOwner(false);
        battleHomeUserReposiotry.save(battleHomeUser);
        Integer battleHomeUserTotal = battleHomeUserReposiotry.findByBattleHome(battleHome).size();
        if (battleHomeUserTotal > battleHome.getTotalPlayer()) {
            log.error("参加箱子失败,房间已满：{}", battleHomeId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.battle.home.room.full"));
        }
//        if(lotteryRobotProfile.getCoin().compareTo(battleHome.getAmount())==-1){
//            log.info("金币不足");
//            // todo 国际化
//        throw new CsgoSkinException(I18nUtils.getMessage("exception.insufficient.coins"));
//        }
        // 记录金币流水
        UserCoinRecordEntity userCoinRecordEntity = new UserCoinRecordEntity();
        userCoinRecordEntity.setUser(lotteryRobot);
        userCoinRecordEntity.setSource(UserCoinChangeSource.BATTLE);
        userCoinRecordEntity.setSourceId(battleHomeId);
        userCoinRecordEntity.setAmount(battleHome.getAmount());
        userCoinRecordEntity.setAfterAmount(lotteryRobotProfile.getCoin().subtract(battleHome.getAmount()));
        userCoinRecordEntity.setIsPositive(false);
        userCoinRecordRepository.save(userCoinRecordEntity);
        lotteryRobotProfile.setCoin(lotteryRobotProfile.getCoin().subtract(battleHome.getAmount()));
        userProfileRepository.save(lotteryRobotProfile);
        if (battleHomeUserTotal == battleHome.getTotalPlayer()) {
            battleHome.setBattleHomeStatus(BattleHomeStatus.RUNNING);
            battleHome = battleHomeReposiotry.save(battleHome);
//            asyncTaskService.startBattleHome(battleHome);
        }
        // 放入websocket通知队列,通知客户端
        BattleHomeVO battleHomeVO = this.queryBattleHomeBaseInfoById(battleHome.getId());
        MessageVO messageVO = new MessageVO(WebSocketMessageMethod.SEND_ALL, WebSocketMessageType.BATTLE_UPDATE, battleHomeVO);
        rabbitTemplate.convertAndSend(exchageName, "websocket.battleHomeJoin", JSONObject.toJSONString(messageVO));
    }


    @Override
    public void robotJoinBattleHome(Long battleHomeId) {

        BattleHomeEntity battleHome = battleHomeReposiotry.findById(battleHomeId).get();
        List<UserEntity> robotUserList = null;
        if (battleHome.getBattleHomeMethod().equals(BattleHomeMethod.PVP)) {
            robotUserList = userRepository.randomCheatRobot(UserType.CHEAT_ROBOT.getCode() - 1, 1);
        } else {
            robotUserList = userRepository.randomCheatRobot(UserType.ROBOT.getCode() - 1, 1);
        }
        for (int i = 1; i < battleHome.getTotalPlayer(); i++) {
            UserEntity lotteryRobot = null;
            UserProfileEntity lotteryRobotProfile = null;
            for (UserEntity robotUser : robotUserList) {
                // 判断是否参与过竞技房
                BattleHomeUserEntity battleHomeUserEntity = battleHomeUserReposiotry.findByBattleHomeAndUser(battleHome, robotUser);
                if (battleHomeUserEntity != null) {
                    continue;
                }
                UserProfileEntity userProfile = userProfileRepository.findByUser(robotUser);
                lotteryRobot = robotUser;
                lotteryRobotProfile = userProfile;
            }

            if (lotteryRobot == null) {
                log.warn("没有就绪的机器人可以加入游戏");
                return;
            }
            if (battleHome == null || !battleHome.getBattleHomeStatus().equals(BattleHomeStatus.WAITING)) {
                log.error("参加竞技房失败：{}", battleHomeId);
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.battle.home.cannot.join"));
            }

            BattleHomeUserEntity battleHomeUser = new BattleHomeUserEntity();
            battleHomeUser.setBattleHome(battleHome);
            battleHomeUser.setIsWin(false);
            battleHomeUser.setUser(lotteryRobot);
            battleHomeUser.setIsOwner(false);
            battleHomeUserReposiotry.save(battleHomeUser);
            Integer battleHomeUserTotal = battleHomeUserReposiotry.findByBattleHome(battleHome).size();
            if (battleHomeUserTotal > battleHome.getTotalPlayer()) {
                log.error("参加箱子失败,房间已满：{}", battleHomeId);
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.battle.home.room.full"));
            }
            // 记录金币流水
            UserCoinRecordEntity userCoinRecordEntity = new UserCoinRecordEntity();
            userCoinRecordEntity.setUser(lotteryRobot);
            userCoinRecordEntity.setSource(UserCoinChangeSource.BATTLE);
            userCoinRecordEntity.setSourceId(battleHomeId);
            userCoinRecordEntity.setAmount(battleHome.getAmount());
            userCoinRecordEntity.setAfterAmount(lotteryRobotProfile.getCoin().subtract(battleHome.getAmount()));
            userCoinRecordEntity.setIsPositive(false);
            userCoinRecordRepository.save(userCoinRecordEntity);
            lotteryRobotProfile.setCoin(lotteryRobotProfile.getCoin().subtract(battleHome.getAmount()));
            userProfileRepository.save(lotteryRobotProfile);
            if (battleHomeUserTotal == battleHome.getTotalPlayer()) {
                battleHome.setBattleHomeStatus(BattleHomeStatus.RUNNING);
                battleHome = battleHomeReposiotry.save(battleHome);
                break;
//            asyncTaskService.startBattleHome(battleHome);
            }
            if (battleHome.getBattleHomeMethod().equals(BattleHomeMethod.PVP)) {
                break;
            }

        }
        // 放入websocket通知队列,通知客户端
        BattleHomeVO battleHomeVO = this.queryBattleHomeBaseInfoById(battleHome.getId());
        MessageVO messageVO = new MessageVO(WebSocketMessageMethod.SEND_ALL, WebSocketMessageType.BATTLE_UPDATE, battleHomeVO);
        rabbitTemplate.convertAndSend(exchageName, "websocket.battleHomeJoin", JSONObject.toJSONString(messageVO));
    }

    @Override
    public BattleHomeVO createBattleHome(BattleHomeCreateDTO battleHomeCreateDTO) {
        UserEntity user = userService.getUser();
        if (!RateLimiterUtils.tryAcquire(user.getId())) {
            log.warn("创建对战被限流: {}", user.getId());
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.operation.too.frequent"));
        }
        UserProfileEntity userProfile = userProfileRepository.findByUser(user);
        BattleHomeEntity battleHome = new BattleHomeEntity();
        List<BattleHomeCaseEntity> battleHomeCaseEntityList = new ArrayList<>();
        BattleHomeUserEntity battleHomeUserEntity = new BattleHomeUserEntity();
        // 数据校验TODO
        battleHome.setBattleHomeMethod(BattleHomeMethod.instance(battleHomeCreateDTO.getMethod()));
        battleHome.setBattleHomeStatus(BattleHomeStatus.WAITING);
        battleHome.setTotalPlayer(battleHomeCreateDTO.getTotalPlayer());
        BigDecimal amount = new BigDecimal(0); // 总消耗
        for (BattleHomeCreateDTO.Case battleHomeCreateCase : battleHomeCreateDTO.getCaseList()) {
            BattleHomeCaseEntity battleHomeCase = new BattleHomeCaseEntity();
            CaseEntity box = caseRepository.findById(battleHomeCreateCase.getCaseId());
            if (box.getDisableBattle()) {
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.case.not.battle.box"));
            }
            if (box == null) {
                log.error("创建对战失败,箱子不存在 {}", battleHomeCreateCase.getCaseId());
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.case.not.exist.or.offline"));
            }
            amount = amount.add(box.getPrice());
            battleHomeCase.setBattleHome(battleHome);
            battleHomeCase.setBox(box);
            battleHomeCase.setStatus(BattleHomeCaseStatus.UNOPEN);
            battleHomeCaseEntityList.add(battleHomeCase);
        }
        battleHome.setAmount(amount);
        battleHome.setRounds(0);
        battleHome.setTotalRounds(battleHomeCreateDTO.getCaseList().size());
        if (user.getType().equals(UserType.ANCHOR)) {
            battleHome.setIsHide(true);
        }
        battleHome = battleHomeReposiotry.save(battleHome);
        battleHomeUserEntity.setBattleHome(battleHome);
        battleHomeUserEntity.setUser(user);
        battleHomeUserEntity.setIsOwner(true);
        battleHomeUserEntity.setIsWin(false);
        // 用户余额消耗 记录
        if (userProfile.getCoin().compareTo(amount) == -1) {
            log.info("金币不足");
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.insufficient.coins"));
        }
        // 记录金币流水
        UserCoinRecordEntity userCoinRecordEntity = new UserCoinRecordEntity();
        userCoinRecordEntity.setUser(userProfile.getUser());
        userCoinRecordEntity.setSource(UserCoinChangeSource.BATTLE);
        userCoinRecordEntity.setSourceId(battleHome.getId());
        userCoinRecordEntity.setAmount(amount);
        userCoinRecordEntity.setAfterAmount(userProfile.getCoin().subtract(amount));
        userCoinRecordEntity.setIsPositive(false);
        userCoinRecordRepository.save(userCoinRecordEntity);

        userProfile.setCoin(userProfile.getCoin().subtract(amount));
        battleHomeUserReposiotry.save(battleHomeUserEntity);
        battleHomeCaseReposiotry.saveAll(battleHomeCaseEntityList);
        userProfileRepository.save(userProfile);
        // 通知客户端
        BattleHomeVO battleHomeVO = this.queryBattleHomeBaseInfoById(battleHome.getId());
        MessageVO messageVO = new MessageVO(WebSocketMessageMethod.SEND_ALL, WebSocketMessageType.BATTLE_CREATE, battleHomeVO);
        asyncTaskService.sendWebSocketMessageToAll(JSONObject.toJSONString(messageVO));
        userService.DailyActivity(user.getId(), DailyActivityType.BATTLE);
        activityService.queryUserConsumePlan(user.getId(), amount);
        return battleHomeVO;
    }

    @Override
    public Page<BattleHomeBaseInfoVO> queryBattleHomeBaseInfo(BattleHomeQueryDTO battleHomeQueryDTO) {
        log.info("+++++++++++++++++++{}", SecurityUtils.isAnonymousUser());
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(battleHomeQueryDTO.getPage(), battleHomeQueryDTO.getSize(), sort);
//        Page<BattleHomeEntity> battleHomeEntityPage = battleHomeReposiotry.findByBattleHomeStatus(BattleHomeStatus.instance(battleHomeQueryDTO.getStatus()), pageable);
        long oneDay = 24 * 60 * 60 * 1000;
        LocalDateTime dateTime = LocalDateTime.now().minus(oneDay, ChronoUnit.MILLIS);
        Date date = Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());
        Page<BattleHomeEntity> battleHomeEntityPage;
        if (SecurityUtils.isAnonymousUser()) {
            battleHomeEntityPage = battleHomeReposiotry.findAllByCreateTimeAfterAndIsHideIsFalse(date, pageable);
        } else {
            UserEntity user = userRepository.findById(SecurityUtils.getUserId());
            if (user.getType().equals(UserType.ACTUAL)) {
                battleHomeEntityPage = battleHomeReposiotry.findAllByCreateTimeAfterAndIsHideIsFalse(date, pageable);
            } else {
                battleHomeEntityPage = battleHomeReposiotry.findAllByCreateTimeAfter(date, pageable);
            }
        }

        List<BattleHomeBaseInfoVO> battleHomeBaseInfoVOList = new ArrayList<>();
        for (BattleHomeEntity battleHome : battleHomeEntityPage.getContent()) {
            List<BattleHomeUserEntity> battleHomeUserEntityList = battleHomeUserReposiotry.findByBattleHome(battleHome);
            List<BattleHomeCaseEntity> battleHomeCaseEntityList = battleHomeCaseReposiotry.findByBattleHome(battleHome);
            Long totalIsOpen = battleHomeCaseEntityList.stream().filter(battleHomeCase -> battleHomeCase.getStatus().equals(BattleHomeStatus.FINISH)).count();
            BattleHomeBaseInfoVO battleHomeBaseInfoVO = new BattleHomeBaseInfoVO();
            battleHomeBaseInfoVO.setId(battleHome.getId());
            battleHomeBaseInfoVO.setMethod(battleHome.getBattleHomeMethod().getCode());
            battleHomeBaseInfoVO.setMethodName(battleHome.getBattleHomeMethod().getValue());
            battleHomeBaseInfoVO.setStatus(battleHome.getBattleHomeStatus().getCode());
            battleHomeBaseInfoVO.setStatusName(battleHome.getBattleHomeStatus().getValue());
            battleHomeBaseInfoVO.setRoundsTotal(battleHomeCaseEntityList.size());
            battleHomeBaseInfoVO.setRounds(totalIsOpen.intValue());
            battleHomeBaseInfoVO.setTotalPlayer(battleHome.getTotalPlayer());
            List<CaseEntity> caseEntityList = battleHomeCaseEntityList.stream().map(battleHomeCaseEntity -> battleHomeCaseEntity.getBox()).collect(Collectors.toList());
            battleHomeBaseInfoVO.setCaseInfoList(caseConverter.toCaseInfoVOS(caseEntityList));

            List<BattleHomeBaseInfoVO.UserInfo> userInfoList = new ArrayList<>();
            for (BattleHomeUserEntity battleHomeUser : battleHomeUserEntityList) {
//                List<BattleHomeCaseUserRecordEntity> battleHomeCaseUserRecordEntityList = battleHomeCaseUserRecordReposiotry.findByUserAndBattleHome(battleHomeUser.getUser(), battleHomeUser.getBattleHome());
                BattleHomeBaseInfoVO.UserInfo userInfo = new BattleHomeBaseInfoVO.UserInfo();
                UserPubicInfoVO userPubicInfoVO = new UserPubicInfoVO();
                userPubicInfoVO.setId(battleHomeUser.getUser().getId());
                userPubicInfoVO.setAvatar(battleHomeUser.getUser().getAvatar());
                userPubicInfoVO.setNickname(battleHomeUser.getUser().getNickname());
                userPubicInfoVO.setIsRobot(battleHomeUser.getUser().getType().equals(UserType.ROBOT) ? true : false);
                userPubicInfoVO.setType(battleHomeUser.getUser().getType().getCode());
//                userInfo.setUser(userService.queryUserPublicInfo(battleHomeUser.getUser().getId()));
                userInfo.setUser(userPubicInfoVO);
                userInfo.setIsWin(battleHomeUser.getIsWin());
                userInfo.setIsOwner(battleHomeUser.getIsOwner());
                userInfoList.add(userInfo);
            }
            battleHomeBaseInfoVO.setUserList(userInfoList);
            battleHomeBaseInfoVOList.add(battleHomeBaseInfoVO);
        }
        return new PageImpl<>(battleHomeBaseInfoVOList, battleHomeEntityPage.getPageable(), battleHomeEntityPage.getTotalElements());
    }

    @Override
    public Page<BattleHomeBaseInfoVO> queryMyBattleHomeBaseInfo(BattleHomeQueryDTO battleHomeQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Long userId = SecurityUtils.getUserId();
        Pageable pageable = PageRequest.of(battleHomeQueryDTO.getPage(), battleHomeQueryDTO.getSize(), sort);
        Page<BattleHomeUserEntity> battleHomeUserEntityPage = battleHomeUserReposiotry.findByUserId(userId, pageable);
        List<BattleHomeBaseInfoVO> battleHomeBaseInfoVOList = new ArrayList<>();
        for (BattleHomeUserEntity battleHomeUser : battleHomeUserEntityPage.getContent()) {
            BattleHomeEntity battleHome = battleHomeUser.getBattleHome();
            List<BattleHomeUserEntity> battleHomeUserEntityList = battleHomeUserReposiotry.findByBattleHome(battleHome);
            List<BattleHomeCaseEntity> battleHomeCaseEntityList = battleHomeCaseReposiotry.findByBattleHome(battleHome);
            Long totalIsOpen = battleHomeCaseEntityList.stream().filter(battleHomeCase -> battleHomeCase.getStatus().equals(BattleHomeCaseStatus.OPEN)).count();
            BattleHomeBaseInfoVO battleHomeBaseInfoVO = new BattleHomeBaseInfoVO();
            battleHomeBaseInfoVO.setId(battleHome.getId());
            battleHomeBaseInfoVO.setMethod(battleHome.getBattleHomeMethod().getCode());
            battleHomeBaseInfoVO.setMethodName(battleHome.getBattleHomeMethod().getValue());
            battleHomeBaseInfoVO.setStatus(battleHome.getBattleHomeStatus().getCode());
            battleHomeBaseInfoVO.setStatusName(battleHome.getBattleHomeStatus().getValue());
            battleHomeBaseInfoVO.setRoundsTotal(battleHomeCaseEntityList.size());
            battleHomeBaseInfoVO.setRounds(totalIsOpen.intValue());
            battleHomeBaseInfoVO.setTotalPlayer(battleHome.getTotalPlayer());
            List<CaseEntity> caseEntityList = battleHomeCaseEntityList.stream().map(battleHomeCaseEntity -> battleHomeCaseEntity.getBox()).collect(Collectors.toList());
            battleHomeBaseInfoVO.setCaseInfoList(caseConverter.toCaseInfoVOS(caseEntityList));

            List<BattleHomeBaseInfoVO.UserInfo> userInfoList = new ArrayList<>();
            for (BattleHomeUserEntity battleHomeUser1 : battleHomeUserEntityList) {
                BattleHomeBaseInfoVO.UserInfo userInfo = new BattleHomeBaseInfoVO.UserInfo();
                userInfo.setUser(userService.queryUserPublicInfo(battleHomeUser1.getUser().getId()));
                userInfo.setIsWin(battleHomeUser1.getIsWin());
                userInfo.setIsOwner(battleHomeUser1.getIsOwner());
                userInfoList.add(userInfo);
            }
            battleHomeBaseInfoVO.setUserList(userInfoList);
            battleHomeBaseInfoVOList.add(battleHomeBaseInfoVO);
        }
        return new PageImpl<>(battleHomeBaseInfoVOList, battleHomeUserEntityPage.getPageable(), battleHomeUserEntityPage.getTotalElements());
    }

    @Override
    public BattleHomeVO queryBattleHomeBaseInfoById(Long battleHomeId) {
        log.info("+++++++++++++++++++++++++++++++++++++:" + battleHomeId);
        BattleHomeEntity battleHome = battleHomeReposiotry.findById(battleHomeId).get();
        if (battleHome == null) {
            log.error("竞技房不存在: {}", battleHomeId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.battle.home.not.exist"));
        }
        List<BattleHomeUserEntity> battleHomeUserEntityList = battleHomeUserReposiotry.findByBattleHome(battleHome);
        List<BattleHomeCaseEntity> battleHomeCaseEntityList = battleHomeCaseReposiotry.findByBattleHome(battleHome);
        Long totalIsOpen = battleHomeCaseEntityList.stream().filter(battleHomeCase -> battleHomeCase.getStatus().equals(BattleHomeCaseStatus.OPEN)).count();
        BattleHomeVO battleHomeVO = new BattleHomeVO();
        battleHomeVO.setId(battleHome.getId());
        battleHomeVO.setMethod(battleHome.getBattleHomeMethod().getCode());
        battleHomeVO.setMethodName(battleHome.getBattleHomeMethod().getValue());
        battleHomeVO.setStatus(battleHome.getBattleHomeStatus().getCode());
        battleHomeVO.setStatusName(battleHome.getBattleHomeStatus().getValue());
        battleHomeVO.setRoundsTotal(battleHomeCaseEntityList.size());
        battleHomeVO.setRounds(totalIsOpen.intValue());
        battleHomeVO.setTotalPlayer(battleHome.getTotalPlayer());
//        List<CaseEntity> caseEntityList = battleHomeCaseEntityList.stream().map(battleHomeCaseEntity -> battleHomeCaseEntity.getBox()).collect(Collectors.toList());
        List<CaseFullInfoVO> caseFullInfoVOList = battleHomeCaseEntityList.stream().map(battleHomeCaseEntity -> caseService.getCaseFullInfo(battleHomeCaseEntity.getBox().getId(), CsgoContants.levelNumber.LEVEL_1)).collect(Collectors.toList());
        battleHomeVO.setCaseInfoList(caseFullInfoVOList);

        List<BattleHomeVO.UserInfo> userInfoList = new ArrayList<>();
        for (BattleHomeUserEntity battleHomeUser : battleHomeUserEntityList) {
            BattleHomeVO.UserInfo userInfo = new BattleHomeVO.UserInfo();
            userInfo.setUser(userService.queryUserPublicInfo(battleHomeUser.getUser().getId()));
            userInfo.setIsWin(battleHomeUser.getIsWin());
            userInfo.setIsOwner(battleHomeUser.getIsOwner());
            List<SkinInfoVO> skinInfoVOList = new ArrayList<>();
            if (battleHome.getBattleHomeStatus().equals(BattleHomeStatus.FINISH)) {
                List<BattleHomeCaseUserSkinEntity> battleHomeCaseUserSkinEntityList = battleHomeCaseUserSkinReposiotry.findByUserAndBattleHome(battleHomeUser.getUser(), battleHome);
                for (BattleHomeCaseUserSkinEntity battleHomeCaseUserSkinEntity : battleHomeCaseUserSkinEntityList) {
                    if (battleHomeCaseUserSkinEntity.getSkin() != null) {
                        skinInfoVOList.add(skinService.querySkinInfoById(battleHomeCaseUserSkinEntity.getSkin().getId()));
                    }
                }
            } else {
                List<BattleHomeCaseUserRecordEntity> battleHomeCaseUserRecordEntityList = battleHomeCaseUserRecordReposiotry.findByUserAndBattleHome(battleHomeUser.getUser(), battleHomeUser.getBattleHome());
                for (BattleHomeCaseUserRecordEntity battleHomeCaseUserRecord : battleHomeCaseUserRecordEntityList) {
                    if (battleHomeCaseUserRecord.getSkin() != null) {
                        skinInfoVOList.add(skinService.querySkinInfoById(battleHomeCaseUserRecord.getSkin().getId()));
                    }
                }
            }
            userInfo.setSkinInfoList(skinInfoVOList);
            userInfoList.add(userInfo);
        }
        battleHomeVO.setUserList(userInfoList);
        return battleHomeVO;
    }

    @Override
    public List<BattleTop10VO> queryBattleTop10VO() {
        String redisBattleHomeKey = redisBattleHomePrefix + ":TOP10:" + "YESTERDAY";
        List<BattleTop10VO> battleTop10VOList = RedisUtils.get(redisBattleHomeKey, ArrayList.class);
        if (battleTop10VOList != null) {
            return battleTop10VOList;
        }
        List<Map<String, Object>> yesterdayTop10 = battleHomeUserReposiotry.queryYesterdayTop10();
        battleTop10VOList = new ArrayList<>();
        for (Map<String, Object> item : yesterdayTop10) {
            BattleTop10VO battleTop10VO = new BattleTop10VO();
            battleTop10VO.setAmount((BigDecimal) item.get("amount"));
            battleTop10VO.setUserInfo(userService.queryUserPublicInfo(Long.valueOf(item.get("user_id").toString())));
            battleTop10VOList.add(battleTop10VO);
        }
        RedisUtils.save(redisBattleHomeKey, battleTop10VOList);
        return battleTop10VOList;
    }

    @Override
    public void robotBattle(Long userId) {
        Random r = new Random();
        UserEntity user = userRepository.findById(userId);
        UserProfileEntity userProfile = userProfileRepository.findByUser(user);
        BattleHomeEntity battleHome = new BattleHomeEntity();
        List<BattleHomeCaseEntity> battleHomeCaseEntityList = new ArrayList<>();
        BattleHomeUserEntity battleHomeUserEntity = new BattleHomeUserEntity();
        // 数据校验TODO
        battleHome.setBattleHomeMethod(BattleHomeMethod.PVP);
        battleHome.setBattleHomeStatus(BattleHomeStatus.WAITING);
        battleHome.setTotalPlayer(r.nextInt(2) + 2);
        BigDecimal amount = new BigDecimal(0); // 总消耗
        Integer boxTotal = r.nextInt(4) + 2; // 1到5个箱子
        for (CaseEntity box : caseRepository.randomBattleCase(boxTotal)) {
            BattleHomeCaseEntity battleHomeCase = new BattleHomeCaseEntity();
            amount = amount.add(box.getPrice());
            battleHomeCase.setBattleHome(battleHome);
            battleHomeCase.setBox(box);
            battleHomeCase.setStatus(BattleHomeCaseStatus.UNOPEN);
            battleHomeCaseEntityList.add(battleHomeCase);
        }
        battleHome.setAmount(amount);
        battleHome.setRounds(0);
        battleHome.setTotalRounds(boxTotal);
        battleHome = battleHomeReposiotry.save(battleHome);
        battleHomeUserEntity.setBattleHome(battleHome);
        battleHomeUserEntity.setUser(user);
        battleHomeUserEntity.setIsOwner(true);
        battleHomeUserEntity.setIsWin(false);
        // 用户余额消耗 记录
        if (userProfile.getCoin().compareTo(amount) == -1) {
            log.info("金币不足");
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.insufficient.coins"));
        }
        // 记录金币流水
        UserCoinRecordEntity userCoinRecordEntity = new UserCoinRecordEntity();
        userCoinRecordEntity.setUser(userProfile.getUser());
        userCoinRecordEntity.setSource(UserCoinChangeSource.BATTLE);
        userCoinRecordEntity.setSourceId(battleHome.getId());
        userCoinRecordEntity.setAmount(amount);
        userCoinRecordEntity.setAfterAmount(userProfile.getCoin().subtract(amount));
        userCoinRecordEntity.setIsPositive(false);
        userCoinRecordRepository.save(userCoinRecordEntity);

        userProfile.setCoin(userProfile.getCoin().subtract(amount));
        battleHomeUserReposiotry.save(battleHomeUserEntity);
        battleHomeCaseReposiotry.saveAll(battleHomeCaseEntityList);
        userProfileRepository.save(userProfile);
        // 通知客户端
        BattleHomeVO battleHomeVO = this.queryBattleHomeBaseInfoById(battleHome.getId());
        MessageVO messageVO = new MessageVO(WebSocketMessageMethod.SEND_ALL, WebSocketMessageType.BATTLE_CREATE, battleHomeVO);
        asyncTaskService.sendWebSocketMessageToAll(JSONObject.toJSONString(messageVO));
        userService.DailyActivity(user.getId(), DailyActivityType.BATTLE);
    }
}
