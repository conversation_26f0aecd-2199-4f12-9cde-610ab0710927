package com.steamgo1.csgoskinapi.service;

import com.steamgo1.csgoskinapi.enums.WxMsgType;
import com.steamgo1.csgoskincommon.entity.BattleHomeEntity;
import com.steamgo1.csgoskincommon.entity.UserEntity;

public interface AsyncTaskService {
    void startBattleHome(BattleHomeEntity battleHome);

    void sendWebSocketMessageToAll(String data);

    void sendWebSocketMessageToOne(Long userId, String data);

    // 发送微信推送
    void sendMessageToWechat(WxMsgType wxMsgType, UserEntity user, String... msg);
}
