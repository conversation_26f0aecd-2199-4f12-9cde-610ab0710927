package com.steamgo1.csgoskinapi.service;

import com.steamgo1.csgoskinapi.dto.LotteryResultDTO;
import com.steamgo1.csgoskinapi.dto.LotteryVerifyDTO;
import com.steamgo1.csgoskinapi.vo.LotteryTestResultVO;
import com.steamgo1.csgoskinapi.vo.LotteryVerifyResult;
import com.steamgo1.csgoskincommon.entity.UserEntity;

public interface LotteryService {
    LotteryTestResultVO testLettery();

    LotteryResultDTO lottery();

    LotteryResultDTO lottery(UserEntity user);

    LotteryVerifyResult lottery(LotteryVerifyDTO lotteryVerifyDTO);

}
