package com.steamgo1.csgoskinapi.service;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;


@Slf4j
@Service
public class RealNameVerifyService {
    @Autowired
    private RestTemplate restTemplate;


    public boolean realNameVerify(String realName, String idCard) {
        String url = "https://jmidcardv1.market.alicloudapi.com/idcard/validate";
        String appcode = "b66f2aaf84614587bb599d12d39f7719";


        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add("idCardNo", idCard);
        formData.add("name", realName);
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "APPCODE " + appcode);
        headers.set("Content-Type", "application/x-www-form-urlencoded");
        // 创建HttpEntity对象，包含请求体和请求头
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(formData, headers);

        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
        String responseBody = responseEntity.getBody();
        log.info("请求响应： {}", responseBody);
        JSONObject res = JSONObject.parseObject(responseBody);
        if (res.getInteger("code").equals(200)) {
            JSONObject data = res.getJSONObject("data");
            if (data.getInteger("result") == 0) {
                return true;
            }
        }
        return false;

    }

}
