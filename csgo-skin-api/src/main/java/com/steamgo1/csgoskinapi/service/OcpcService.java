package com.steamgo1.csgoskinapi.service;

import com.steamgo1.csgoskinapi.vo.OcpcBaiduKeyWordResponse;

import java.io.IOException;

public interface OcpcService {
    void sendRegisterToBaidu(String ip);

    void addClickBaiduData(String ip);

    void addRegisterBaiduData(String ip, Long userId);

    void addChangeBaiduData(String ip, Long userId, Long orderChargeId);

    OcpcBaiduKeyWordResponse queryBaidukeyWord(String keyWordId, String userName, String accessToken) throws IOException;

    void sendRegisterTo360(String ip);

    void sendRegisterToMeta(String ip);

    void addClickMetaData(String ip);

    void addRegisterMetaData(String ip, Long userId);

    void addChangeMetaData(String ip, Long userId, Long orderChargeId);

    // Google OCPC methods
    void sendRegisterToGoogle(String ip);

    void addClickGoogleData(String ip);

    void addRegisterGoogleData(String ip, Long userId);

    void addChangeGoogleData(String ip, Long userId, Long orderChargeId);

}
