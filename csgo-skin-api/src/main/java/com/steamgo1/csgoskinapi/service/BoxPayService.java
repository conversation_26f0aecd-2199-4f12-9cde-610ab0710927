package com.steamgo1.csgoskinapi.service;

import com.steamgo1.csgoskinapi.dto.boxpay.OrderParams;
import com.steamgo1.csgoskinapi.utils.BoxPayUtils;
import lombok.Getter;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.Map;

@Service
public class BoxPayService {
    private String baseUrl = "http://api.koalazf.com";
    private String customerid = "1072";
    @Getter
    private String apiKey = "a297c01a4fced011ba94960970636cdcc30da3d8";

    public static void main(String[] args) {
        OrderParams params = new OrderParams();
        params.setSdorderno("TEST20250403211213123118");
        params.setTotal_fee("1.00");
        params.setPaytype("weixin");
        params.setClientip("*************");
        params.setRemark("硬币充值");

        BoxPayService boxPayService = new BoxPayService();
        boxPayService.pay(params);
    }

    public Map<String, String> pay(OrderParams params) {
        MultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
        map.add("version", "1.0");
        map.add("customerid", customerid);
        map.add("sdorderno", params.getSdorderno());
        map.add("total_fee", params.getTotal_fee());
        map.add("notifyurl", "http://www.kk8skins.com/api/csgo/api/order/boxpay/notify");
        map.add("returnurl", "http://www.kk8skins.com/api/csgo/api/order/boxpay/return");
        map.add("access_type", "API");
        map.add("clientip", params.getClientip());
        map.add("remark", params.getRemark());
        map.add("paytype", params.getPaytype());

        String signStr = BoxPayUtils.sortMapByValues(map.toSingleValueMap());
        String apiUrl = baseUrl + "/apisubmit";
//        System.out.println(signStr);
//        System.out.println(apiKey);
        String sign = BoxPayUtils.encryptToMD5(signStr + "&" + apiKey);
//        System.out.println(sign);
        map.add("name", params.getName());
        map.add("cert_no", params.getCert_no());
        map.add("sign", sign);
        map.add("is_jump", false);
        Map<String, String> resMap = BoxPayUtils.postFormData(map, apiUrl);
//        System.out.println(resMap);
        return resMap;

    }
}
