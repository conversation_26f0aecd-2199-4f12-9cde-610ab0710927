package com.steamgo1.csgoskinapi.service;

import com.steamgo1.csgoskinapi.dto.GmailUserInfo;

/**
 * Gmail OAuth2 认证服务接口
 * 提供Gmail OAuth2认证相关的业务逻辑
 * 
 * <AUTHOR>
 */
public interface GmailOAuthService {
    
    /**
     * 通过OAuth2授权码获取Gmail用户信息
     * 
     * OAuth2流程说明：
     * 1. 前端引导用户到Google授权页面
     * 2. 用户同意授权后，Google重定向到回调地址并携带授权码
     * 3. 后端使用授权码换取访问令牌
     * 4. 使用访问令牌调用Google API获取用户信息
     * 
     * @param authCode OAuth2授权码，由Google在用户授权后返回
     * @return GmailUserInfo Gmail用户信息，包含邮箱、姓名、头像等
     * @throws CsgoSkinException 当OAuth2验证失败时抛出异常
     */
    GmailUserInfo getUserInfoByAuthCode(String authCode);
}