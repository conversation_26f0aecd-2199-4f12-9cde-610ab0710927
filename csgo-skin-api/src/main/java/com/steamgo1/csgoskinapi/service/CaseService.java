package com.steamgo1.csgoskinapi.service;

import com.steamgo1.csgoskinapi.dto.CaseQueryParamDTO;
import com.steamgo1.csgoskinapi.dto.PageQueryDTO;
import com.steamgo1.csgoskinapi.dto.UserCaseLotteryRecordQueryDTO;
import com.steamgo1.csgoskinapi.vo.*;
import com.steamgo1.csgoskincommon.entity.CaseEntity;
import org.springframework.data.domain.Page;

import java.util.List;

public interface CaseService {
    /**
     * 获取箱子分类
     *
     * @return
     */
    List<CaseCategoryVO> getCaseCategory();

    /**
     * 获取箱子详情带分类
     *
     * @return
     */
    List<CaseInfoOfCategoryVO> getCaseInfoOfCategory(CaseQueryParamDTO caseQueryParamDTO);

    /**
     * 箱子ID 获取箱子详情
     *
     * @param caseId
     * @return
     */
    CaseFullInfoVO getCaseFullInfo(Long caseId, int level);

    /**
     * 箱子ID 用户Id 保底
     * @param caseId
     * @return
     */
//    CaseFullInfoVO getCaseFullInfoSafety(Long caseId, Long userId);

    /**
     * 开箱
     *
     * @param caseId
     * @param rounds
     * @param userId
     * @return
     */
    List<CaseLotterResultVO> getCaseLotterResult(Long caseId, Integer rounds, Long userId);

    int getLevel(Long caseId, Long userId, CaseEntity caseEntity);

    /**
     * 获取用户开箱记录
     *
     * @param userCaseLotteryRecordQueryDTO
     * @return
     */
    Page<CaseUserLotteryRecordVO> getUserLotterRecord(UserCaseLotteryRecordQueryDTO userCaseLotteryRecordQueryDTO, Long userId);

    /**
     * 单个箱子被开记录查询
     *
     * @param pageQueryDTO
     * @param caseId
     * @return
     */
    Page<CaseUserLotteryRecordOfCaseVO> queryCaseUserLotteryRecordOfCase(PageQueryDTO pageQueryDTO, Long caseId);

    /**
     * 随机查询推荐箱子
     *
     * @return
     */
    List<CaseFullInfoVO> queryRecommendofRandom();

    /**
     * 获取开箱最新10条中奖记录
     */
    List<UserLotteryResultVO> queryOpenCaseUserLotteryResult();

    /**
     * 机器人开箱子
     *
     * @param robotId
     */
    void rorbotOpenCase(Long robotId);

    /**
     * 指定随机数量机器人开箱子
     */
    void rorbotOpenCaseByRound(Integer robotNum, Long caseId);

    /**
     * 查询自定义箱子饰品颜色
     *
     * @return
     */
    List<SkinRarityColorFilterVO> querySkinRarityColorFilterVO();

    /**
     * 查询自定义饰品箱子
     */
    /**
     * 寻宝箱子
     *
     * @param
     * @return
     */
    ConsumeCaseVO queryConsumeCase();

    /**
     * 查询日常任务箱子
     */
    List<DailyTaskCaseVO> queryDailyTaskCase();
}
