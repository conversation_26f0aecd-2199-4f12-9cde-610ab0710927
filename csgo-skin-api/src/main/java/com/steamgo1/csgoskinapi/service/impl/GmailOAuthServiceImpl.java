package com.steamgo1.csgoskinapi.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinapi.config.oauth.GmailOAuthConfig;
import com.steamgo1.csgoskinapi.dto.GmailUserInfo;
import com.steamgo1.csgoskinapi.service.GmailOAuthService;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

/**
 * Gmail OAuth2 认证服务实现类
 * 实现Gmail OAuth2认证的具体业务逻辑
 * 
 * OAuth2认证流程详解：
 * 1. 前端引导用户访问Google授权页面（Authorization Server）
 * 2. 用户在Google页面完成登录并授权应用访问其基本信息
 * 3. Google重定向到预设的回调地址，并在URL中携带授权码（authorization_code）
 * 4. 后端接收到授权码后，向Google的Token端点发送POST请求换取访问令牌（access_token）
 * 5. 使用获得的访问令牌调用Google的UserInfo API获取用户详细信息
 * 6. 根据获取的用户信息完成登录或注册流程
 * 
 * 安全考虑：
 * - 授权码具有时效性，通常几分钟内过期
 * - 访问令牌也有时效性，过期后需要使用刷新令牌重新获取
 * - 所有网络请求都通过HTTPS加密传输
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class GmailOAuthServiceImpl implements GmailOAuthService {

    /**
     * Gmail OAuth2配置信息
     * 包含客户端ID、密钥、重定向URI等关键配置参数
     */
    @Autowired
    private GmailOAuthConfig gmailOAuthConfig;

    /**
     * Spring的HTTP客户端工具
     * 用于发送HTTP请求到Google的OAuth2端点
     */
    @Autowired
    private RestTemplate restTemplate;

    /**
     * 通过OAuth2授权码获取Gmail用户信息
     * 
     * 这是整个OAuth2流程的核心方法，包含两个关键步骤：
     * 1. 授权码换取访问令牌（Code to Token）
     * 2. 访问令牌获取用户信息（Token to UserInfo）
     * 
     * @param authCode OAuth2授权码，由Google在用户授权后通过回调URL返回
     * @return GmailUserInfo 包含用户邮箱、姓名、头像等基本信息的DTO对象
     * @throws CsgoSkinException 当OAuth2验证过程中发生任何错误时抛出业务异常
     */
    @Override
    public GmailUserInfo getUserInfoByAuthCode(String authCode) {
        try {
            // 记录开始验证的日志，出于安全考虑只显示授权码的前10位
            log.info("开始Gmail OAuth2验证，授权码: {}", authCode.substring(0, Math.min(authCode.length(), 10)) + "...");
            
            // 第一步：使用授权码换取访问令牌
            // 这一步验证授权码的有效性，并获取后续API调用所需的访问令牌
            String accessToken = getAccessToken(authCode);
            log.info("成功获取访问令牌");
            
            // 第二步：使用访问令牌获取用户信息
            // 调用Google的UserInfo API获取用户的基本资料信息
            GmailUserInfo userInfo = getUserInfo(accessToken);
            log.info("成功获取用户信息，邮箱: {}", userInfo.getEmail());
            
            return userInfo;
        } catch (Exception e) {
            // 统一异常处理，记录详细错误信息并抛出业务异常
            log.error("Gmail OAuth验证失败: {}", e.getMessage(), e);
            throw new CsgoSkinException("Gmail授权验证失败");
        }
    }

    /**
     * 使用授权码换取访问令牌
     * 
     * 根据OAuth2标准规范（RFC 6749），向授权服务器的Token端点发送POST请求
     * 请求必须包含以下参数：
     * - client_id: 应用在Google Cloud Console中注册时获得的客户端标识符
     * - client_secret: 应用的客户端密钥，用于验证应用身份
     * - code: 用户授权后获得的授权码，具有时效性（通常10分钟）
     * - grant_type: 固定值"authorization_code"，表示使用授权码模式
     * - redirect_uri: 重定向URI，必须与注册时配置的完全一致
     * 
     * Google Token端点响应格式：
     * {
     *   "access_token": "ya29.a0AfH6SMC...",
     *   "expires_in": 3599,
     *   "refresh_token": "1//04...",
     *   "scope": "openid email profile",
     *   "token_type": "Bearer"
     * }
     * 
     * @param authCode OAuth2授权码
     * @return 访问令牌字符串，用于后续API调用
     * @throws CsgoSkinException 当令牌交换失败时抛出异常
     */
    private String getAccessToken(String authCode) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("client_id", gmailOAuthConfig.getClientId());
        params.add("client_secret", gmailOAuthConfig.getClientSecret());
        params.add("code", authCode);
        params.add("grant_type", "authorization_code");
        params.add("redirect_uri", gmailOAuthConfig.getRedirectUri());

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

        log.debug("向Google token端点发送请求: {}", gmailOAuthConfig.getTokenUri());
        
        try {
            // 添加重试机制
            ResponseEntity<String> response = null;
            int maxRetries = 3;
            for (int i = 0; i < maxRetries; i++) {
                try {
                    response = restTemplate.postForEntity(gmailOAuthConfig.getTokenUri(), request, String.class);
                    break; // 成功则跳出循环
                } catch (Exception e) {
                    log.warn("第{}次请求Google token端点失败: {}", i + 1, e.getMessage());
                    if (i == maxRetries - 1) {
                        throw e; // 最后一次重试失败则抛出异常
                    }
                    // 等待1秒后重试
                    Thread.sleep(1000);
                }
            }

            if (response.getStatusCode() == HttpStatus.OK) {
                JSONObject jsonObject = JSON.parseObject(response.getBody());
                String accessToken = jsonObject.getString("access_token");
                
                if (accessToken == null || accessToken.isEmpty()) {
                    log.error("响应中未包含访问令牌，响应内容: {}", response.getBody());
                    throw new CsgoSkinException("获取访问令牌失败：响应格式错误");
                }
                
                return accessToken;
            } else {
                log.error("获取访问令牌失败，状态码: {}, 响应: {}", response.getStatusCode(), response.getBody());
                throw new CsgoSkinException("获取访问令牌失败");
            }
        } catch (Exception e) {
            log.error("请求Google token端点异常: {}", e.getMessage(), e);
            throw new CsgoSkinException("网络连接失败，请检查网络或稍后重试");
        }
    }

    /**
     * 使用访问令牌获取用户信息
     * 
     * 调用Google的UserInfo API获取用户基本信息
     * API端点：https://www.googleapis.com/oauth2/v2/userinfo
     * 
     * 请求方式：GET
     * 认证方式：Bearer Token（在Authorization头中携带访问令牌）
     * 
     * Google UserInfo API响应格式：
     * {
     *   "id": "123456789",
     *   "email": "<EMAIL>",
     *   "verified_email": true,
     *   "name": "User Name",
     *   "given_name": "User",
     *   "family_name": "Name",
     *   "picture": "https://lh3.googleusercontent.com/...",
     *   "locale": "en"
     * }
     * 
     * @param accessToken 有效的访问令牌
     * @return Gmail用户信息DTO对象
     * @throws CsgoSkinException 当API调用失败时抛出异常
     */
    private GmailUserInfo getUserInfo(String accessToken) {
        // 设置Bearer认证头
        // Bearer Token是OAuth2标准的访问令牌传递方式
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);

        // 创建HTTP请求实体，只包含请求头（GET请求无请求体）
        HttpEntity<String> request = new HttpEntity<>(headers);

        log.debug("向Google用户信息端点发送请求: {}", gmailOAuthConfig.getUserInfoUri());
        
        // 向Google的UserInfo端点发送GET请求
        ResponseEntity<String> response = restTemplate.exchange(
                gmailOAuthConfig.getUserInfoUri(), HttpMethod.GET, request, String.class);

        // 检查HTTP响应状态码
        if (response.getStatusCode() == HttpStatus.OK) {
            // 解析JSON响应数据
            JSONObject userInfo = JSON.parseObject(response.getBody());
            
            // 将Google API返回的JSON数据映射到我们的DTO对象
            GmailUserInfo gmailUserInfo = new GmailUserInfo();
            gmailUserInfo.setEmail(userInfo.getString("email"));                    // 用户邮箱地址
            gmailUserInfo.setName(userInfo.getString("name"));                      // 用户显示名称
            gmailUserInfo.setPicture(userInfo.getString("picture"));                // 用户头像URL
            gmailUserInfo.setVerifiedEmail(userInfo.getBoolean("verified_email"));  // 邮箱验证状态
            
            // 记录成功获取的用户信息（不记录敏感信息）
            log.debug("解析用户信息成功: email={}, name={}, verified={}", 
                    gmailUserInfo.getEmail(), gmailUserInfo.getName(), gmailUserInfo.getVerifiedEmail());
            
            return gmailUserInfo;
        } else {
            // 记录API调用失败的详细信息
            log.error("获取用户信息失败，状态码: {}, 响应: {}", response.getStatusCode(), response.getBody());
            throw new CsgoSkinException("获取用户信息失败");
        }
    }
}