package com.steamgo1.csgoskinapi.service;

import com.steamgo1.csgoskinapi.dto.*;
import com.steamgo1.csgoskinapi.vo.*;
import com.steamgo1.csgoskincommon.entity.UserEntity;
import com.steamgo1.csgoskincommon.entity.enums.DailyActivityType;
import com.steamgo1.csgoskincommon.vo.TokenVO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;

public interface UserService {
    /**
     * 创建机器人
     */
    void createRobot();


    /**
     * 手机号 验证码登录
     *
     * @param phone
     * @param code
     * @return
     */
    TokenVO login(String phone, String code, String inviteCode, HttpServletRequest request);

    /**
     * 邮箱 验证码登录
     *
     * @param email
     * @param code
     * @return
     */
    TokenVO loginByEmail(String email, String code, String inviteCode, HttpServletRequest request);

    /**
     * steamId 登录
     *
     * @param steamId steamId
     * @return
     */
    TokenVO loginBySteamId(String steamId, HttpServletRequest request);

    /**
     * Gmail 授权登录
     *
     * @param authCode 授权码
     * @param inviteCode 邀请码
     * @param request HTTP请求
     * @return TokenVO
     */
    TokenVO loginByGmail(String authCode, String inviteCode, HttpServletRequest request);

    /**
     * 获取用户信息
     *
     * @param
     * @return
     */
    UserInfoVO queryUserInfo();

    /**
     * 获取用户当前的算法数据
     *
     * @return
     */
    UserAlgorithmDataVO queryUserAlgorihmData();

    /**
     * 获取用户全量算法数据
     *
     * @return
     */
    List<UserAlgorithmDataFullVO> queryUserAlgorihmDataFull(Long userId);

    /**
     * 重置用户算法数据
     *
     * @return
     */
    UserAlgorithmDataVO resetUserAlgorihmData();

    /**
     * 用户查询背包
     *
     * @param packageQueryParam
     * @return
     */
    Page<UserPackageVO> queryUserPackageVO(UserPackageQueryParamDTO packageQueryParam);

    /**
     * 其他用户查询背包
     *
     * @param packageQueryParam
     * @return
     */
    Page<UserPackageVO> queryOtherUserPackageVO(OtherUserPackageQueryParamDTO packageQueryParam);

    /**
     * 用户更新个人种子
     *
     * @param clientSeed
     * @return
     */
    UserAlgorithmDataVO updateClientSeed(String clientSeed);

    /**
     * 获取用户基本数据
     *
     * @param id
     * @return
     */
    UserPubicInfoVO queryUserPublicInfo(Long id);

    /**
     * 获取当前登录用户
     *
     * @return
     */
    UserEntity getUser();

    /**
     * 更新用户名
     *
     * @param nickName
     * @return
     */
    UserInfoVO updateUserName(String nickName);

    /**
     * 更新用户steam交易链接
     *
     * @param tradeOfferAccessUrl
     * @return
     */
    UserInfoVO updateUserTradeOfferAccessUrl(String tradeOfferAccessUrl);

    /**
     * 更新用户头像
     *
     * @param avatar
     * @return
     */
    UserInfoVO updateUserAvatar(String avatar);


    /**
     * 用户金币流水
     *
     * @param userCoinRecordQueryDTO
     * @return
     */
    Page<UserCoinRecordVO> queryUserCoinRecord(UserCoinRecordQueryDTO userCoinRecordQueryDTO);

    /**
     * 用户钻石流水
     *
     * @param userCoinRecordQueryDTO
     * @return
     */
    Page<UserDiamondRecordVO> queryUserDiamondRecord(UserDiamondRecordQueryDTO userCoinRecordQueryDTO);

    /**
     * 用户饰品取回
     *
     * @param packageIds
     */
    void pickUpSkin(List<Long> packageIds);

    /**
     * 用户饰品取回查询
     *
     * @param userPackageSkinPickUpRecordQueryDTO
     * @return
     */
    Page<UserPackageSkinPickUpRecordVO> queryUserPickagePickUpRecord(UserPackageSkinPickUpRecordQueryDTO userPackageSkinPickUpRecordQueryDTO);

    /**
     * 其他用户饰品取回查询
     *
     * @param userPackageSkinPickUpRecordQueryDTO
     * @return
     */
    Page<UserPackageSkinPickUpRecordVO> queryOtherUserPickagePickUpRecord(OtherUserPackageSkinPickUpRecordQueryDTO userPackageSkinPickUpRecordQueryDTO);

    /**
     * 用户出售饰品
     *
     * @param packageSkinIds
     * @return
     */
    WalletVO sellSkin(List<Long> packageSkinIds);

    /**
     * 用戶钻石兑换金币
     *
     * @param decimal
     * @return
     */
    WalletVO diamondToCoin(BigDecimal decimal);

    /**
     * 用户钻石购买饰品
     *
     * @param skinId
     * @return
     */
    WalletVO buySkin(Long skinId);

    /**
     * 用戶購買飾品記錄查詢
     *
     * @param userBuySkinRecordQueryDTO
     * @return
     */
    Page<UserBuySkinRecordVO> buySkinRecord(UserBuySkinRecordQueryDTO userBuySkinRecordQueryDTO);

    /**
     * 查询用户钱包
     *
     * @return
     */
    WalletVO queryUserWallet();

    /**
     * 查询兑换记录
     *
     * @return
     */
    Page<UserDiamondToCoinRecordVO> queryUserDiamondToCoinRecord(UserDiamondToCoinRecordQueryDTO userDiamondToCoinRecordQueryDTO);


    /**
     * 查询其他兑换记录
     *
     * @return
     */
    Page<UserDiamondToCoinRecordVO> queryOtherUserDiamondToCoinRecord(OtherUserDiamondToCoinRecordQueryDTO userDiamondToCoinRecordQueryDTO);

    /**
     * 获取用户推广信息
     *
     * @return
     */
    UserInviteVO getUserInviteInfo();

    /**
     * 删除用户缓存
     *
     * @param userId
     */
    void cleanUserCache(Long userId);

    /**
     * 用户邀请奖励查询
     *
     * @param userInviteEncouragRecordQueryDTO
     * @return
     */
    Page<UserInviteEncourageRecordVO> queryUserInviteEncourageRecord(UserInviteEncouragRecordQueryDTO userInviteEncouragRecordQueryDTO);


    /**
     * 查询出售记录
     *
     * @return
     */
    Page<UserPackageSkinSellRecordVO> queryUserPackageSkinSellRecordVO(UserPackageSkinSellRecordQueryDTO userPackageSkinSellRecordQueryDTO);

    /**
     * 查询其他用户出售记录
     *
     * @return
     */
    Page<UserPackageSkinSellRecordVO> queryOtherUserPackageSkinSellRecordVO(OtherUserPackageSkinSellRecordQueryDTO userPackageSkinSellRecordQueryDTO);

    /**
     * 同步缓存
     *
     * @param userId
     */
    void SyncCache(Long userId);

    /**
     * 用户日活
     *
     * @param userId
     */
    void DailyActivity(Long userId, DailyActivityType type);

    /**
     * 获取用户免费宝箱额外开启次数
     */
    Integer queryFreeCase(Long userId);

    /**
     * 查询用户库存价值
     *
     * @param userId
     * @return
     */
    UserPackageValueVO queryUserPackageValue(Long userId);

    /**
     * 获取背包饰品来源查询条件
     */
    UserPackageSkinSourceVO pacekageSkinSource();

    /**
     * 用户饰品锁定or解锁
     *
     * @param packageIds
     */
    void lockSkin(List<Long> packageIds, Boolean isLock);

    Page<UserAlgorithmDataFullVO> queryHistoryUserAlorithmData(PageQueryDTO pageQueryDTO);

    // 工具方法同步用户背包饰品价格

    void syncUserpackSkin();

    /**
     * 用户实名认证
     */
    void idCardValidate(UserVerifyDTO verifyDTO);

}
