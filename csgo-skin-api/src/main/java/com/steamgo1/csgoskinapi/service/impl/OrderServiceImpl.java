package com.steamgo1.csgoskinapi.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.steamgo1.csgoskinapi.dto.boxpay.OrderParams;
import com.steamgo1.csgoskinapi.dto.epay.CreateOrderParmas;
import com.steamgo1.csgoskinapi.dto.xinfupaybank.PayOrderBxRequest;
import com.steamgo1.csgoskinapi.dto.xinfupaybank.PayOrderBxResponse;
import com.steamgo1.csgoskinapi.dto.xinfupaybank.PayOrderRequest;
import com.steamgo1.csgoskinapi.service.*;
import com.steamgo1.csgoskinapi.utils.IPUtils;
import com.steamgo1.csgoskinapi.utils.SecurityUtils;
import com.steamgo1.csgoskinapi.vo.OrderChargeCreateVO;
import com.steamgo1.csgoskinapi.vo.PayNotifyVO;
import com.steamgo1.csgoskinapi.vo.WalletVO;
import com.steamgo1.csgoskincommon.bo.OcpcTaskBO;
import com.steamgo1.csgoskincommon.dao.*;
import com.steamgo1.csgoskincommon.entity.*;
import com.steamgo1.csgoskincommon.entity.enums.DailyActivityType;
import com.steamgo1.csgoskincommon.entity.enums.OrderStatus;
import com.steamgo1.csgoskincommon.entity.enums.PayType;
import com.steamgo1.csgoskincommon.entity.enums.UserCoinChangeSource;
import com.steamgo1.csgoskincommon.enums.OcpcRabbitmqType;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageMethod;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageType;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import com.steamgo1.csgoskincommon.utils.Utils;
import com.steamgo1.csgoskincommon.vo.websocket.MessageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.annotation.Transient;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class OrderServiceImpl implements OrderService {
    @Autowired
    HttpServletRequest httpServletRequest;
    @Autowired
    WxPayService wxPayService;
    @Autowired
    UserCoinRecordRepository userCoinRecordRepository;
    @Autowired
    UserInviteRepository userInviteRepository;
    @Value("${spring.redis.prefix.user}")
    private String redisUserPrefix;
    @Value("${rabbitmq.exchange.csgo}")
    private String exchangeName;
    @Value("${rabbitmq.queue.charge-order}")
    private String chargeOrderQueue;
    @Value("${site.order-outtime}")
    private Long orderOuttime;
    @Autowired
    private ChargeGoodsRepository chargeGoodsRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private OrderChargeRepository orderChargeRepository;
    @Autowired
    private UserProfileRepository userProfileRepository;
    @Autowired
    private SiteService siteService;

    @Autowired
    private AsyncTaskService asyncTaskService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private UserService userService;

    @Autowired
    private UserInviteEncourageRecordRepository userInviteEncourageRecordRepository;

    @Autowired
    private BoxPayService boxPayService;

    @Autowired
    private EpayService epayService;


    @Autowired
    private XinfuPayBankService xinfuPayBankService;

    @Autowired
    private PayChannelRepository payChannelRepository;

    @Override
    @Transient
    public OrderChargeCreateVO createOrderCharge(Long chargeGoodsId, PayType payType, HttpServletRequest request) {
        Long userId = SecurityUtils.getUserId();
        ChargeGoodsEntity chargeGoods = chargeGoodsRepository.findById(chargeGoodsId).get();
        if (chargeGoods == null) {
            log.error("商品不存在");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.product.not.exist"));
        }
        String ip = IPUtils.getIpAddr(request);
        UserEntity user = userRepository.findById(userId);
        List<OrderChargeEntity> orderChargeEntityList = orderChargeRepository.findByUserIdAndChargeGoodsIdAndPayTypeAndOrderStatus(userId, chargeGoodsId, payType, OrderStatus.UNPAY);
        OrderChargeEntity orderChargeEntity = null;
        if (orderChargeEntityList.size() != 0) {
            orderChargeEntity = orderChargeEntityList.get(0);
            log.info("订单已存在, userId: {}, chargeGoodsId: {}, payType: {}, orderNo: {}", userId, chargeGoodsId, payType.getValue(), orderChargeEntity.getOrderNo());
            OrderChargeCreateVO orderChargeCreateVO = new OrderChargeCreateVO();
            orderChargeCreateVO.setAmount(chargeGoods.getPrice());
            orderChargeCreateVO.setOrderNo(orderChargeEntity.getOrderNo());
            orderChargeCreateVO.setPayUrl(orderChargeEntity.getPayUrl());
            return orderChargeCreateVO;
        }
        String orderNo = Utils.generateOrderNo(userId, "CSGO");
        log.info("创建订单, 订单号：{}, 支付方式： {}", orderNo, payType);
        // 根据支付类型查询汇率
        PayChannelEntity payChannel = payChannelRepository.findByPayTypeAndStatus(payType, 1);
        BigDecimal exchangeRate = BigDecimal.ONE;
        if (payChannel != null) {
            exchangeRate = payChannel.getExchangeRate();
        }

        // 计算最终支付价格
        BigDecimal finalAmount = chargeGoods.getPrice().multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP);

        orderChargeEntity = new OrderChargeEntity();
        orderChargeEntity.setOrderNo(orderNo);
        orderChargeEntity.setChargeGoods(chargeGoods);
        orderChargeEntity.setAmount(chargeGoods.getPrice());
        orderChargeEntity.setActualAmount(finalAmount);
        orderChargeEntity.setExchangeRate(exchangeRate);
        orderChargeEntity.setPayType(payType);
        orderChargeEntity.setOrderStatus(OrderStatus.UNPAY);
        orderChargeEntity.setUser(user);
        try {
            OrderChargeCreateVO orderChargeCreateVO = new OrderChargeCreateVO();
            CreateOrderParmas createOrderParmas = new CreateOrderParmas();
            createOrderParmas.setType("web");
            createOrderParmas.setOutTradeNo(createOrderParmas.getOutTradeNo());
            createOrderParmas.setName(chargeGoods.getCoin() + " 硬币充值");
            createOrderParmas.setMoney(chargeGoods.getPrice() + "");
            createOrderParmas.setClientip(ip);
            createOrderParmas.setOutTradeNo(orderNo);
            UserProfileEntity userProfile = userProfileRepository.findByUserId(userId);
            switch (payType) {
                case BOX_ALIPAY:
                    // 国内支付才需要实名认证
                    if (!user.getIsReal()) {
                        throw new CsgoSkinException(I18nUtils.getMessage("exception.real.name.auth.required"));
                    }
                    OrderParams params = new OrderParams();
                    params.setSdorderno(orderNo);
                    params.setTotal_fee(String.valueOf(finalAmount));
                    params.setPaytype("alipay");
                    params.setClientip(ip);
                    params.setRemark(chargeGoods.getCoin() + " 硬币充值");
                    params.setName(userProfile.getRealName());
                    params.setCert_no(userProfile.getIdCard());
                    orderChargeCreateVO.setAmount(finalAmount);
                    orderChargeCreateVO.setOrderNo(orderNo);
                    Map<String, String> res = boxPayService.pay(params);
                    orderChargeCreateVO.setAmount(finalAmount);
                    orderChargeCreateVO.setOrderNo(orderNo);
                    orderChargeCreateVO.setPayUrl(res.get("url"));
                    break;
                case XINFU_BX_PAY:
                    PayOrderRequest payOrderRequest = new PayOrderBxRequest();
                    payOrderRequest.setMerchantOrderNo(orderNo);
                    payOrderRequest.setAmount(finalAmount.doubleValue());
                    payOrderRequest.setName(userProfile.getRealName());
                    payOrderRequest.setEmail(userProfile.getUser().getEmail());
                    payOrderRequest.setPhone(userProfile.getUser().getPhone());
                    payOrderRequest.setRemark(chargeGoods.getCoin() + " 硬币充值");
                    orderChargeCreateVO.setAmount(finalAmount);
                    orderChargeCreateVO.setOrderNo(orderNo);
                    PayOrderBxResponse payOrder = xinfuPayBankService.createPayOrder(payOrderRequest, PayOrderBxResponse.class);

                    orderChargeCreateVO.setAmount(finalAmount);
                    orderChargeCreateVO.setOrderNo(orderNo);
                    orderChargeCreateVO.setPayUrl(payOrder.getPaymentUrl());
                    break;
                case EASY_ALIPAY:
                    createOrderParmas.setType("alipay");
                    createOrderParmas.setMoney(finalAmount.toString());
                    Map<String, String> res1 = epayService.payV1(createOrderParmas);
                    orderChargeCreateVO.setAmount(finalAmount);
                    orderChargeCreateVO.setOrderNo(res1.get("order_no"));
                    orderChargeCreateVO.setPayUrl(res1.get("url"));
                    break;
                default:
                    // todo 国际化
                    throw new CsgoSkinException(I18nUtils.getMessage("exception.payment.method.invalid"));
            }
            // 保存payUrl
            orderChargeEntity.setPayUrl(orderChargeCreateVO.getPayUrl());
            orderChargeRepository.save(orderChargeEntity);
            log.info("exchageName: {}, routingkey: {}, data: {}, expiratime: {}", exchangeName, "order.charge", orderChargeEntity.getOrderNo(), orderOuttime);
            rabbitTemplate.convertAndSend(exchangeName, "order.charge", orderChargeEntity.getOrderNo(), a -> {
                a.getMessageProperties().setExpiration(String.valueOf(orderOuttime));
                return a;
            });
            // 上传OCPC
            OrderChargeEntity finalOrderChargeEntity = orderChargeEntity;
            OcpcTaskBO ocpcTaskBO = new OcpcTaskBO() {{
                setIp(ip);
                setOrderChargeId(finalOrderChargeEntity.getId());
                setUserId(user.getId());
                setType(OcpcRabbitmqType.CHARGE);
            }};
            rabbitTemplate.convertAndSend(exchangeName, "ocpc.baidu.charge", JSONObject.toJSON(ocpcTaskBO));
            userService.DailyActivity(user.getId(), DailyActivityType.CHARGE);
            return orderChargeCreateVO;
        } catch (Exception e) {
            log.error("支付失败！订单号：{},原因:{}", orderNo, e.getMessage());
            throw new CsgoSkinException(I18nUtils.getMessage("exception.order.creation.failed"));
        }
    }

    @Override
    @Transactional
    public void updateOrderStatus(String orderNo) {
        OrderChargeEntity orderCharge = orderChargeRepository.findByOrderNo(orderNo);
        // 回调待完成
        if (orderCharge.getOrderStatus().equals(OrderStatus.UNPAY)) {
            orderCharge.setOrderStatus(OrderStatus.PADYED);
            UserProfileEntity userProfile = userProfileRepository.findByUser(orderCharge.getUser());
            UserEntity user = userProfile.getUser();
            userProfile.setExperience(userProfile.getExperience() + 10);
            userProfile.setCoin(userProfile.getCoin().add(orderCharge.getChargeGoods().getCoin()));
            userProfileRepository.save(userProfile);
            orderChargeRepository.save(orderCharge);
            // 记录金币流水
            UserCoinRecordEntity userCoinRecordEntity = new UserCoinRecordEntity();
            userCoinRecordEntity.setUser(userProfile.getUser());
            userCoinRecordEntity.setSource(UserCoinChangeSource.CHARGE);
            userCoinRecordEntity.setAmount(orderCharge.getChargeGoods().getCoin());
            userCoinRecordEntity.setAfterAmount(userProfile.getCoin());
            userCoinRecordEntity.setIsPositive(true);
            userCoinRecordEntity.setSourceId(orderCharge.getId());
            userCoinRecordRepository.save(userCoinRecordEntity);
            // 如果是首充
            if (user.getIsFirstCharge()) {
                // 全局首冲赠送比例
                if (siteService.getSysUserBenefits().getFirstChargePercentage() != null && siteService.getSysUserBenefits().getFirstChargePercentage().compareTo(BigDecimal.ZERO) == 1) {
                    BigDecimal freeCoin = siteService.getSysUserBenefits().getFirstChargePercentage().multiply(orderCharge.getChargeGoods().getCoin());
                    userProfile.setCoin(userProfile.getCoin().add(freeCoin));
                    userProfileRepository.save(userProfile);
                    // 记录金币流水
                    UserCoinRecordEntity firstChargeUserCoinRecordEntity = new UserCoinRecordEntity();
                    firstChargeUserCoinRecordEntity.setUser(userProfile.getUser());
                    firstChargeUserCoinRecordEntity.setSource(UserCoinChangeSource.FIRST_CHARGE);
                    firstChargeUserCoinRecordEntity.setSourceId(orderCharge.getId());
                    firstChargeUserCoinRecordEntity.setAmount(freeCoin);
                    firstChargeUserCoinRecordEntity.setAfterAmount(userProfile.getCoin());
                    firstChargeUserCoinRecordEntity.setIsPositive(true);
                    userCoinRecordRepository.save(firstChargeUserCoinRecordEntity);
                }
                // 金币首冲赠送金币
                if (orderCharge.getChargeGoods().getFirstChargeFreeCoin() != null && orderCharge.getChargeGoods().getFirstChargeFreeCoin().compareTo(BigDecimal.ZERO) == 1) {
                    BigDecimal freeCoin = orderCharge.getChargeGoods().getFirstChargeFreeCoin();
                    userProfile.setCoin(userProfile.getCoin().add(freeCoin));
                    userProfileRepository.save(userProfile);
                    // 记录金币流水
                    UserCoinRecordEntity firstChargeUserCoinRecordEntity = new UserCoinRecordEntity();
                    firstChargeUserCoinRecordEntity.setUser(userProfile.getUser());
                    firstChargeUserCoinRecordEntity.setSource(UserCoinChangeSource.FIRST_CHARGE);
                    firstChargeUserCoinRecordEntity.setSourceId(orderCharge.getId());
                    firstChargeUserCoinRecordEntity.setAmount(freeCoin);
                    firstChargeUserCoinRecordEntity.setAfterAmount(userProfile.getCoin());
                    firstChargeUserCoinRecordEntity.setIsPositive(true);
                    userCoinRecordRepository.save(firstChargeUserCoinRecordEntity);
                }
                // 金币首冲赠送比例
                if (orderCharge.getChargeGoods().getFreeCoinPercentage() != null && orderCharge.getChargeGoods().getFreeCoinPercentage().compareTo(BigDecimal.ZERO) == 1) {
                    BigDecimal freeCoin = orderCharge.getChargeGoods().getFreeCoinPercentage().multiply(orderCharge.getChargeGoods().getCoin());
                    userProfile.setCoin(userProfile.getCoin().add(freeCoin));
                    userProfileRepository.save(userProfile);
                    // 记录金币流水
                    UserCoinRecordEntity firstChargeUserCoinRecordEntity = new UserCoinRecordEntity();
                    firstChargeUserCoinRecordEntity.setUser(userProfile.getUser());
                    firstChargeUserCoinRecordEntity.setSource(UserCoinChangeSource.FIRST_CHARGE);
                    firstChargeUserCoinRecordEntity.setSourceId(orderCharge.getId());
                    firstChargeUserCoinRecordEntity.setAmount(freeCoin);
                    firstChargeUserCoinRecordEntity.setAfterAmount(userProfile.getCoin());
                    firstChargeUserCoinRecordEntity.setIsPositive(true);
                    userCoinRecordRepository.save(firstChargeUserCoinRecordEntity);
                }
            }
            // 推广
            if (user.getInviterId() != null) {
//                BigDecimal inviteEncorageCoin = BigDecimal.ZERO;
//                if(user.getIsFirstCharge()){
//                    inviteEncorageCoin = siteService.getSysUserBenefits().getInviteFirstChargeFreeCoin();
//                }else {
//                    inviteEncorageCoin = siteService.getSysUserBenefits().getInviteEncoragePercentage().multiply(orderCharge.getChargeGoods().getCoin()).setScale(2, BigDecimal.ROUND_DOWN);
//                }
                BigDecimal inviteEncorageCoin = siteService.getSysUserBenefits().getInviteEncoragePercentage().multiply(orderCharge.getChargeGoods().getCoin()).setScale(2, BigDecimal.ROUND_DOWN);
                log.info("用户{} 推广 {}充值成功奖励金币 {}", user.getId(), user.getInviterId(), inviteEncorageCoin);
                UserInviteEntity inviter = userInviteRepository.findByUserId(user.getInviterId());
                inviter.setTotalCharge(inviter.getTotalCharge().add(orderCharge.getChargeGoods().getCoin()));
                inviter.setTotalEncourage(inviteEncorageCoin.add(inviter.getTotalEncourage()));
                if (user.getIsFirstCharge()) {
                    inviter.setFreeCase(inviter.getFreeCase() + 1);
                }
                userInviteRepository.save(inviter);
                // 推广记录
                UserInviteEncourageRecordEntity userInviteEncourageRecordEntity = new UserInviteEncourageRecordEntity();
                userInviteEncourageRecordEntity.setUser(inviter.getUser());
                userInviteEncourageRecordEntity.setEncourageCoin(inviteEncorageCoin);
                userInviteEncourageRecordEntity.setChargeCoin(orderCharge.getChargeGoods().getCoin());
                userInviteEncourageRecordEntity.setIsFirstCharge(user.getIsFirstCharge());
                userInviteEncourageRecordEntity.setOrderCharge(orderCharge);
                userInviteEncourageRecordRepository.save(userInviteEncourageRecordEntity);
                // 推广到账
                UserEntity userInviter = inviter.getUser();
                UserProfileEntity userProfileInviter = userProfileRepository.findByUser(userInviter);
                userProfileInviter.setCoin(userProfileInviter.getCoin().add(inviteEncorageCoin));
                userProfileRepository.save(userProfileInviter);
                // 记录金币流水
                UserCoinRecordEntity userCoinRecord = new UserCoinRecordEntity();
                userCoinRecord.setUser(userInviter);
                userCoinRecord.setSource(UserCoinChangeSource.INVITE);
                userCoinRecord.setSourceId(userInviteEncourageRecordEntity.getId());
                userCoinRecord.setAmount(inviteEncorageCoin);
                userCoinRecord.setAfterAmount(userProfile.getCoin());
                userCoinRecord.setIsPositive(true);
                userCoinRecordRepository.save(userCoinRecord);

            }
            user.setIsFirstCharge(false);
            userRepository.save(user);
            // 通知用户
            // 用户本月充值
            BigDecimal userMonthCharge = userCoinRecordRepository.userMonthCharge(user.getId());
            WalletVO walletVO = new WalletVO() {{
                setCoin(userProfile.getCoin());
                setDiamond(userProfile.getDiamond());
                setMessage(userMonthCharge.compareTo(BigDecimal.valueOf(32.5)) == 0 ? "本月再充值32.5即可开启日常任务" : null);
            }};
            MessageVO messageResultVO = new MessageVO(WebSocketMessageMethod.SEND_ONE, WebSocketMessageType.PAY_NOTIFY, new PayNotifyVO() {{
                setOrderNo(orderCharge.getOrderNo());
                setSuccess(true);
                setWalletVO(walletVO);
            }});
//            asyncTaskService.sendWebSocketMessageToAll(JSONObject.toJSONString(messageResultVO));
            asyncTaskService.sendWebSocketMessageToOne(user.getId(), JSONObject.toJSONString(messageResultVO));
        }
    }

    @Override
    public void updateOrderStatusAdmin(String orderNo) {
        OrderChargeEntity orderCharge = orderChargeRepository.findByOrderNo(orderNo);
        // 回调待完成
        if (!orderCharge.getOrderStatus().equals(OrderStatus.PADYED)) {
            orderCharge.setOrderStatus(OrderStatus.PADYED);
            UserProfileEntity userProfile = userProfileRepository.findByUser(orderCharge.getUser());
            UserEntity user = userProfile.getUser();
            userProfile.setExperience(userProfile.getExperience() + 10);
            userProfile.setCoin(userProfile.getCoin().add(orderCharge.getChargeGoods().getCoin()));
            userProfileRepository.save(userProfile);
            orderChargeRepository.save(orderCharge);
            // 记录金币流水
            UserCoinRecordEntity userCoinRecordEntity = new UserCoinRecordEntity();
            userCoinRecordEntity.setUser(userProfile.getUser());
            userCoinRecordEntity.setSource(UserCoinChangeSource.CHARGE);
            userCoinRecordEntity.setAmount(orderCharge.getChargeGoods().getCoin());
            userCoinRecordEntity.setAfterAmount(userProfile.getCoin());
            userCoinRecordEntity.setIsPositive(true);
            userCoinRecordEntity.setSourceId(orderCharge.getId());
            userCoinRecordRepository.save(userCoinRecordEntity);
            // 如果是首充
            if (user.getIsFirstCharge()) {
                // 全局首冲赠送比例
                if (siteService.getSysUserBenefits().getFirstChargePercentage() != null && siteService.getSysUserBenefits().getFirstChargePercentage().compareTo(BigDecimal.ZERO) == 1) {
                    BigDecimal freeCoin = siteService.getSysUserBenefits().getFirstChargePercentage().multiply(orderCharge.getChargeGoods().getCoin());
                    userProfile.setCoin(userProfile.getCoin().add(freeCoin));
                    userProfileRepository.save(userProfile);
                    // 记录金币流水
                    UserCoinRecordEntity firstChargeUserCoinRecordEntity = new UserCoinRecordEntity();
                    firstChargeUserCoinRecordEntity.setUser(userProfile.getUser());
                    firstChargeUserCoinRecordEntity.setSource(UserCoinChangeSource.FIRST_CHARGE);
                    firstChargeUserCoinRecordEntity.setSourceId(orderCharge.getId());
                    firstChargeUserCoinRecordEntity.setAmount(freeCoin);
                    firstChargeUserCoinRecordEntity.setAfterAmount(userProfile.getCoin());
                    firstChargeUserCoinRecordEntity.setIsPositive(true);
                    userCoinRecordRepository.save(firstChargeUserCoinRecordEntity);
                }
                // 金币首冲赠送金币
                if (orderCharge.getChargeGoods().getFirstChargeFreeCoin() != null && orderCharge.getChargeGoods().getFirstChargeFreeCoin().compareTo(BigDecimal.ZERO) == 1) {
                    BigDecimal freeCoin = orderCharge.getChargeGoods().getFirstChargeFreeCoin();
                    userProfile.setCoin(userProfile.getCoin().add(freeCoin));
                    userProfileRepository.save(userProfile);
                    // 记录金币流水
                    UserCoinRecordEntity firstChargeUserCoinRecordEntity = new UserCoinRecordEntity();
                    firstChargeUserCoinRecordEntity.setUser(userProfile.getUser());
                    firstChargeUserCoinRecordEntity.setSource(UserCoinChangeSource.FIRST_CHARGE);
                    firstChargeUserCoinRecordEntity.setSourceId(orderCharge.getId());
                    firstChargeUserCoinRecordEntity.setAmount(freeCoin);
                    firstChargeUserCoinRecordEntity.setAfterAmount(userProfile.getCoin());
                    firstChargeUserCoinRecordEntity.setIsPositive(true);
                    userCoinRecordRepository.save(firstChargeUserCoinRecordEntity);
                }
                // 金币首冲赠送比例
                if (orderCharge.getChargeGoods().getFreeCoinPercentage() != null && orderCharge.getChargeGoods().getFreeCoinPercentage().compareTo(BigDecimal.ZERO) == 1) {
                    BigDecimal freeCoin = orderCharge.getChargeGoods().getFreeCoinPercentage().multiply(orderCharge.getChargeGoods().getCoin());
                    userProfile.setCoin(userProfile.getCoin().add(freeCoin));
                    userProfileRepository.save(userProfile);
                    // 记录金币流水
                    UserCoinRecordEntity firstChargeUserCoinRecordEntity = new UserCoinRecordEntity();
                    firstChargeUserCoinRecordEntity.setUser(userProfile.getUser());
                    firstChargeUserCoinRecordEntity.setSource(UserCoinChangeSource.FIRST_CHARGE);
                    firstChargeUserCoinRecordEntity.setSourceId(orderCharge.getId());
                    firstChargeUserCoinRecordEntity.setAmount(freeCoin);
                    firstChargeUserCoinRecordEntity.setAfterAmount(userProfile.getCoin());
                    firstChargeUserCoinRecordEntity.setIsPositive(true);
                    userCoinRecordRepository.save(firstChargeUserCoinRecordEntity);
                }
            }
            // 推广
            if (user.getInviterId() != null) {
                BigDecimal inviteEncorageCoin = siteService.getSysUserBenefits().getInviteEncoragePercentage().multiply(orderCharge.getChargeGoods().getCoin()).setScale(2, BigDecimal.ROUND_DOWN);
                log.info("用户{} 推广 {}充值成功奖励金币 {}", user.getId(), user.getInviterId(), inviteEncorageCoin);
                UserInviteEntity inviter = userInviteRepository.findByUserId(user.getInviterId());
                inviter.setTotalCharge(inviter.getTotalCharge().add(orderCharge.getChargeGoods().getCoin()));
                inviter.setTotalEncourage(inviteEncorageCoin.add(inviter.getTotalEncourage()));
                if (user.getIsFirstCharge()) {
                    inviter.setFreeCase(inviter.getFreeCase() + 1);
                }
                userInviteRepository.save(inviter);
                // 推广记录
                UserInviteEncourageRecordEntity userInviteEncourageRecordEntity = new UserInviteEncourageRecordEntity();
                userInviteEncourageRecordEntity.setUser(inviter.getUser());
                userInviteEncourageRecordEntity.setEncourageCoin(inviteEncorageCoin);
                userInviteEncourageRecordEntity.setChargeCoin(orderCharge.getChargeGoods().getCoin());
                userInviteEncourageRecordEntity.setIsFirstCharge(user.getIsFirstCharge());
                userInviteEncourageRecordEntity.setOrderCharge(orderCharge);
                userInviteEncourageRecordRepository.save(userInviteEncourageRecordEntity);
                // 推广到账
                UserEntity userInviter = inviter.getUser();
                UserProfileEntity userProfileInviter = userProfileRepository.findByUser(userInviter);
                userProfileInviter.setCoin(userProfileInviter.getCoin().add(inviteEncorageCoin));
                userProfileRepository.save(userProfileInviter);
                // 记录金币流水
                UserCoinRecordEntity userCoinRecord = new UserCoinRecordEntity();
                userCoinRecord.setUser(userInviter);
                userCoinRecord.setSource(UserCoinChangeSource.INVITE);
                userCoinRecord.setSourceId(userInviteEncourageRecordEntity.getId());
                userCoinRecord.setAmount(inviteEncorageCoin);
                userCoinRecord.setAfterAmount(userProfile.getCoin());
                userCoinRecord.setIsPositive(true);
                userCoinRecordRepository.save(userCoinRecord);

            }
            user.setIsFirstCharge(false);
            userRepository.save(user);
            // 通知用户
            // 用户本月充值
            BigDecimal userMonthCharge = userCoinRecordRepository.userMonthCharge(user.getId());
            WalletVO walletVO = new WalletVO() {{
                setCoin(userProfile.getCoin());
                setDiamond(userProfile.getDiamond());
                setMessage(userMonthCharge.compareTo(BigDecimal.valueOf(32.5)) == 0 ? "本月再充值32.5即可开启日常任务" : null);
            }};
            MessageVO messageResultVO = new MessageVO(WebSocketMessageMethod.SEND_ONE, WebSocketMessageType.PAY_NOTIFY, new PayNotifyVO() {{
                setOrderNo(orderCharge.getOrderNo());
                setSuccess(true);
                setWalletVO(walletVO);
            }});
//            asyncTaskService.sendWebSocketMessageToAll(JSONObject.toJSONString(messageResultVO));
            asyncTaskService.sendWebSocketMessageToOne(user.getId(), JSONObject.toJSONString(messageResultVO));
        }
    }

    @Override
    public void wxPayNotify(String xmlData) throws WxPayException {
        WxPayOrderNotifyResult notifyResult = wxPayService.parseOrderNotifyResult(xmlData);
        // TODO 根据自己业务场景需要构造返回对象
        String orderNo = notifyResult.getOutTradeNo();
        log.info("订单号: {} 成功回调", orderNo);
        updateOrderStatus(orderNo);
    }

    @Override
    public void aliPayNotify(Map<String, String[]> data) {
        if (data.isEmpty()) {
            return;
        }
        // 将 Map<String,String[]> 转为 Map<String,String>
        Map<String, String> map = new HashMap<>();
        for (Iterator<String> iter = data.keySet().iterator(); iter.hasNext(); ) {
            String name = iter.next();
            String[] values = data.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
            }
            map.put(name, valueStr);
        }
        updateOrderStatus(map.get("out_trade_no"));
        // 验签
//        boolean signVerified = AlipaySignature.rsaCheckV1(map, AlipayConfig.alipay_public_key, AlipayConfig.charset,
//                AlipayConfig.sign_type);
//        // 验签通过
//        if (signVerified) {
//            //支付成功后进行操作
//        }
//        return "failure";
    }

    @Override
    public void boxPayNotify(String orderNo) {
        log.info("订单号: {} 成功回调", orderNo);
        updateOrderStatus(orderNo);
    }

    @Override
    public void xinfuPayNotify(String orderNo) {
        log.info("订单号: {} 信付支付成功回调", orderNo);
        updateOrderStatus(orderNo);
    }
}
