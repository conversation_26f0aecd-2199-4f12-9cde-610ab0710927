package com.steamgo1.csgoskinapi.service.impl;

import cc.siyecao.uid.core.UidGenerator;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinapi.config.jwt.JwtProvider;
import com.steamgo1.csgoskinapi.converter.UserInfoConverter;
import com.steamgo1.csgoskinapi.converter.UserLogConverter;
import com.steamgo1.csgoskinapi.dto.*;
import com.steamgo1.csgoskinapi.service.*;
import com.steamgo1.csgoskinapi.utils.IPUtils;
import com.steamgo1.csgoskinapi.utils.SecurityUtils;
import com.steamgo1.csgoskinapi.vo.*;
import com.steamgo1.csgoskincommon.bo.OcpcTaskBO;
import com.steamgo1.csgoskincommon.dao.*;
import com.steamgo1.csgoskincommon.dto.UserLogVo;
import com.steamgo1.csgoskincommon.entity.*;
import com.steamgo1.csgoskincommon.entity.enums.*;
import com.steamgo1.csgoskincommon.enums.OcpcRabbitmqType;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.service.IGXEService;
import com.steamgo1.csgoskincommon.service.IO661Service;
import com.steamgo1.csgoskincommon.service.ZBTService;
import com.steamgo1.csgoskincommon.utils.*;
import com.steamgo1.csgoskincommon.vo.TokenVO;
import com.steamgo1.csgoskincommon.vo.ZBTSteamInfoVO;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.annotation.Transient;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletRequest;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class UserServiceImpl implements UserService {
    @Value("${spring.redis.prefix.login-captcha}")
    private String redisLoginPrefix;

    @Value("${spring.redis.prefix.user}")
    private String redisUserPrefix;

    @Value("${spring.redis.expire.login-captcha}")
    private Long redisLoginExpire;


    @Value("${spring.redis.expire.disable-pickup}")
    private Long redisUserDisablePickupExpire;
    @Value("${app.index-url}")
    private String indexUrl;

    @Value("${site.default-user-avator}")
    private String defaultAvator;

    @Value("${rabbitmq.exchange.csgo}")
    private String exchageName;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private JwtProvider jwtProvider;

    @Autowired
    private OcpcChannelRepository ocpcChannelRepository;

    @Autowired
    private AlgorithmDataRepository algorithmDataRepository;

    @Autowired
    private UserInfoConverter userInfoConverter;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private UserPackageRepository userPackageRepository;

    @Autowired
    private SiteService siteService;

    @Autowired
    private UserCoinRecordRepository userCoinRecordRepository;

    @Autowired
    private UserPackagePickupRepository userPackagePickupRepository;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Resource
    private UidGenerator uidGenerator;

    @Autowired
    private UserPackageSellRepository userPackageSellRepository;

    @Autowired
    private UserDiamondRecordRepository userDiamondRecordRepository;

    @Autowired
    private SkinRepository skinRepository;

    @Autowired
    private UserExchangeRepository userExchangeRepository;

    @Autowired
    private UserBuySkinRepository userBuySkinRepository;

    @Autowired
    private SkinService skinService;

    @Autowired
    private UserInviteRepository userInviteRepository;

    @Autowired
    private UserInviteEncourageRecordRepository userInviteEncourageRecordRepository;

    @Autowired
    private ZBTService zbtService;

    @Autowired
    private IGXEService igxeService;

    @Autowired
    private IO661Service io661Service;

    @Autowired
    private SysExchangeRateRepository sysExchangeRateRepository;

    @Autowired
    private GmailOAuthService gmailOAuthService;

    @Autowired
    private UserDisableRepository userDisableRepository;


    @Autowired
    private DailyActivityRepository dailyActivityRepository;


    @Autowired
    private UserInfoChangeRecordRepository userInfoChangeRecordRepository;

    @Autowired
    private CaseUserRecordRepository caseUserRecordRepository;

    @Autowired
    private ConsumePlanUserRecordRepository consumePlanUserRecordRepository;

    @Autowired
    private RealNameVerifyService realNameVerifyService;
    @Autowired
    private UserLogConverter userLogConverter;

    //    @PostConstruct
    @Override
    public void createRobot() {
        log.info("当前机器人： {}", userRepository.countByType(UserType.ROBOT));
        for (int i = 0; i < 3 - userRepository.countByType(UserType.ROBOT); i++) {
            log.info("开始创建机器人: {}", i);
            UserEntity user = new UserEntity();
            user.setPhone(CharUtil.getRandomNum(11));
            user.setNickname(CharUtil.getRandomString(8).toUpperCase());
            user.setAvatar(defaultAvator);
            user.setType(UserType.ROBOT);
            user = userRepository.save(user);
//            初始化用户算法数据
            AlgorithmDataEntity algorithmDataEntity = new AlgorithmDataEntity();
            algorithmDataEntity.setUser(user);
            algorithmDataEntity.setRounds(1);
            algorithmDataEntity.setSecretHash(CharUtil.getRandomString(128));
            algorithmDataEntity.setSecretSalt(CharUtil.getRandomString(64));
            algorithmDataEntity.setPublicHash(HashUtils.SHA256(algorithmDataEntity.getSecretHash(), algorithmDataEntity.getSecretHash()));
            algorithmDataEntity.setClientSeed(CharUtil.getRandomString(64));
            algorithmDataEntity.setIsUsed(true);
//            用户钱包等数据
            UserProfileEntity userProfile = new UserProfileEntity();
            userProfile.setUser(user);
            userProfile.setDiamond(BigDecimal.ZERO);
            BigDecimal freeCoin = BigDecimal.valueOf(10000000);
            userProfile.setCoin(freeCoin);
            userProfile.setLavel(1);
            userProfile.setExperience(0);
            // 初始化推广信息
            UserInviteEntity userInviteEntity = new UserInviteEntity();
            userInviteEntity.setUser(user);
            userInviteEntity.setCode(CharUtil.getRandomString(8));
            userInviteEntity.setTotalCharge(BigDecimal.ZERO);
            userInviteEntity.setTotalEncourage(BigDecimal.ZERO);
            userInviteEntity.setTotalRegister(0);
            // 金币记录
            if (freeCoin.compareTo(BigDecimal.ZERO) != 0) {
                UserCoinRecordEntity userCoinRecordEntity = new UserCoinRecordEntity();
                userCoinRecordEntity.setUser(user);
                userCoinRecordEntity.setSource(UserCoinChangeSource.SYS);
                userCoinRecordEntity.setAmount(freeCoin);
                userCoinRecordEntity.setAfterAmount(userProfile.getCoin());
                userCoinRecordEntity.setIsPositive(true);
                userCoinRecordRepository.save(userCoinRecordEntity);
            }
            // 禁用功能
            UserDisableEntity userDisableEntity = new UserDisableEntity();
            userDisableEntity.setUser(user);
            userDisableEntity.setIsEffective(true);
            userDisableEntity.setType(UserDisableType.EXCHANGE);
            userDisableEntity.setRemarks(I18nUtils.getMessage("system.disable.exchange.unpaid"));
            userDisableRepository.save(userDisableEntity);
            algorithmDataRepository.save(algorithmDataEntity);
            userProfileRepository.save(userProfile);
            userInviteRepository.save(userInviteEntity);
        }
    }

    @Override
    public UserEntity getUser() {
        UserEntity user = userRepository.findById(SecurityUtils.getUserId());
        return user;
    }

    @Override
    public UserInfoVO updateUserName(String nickName) {
        Long userId = SecurityUtils.getUserId();
        UserEntity user = userRepository.findById(userId);
        if (nickName.isEmpty()) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.user.name.empty"));
        }
        UserInfoChangeRecordEntity lastRecord = userInfoChangeRecordRepository.findTopByUserIdAndTypeOrderByCreateTime(userId, UserInfoChangeType.NICKNAME);
        if (lastRecord != null) {
            LocalDateTime enableTime = lastRecord.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().plusDays(10);
            if (enableTime.isAfter(LocalDateTime.now())) {
                // todo 国际化
                throw new CsgoSkinException(DateUtils.to(enableTime) + I18nUtils.getMessage("exception.user.name.change.limit"));
            }
        }
        // 记录信息变更记录
        UserInfoChangeRecordEntity userInfoChangeRecordEntity = new UserInfoChangeRecordEntity();
        userInfoChangeRecordEntity.setUser(user);
        userInfoChangeRecordEntity.setType(UserInfoChangeType.NICKNAME);
        userInfoChangeRecordEntity.setBeforeValue(user.getNickname());
        userInfoChangeRecordEntity.setValue(nickName);
        userInfoChangeRecordRepository.save(userInfoChangeRecordEntity);
        user.setNickname(nickName);
        userRepository.save(user);
        UserInfoVO userInfoVO = queryUserInfo();
        return userInfoVO;
    }

    @Override
    public UserInfoVO updateUserTradeOfferAccessUrl(String tradeOfferAccessUrl) {
        Long userId = SecurityUtils.getUserId();
        log.info("用户: {}, 配置交易链接: {}", userId, tradeOfferAccessUrl);
        UserEntity user = userRepository.findById(userId);
        if (tradeOfferAccessUrl.isEmpty()) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.trade.url.empty"));
        }
        if (!zbtService.createSteamInfoByTradeUrl(tradeOfferAccessUrl)) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.trade.url.empty"));
        }
        if (!Checker.checkSteamTradeoffer(tradeOfferAccessUrl)) {
            log.error("用户： {} 配置交易链接错误： {}", userId, tradeOfferAccessUrl);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.trade.url.invalid"));
        }
        if (user.getTradeOfferAccessUrl() != null && user.getTradeOfferAccessUrl().equals(tradeOfferAccessUrl)) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.trade.url.same"));
        }
        if (userRepository.existsByTradeOfferAccessUrl(tradeOfferAccessUrl)) {
            log.error("用户： {} 配置交易链接已存在： {}", userId, tradeOfferAccessUrl);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.trade.url.bound"));
        }
        if (user.getTradeOfferAccessUrl() != null) {
            UserInfoChangeRecordEntity lastRecord = userInfoChangeRecordRepository.findTopByUserIdAndTypeOrderByCreateTime(userId, UserInfoChangeType.TRADEOFFERACCESS_URL);
            if (lastRecord != null) {
                LocalDateTime enableTime = lastRecord.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().plusMinutes(10);
                if (enableTime.isAfter(LocalDateTime.now())) {
                    // todo 国际化
                    throw new CsgoSkinException(DateUtils.to(enableTime) + I18nUtils.getMessage("exception.trade.url.change.limit"));
                }
            }
        }
        ZBTSteamInfoVO zbtSteamInfoVO = zbtService.querySteamInfoByTradeUrl(tradeOfferAccessUrl);
        if (zbtSteamInfoVO == null) {
            log.error("用户： {} 配置交易链接可能不可用： {}", userId, tradeOfferAccessUrl);
//            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.trade.url.invalid"));
        }
        // 记录信息变更记录
        UserInfoChangeRecordEntity userInfoChangeRecordEntity = new UserInfoChangeRecordEntity();
        userInfoChangeRecordEntity.setUser(user);
        userInfoChangeRecordEntity.setType(UserInfoChangeType.TRADEOFFERACCESS_URL);
        userInfoChangeRecordEntity.setBeforeValue(user.getTradeOfferAccessUrl());
        userInfoChangeRecordEntity.setValue(tradeOfferAccessUrl);
        userInfoChangeRecordRepository.save(userInfoChangeRecordEntity);
        user.setTradeOfferAccessUrl(tradeOfferAccessUrl);
        if (zbtSteamInfoVO != null) {
            if (!userInfoChangeRecordRepository.existsByUserIdAndType(userId, UserInfoChangeType.NICKNAME)) {
                user.setNickname(zbtSteamInfoVO.getSteamInfo().getNickName());
            }
            if (!userInfoChangeRecordRepository.existsByUserIdAndType(userId, UserInfoChangeType.AVATAR)) {
                user.setAvatar(zbtSteamInfoVO.getSteamInfo().getAvatar());
            }
        }
        userRepository.save(user);
        if (!StringUtil.isNullOrEmpty(userInfoChangeRecordEntity.getBeforeValue())) {
            // 禁用功能
            //获取后一天
//        Calendar calendar=Calendar.getInstance();
//        calendar.setTime(new Date());
//        calendar.add(Calendar.DAY_OF_MONTH, 1);
            UserDisableEntity userDisableEntity = new UserDisableEntity();
            userDisableEntity.setUser(userDisableEntity.getUser());
            userDisableEntity.setIsEffective(true);
            userDisableEntity.setType(UserDisableType.PICK_UP);
//        userDisableEntity.setDisableExpire(calendar.getTime());
            userDisableEntity.setRemarks(I18nUtils.getMessage("system.remark.trade.url.change"));
            userDisableEntity.setUser(user);
            userDisableRepository.save(userDisableEntity);
        }
        UserInfoVO userInfoVO = queryUserInfo();
        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                updateUserInfoByTradeOfferAccessUrl(userId, tradeOfferAccessUrl);
            }
        }, 10 * 1000);
        return userInfoVO;
    }


    @Async("async-executor-guava")
    public void updateUserInfoByTradeOfferAccessUrl(Long userId, String tradeOfferAccessUrl) {
        UserEntity user = userRepository.findById(userId);
        ZBTSteamInfoVO zbtSteamInfoVO = zbtService.querySteamInfoByTradeUrl(tradeOfferAccessUrl);
        if (zbtSteamInfoVO != null) {
            if (!userInfoChangeRecordRepository.existsByUserIdAndType(userId, UserInfoChangeType.NICKNAME)) {
                user.setNickname(zbtSteamInfoVO.getSteamInfo().getNickName());
            }
            if (!userInfoChangeRecordRepository.existsByUserIdAndType(userId, UserInfoChangeType.AVATAR)) {
                user.setAvatar(zbtSteamInfoVO.getSteamInfo().getAvatar());
            }
        }
        userRepository.save(user);
    }

    @Override
    public UserInfoVO updateUserAvatar(String avatar) {
        Long userId = SecurityUtils.getUserId();
        UserEntity user = userRepository.findById(userId);
        if (avatar.isEmpty()) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.avatar.empty"));
        }
        // 记录信息变更记录
        UserInfoChangeRecordEntity userInfoChangeRecordEntity = new UserInfoChangeRecordEntity();
        userInfoChangeRecordEntity.setUser(user);
        userInfoChangeRecordEntity.setType(UserInfoChangeType.AVATAR);
        userInfoChangeRecordEntity.setBeforeValue(user.getAvatar());
        userInfoChangeRecordEntity.setValue(avatar);
        userInfoChangeRecordRepository.save(userInfoChangeRecordEntity);
        user.setAvatar(avatar);
        userRepository.save(user);
        return queryUserInfo();
    }


    @Override
    public Page<UserCoinRecordVO> queryUserCoinRecord(UserCoinRecordQueryDTO userCoinRecordQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(userCoinRecordQueryDTO.getPage(), userCoinRecordQueryDTO.getSize(), sort);
        Page<UserCoinRecordEntity> userCoinRecordEntityPage = userCoinRecordRepository.findByUserId(SecurityUtils.getUserId(), pageable);
        List<UserCoinRecordVO> userCoinRecordVOList = new ArrayList<>();
        for (UserCoinRecordEntity userCoinRecordEntity : userCoinRecordEntityPage.getContent()) {
            UserCoinRecordVO userCoinRecordVO = new UserCoinRecordVO();
            userCoinRecordVO.setTime(userCoinRecordEntity.getCreateTime());
            userCoinRecordVO.setSourceValue(userCoinRecordEntity.getSource().getValue());
            userCoinRecordVO.setSourceCode(userCoinRecordEntity.getSource().getCode());
            userCoinRecordVO.setAmount(userCoinRecordEntity.getAmount());
            userCoinRecordVO.setAfterAmount(userCoinRecordEntity.getAfterAmount());
            userCoinRecordVO.setIsPositive(userCoinRecordEntity.getIsPositive());
            userCoinRecordVOList.add(userCoinRecordVO);
        }
        return new PageImpl<>(userCoinRecordVOList, userCoinRecordEntityPage.getPageable(), userCoinRecordEntityPage.getTotalElements());

    }

    @Override
    public Page<UserDiamondRecordVO> queryUserDiamondRecord(UserDiamondRecordQueryDTO userCoinRecordQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(userCoinRecordQueryDTO.getPage(), userCoinRecordQueryDTO.getSize(), sort);
        Page<UserDiamondRecordEntity> userDiamondRecordEntityPage = userDiamondRecordRepository.findByUserId(SecurityUtils.getUserId(), pageable);
        List<UserDiamondRecordVO> userDiamondRecordVOList = new ArrayList<>();
        for (UserDiamondRecordEntity userDiamondRecordEntity : userDiamondRecordEntityPage.getContent()) {
            UserDiamondRecordVO userDiamondRecordVO = new UserDiamondRecordVO();
            userDiamondRecordVO.setTime(userDiamondRecordEntity.getCreateTime());
            userDiamondRecordVO.setSourceValue(userDiamondRecordEntity.getSource().getValue());
            userDiamondRecordVO.setSourceCode(userDiamondRecordEntity.getSource().getCode());
            userDiamondRecordVO.setAmount(userDiamondRecordEntity.getAmount());
            userDiamondRecordVO.setAfterAmount(userDiamondRecordEntity.getAfterAmount());
            userDiamondRecordVO.setIsPositive(userDiamondRecordEntity.getIsPositive());
            userDiamondRecordVOList.add(userDiamondRecordVO);
        }
        return new PageImpl<>(userDiamondRecordVOList, userDiamondRecordEntityPage.getPageable(), userDiamondRecordEntityPage.getTotalElements());
    }

    @Override
    @Transactional
    public void pickUpSkin(List<Long> packageIds) {
        Long userId = SecurityUtils.getUserId();
        if (!RateLimiterUtils.tryAcquire(userId)) {
            log.warn("用户取回限流: {}", userId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.operation.too.frequent"));
        }
        UserEntity user = userRepository.findById(userId);
        if (StrUtil.isEmpty(user.getSteamId())) {
            log.error("用户： {} 未绑定steam账号", userId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.steam.not.bound"));
        }
        if (!Checker.checkSteamTradeoffer(user.getTradeOfferAccessUrl())) {
            log.error("用户： {} 配置交易链接错误： {}", userId, user.getTradeOfferAccessUrl());
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.trade.url.invalid"));
        }
        for (Long packageId : packageIds) {
            UserPackageEntity userPackage = userPackageRepository.findTopById(packageId);
            if (!userPackage.getUser().getId().equals(userId)) {
                log.error("用户: {}取别人的, {}", userId, packageId);
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.operation.invalid"));
            }
            if (user.getType().equals(UserType.ACTUAL) && !userCoinRecordRepository.existsByUserIdAndSource(userId, UserCoinChangeSource.CHARGE)) {
                log.error("用户: {}未充值, {}", userId, packageId);
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.function.charge.required"));
            }
            if (userPackage == null) {
                log.error("用户: {}取回饰品失败, 饰品不存在: {}", userId, packageId);
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.inventory.not.owned"));
            }
            if (userPackage.getIsLocked()) {
                log.error("用户: {}取回饰品失败, 饰品锁定: {}", userId, packageId);
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.inventory.out.of.stock"));
            }
            if (userPackage.getIsReceived() || userPackage.getIsSelled()) {
                log.error("用户: {}取回饰品失败, 饰品已出售或已取回: {}", userId, packageId);
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.inventory.already.processed"));
            }
            if (user.getTradeOfferAccessUrl() == null) {
                log.error("用户: {}取回饰品失败, 未配置steam交易链接: {}", userId, packageId);
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.trade.url.not.set"));
            }
            if (userPackage.getPrice() != null && userPackage.getSkin().getPrice().compareTo(BigDecimal.valueOf(4.5)) > 0
                    && userPackage.getSkin().getPrice().compareTo(userPackage.getPrice().multiply(BigDecimal.valueOf(1.05))) > 0) {
                log.error("用户: {}取回饰品失败, 商品涨价了超过百分之五: {}", userId, packageId);
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.inventory.out.of.stock"));
            }
            if (userPackage.getSkin().getIsDeleted() || userPackage.getSkin().getIsAbnormal()) {
                log.error("用户: {}取回饰品失败, 价格异常: {}", userId, packageId);
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.inventory.out.of.stock"));
            }
//            String disablePickup = RedisUtils.get(redisUserPrefix + ":DISABLE_PICKUP:" + userPackage.getUser().getId().toString(), String.class);
            UserDisableEntity userDisable = userDisableRepository.findTopByUserIdAndTypeAndIsEffectiveIsTrue(userId, UserDisableType.PICK_UP);
            if (userDisable != null) {
                if (userDisable.getDisableExpire() == null) {
                    log.error("用户: {} 取回功能禁用：{},无法自动解封", userId, userDisable.getRemarks());
                    // todo 国际化
                    throw new CsgoSkinException(userDisable.getRemarks() + ",取回功能禁用,联系客服处理");
                }
                if (System.currentTimeMillis() - userDisable.getDisableExpire().getTime() < 0) {
                    log.error("用户: {} 取回功能禁用至: {}，原因： {}", userId, userDisable.getDisableExpire(), userDisable.getRemarks());
                    // todo 国际化
                    throw new CsgoSkinException(userDisable.getRemarks() + ",取回功能禁用至: " + userDisable.getDisableExpire());
                }
                userDisable.setIsEffective(false);
                userDisableRepository.save(userDisable);
            }
            UserPackagePickupEntity userPackagePickupEntity = new UserPackagePickupEntity();
            userPackagePickupEntity.setUser(user);
            userPackagePickupEntity.setUserPackage(userPackage);
            userPackagePickupEntity.setOrderNo(Utils.generateOrderNo(userId, "PICKUP"));
            userPackagePickupEntity.setSteamTradeUrl(user.getTradeOfferAccessUrl());
            userPackagePickupEntity.setStatus(user.getType().equals(UserType.ACTUAL) ? PackagePickupStatus.PICKUPING : PackagePickupStatus.CHECKING);
            userPackage.setIsReceived(true);
            if (userPackage.getSkin().getPrice().compareTo(BigDecimal.valueOf(20)) > 0) {
                log.info("需要审核");
                userPackagePickupEntity.setStatus(PackagePickupStatus.CHECKING);
            }
            if (userPackage.getSkin().getPrice().compareTo(userPackage.getPrice().multiply(BigDecimal.valueOf(1.05))) > 0) {
                log.info("需要审核");
                userPackagePickupEntity.setStatus(PackagePickupStatus.CHECKING);
            }
            userPackagePickupRepository.save(userPackagePickupEntity);
            userPackageRepository.save(userPackage);
//            zbtService.SyncSkinInfoOne(userPackage.getSkin().getId());
            zbtService.SyncSkinInfoOne(userPackage.getSkin().getId());
            // 放入队列
            rabbitTemplate.convertAndSend(exchageName, "skin.pickup", JSONObject.toJSON(userPackagePickupEntity));
            DailyActivity(user.getId(), DailyActivityType.PICK_UP);
        }

    }

    @Override
    public Page<UserPackageSkinPickUpRecordVO> queryUserPickagePickUpRecord(UserPackageSkinPickUpRecordQueryDTO userPackageSkinPickUpRecordQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(userPackageSkinPickUpRecordQueryDTO.getPage(), userPackageSkinPickUpRecordQueryDTO.getSize(), sort);
        Page<UserPackagePickupEntity> userPackagePickupEntityPage = userPackagePickupRepository.findByUserId(SecurityUtils.getUserId(), pageable);
        List<UserPackageSkinPickUpRecordVO> userPackageSkinPickUpRecordVOList = new ArrayList<>();
        for (UserPackagePickupEntity userPackagePickupEntity : userPackagePickupEntityPage.getContent()) {
            SkinEntity skinEntity = userPackagePickupEntity.getUserPackage().getSkin();
            UserPackageSkinPickUpRecordVO userPackageSkinPickUpRecordVO = new UserPackageSkinPickUpRecordVO();
            userPackageSkinPickUpRecordVO.setCreateTime(userPackagePickupEntity.getCreateTime());
            userPackageSkinPickUpRecordVO.setUpdateTime(userPackagePickupEntity.getUpdateTime());
            userPackageSkinPickUpRecordVO.setOrderNo(userPackagePickupEntity.getOrderNo());
            userPackageSkinPickUpRecordVO.setStatus(userPackagePickupEntity.getStatus().getCode());
            userPackageSkinPickUpRecordVO.setStatusValue(userPackagePickupEntity.getStatus().getValue());
            userPackageSkinPickUpRecordVO.setSkinName(skinEntity.getName());
            userPackageSkinPickUpRecordVO.setSkinPicture(skinEntity.getPicture());
            userPackageSkinPickUpRecordVO.setRemarks(userPackagePickupEntity.getRemarks());
            userPackageSkinPickUpRecordVOList.add(userPackageSkinPickUpRecordVO);
        }
        return new PageImpl<>(userPackageSkinPickUpRecordVOList, userPackagePickupEntityPage.getPageable(), userPackagePickupEntityPage.getTotalElements());
    }

    @Override
    public Page<UserPackageSkinPickUpRecordVO> queryOtherUserPickagePickUpRecord(OtherUserPackageSkinPickUpRecordQueryDTO userPackageSkinPickUpRecordQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(userPackageSkinPickUpRecordQueryDTO.getPage(), userPackageSkinPickUpRecordQueryDTO.getSize(), sort);
        Page<UserPackagePickupEntity> userPackagePickupEntityPage = userPackagePickupRepository.findByUserId(userPackageSkinPickUpRecordQueryDTO.getUserId(), pageable);
        List<UserPackageSkinPickUpRecordVO> userPackageSkinPickUpRecordVOList = new ArrayList<>();
        for (UserPackagePickupEntity userPackagePickupEntity : userPackagePickupEntityPage.getContent()) {
            SkinEntity skinEntity = userPackagePickupEntity.getUserPackage().getSkin();
            UserPackageSkinPickUpRecordVO userPackageSkinPickUpRecordVO = new UserPackageSkinPickUpRecordVO();
            userPackageSkinPickUpRecordVO.setCreateTime(userPackagePickupEntity.getCreateTime());
            userPackageSkinPickUpRecordVO.setUpdateTime(userPackagePickupEntity.getUpdateTime());
            userPackageSkinPickUpRecordVO.setOrderNo(userPackagePickupEntity.getOrderNo());
            userPackageSkinPickUpRecordVO.setStatus(userPackagePickupEntity.getStatus().getCode());
            userPackageSkinPickUpRecordVO.setStatusValue(userPackagePickupEntity.getStatus().getValue());
            userPackageSkinPickUpRecordVO.setSkinName(skinEntity.getName());
            userPackageSkinPickUpRecordVO.setSkinPicture(skinEntity.getPicture());
            userPackageSkinPickUpRecordVOList.add(userPackageSkinPickUpRecordVO);
        }
        return new PageImpl<>(userPackageSkinPickUpRecordVOList, userPackagePickupEntityPage.getPageable(), userPackagePickupEntityPage.getTotalElements());
    }


    @Transactional
    @Override
    public WalletVO sellSkin(List<Long> packageSkinIds) {
        Long userId = SecurityUtils.getUserId();
        if (!RateLimiterUtils.tryAcquire(userId)) {
            log.warn("出售被限流: {}", userId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.operation.too.frequent"));
        }
        UserEntity user = userRepository.findById(userId);
        for (Long packageSkinId : packageSkinIds) {
//            UserPackageEntity userPackage = userPackageRepository.findById(packageSkinId).orElse(null);
            UserPackageEntity userPackage = userPackageRepository.findTopById(packageSkinId);
            if (userPackage == null) {
                log.error("用户: {}取回饰品失败, 饰品不存在: {}", userId, packageSkinId);
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.inventory.not.owned"));
            }
            if (userPackage.getIsLocked()) {
                log.error("用户: {}取回饰品失败, 饰品锁定: {}", userId, packageSkinId);
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.inventory.out.of.stock"));
            }
            SkinEntity skin = userPackage.getSkin();
//            if(skin.getIsDeleted() || skin.getIsAbnormal()){
//                log.error("用户: {}取回饰品失败, 饰品价格异常: {}", userId, packageSkinId);
//                // todo 国际化
//            throw new CsgoSkinException(I18nUtils.getMessage("exception.inventory.out.of.stock"));
//            }
            if (skin.getIsDeleted()) {
                log.error("用户: {}取回饰品失败, 饰品价格异常: {}", userId, packageSkinId);
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.inventory.out.of.stock"));
            }
            log.info("userID {}", userId);
            if (!userPackage.getUser().getId().equals(userId)) {
                log.error("用户饰品出售错误： userID：{} packageId: {}", userId, packageSkinId);
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.inventory.not.found"));
            }
            if (userPackage.getIsSelled() || userPackage.getIsReceived()) {
                log.error("用户饰品出售错误： userID：{} packageId: {}", userId, packageSkinId);
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.inventory.not.found"));
            }
            UserProfileEntity userProfile = userProfileRepository.findByUserId(userId);
            userProfile.setDiamond(userProfile.getDiamond().add(userPackage.getDiamond()));
            userProfile = userProfileRepository.save(userProfile);

//        出售记录
            UserPackageSellEntity userPackageSellEntity = new UserPackageSellEntity();
            userPackageSellEntity.setUser(user);
            userPackageSellEntity.setOrderNo(Utils.generateOrderNo(userId, "SELL"));
            userPackageSellEntity.setUserPackage(userPackage);
            userPackageSellEntity.setStatus(PackageSellStatus.SUCCESS);
            userPackageSellEntity = userPackageSellRepository.save(userPackageSellEntity);

//        钻石流水
            UserDiamondRecordEntity userDiamondRecordEntity = new UserDiamondRecordEntity();
            userDiamondRecordEntity.setUser(user);
            userDiamondRecordEntity.setAmount(userPackage.getDiamond());
            userDiamondRecordEntity.setAfterAmount(userProfile.getDiamond());
            userDiamondRecordEntity.setIsPositive(true);
            userDiamondRecordEntity.setSource(UserDiamondChangeSource.SELL);
            userDiamondRecordEntity.setSourceId(userPackageSellEntity.getId());
            userDiamondRecordRepository.save(userDiamondRecordEntity);

            userPackage.setIsSelled(true);
            userPackageRepository.save(userPackage);
            log.info("用户：{} 出售 背包ID: {} 饰品ID: {} 饰品名：{} 获取钻石: {} 余额: {}", userId, packageSkinId, skin.getId(), skin.getName(), userPackage.getDiamond(), userProfile.getDiamond());
        }
        UserProfileEntity userProfile = userProfileRepository.findByUser(user);
        return new WalletVO() {{
            setCoin(userProfile.getCoin());
            setDiamond(userProfile.getDiamond());
        }};
    }

    @Override
    @Transactional
    public WalletVO diamondToCoin(BigDecimal diamond) {
        Long userId = SecurityUtils.getUserId();
        if (!RateLimiterUtils.tryAcquire(userId)) {
            log.warn("兑换金币被限流: {}", userId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.operation.too.frequent"));
        }
        UserEntity user = userRepository.findById(userId);
        UserProfileEntity userProfile = userProfileRepository.findByUserId(userId);
        if (diamond.compareTo(BigDecimal.ZERO) < 1) {
            log.info("用户兑换金币：{}, 金币<=0", userId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.diamond.required"));
        }
        if (userProfile.getDiamond().compareTo(diamond) == -1) {
            log.info("用户兑换金币：{}, 钻石不足", userId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.diamond.insufficient"));
        }
        if (user.getType().equals(UserType.ACTUAL) && !userCoinRecordRepository.existsByUserIdAndSource(userId, UserCoinChangeSource.CHARGE)) {
            log.error("用户: {}未充值, {}", userId, diamond);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.inventory.out.of.stock"));
        }
        UserDisableEntity userDisable = userDisableRepository.findTopByUserIdAndTypeAndIsEffectiveIsTrue(userId, UserDisableType.EXCHANGE);
        if (userDisable != null) {
            if (userDisable.getDisableExpire() == null) {
                log.error("用户: {} 兑换功能禁用：{},无法自动解封", userId, userDisable.getRemarks());
                // todo 国际化
                throw new CsgoSkinException(userDisable.getRemarks() + ",兑换功能禁用,联系客服处理");
            }
            if (System.currentTimeMillis() - userDisable.getDisableExpire().getTime() < 0) {
                log.error("用户: {} 兑换功能禁用至: {}，原因： {}", userId, userDisable.getDisableExpire(), userDisable.getRemarks());
                // todo 国际化
                throw new CsgoSkinException(userDisable.getRemarks() + ",兑换功能禁用至: " + userDisable.getDisableExpire());
            }
            userDisable.setIsEffective(false);
            userDisableRepository.save(userDisable);
        }
        BigDecimal decimalToCoin = diamond.divide(siteService.getExchangeRate().getCoinToDiamond(), 2, BigDecimal.ROUND_DOWN);
        userProfile.setDiamond(userProfile.getDiamond().subtract(diamond));
        userProfile.setCoin(userProfile.getCoin().add(decimalToCoin));
        userProfile = userProfileRepository.save(userProfile);

        // 兑换记录
        UserExchangeEntity userExchangeEntity = new UserExchangeEntity();
        userExchangeEntity.setUser(user);
        userExchangeEntity.setStatus(UserExchangeStatus.SUCCESS);
        userExchangeEntity.setDiamondAmount(diamond);
        userExchangeEntity.setCoinAmount(decimalToCoin);
        userExchangeEntity.setOrderNo(Utils.generateOrderNo(userId, "DIAMONDTOCOIN"));
        userExchangeRepository.save(userExchangeEntity);
        // 金币变动记录
        UserCoinRecordEntity userCoinRecordEntity = new UserCoinRecordEntity();
        userCoinRecordEntity.setUser(user);
        userCoinRecordEntity.setSource(UserCoinChangeSource.EXCHANGE);
        userCoinRecordEntity.setSourceId(userExchangeEntity.getId());
        userCoinRecordEntity.setIsPositive(true);
        userCoinRecordEntity.setAmount(decimalToCoin);
        userCoinRecordEntity.setAfterAmount(userProfile.getCoin());
        userCoinRecordRepository.save(userCoinRecordEntity);

        // 钻石变动记录
        UserDiamondRecordEntity userDiamondRecordEntity = new UserDiamondRecordEntity();
        userDiamondRecordEntity.setUser(user);
        userDiamondRecordEntity.setAmount(diamond);
        userDiamondRecordEntity.setAfterAmount(userProfile.getDiamond());
        userDiamondRecordEntity.setIsPositive(false);
        userDiamondRecordEntity.setSource(UserDiamondChangeSource.EXCHANGE);
        userDiamondRecordEntity.setSourceId(userExchangeEntity.getId());
        userDiamondRecordRepository.save(userDiamondRecordEntity);
        UserProfileEntity finalUserProfile = userProfile;
        return new WalletVO() {{
            setCoin(finalUserProfile.getCoin());
            setDiamond(finalUserProfile.getDiamond());
        }};
    }

    @Override
    public WalletVO buySkin(Long skinId) {
        Long userId = SecurityUtils.getUserId();
        if (!RateLimiterUtils.tryAcquire(userId)) {
            log.warn("用户购买饰品限流: {}", userId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.operation.too.frequent"));
        }
        UserEntity user = userRepository.findById(userId);
        UserProfileEntity userProfile = userProfileRepository.findByUserId(userId);
        SkinEntity skin = skinRepository.findById(skinId).get();
        if (skin == null) {
            log.error("用户购买饰品失败：{} 饰品不存在", skin.getId());
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.skin.not.found", new Object[]{""}));
        }
        if (userProfile.getDiamond().compareTo(skin.getDiamond()) == -1) {
            log.error("用户购买饰品失败：{} 钻石不足", skin.getId());
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.diamond.insufficient"));
        }
        if (skin.getIsAbnormal() || !skin.getIsSale() || skin.getIsDeleted()) {
            log.error("用户购买饰品失败：{} 数据异常", skin.getId());
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.inventory.locked"));
        }
        userProfile.setDiamond(userProfile.getDiamond().subtract(skin.getDiamond()));
        userProfile = userProfileRepository.save(userProfile);
        // 购买记录
        UserBuySkinEntity userBuySkinEntity = new UserBuySkinEntity();
        userBuySkinEntity.setSkin(skin);
        userBuySkinEntity.setUser(user);
        userBuySkinEntity.setDiamond(skin.getDiamond());
        userBuySkinEntity.setStatus(UserBuySkinStatus.BUYING);
        userBuySkinEntity.setOrderNo(Utils.generateOrderNo(userId, "BUYSKIN"));
        userBuySkinRepository.save(userBuySkinEntity);
        // 钻石变动
        UserDiamondRecordEntity userDiamondRecordEntity = new UserDiamondRecordEntity();
        userDiamondRecordEntity.setUser(user);
        userDiamondRecordEntity.setAmount(skin.getDiamond());
        userDiamondRecordEntity.setAfterAmount(userProfile.getDiamond());
        userDiamondRecordEntity.setIsPositive(false);
        userDiamondRecordEntity.setSource(UserDiamondChangeSource.BUY);
        userDiamondRecordEntity.setSourceId(userBuySkinEntity.getId());
        userDiamondRecordRepository.save(userDiamondRecordEntity);
        // 放入背包
        UserPackageEntity userPackage = new UserPackageEntity();
        userPackage.setUser(userProfile.getUser());
        userPackage.setSkin(skin);
        userPackage.setPrice(skin.getPrice());
        userPackage.setDiamond(skin.getDiamond());
        userPackage.setSource(UserPackageSource.BUY);
        userPackage.setIsReceived(false);
        userPackage.setIsSelled(false);

        userPackageRepository.save(userPackage);
        UserProfileEntity finalUserProfile = userProfile;
        return new WalletVO() {{
            setCoin(finalUserProfile.getCoin());
            setDiamond(finalUserProfile.getDiamond());
        }};
    }

    @Override
    public Page<UserBuySkinRecordVO> buySkinRecord(UserBuySkinRecordQueryDTO userBuySkinRecordQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(userBuySkinRecordQueryDTO.getPage(), userBuySkinRecordQueryDTO.getSize(), sort);
        Page<UserBuySkinEntity> userBuySkinEntityPage = userBuySkinRepository.findByUserId(SecurityUtils.getUserId(), pageable);
        List<UserBuySkinRecordVO> userBuySkinRecordVOList = new ArrayList<>();
        for (UserBuySkinEntity userBuySkinEntity : userBuySkinEntityPage.getContent()) {
            UserBuySkinRecordVO userBuySkinRecordVO = new UserBuySkinRecordVO();
            userBuySkinRecordVO.setSkinInfo(skinService.querySkinInfoById(userBuySkinEntity.getSkin().getId()));
            userBuySkinRecordVO.setStatus(userBuySkinEntity.getStatus().getCode());
            userBuySkinRecordVO.setStatusValue(userBuySkinEntity.getStatus().getValue());
            userBuySkinRecordVO.setOrderNo(userBuySkinEntity.getOrderNo());
            userBuySkinRecordVOList.add(userBuySkinRecordVO);
        }
        return new PageImpl<>(userBuySkinRecordVOList, userBuySkinEntityPage.getPageable(), userBuySkinEntityPage.getTotalElements());
    }

    @Override
    public WalletVO queryUserWallet() {
        Long userId = SecurityUtils.getUserId();
        UserProfileEntity userProfile = userProfileRepository.findByUserId(userId);
        return new WalletVO() {{
            setCoin(userProfile.getCoin());
            setDiamond(userProfile.getDiamond());
        }};
    }

    @Override
    public Page<UserDiamondToCoinRecordVO> queryUserDiamondToCoinRecord(UserDiamondToCoinRecordQueryDTO userDiamondToCoinRecordQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(userDiamondToCoinRecordQueryDTO.getPage(), userDiamondToCoinRecordQueryDTO.getSize(), sort);
        Page<UserExchangeEntity> userExchangeEntityPage = userExchangeRepository.findByUserId(SecurityUtils.getUserId(), pageable);

        List<UserDiamondToCoinRecordVO> userDiamondToCoinRecordVOList = new ArrayList<>();
        for (UserExchangeEntity userExchangeEntity : userExchangeEntityPage.getContent()) {
            UserDiamondToCoinRecordVO userDiamondToCoinRecordVO = new UserDiamondToCoinRecordVO();
            userDiamondToCoinRecordVO.setCreateTime(userExchangeEntity.getCreateTime());
            userDiamondToCoinRecordVO.setUpdateTime(userExchangeEntity.getUpdateTime());
            userDiamondToCoinRecordVO.setDiamondAmount(userExchangeEntity.getDiamondAmount());
            userDiamondToCoinRecordVO.setCoinAmount(userExchangeEntity.getCoinAmount());
            userDiamondToCoinRecordVO.setStatus(userExchangeEntity.getStatus().getCode());
            userDiamondToCoinRecordVO.setStatusValue(userExchangeEntity.getStatus().getValue());
            userDiamondToCoinRecordVO.setOrderNo(userExchangeEntity.getOrderNo());
            userDiamondToCoinRecordVOList.add(userDiamondToCoinRecordVO);
        }
        return new PageImpl<>(userDiamondToCoinRecordVOList, userExchangeEntityPage.getPageable(), userExchangeEntityPage.getTotalElements());
    }

    @Override
    public Page<UserDiamondToCoinRecordVO> queryOtherUserDiamondToCoinRecord(OtherUserDiamondToCoinRecordQueryDTO userDiamondToCoinRecordQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(userDiamondToCoinRecordQueryDTO.getPage(), userDiamondToCoinRecordQueryDTO.getSize(), sort);
        Page<UserExchangeEntity> userExchangeEntityPage = userExchangeRepository.findByUserId(userDiamondToCoinRecordQueryDTO.getUserId(), pageable);

        List<UserDiamondToCoinRecordVO> userDiamondToCoinRecordVOList = new ArrayList<>();
        for (UserExchangeEntity userExchangeEntity : userExchangeEntityPage.getContent()) {
            UserDiamondToCoinRecordVO userDiamondToCoinRecordVO = new UserDiamondToCoinRecordVO();
            userDiamondToCoinRecordVO.setCreateTime(userExchangeEntity.getCreateTime());
            userDiamondToCoinRecordVO.setUpdateTime(userExchangeEntity.getUpdateTime());
            userDiamondToCoinRecordVO.setDiamondAmount(userExchangeEntity.getDiamondAmount());
            userDiamondToCoinRecordVO.setCoinAmount(userExchangeEntity.getCoinAmount());
            userDiamondToCoinRecordVO.setStatus(userExchangeEntity.getStatus().getCode());
            userDiamondToCoinRecordVO.setStatusValue(userExchangeEntity.getStatus().getValue());
            userDiamondToCoinRecordVO.setOrderNo(userExchangeEntity.getOrderNo());
            userDiamondToCoinRecordVOList.add(userDiamondToCoinRecordVO);
        }
        return new PageImpl<>(userDiamondToCoinRecordVOList, userExchangeEntityPage.getPageable(), userExchangeEntityPage.getTotalElements());
    }

    @Override
    public UserInviteVO getUserInviteInfo() {
        UserEntity user = userRepository.findById(SecurityUtils.getUserId());
        UserInviteEntity userInviteEntity = userInviteRepository.findByUserId(SecurityUtils.getUserId());
        if (userInviteEntity == null) {
            // 初始化推广信息
            userInviteEntity = new UserInviteEntity();
            userInviteEntity.setUser(user);
            userInviteEntity.setCode(CharUtil.getRandomString(8));
            userInviteEntity.setTotalCharge(BigDecimal.ZERO);
            userInviteEntity.setTotalEncourage(BigDecimal.ZERO);
            userInviteEntity.setTotalRegister(0);
            userInviteEntity.setFreeCase(0);
            userInviteRepository.save(userInviteEntity);
        }
        UserInviteEntity finalUserInviteEntity = userInviteEntity;
        return new UserInviteVO() {{
            setCode(finalUserInviteEntity.getCode());
//            setTotalOpen(finalUserInviteEntity.getTotalOpen());
            setFreeCase(finalUserInviteEntity.getFreeCase());
            setTotalRegister(finalUserInviteEntity.getTotalRegister());
            setTotalEncourage(finalUserInviteEntity.getTotalEncourage());
            setUrl(indexUrl + "?invite=" + finalUserInviteEntity.getCode());
        }};
    }

    @Override
    public void cleanUserCache(Long userId) {
        String userInfoRedisKey = redisUserPrefix + ":" + userId;
        UserInfoVO userInfoVO = RedisUtils.get(userInfoRedisKey, UserInfoVO.class);
        if (userInfoVO != null) {
            RedisUtils.delete(userInfoRedisKey);
        }
    }

    @Override
    public Page<UserInviteEncourageRecordVO> queryUserInviteEncourageRecord(UserInviteEncouragRecordQueryDTO userInviteEncouragRecordQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(userInviteEncouragRecordQueryDTO.getPage(), userInviteEncouragRecordQueryDTO.getSize(), sort);
        Page<UserInviteEncourageRecordEntity> userInviteEncourageRecordEntityPage = userInviteEncourageRecordRepository.findByUserId(SecurityUtils.getUserId(), pageable);
        List<UserInviteEncourageRecordVO> userInviteEncourageRecordVOList = new ArrayList<>();
        for (UserInviteEncourageRecordEntity userInviteEncourageRecordEntity : userInviteEncourageRecordEntityPage.getContent()) {
            userInviteEncourageRecordVOList.add(new UserInviteEncourageRecordVO() {{
                setChargeCoin(userInviteEncourageRecordEntity.getChargeCoin());
                setEncourageCoin(userInviteEncourageRecordEntity.getEncourageCoin());
                setIsFirstCharge(userInviteEncourageRecordEntity.getIsFirstCharge());
                setCreateTime(userInviteEncourageRecordEntity.getCreateTime());
            }});
        }
        return new PageImpl<>(userInviteEncourageRecordVOList, userInviteEncourageRecordEntityPage.getPageable(), userInviteEncourageRecordEntityPage.getTotalElements());
    }

    @Override
    public Page<UserPackageSkinSellRecordVO> queryUserPackageSkinSellRecordVO(UserPackageSkinSellRecordQueryDTO userPackageSkinSellRecordQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(userPackageSkinSellRecordQueryDTO.getPage(), userPackageSkinSellRecordQueryDTO.getSize(), sort);
        Page<UserPackageSellEntity> userPackageSellEntityPage = userPackageSellRepository.findByUserId(SecurityUtils.getUserId(), pageable);
        List<UserPackageSkinSellRecordVO> userPackageSkinPickUpRecordVOList = new ArrayList<>();
        for (UserPackageSellEntity userPackageSellEntity : userPackageSellEntityPage.getContent()) {
            SkinEntity skinEntity = userPackageSellEntity.getUserPackage().getSkin();
            UserPackageSkinSellRecordVO userPackageSkinSellRecordVO = new UserPackageSkinSellRecordVO();
            userPackageSkinSellRecordVO.setCreateTime(userPackageSellEntity.getCreateTime());
            userPackageSkinSellRecordVO.setUpdateTime(userPackageSellEntity.getUpdateTime());
            userPackageSkinSellRecordVO.setOrderNo(userPackageSellEntity.getOrderNo());
            userPackageSkinSellRecordVO.setStatus(userPackageSellEntity.getStatus().getCode());
            userPackageSkinSellRecordVO.setStatusValue(userPackageSellEntity.getStatus().getValue());
            userPackageSkinSellRecordVO.setSkinName(skinEntity.getName());
            userPackageSkinSellRecordVO.setSkinPicture(skinEntity.getPicture());
            userPackageSkinPickUpRecordVOList.add(userPackageSkinSellRecordVO);
        }
        return new PageImpl<>(userPackageSkinPickUpRecordVOList, userPackageSellEntityPage.getPageable(), userPackageSellEntityPage.getTotalElements());
    }

    @Override
    public Page<UserPackageSkinSellRecordVO> queryOtherUserPackageSkinSellRecordVO(OtherUserPackageSkinSellRecordQueryDTO userPackageSkinSellRecordQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(userPackageSkinSellRecordQueryDTO.getPage(), userPackageSkinSellRecordQueryDTO.getSize(), sort);
        Page<UserPackageSellEntity> userPackageSellEntityPage = userPackageSellRepository.findByUserId(userPackageSkinSellRecordQueryDTO.getUserId(), pageable);
        List<UserPackageSkinSellRecordVO> userPackageSkinPickUpRecordVOList = new ArrayList<>();
        for (UserPackageSellEntity userPackageSellEntity : userPackageSellEntityPage.getContent()) {
            SkinEntity skinEntity = userPackageSellEntity.getUserPackage().getSkin();
            UserPackageSkinSellRecordVO userPackageSkinSellRecordVO = new UserPackageSkinSellRecordVO();
            userPackageSkinSellRecordVO.setCreateTime(userPackageSellEntity.getCreateTime());
            userPackageSkinSellRecordVO.setUpdateTime(userPackageSellEntity.getUpdateTime());
            userPackageSkinSellRecordVO.setOrderNo(userPackageSellEntity.getOrderNo());
            userPackageSkinSellRecordVO.setStatus(userPackageSellEntity.getStatus().getCode());
            userPackageSkinSellRecordVO.setStatusValue(userPackageSellEntity.getStatus().getValue());
            userPackageSkinSellRecordVO.setSkinName(skinEntity.getName());
            userPackageSkinSellRecordVO.setSkinPicture(skinEntity.getPicture());
            userPackageSkinPickUpRecordVOList.add(userPackageSkinSellRecordVO);
        }
        return new PageImpl<>(userPackageSkinPickUpRecordVOList, userPackageSellEntityPage.getPageable(), userPackageSellEntityPage.getTotalElements());
    }

    @Override
    public void SyncCache(Long userId) {
        String userInfoRedisKey = redisUserPrefix + ":" + userId;
        UserInfoVO userInfoVO = RedisUtils.get(userInfoRedisKey, UserInfoVO.class);
        if (userInfoVO != null) {
            RedisUtils.delete(userInfoRedisKey);
        }
        List<AlgorithmDataEntity> algorithmDataEntitys = algorithmDataRepository.findByUserIdAndIsUsed(userId, true);
        if (algorithmDataEntitys.isEmpty() || algorithmDataEntitys.size() > 1) {
            log.error("用户{} 算法数据据异常", userId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.algorithm.data.error"));
        }
        UserProfileEntity userProfile = userProfileRepository.findByUserId(userId);
        if (userProfile == null) {
            log.error("用户{} 数据异常", userId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.user.data.error.contact"));
        }
        UserEntity userEntity = userRepository.findById(userId);
        userInfoVO = userInfoConverter.toUserInfoVO(userEntity, algorithmDataEntitys.get(0), userProfile);
        RedisUtils.save(userInfoRedisKey, userInfoVO);
    }

    @Override
    public void DailyActivity(Long userId, DailyActivityType type) {
        log.info("统计日活++++++++++++++++++++++++++++++++++++++++++++++++++++");
        DailyActivityEntity dailyActivityEntity = new DailyActivityEntity();
        dailyActivityEntity.setUserId(userId);
        dailyActivityEntity.setType(type);
        dailyActivityRepository.save(dailyActivityEntity);
    }

    @Override
    public Integer queryFreeCase(Long userId) {
        UserInviteEntity userInvite = userInviteRepository.findByUserId(userId);
        if (userInvite == null) {
            return 0;
        }
        return userInvite.getFreeCase();
    }

    @Override
    public UserPackageValueVO queryUserPackageValue(Long userId) {
        return new UserPackageValueVO() {{
            setInventoryValue(userPackageRepository.userPackageTotalValue(userId));
            setReceivedValue(userPackageRepository.userPackageReceivedTotalValue(userId));
        }};
    }

    @Override
    public UserPackageSkinSourceVO pacekageSkinSource() {
        List<UserPackageSkinSourceVO.Source> sourceList = new ArrayList<>();
        for (UserPackageSource userPackageSource : UserPackageSource.values()) {
            sourceList.add(new UserPackageSkinSourceVO.Source() {{
                setCode(userPackageSource.getCode());
                setValue(userPackageSource.getValue());
            }});
        }
        return new UserPackageSkinSourceVO() {{
            setSourceList(sourceList);
        }};
    }

    @Override
    public void lockSkin(List<Long> packageIds, Boolean isLock) {
        Long userId = SecurityUtils.getUserId();
        List<UserPackageEntity> userPackageEntityList = new ArrayList<>();
        for (Long packageId : packageIds) {
            UserPackageEntity userPackage = userPackageRepository.findById(packageId).orElse(null);
            if (userPackage == null) {
                log.error("用户: {}锁定/解锁, 饰品不存在: {}", userId, packageId);
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.operation.failed") + "," + I18nUtils.getMessage("exception.inventory.not.owned"));
            }
            if (!userPackage.getUser().getId().equals(userId)) {
                log.error("用户锁定/解锁错误： userID：{} packageId: {}", userId, packageId);
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.inventory.not.found"));
            }
            if (userPackage.getIsSelled() || userPackage.getIsReceived()) {
                log.error("用户饰品锁定/解锁错误： userID：{} packageId: {}", userId, packageId);
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.inventory.not.found"));
            }
            userPackage.setIsLocked(isLock);
            userPackageEntityList.add(userPackage);
        }
        userPackageRepository.saveAll(userPackageEntityList);
    }

    @Override
    public Page<UserAlgorithmDataFullVO> queryHistoryUserAlorithmData(PageQueryDTO pageQueryDTO) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(pageQueryDTO.getPage(), pageQueryDTO.getSize(), sort);
        Page<AlgorithmDataEntity> algorithmDataEntitys = algorithmDataRepository.findByUserIdAndIsUsedIsFalseAndIsDeletedIsFalse(SecurityUtils.getUserId(), pageable);
        List<UserAlgorithmDataFullVO> userAlgorithmDataFullVOList = new ArrayList<>();
        for (AlgorithmDataEntity algorithmDataEntity : algorithmDataEntitys.getContent()) {
            UserAlgorithmDataFullVO userAlgorithmDataFullVO = new UserAlgorithmDataFullVO();
            userAlgorithmDataFullVO.setCreateTime(algorithmDataEntity.getCreateTime());
            userAlgorithmDataFullVO.setSecretHash(algorithmDataEntity.getSecretHash());
            userAlgorithmDataFullVO.setSecretSalt(algorithmDataEntity.getSecretSalt());
            userAlgorithmDataFullVO.setPublicHash(algorithmDataEntity.getPublicHash());
            userAlgorithmDataFullVO.setClientSeed(algorithmDataEntity.getClientSeed());
            userAlgorithmDataFullVO.setRounds(algorithmDataEntity.getRounds());
            userAlgorithmDataFullVOList.add(userAlgorithmDataFullVO);
        }
        return new PageImpl<UserAlgorithmDataFullVO>(userAlgorithmDataFullVOList, algorithmDataEntitys.getPageable(), algorithmDataEntitys.getTotalElements());

    }

    @Override
    public void syncUserpackSkin() {
        for (UserPackageEntity userPackageEntity : userPackageRepository.findAll()) {
            userPackageEntity.setPrice(userPackageEntity.getSkin().getPrice());
            userPackageEntity.setDiamond(userPackageEntity.getSkin().getDiamond());
            userPackageRepository.save(userPackageEntity);
        }
    }

    @Override
    public void idCardValidate(UserVerifyDTO verifyDTO) {
        log.info("用户实名: {}", verifyDTO);
        if (!verifyDTO.validateIdCard()) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.real.name.error"));
        }
        if (!realNameVerifyService.realNameVerify(verifyDTO.getRealName(), verifyDTO.getIdCard())) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.real.name.error"));
        }
        Long userId = SecurityUtils.getUserId();
        log.info("更新用户实名信息: {}, {}", userId, verifyDTO.getRealName());
        UserProfileEntity userProfile = userProfileRepository.findByUserId(userId);
        userProfile.setRealName(verifyDTO.getRealName());
        userProfile.setIdCard(verifyDTO.getIdCard());
        userProfileRepository.save(userProfile);
        UserEntity user = userProfile.getUser();
        user.setIsReal(true);
        userRepository.save(user);
    }


    @Override
    @Transient
    public TokenVO login(String phone, String code, String invite, HttpServletRequest request) {
        String key = redisLoginPrefix + ":" + phone;
        String tempCode = RedisUtils.get("temp_code", String.class);
//        if(!code.equals("123456")){
//            if (!RedisUtils.hasKey("temp_code") || !code.equals(tempCode)) {
//                if (!RedisUtils.hasKey(key) || !RedisUtils.get(key, String.class).equals(code)) {
//                    // todo 国际化
//        throw new CsgoSkinException(I18nUtils.getMessage("exception.verification.code.error"));
//                }
//            }
//        }
        if (!RedisUtils.hasKey("temp_code") || !code.equals(tempCode)) {
            if (!RedisUtils.hasKey(key) || !RedisUtils.get(key, String.class).equals(code)) {
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.verification.code.error"));
            }
        }
        UserEntity user = userRepository.findUserByPhone(phone);
        if (user == null) {
            // 注册
            log.info("用户不存在，注册用户手机号: {}", phone);
            user = new UserEntity();
            user.setPhone(phone);
            user = newUser(invite, request, user);
        }
        return getTokenVO(user);
    }

    @Override
    @Transient
    public TokenVO loginByEmail(String email, String code, String invite, HttpServletRequest request) {
        String key = redisLoginPrefix + ":" + email;
        if (!RedisUtils.hasKey(key) || !RedisUtils.get(key, String.class).equals(code)) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.verification.code.error"));
        }
        UserEntity user = userRepository.findByEmail(email);
        if (user == null) {
            // 注册
            log.info("用户不存在，注册用户email: {}", email);
            user = new UserEntity();
            user.setEmail(email);
            user = newUser(invite, request, user);
        }
        return getTokenVO(user);
    }

    @Override
    public TokenVO loginBySteamId(String steamId, HttpServletRequest request) {
        UserEntity user = userRepository.findBySteamId(steamId);
        if (user == null) {
            // 注册
            log.info("用户不存在，注册用户steam: {}", steamId);
            user = new UserEntity();
            user.setSteamId(steamId);
            user = newUser(null, request, user);
        }
        return getTokenVO(user);
    }

    @Override
    @Transient
    public TokenVO loginByGmail(String authCode, String inviteCode, HttpServletRequest request) {
        // 1. 通过OAuth2授权码获取Gmail用户信息
        // 调用GmailOAuthService完成OAuth2认证流程，获取用户基本信息
        GmailUserInfo gmailUserInfo = gmailOAuthService.getUserInfoByAuthCode(authCode);
        
        // 2. 验证邮箱是否已通过Google验证
        // Google会验证用户邮箱的真实性，只有验证过的邮箱才能用于登录
        // 这是一个重要的安全检查，防止使用未验证的邮箱进行恶意注册
        if (!gmailUserInfo.getVerifiedEmail()) {
            throw new CsgoSkinException("Gmail邮箱未验证");
        }
        
        String email = gmailUserInfo.getEmail();
        log.info("Gmail授权登录，邮箱: {}", email);
        
        // 3. 查找或创建用户账号
        // 首先尝试根据邮箱查找已存在的用户账号
        UserEntity user = userRepository.findByEmail(email);
        if (user == null) {
            // 如果用户不存在，则创建新用户账号（自动注册）
            log.info("用户不存在，注册用户gmail: {}", email);
            user = new UserEntity();
            user.setEmail(email);                           // 设置邮箱
            user.setNickname(gmailUserInfo.getName());      // 使用Google账号名作为默认昵称
            user.setAvatar(gmailUserInfo.getPicture());     // 使用Google头像作为默认头像
            // 调用通用的新用户创建方法，处理邀请码、IP记录等逻辑
            user = newUser(inviteCode, request, user);
        }
        
        // 4. 生成并返回JWT令牌
        // 调用通用的令牌生成方法，完成登录流程
        return getTokenVO(user);
    }

    @NotNull
    private TokenVO getTokenVO(UserEntity user) {
        if (user.getIsBan()) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.user.banned"));
        }
        String token = jwtProvider.createToken(user, null, null);
        UserLogVo vo = new UserLogVo();
        vo.setUserId(user.getId());
        vo.setLoginTime(new Date());
        rabbitTemplate.convertAndSend(exchageName, "userlog.login", JSONObject.toJSON(vo));
        return new TokenVO() {{
            setToken(token);
        }};
    }

    @NotNull
    private UserEntity newUser(String invite, HttpServletRequest request, UserEntity user) {
        user.setNickname(CharUtil.getRandomString(8).toUpperCase());
        user.setAvatar(defaultAvator);
        user.setType(UserType.ACTUAL);
        String logidUrl = request.getHeader("X-HTTP-REFERER");
        String relmName = Utils.regexrelamName(logidUrl);
        OcpcChannelEntity ocpcChannel = ocpcChannelRepository.findTopByRelamName(relmName);
        user.setChannel(ocpcChannel);
        log.info("++++++++++++++++++++++++++++++++++++++++++++++邀请注册{}", invite);
        if (invite != null) {
            UserInviteEntity userInvite = userInviteRepository.findByCode(invite);
            if (userInvite != null) {
                user.setInviterId(userInvite.getUser().getId());
                // 邀请成功，注册+1
                userInvite.setTotalRegister(userInvite.getTotalRegister() + 1);
                userInviteRepository.save(userInvite);
            }
        }
        user = userRepository.save(user);
//            初始化用户算法数据
        AlgorithmDataEntity algorithmDataEntity = new AlgorithmDataEntity();
        algorithmDataEntity.setUser(user);
        algorithmDataEntity.setRounds(1);
        algorithmDataEntity.setSecretHash(CharUtil.getRandomString(128));
        algorithmDataEntity.setSecretSalt(CharUtil.getRandomString(64));
        algorithmDataEntity.setPublicHash(HashUtils.SHA256(algorithmDataEntity.getSecretHash(), algorithmDataEntity.getSecretSalt()));
        algorithmDataEntity.setClientSeed(CharUtil.getRandomString(64));
        algorithmDataEntity.setIsUsed(true);
//            用户钱包等数据
        UserProfileEntity userProfile = new UserProfileEntity();
        userProfile.setUser(user);
        userProfile.setDiamond(BigDecimal.ZERO);
        BigDecimal freeCoin = siteService.getSysUserBenefits().getFreeCoin();
        userProfile.setCoin(freeCoin);
        userProfile.setLavel(1);
        userProfile.setExperience(0);
        // 初始化推广信息
        UserInviteEntity userInviteEntity = new UserInviteEntity();
        userInviteEntity.setUser(user);
        userInviteEntity.setCode(CharUtil.getRandomString(8));
        userInviteEntity.setTotalCharge(BigDecimal.ZERO);
        userInviteEntity.setTotalEncourage(BigDecimal.ZERO);
        userInviteEntity.setTotalRegister(0);
        userInviteEntity.setFreeCase(0);
        // 金币记录
        if (freeCoin.compareTo(BigDecimal.ZERO) != 0) {
            UserCoinRecordEntity userCoinRecordEntity = new UserCoinRecordEntity();
            userCoinRecordEntity.setUser(user);
            userCoinRecordEntity.setSource(UserCoinChangeSource.REGISTER);
            userCoinRecordEntity.setAmount(freeCoin);
            userCoinRecordEntity.setAfterAmount(userProfile.getCoin());
            userCoinRecordEntity.setIsPositive(true);
            userCoinRecordRepository.save(userCoinRecordEntity);
        }
        algorithmDataRepository.save(algorithmDataEntity);
        userProfileRepository.save(userProfile);
        userInviteRepository.save(userInviteEntity);
        // 上传OCPC
        UserEntity finalUser = user;
        OcpcTaskBO ocpcTaskBO = new OcpcTaskBO() {{
            setIp(IPUtils.getIpAddr(request));
            setUserId(finalUser.getId());
            setType(OcpcRabbitmqType.REGISTER);
        }};
        log.info("进入消息队列：ocpc.baidu.register{}", JSONObject.toJSON(ocpcTaskBO));
        rabbitTemplate.convertAndSend(exchageName, "ocpc.baidu.register", JSONObject.toJSON(ocpcTaskBO));
        return user;
    }


    @Override
    public UserInfoVO queryUserInfo() {
        log.info("{}", uidGenerator.getUID());
        Long userId = SecurityUtils.getUserId();
        String userInfoRedisKey = redisUserPrefix + ":" + userId;
        UserInfoVO userInfoVO = RedisUtils.get(userInfoRedisKey, UserInfoVO.class);
//        if (userInfoVO != null) {
//            return userInfoVO;
//        }
        List<AlgorithmDataEntity> algorithmDataEntitys = algorithmDataRepository.findByUserIdAndIsUsed(userId, true);
        if (algorithmDataEntitys.isEmpty() || algorithmDataEntitys.size() > 1) {
            log.error("用户{} 算法数据据异常", userId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.algorithm.data.error"));
        }
        UserProfileEntity userProfile = userProfileRepository.findByUserId(userId);
        if (userProfile == null) {
            log.error("用户{} 数据异常", userId);
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.user.data.error.contact"));
        }
        UserEntity userEntity = getUser();
        userInfoVO = userInfoConverter.toUserInfoVO(userEntity, algorithmDataEntitys.get(0), userProfile);
        userInfoVO.setIsNewUser(!dailyActivityRepository.existsByUserIdAndTypeNot(userId, DailyActivityType.ONLINE));
        Integer totalFreeCase = 5 + queryFreeCase(userId);
        Integer totalDone = caseUserRecordRepository.countByUserIdAndBoxCaseType(userId, CaseType.NOVICE);
        userInfoVO.setTotalFreeCase(totalFreeCase - totalDone < 0 ? 0 : totalFreeCase - totalDone);
        BigDecimal userConsume = userCoinRecordRepository.userDayConsume(userId);
        BigDecimal monthConsume = userCoinRecordRepository.userMonthConsume(userId);
        userConsume = userConsume == null ? BigDecimal.ZERO : userConsume;
        monthConsume = monthConsume == null ? BigDecimal.ZERO : monthConsume;
        userInfoVO.setTodayConsume(userConsume);
        userInfoVO.setMonthConsume(monthConsume);
        userInfoVO.setIsBandingWx(!StringUtil.isNullOrEmpty(userInfoVO.getWxOpenid()));
        userInfoVO.setIsBandingSteam(!StringUtil.isNullOrEmpty(userEntity.getSteamId()));
        RedisUtils.save(userInfoRedisKey, userInfoVO);
        return userInfoVO;
    }

    @Override
    public UserAlgorithmDataVO queryUserAlgorihmData() {
        List<AlgorithmDataEntity> algorithmDataEntitys = algorithmDataRepository.findByUserIdAndIsUsed(SecurityUtils.getUserId(), true);
        if (algorithmDataEntitys.isEmpty() || algorithmDataEntitys.size() > 1) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.algorithm.data.error"));
        }
        return userInfoConverter.toUserAlgorithmDataVO(algorithmDataEntitys.get(0));
    }

    @Override
    public List<UserAlgorithmDataFullVO> queryUserAlgorihmDataFull(Long userId) {
        List<AlgorithmDataEntity> algorithmDataEntitys = algorithmDataRepository.findByUserIdAndIsUsed(SecurityUtils.getUserId(), true);
        List<UserAlgorithmDataFullVO> userAlgorithmDataFullVOList = new ArrayList<>();
        for (AlgorithmDataEntity algorithmDataEntity : algorithmDataEntitys) {
            UserAlgorithmDataFullVO userAlgorithmDataFullVO = new UserAlgorithmDataFullVO();
            if (!algorithmDataEntity.getIsUsed()) {
                userAlgorithmDataFullVO.setSecretHash(algorithmDataEntity.getSecretHash());
                userAlgorithmDataFullVO.setSecretSalt(algorithmDataEntity.getSecretSalt());
            }
            userAlgorithmDataFullVO.setPublicHash(algorithmDataEntity.getPublicHash());
            userAlgorithmDataFullVO.setClientSeed(algorithmDataEntity.getClientSeed());
            userAlgorithmDataFullVO.setRounds(algorithmDataEntity.getRounds());
            userAlgorithmDataFullVOList.add(userAlgorithmDataFullVO);
        }
        return userAlgorithmDataFullVOList;
    }

    @Override
    public UserAlgorithmDataVO resetUserAlgorihmData() {
        List<AlgorithmDataEntity> algorithmDataEntitys = algorithmDataRepository.findByUserIdAndIsUsed(SecurityUtils.getUserId(), true);
        if (!algorithmDataEntitys.isEmpty()) {
            algorithmDataEntitys.stream().peek(algorithmDataEntity -> algorithmDataEntity.setIsUsed(false)).collect(Collectors.toList());
            algorithmDataRepository.saveAll(algorithmDataEntitys);
        }
        AlgorithmDataEntity algorithmDataEntity = new AlgorithmDataEntity();
        algorithmDataEntity.setUser(getUser());
        algorithmDataEntity.setRounds(1);
        algorithmDataEntity.setSecretHash(CharUtil.getRandomString(128));
        algorithmDataEntity.setSecretSalt(CharUtil.getRandomString(64));
        algorithmDataEntity.setPublicHash(HashUtils.SHA256(algorithmDataEntity.getSecretHash(), algorithmDataEntity.getSecretSalt()));
        algorithmDataEntity.setClientSeed(CharUtil.getRandomString(64));
        algorithmDataEntity.setIsUsed(true);
        algorithmDataRepository.save(algorithmDataEntity);
        return userInfoConverter.toUserAlgorithmDataVO(algorithmDataEntity);
    }

    @Override
    public Page<UserPackageVO> queryUserPackageVO(UserPackageQueryParamDTO packageQueryParam) {
        Long userId = SecurityUtils.getUserId();
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        if (packageQueryParam.getPriceAsc() != null && !packageQueryParam.getPriceAsc()) {
            sort = Sort.by(Sort.Direction.DESC, "skinPrice");
        }
        Pageable pageable = PageRequest.of(packageQueryParam.getPage(), packageQueryParam.getSize(), sort);
        Page<UserPackageEntity> userPackageEntities = userPackageRepository.findAll((Specification<UserPackageEntity>) (root, query, cb) -> {
            List<Predicate> ps = new ArrayList<>();
            if (StringUtils.isEmpty(packageQueryParam.getIsReceived()) || !packageQueryParam.getIsReceived()) {
                ps.add(cb.equal(root.get("isReceived"), 0));
            } else {
                ps.add(cb.equal(root.get("isReceived"), 1));
            }
            if (!StringUtils.isEmpty(packageQueryParam.getSource())) {
                ps.add(cb.equal(root.get("source"), packageQueryParam.getSource() - 1));
            }
            ps.add(cb.equal(root.get("isSelled"), 0));
            ps.add(cb.equal(root.get("user"), userId));
            return query.where(ps.toArray(new Predicate[ps.size()])).getRestriction();
        }, pageable);


//        Page<UserPackageEntity> userPackageEntities = null;
//        if(packageQueryParam.getIsReceived()){
//            userPackageEntities = userPackageRepository.findByUserIdAndIsReceivedIsTrue(SecurityUtils.getUserId(), pageable);
//        }else{
//            userPackageEntities = userPackageRepository.findByUserIdAndIsReceivedIsFalseAndIsSelledIsFalse(SecurityUtils.getUserId(), pageable);
//        }
        List<UserPackageVO> userPackageVOS = new ArrayList<>();
        List<SkinEntity> skinEntityList = new ArrayList<>();
        for (UserPackageEntity userPackageEntity : userPackageEntities.getContent()) {
            SkinEntity skin = userPackageEntity.getSkin();
            skinEntityList.add(skin);
            UserPackageVO userPackageVO = new UserPackageVO();
            userPackageVO.setCreateTime(userPackageEntity.getCreateTime());
            userPackageVO.setPackageId(userPackageEntity.getId());
            userPackageVO.setName(skin.getName());
            userPackageVO.setEnglishName(skin.getEnglishName());
//            userPackageVO.setDiamond(skin.getDiamond());
            userPackageVO.setDiamond(userPackageEntity.getDiamond());
            userPackageVO.setPicture(skin.getPicture());
            userPackageVO.setExterior(skin.getExterior());
            userPackageVO.setQuality(skin.getQuality());
            userPackageVO.setRarity(skin.getRarity());
            userPackageVO.setPrototype(skin.getPrototype());
            userPackageVO.setExteriorValue(siteService.getValueByKeyID(skin.getExterior()));
            userPackageVO.setQualityValue(siteService.getValueByKeyID(skin.getQuality()));
            userPackageVO.setRarityValue(siteService.getValueByKeyID(skin.getRarity()));
            userPackageVO.setPrototypeValue(siteService.getValueByKeyID(skin.getPrototype()));
            userPackageVO.setIsLocked(userPackageEntity.getIsLocked());
            userPackageVOS.add(userPackageVO);
        }
//        zbtService.SycnSKinList(skinEntityList);
        return new PageImpl<UserPackageVO>(userPackageVOS, userPackageEntities.getPageable(), userPackageEntities.getTotalElements());
    }

    @Override
    public Page<UserPackageVO> queryOtherUserPackageVO(OtherUserPackageQueryParamDTO packageQueryParam) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of(packageQueryParam.getPage(), packageQueryParam.getSize(), sort);
        Page<UserPackageEntity> userPackageEntities = null;
        if (packageQueryParam.getIsReceived()) {
            userPackageEntities = userPackageRepository.findByUserIdAndIsReceivedIsTrue(SecurityUtils.getUserId(), pageable);
        } else {
            userPackageEntities = userPackageRepository.findByUserIdAndIsReceivedIsFalseAndIsSelledIsFalse(SecurityUtils.getUserId(), pageable);
        }
        List<UserPackageVO> userPackageVOS = new ArrayList<>();
        for (UserPackageEntity userPackageEntity : userPackageEntities.getContent()) {
            SkinEntity skin = userPackageEntity.getSkin();
            UserPackageVO userPackageVO = new UserPackageVO();
            userPackageVO.setCreateTime(userPackageEntity.getCreateTime());
            userPackageVO.setPackageId(userPackageEntity.getId());
            userPackageVO.setName(skin.getName());
            userPackageVO.setEnglishName(skin.getEnglishName());
            userPackageVO.setDiamond(skin.getDiamond());
            userPackageVO.setPicture(skin.getPicture());
            userPackageVO.setRarity(skin.getRarity());
            userPackageVO.setPrototype(skin.getPrototype());
            userPackageVO.setExteriorValue(siteService.getValueByKeyID(skin.getExterior()));
            userPackageVO.setQualityValue(siteService.getValueByKeyID(skin.getQuality()));
            userPackageVO.setRarityValue(siteService.getValueByKeyID(skin.getRarity()));
            userPackageVO.setPrototypeValue(siteService.getValueByKeyID(skin.getPrototype()));
            userPackageVOS.add(userPackageVO);
        }
        return new PageImpl<UserPackageVO>(userPackageVOS, userPackageEntities.getPageable(), userPackageEntities.getTotalElements());
    }

    @Override
    @Transient
    public UserAlgorithmDataVO updateClientSeed(String clientSeed) {
        List<AlgorithmDataEntity> algorithmDataEntitys = algorithmDataRepository.findByUserIdAndIsUsed(SecurityUtils.getUserId(), true);
        if (algorithmDataEntitys.isEmpty() || algorithmDataEntitys.size() > 1) {
            log.error("用户算法数据异常");
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.algorithm.data.error"));
        }
        AlgorithmDataEntity lastAlgorithmData = algorithmDataEntitys.get(0);
        AlgorithmDataEntity algorithmDataEntity = new AlgorithmDataEntity();
        algorithmDataEntity.setUser(getUser());
        algorithmDataEntity.setRounds(1);
        algorithmDataEntity.setSecretHash(lastAlgorithmData.getSecretHash());
        algorithmDataEntity.setSecretSalt(lastAlgorithmData.getSecretSalt());
        algorithmDataEntity.setPublicHash(lastAlgorithmData.getPublicHash());
        algorithmDataEntity.setClientSeed(clientSeed);
        algorithmDataEntity.setIsUsed(true);
        lastAlgorithmData.setIsUsed(false);
        lastAlgorithmData.setIsDeleted(true);
        algorithmDataRepository.save(lastAlgorithmData);
        algorithmDataRepository.save(algorithmDataEntity);
        return userInfoConverter.toUserAlgorithmDataVO(algorithmDataEntity);
    }

    @Override
    public UserPubicInfoVO queryUserPublicInfo(Long userId) {
        String userInfoRedisKey = redisUserPrefix + ":" + userId;
        UserInfoVO userInfoVO = RedisUtils.get(userInfoRedisKey, UserInfoVO.class);
        UserPubicInfoVO userPubicInfoVO = new UserPubicInfoVO();
        if (userInfoVO != null) {
            userPubicInfoVO.setAvatar(userInfoVO.getAvatar());
            userPubicInfoVO.setNickname(userInfoVO.getNickname());
            userPubicInfoVO.setId(userInfoVO.getId());
            userPubicInfoVO.setType(userInfoVO.getType());
            userPubicInfoVO.setIsRobot(userInfoVO.getType().equals(UserType.ROBOT) ? true : false);
            return userPubicInfoVO;
        }
        log.warn("用户信息缓存未命中：{}", userId);
        UserProfileEntity userProfile = userProfileRepository.findByUserId(userId);
        UserEntity user = userProfile.getUser();
        userPubicInfoVO.setId(userId);
        userPubicInfoVO.setAvatar(user.getAvatar());
        userPubicInfoVO.setNickname(user.getNickname());
        userPubicInfoVO.setIsRobot(user.getType().equals(UserType.ROBOT) ? true : false);
        userPubicInfoVO.setType(user.getType().getCode());
        return userPubicInfoVO;
    }
}


