package com.steamgo1.csgoskinapi.service;

import com.steamgo1.csgoskinapi.dto.PageQueryDTO;
import com.steamgo1.csgoskinapi.dto.UserLotteryPercentageDTO;
import com.steamgo1.csgoskinapi.dto.UserPercentageLotteryRecordQueryDTO;
import com.steamgo1.csgoskinapi.vo.PercentageLotteryRecordVO;
import com.steamgo1.csgoskinapi.vo.PercentageResultVO;
import com.steamgo1.csgoskinapi.vo.PercetageUserLotteryRecordVO;
import com.steamgo1.csgoskinapi.vo.UserLotteryResultVO;
import org.springframework.data.domain.Page;

import java.util.List;

public interface PercentageService {
    /**
     * 追梦抽奖
     *
     * @param userLotteryPercentageDTO
     * @return
     */
    PercentageResultVO percentageLottery(UserLotteryPercentageDTO userLotteryPercentageDTO);

    Page<PercetageUserLotteryRecordVO> queryPercetageUserLotteryRecord(UserPercentageLotteryRecordQueryDTO userPercentageLotteryRecordQueryDTO, Long userId);

    Page<PercentageLotteryRecordVO> queryPercentageLotteryRecord(PageQueryDTO pageQueryDTO);

    /**
     * 获取追梦最新10条中奖记录
     */
    List<UserLotteryResultVO> queryPercentageUserLotteryResult();

    /**
     * 机器人追梦
     */
    void robotPercentage(Long robotId);
}
