package com.steamgo1.csgoskinapi.config.jwt;

import cn.hutool.core.util.StrUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTException;
import com.steamgo1.csgoskinapi.utils.IPUtils;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * jwt过滤器
 *
 * <AUTHOR>
 */
@Slf4j
public class JwtFilter extends BasicAuthenticationFilter {


    private final JwtProvider jwtProvider;

    private final String redisPrefixOcpc;

    private final Long redisExpireOcpcLogidUrl;

    public JwtFilter(AuthenticationManager authenticationManager, JwtProvider jwtProvider, String redisPrefixOcpc, Long redisExpireOcpcLogidUrl) {
        super(authenticationManager);
        this.jwtProvider = jwtProvider;
        this.redisPrefixOcpc = redisPrefixOcpc;
        this.redisExpireOcpcLogidUrl = redisExpireOcpcLogidUrl;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws IOException, ServletException {
        if (request.getServletPath().equals("/site/skin/win/top3")) {
            String userIp = IPUtils.getIpAddr(request);
            String key = redisPrefixOcpc + ":logidUrl:" + userIp;
            String logidUrl = request.getHeader("X-HTTP-REFERER");
            log.info("0.0 用户IP: {} 落地页: {}", userIp, logidUrl);
            if (!RedisUtils.hasKey(key)) {
                log.info("用户IP: {} 落地页: {}", userIp, logidUrl);
                log.info("{}", key);
                RedisUtils.save(key, logidUrl, redisExpireOcpcLogidUrl);
            }
        }
        if (request.getServletPath().equals("/auth/login")) {
            chain.doFilter(request, response);
            return;
        }
        String token = null;
        if (request.getServletPath().equals("/websocket")) {
            token = request.getHeader(JwtProvider.WEBSOCKET_TOKEN_HEADER);
            log.info("#### => Header: {} = {}", JwtProvider.WEBSOCKET_TOKEN_HEADER, token);
            if (StrUtil.isEmpty(token)) {
                Map<String, String[]> parameterMap = request.getParameterMap();
                if(parameterMap.containsKey(JwtProvider.TOKEN_HEADER)) {
                    token = parameterMap.get(JwtProvider.TOKEN_HEADER)[0];
                }
                if(parameterMap.containsKey(JwtProvider.TOKEN_HEADER.toLowerCase())) {
                    token = parameterMap.get(JwtProvider.TOKEN_HEADER.toLowerCase())[0];
                }
                log.info("#### => Header: {} = {}", JwtProvider.TOKEN_HEADER, token);
            }
            response.setHeader(JwtProvider.WEBSOCKET_TOKEN_HEADER, token);
        } else {
            String authorization = request.getHeader(JwtProvider.TOKEN_HEADER);
            if (StrUtil.isEmpty(authorization)) {
                Map<String, String[]> parameterMap = request.getParameterMap();
                if(parameterMap.containsKey(JwtProvider.TOKEN_HEADER)) {
                    authorization = parameterMap.get(JwtProvider.TOKEN_HEADER)[0];
                }
                if(parameterMap.containsKey(JwtProvider.TOKEN_HEADER.toLowerCase())) {
                    authorization = parameterMap.get(JwtProvider.TOKEN_HEADER.toLowerCase())[0];
                }
                if (StrUtil.isNotEmpty(authorization)) {
                    authorization = "Bearer " + authorization;
                }
            }
            if (StrUtil.isEmpty(authorization)) {
                chain.doFilter(request, response);
                return;
            }
            Pattern authorizationPattern = Pattern.compile("^Bearer (?<token>[a-zA-Z0-9-:._~+/]+=*)$", Pattern.CASE_INSENSITIVE);
            Matcher matcher = authorizationPattern.matcher(authorization);
            if (matcher.matches()) {
                token = matcher.group("token");
            }
        }

        // 这里如果没有jwt，继续往后走，因为后面还有鉴权管理器等去判断是否拥有身份凭证，所以是可以放行的
        // 注意登录等放行接口请求头不可带token，否则会进来认证
        log.info("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++");
        log.info("前台 url: {} token {}", request.getServletPath(), token);
        log.info("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++");
        if (token == null || token.isEmpty()) {
            chain.doFilter(request, response);
            return;
        }
        JWT jwt = jwtProvider.decodeToken(token);
        if (jwt == null) {
            throw new JWTException("token 异常");
        }
        // 获取角色和权限
        List<GrantedAuthority> authority = this.getAuthority(jwt);
        // 构建UsernamePasswordAuthenticationToken,这里密码为null，是因为提供了正确的JWT,实现自动登录
        UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken = new UsernamePasswordAuthenticationToken(jwt, null, authority);
        SecurityContextHolder.getContext().setAuthentication(usernamePasswordAuthenticationToken);
        chain.doFilter(request, response);
    }

    /**
     * 从token中获取用户权限
     */
    private List<GrantedAuthority> getAuthority(JWT jwt) {
        String authsStr = (String) jwt.getPayload(JwtProvider.AUTHORITY);
        if (!StrUtil.isBlank(authsStr)) {
            // 角色和权限都在这里添加，角色以ROLE_前缀，不是ROLE_前缀的视为权限
            return AuthorityUtils.commaSeparatedStringToAuthorityList(authsStr);
        }
        return null;
    }

}
