package com.steamgo1.csgoskinapi.config.pay.xinfupaybank;

import com.steamgo1.csgoskinapi.service.XinfuPayBankService;
import lombok.AllArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import javax.annotation.Resource;

@Configuration
@ConditionalOnClass(XinfuPayBankProperties.class)
@EnableConfigurationProperties(XinfuPayBankProperties.class)
@AllArgsConstructor
public class XinfuPayBankConfiguration {

    @Resource
    private XinfuPayBankProperties properties;

    @Bean
    @Lazy
    public XinfuPayBankService xinfuPayBankService() {
        XinfuPayBankService service = new XinfuPayBankService();
        service.setMachId(properties.getMachId());
        service.setMachAppId(properties.getMachAppId());
        service.setPaySecret(properties.getPaySecret());
        service.setWithdrawalSecret(properties.getWithdrawalSecret());
        service.setPayReturnUrl(properties.getPayReturnUrl());
        service.setWithdrawalReturnUrl(properties.getWithdrawalReturnUrl());
        return service;
    }

    public XinfuPayBankProperties getProperties() {
        return properties;
    }
}