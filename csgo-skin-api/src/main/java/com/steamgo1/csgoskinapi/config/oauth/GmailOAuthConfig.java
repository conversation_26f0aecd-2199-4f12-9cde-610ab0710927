package com.steamgo1.csgoskinapi.config.oauth;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Gmail OAuth2 配置类
 * 用于管理Gmail OAuth2认证所需的配置参数
 * 
 * 配置参数说明：
 * 1. clientId和clientSecret：在Google Cloud Console中创建OAuth2应用时获得
 * 2. redirectUri：用户授权后的回调地址，必须与Google Console中配置的完全一致
 * 3. tokenUri：Google的令牌交换端点，用于将授权码换取访问令牌
 * 4. userInfoUri：Google的用户信息端点，用于获取用户基本信息
 * 
 * Google Cloud Console配置步骤：
 * 1. 创建项目并启用Google+ API
 * 2. 创建OAuth2客户端ID凭据
 * 3. 配置授权重定向URI
 * 4. 获取客户端ID和密钥
 * 
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "oauth.gmail")
public class GmailOAuthConfig {
    
    /** 
     * Google OAuth2 客户端ID
     * 在Google Cloud Console中创建OAuth2应用时获得的唯一标识符
     * 用于向Google标识我们的应用程序
     */
    private String clientId;
    
    /** 
     * Google OAuth2 客户端密钥
     * 与clientId配对使用，用于验证应用程序的身份
     * 这是敏感信息，应该安全存储，不能暴露给前端
     */
    private String clientSecret;
    
    /** 
     * OAuth2 重定向URI（回调地址）
     * 用户完成授权后，Google会重定向到这个地址并携带授权码
     * 必须与Google Cloud Console中配置的重定向URI完全一致，包括协议、域名、端口、路径
     * 示例：http://localhost:5002/csgo/api/auth/gmail/callback
     */
    private String redirectUri;
    
    /** 
     * Google OAuth2 令牌交换端点
     * 用于将用户授权后获得的授权码换取访问令牌
     * 这是Google提供的标准OAuth2端点，通常不需要修改
     */
    private String tokenUri = "https://oauth2.googleapis.com/token";
    
    /** 
     * Google用户信息获取端点
     * 用于通过访问令牌获取用户的基本信息（邮箱、姓名、头像等）
     * 这是Google提供的标准API端点，通常不需要修改
     */
    private String userInfoUri = "https://www.googleapis.com/oauth2/v2/userinfo";
}