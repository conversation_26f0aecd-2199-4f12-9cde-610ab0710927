package com.steamgo1.csgoskinapi.config.pay.paypal;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * wxpay pay properties.
 *
 * <AUTHOR> Wang
 */
@Data
@ConfigurationProperties(prefix = "pay.paypal")
public class PayPalProperties {

    private String clientId;

    private String clientSecret;

    private String mode;

    private String returnUrl;

    private String cancelUrl;

}
