package com.steamgo1.csgoskinapi.config.pay.paypal;

import com.paypal.base.rest.APIContext;
import com.paypal.base.rest.OAuthTokenCredential;
import com.paypal.base.rest.PayPalRESTException;
import lombok.AllArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Configuration
@ConditionalOnClass(PayPalProperties.class)
@EnableConfigurationProperties(PayPalProperties.class)
@AllArgsConstructor
public class PayPalConfiguration {

    @Resource
    private PayPalProperties properties;

    @Bean
    @Lazy
    public Map<String, String> paypalSdkConfig() {
        Map<String, String> configMap = new HashMap<>();
        configMap.put("mode", properties.getMode());
        return configMap;
    }

    @Bean
    @Lazy
    public OAuthTokenCredential oAuthTokenCredential() {
        return new OAuthTokenCredential(properties.getClientId(), properties.getClientSecret(), paypalSdkConfig());
    }

    @Bean
    @Lazy
    public APIContext apiContext() throws PayPalRESTException {
        APIContext context = new APIContext(oAuthTokenCredential().getAccessToken());
        context.setConfigurationMap(paypalSdkConfig());
        return context;
    }

    public PayPalProperties getProperties() {
        return properties;
    }
}
