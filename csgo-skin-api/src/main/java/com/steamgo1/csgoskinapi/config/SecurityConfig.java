package com.steamgo1.csgoskinapi.config;


import com.steamgo1.csgoskinapi.config.hander.AccessFailure;
import com.steamgo1.csgoskinapi.config.hander.JwtAuthFailure;
import com.steamgo1.csgoskinapi.config.jwt.JwtFilter;
import com.steamgo1.csgoskinapi.config.jwt.JwtProvider;
import com.steamgo1.csgoskincommon.filter.LocaleFilter;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.logout.SimpleUrlLogoutSuccessHandler;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import javax.servlet.Filter;
import java.util.Locale;

/**
 * spring-security权限管理的核心配置
 * `@EnableGlobalMethodSecurity(prePostEnabled = true)` 开启权限注解控制
 * SecurityExpressionRoot 为权限el表达处理类
 *
 * <AUTHOR>
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
@RequiredArgsConstructor
public class SecurityConfig extends WebSecurityConfigurerAdapter {
    /**
     * 白名单
     */
    public final static String[] AUTH_WHITELIST = {
            "/**/swagger-ui/**",
            "/**/swagger-resources/**",
            "/**/webjars/**",
            "/**/v3/**",
            "/**/swagger-ui.html/**",
            "/**/storage/**",
            "/**/auth/login",
            "/**/auth/sms",
            "/**/auth/email/login",
            "/**/auth/email",
            "/**/case/**",
            "/**/market/**",
            "/**/test/**",
            "/**/site/**",
            "/**/websocket/**",
            "/**/order/wechat/notify",
            "/**/order/ali/notify",
            "/**/order/boxpay/notify",
            "/**/order/epay/notify",
            "/**/order/boxpay/notify",
            "/**/order/xinfuPay/notify",
            "/**/order/xinfuPay/withdrawal/notify",
            "/**/order/boxpay/return",
            "/**/auth/steam/login",
            "/**/auth/steam/callback",
            "/**/auth/gmail/**",
            "/**/wx/portal/**",
            "/**/schedule/**",
//            "/**/test/zbt/skin/sync"
            "/**/test/**"
    };
    private final SimpleUrlLogoutSuccessHandler logoutSuccess;
    private final JwtAuthFailure authFailure;
    private final AccessFailure accessFailure;
    private final JwtProvider jwtProvider;
    @Value("${spring.redis.prefix.ocpc}")
    private String redisPrefixOcpc;
    @Value("${spring.redis.expire.ocpc-logid-url}")
    private Long redisExpireOcpcLogidUrl;

    @Bean
    public LocaleResolver localeResolver() {
        SessionLocaleResolver slr = new SessionLocaleResolver();
        slr.setDefaultLocale(Locale.SIMPLIFIED_CHINESE);
        return slr;
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        // 创建 LocaleFilter 实例
        Filter localeFilter = new LocaleFilter(localeResolver());
        // 创建 JwtFilter 实例
        JwtFilter jwtFilter = new JwtFilter(authenticationManager(), jwtProvider, redisPrefixOcpc, redisExpireOcpcLogidUrl);
        http
                .addFilterBefore(localeFilter, JwtFilter.class)
                .addFilterBefore(jwtFilter, BasicAuthenticationFilter.class)
                .logout()
                .logoutSuccessHandler(logoutSuccess)
                // 禁用session
                .and()
                .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                // 配置拦截规则
                .and()
                .authorizeRequests()
                .antMatchers(AUTH_WHITELIST).permitAll()
                .anyRequest().authenticated()
                // 异常处理器
                .and()
                .exceptionHandling()
                // jwt认证失败
                .authenticationEntryPoint(authFailure)
                // 拒绝访问
                .accessDeniedHandler(accessFailure)
                // 配置自定义的过滤器
                .and()
                // 验证码过滤器放在UsernamePassword过滤器之前
                // .addFilterBefore(captchaFilter, UsernamePasswordAuthenticationFilter.class)
                //.addFilterBefore(localeFilter(localeResolver()), JwtFilter.class)
                // 关闭跨域
                .cors().and().csrf().disable();
    }


    @Override
    public void configure(WebSecurity web) {
        // 配置静态文件不需要认证
        web.ignoring().antMatchers("/static/**");
    }

    /**
     * 指定加密方式
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

}
