package com.steamgo1.csgoskinapi.config.hander;

import com.steamgo1.csgoskinapi.utils.IPUtils;
import com.steamgo1.csgoskincommon.contant.CsgoContants;
import com.steamgo1.csgoskincommon.dao.IpBlackRepository;
import com.steamgo1.csgoskincommon.dao.RequestLogRepository;
import com.steamgo1.csgoskincommon.entity.IpBlackEntity;
import com.steamgo1.csgoskincommon.entity.RequestLogEntity;
import com.steamgo1.csgoskincommon.entity.enums.Status;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.transaction.Transactional;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Aspect
@Component
@Slf4j
public class ControllerAspect {

    @Autowired
    private RequestLogRepository requestLogRepository;

    @Autowired
    private IpBlackRepository ipBlackRepository;

    @Value("${spring.redis.prefix.black}")
    private String blackPrefix;

    /**
     * 切入点表达式配置
     * 匹配所有Controller包下的所有方法
     */
    @Pointcut("execution(* com.steamgo1.csgoskinapi.controller..*.*(..))")
    public void controllerPointcut() {
    }

    /**
     * 前置通知：在Controller方法执行前执行
     */
    @Before("controllerPointcut()")
    @Transactional
    public void beforeControllerMethod(JoinPoint joinPoint) {
        try {
            // 获取请求对象
            ServletRequestAttributes attributes =
                    (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();

            String ip = IPUtils.getIpAddr(request);
            String key = blackPrefix + ":loginId:" + ip;
            if (RedisUtils.hasKey(key)) {
                //throw new CsgoSkinException("请求失败！请求次数过多！请联系客服！");
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.too.many.requests"));
            }

            // 记录请求信息
            RequestLogEntity requestLog = new RequestLogEntity();
            requestLog.setRequestPath(request.getRequestURI());
            requestLog.setRequestMethod(request.getMethod());
            requestLog.setRequestSignMethod(joinPoint.getSignature().toShortString());
            requestLog.setRequestParam(Arrays.toString(joinPoint.getArgs()));
            requestLog.setRequestIp(ip);
            requestLogRepository.save(requestLog);


            //删除30天前的日志
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, -30);
            Date endTime = calendar.getTime();
            requestLogRepository.deleteByCreateTimeBefore(endTime);

            //是否存在黑名单
            List<IpBlackEntity> ipBlackEntities = ipBlackRepository.findByIpAndStatus(requestLog.getRequestIp(), Status.OPEN);
            if (ipBlackEntities != null && !ipBlackEntities.isEmpty()) {
                RedisUtils.save(key, ipBlackEntities.get(0), CsgoContants.redisTimeout.REDIS_TIMEOUT_1H);
                // todo 国际化
                throw new CsgoSkinException(I18nUtils.getMessage("exception.too.many.requests"));
            }
        } catch (CsgoSkinException e) {
            // todo 国际化
            throw new CsgoSkinException(I18nUtils.getMessage("exception.too.many.requests"));
        } catch (Exception e) {
            log.error("beforeControllerMethod,{}", e.getMessage());
        }
    }
}
