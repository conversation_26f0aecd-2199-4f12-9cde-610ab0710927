package com.steamgo1.csgoskinapi.config.hander;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskincommon.enums.ResponseCode;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


/**
 * 拒绝访问，无权限
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AccessFailure implements AccessDeniedHandler {

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException e) throws IOException {
        log.info("拒绝访问，无权限");
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write(JSONObject.toJSONString(ResponseUtil.fail(ResponseCode.HTTP_STATUS_401)));
    }
}
