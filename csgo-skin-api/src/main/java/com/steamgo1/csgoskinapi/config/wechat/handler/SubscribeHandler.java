package com.steamgo1.csgoskinapi.config.wechat.handler;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinapi.config.wechat.builder.TextBuilder;
import com.steamgo1.csgoskinapi.service.AsyncTaskService;
import com.steamgo1.csgoskinapi.vo.BindWxVO;
import com.steamgo1.csgoskincommon.dao.UserRepository;
import com.steamgo1.csgoskincommon.entity.UserEntity;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageMethod;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageType;
import com.steamgo1.csgoskincommon.vo.websocket.MessageVO;
import io.netty.util.internal.StringUtil;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@Component
@Lazy
public class SubscribeHandler extends AbstractHandler {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public WxMpXmlOutMessage handle(WxMpXmlMessage wxMessage,
                                    Map<String, Object> context, WxMpService weixinService,
                                    WxSessionManager sessionManager) throws WxErrorException {

        this.logger.info("新关注用户 OPENID: " + wxMessage.getFromUser());
        AsyncTaskService asyncTaskService = null;
        String[] beanNames = applicationContext.getBeanNamesForType(AsyncTaskService.class);
        for (String beanName : beanNames) {
            System.out.println(beanName);
            asyncTaskService = applicationContext.getBean(beanName, AsyncTaskService.class);
        }
        // 获取微信用户基本信息
        try {
            WxMpUser userWxInfo = weixinService.getUserService()
                    .userInfo(wxMessage.getFromUser(), null);
            if (userWxInfo != null) {
                String qrSceneStr = userWxInfo.getQrSceneStr();
                if (!StringUtil.isNullOrEmpty(qrSceneStr)) {
                    Long userId = Long.valueOf(qrSceneStr);
                    String openId = userWxInfo.getOpenId();
                    logger.info("用户： {}, 扫码： {}扫码加入公众号", userId, openId);
                    BindWxVO bindWxVO = new BindWxVO();
                    if (userRepository.existsByWxOpenid(openId)) {
                        logger.error("该微信号已绑定");
                        bindWxVO.setIsSuccess(false);
                        bindWxVO.setMessage("该微信号已绑定");
                    } else {
                        UserEntity user = userRepository.findById(userId);
                        if (user == null) {
                            logger.error("用户不存在");
                            return null;
                        } else {
                            bindWxVO.setIsSuccess(true);
                            bindWxVO.setMessage("");
                            user.setWxOpenid(openId);
                            userRepository.save(user);
                        }
                    }
                    MessageVO messageResultVO = new MessageVO(WebSocketMessageMethod.SEND_ONE, WebSocketMessageType.BIND_WX, bindWxVO);
                    asyncTaskService.sendWebSocketMessageToOne(userId, JSONObject.toJSONString(messageResultVO));
                }
            }
        } catch (WxErrorException e) {
            if (e.getError().getErrorCode() == 48001) {
                this.logger.info("该公众号没有获取用户信息权限！");
            }
        }


        WxMpXmlOutMessage responseResult = null;
        try {
            responseResult = this.handleSpecial(wxMessage);
        } catch (Exception e) {
            this.logger.error(e.getMessage(), e);
        }

        if (responseResult != null) {
            return responseResult;
        }

        try {
            return new TextBuilder().build("感谢关注", wxMessage, weixinService);
        } catch (Exception e) {
            this.logger.error(e.getMessage(), e);
        }

        return null;
    }

    /**
     * 处理特殊请求，比如如果是扫码进来的，可以做相应处理
     */
    private WxMpXmlOutMessage handleSpecial(WxMpXmlMessage wxMessage) throws Exception {
        //TODO
        return null;
    }

}
