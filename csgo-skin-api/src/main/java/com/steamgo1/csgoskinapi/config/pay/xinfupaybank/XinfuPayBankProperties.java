package com.steamgo1.csgoskinapi.config.pay.xinfupaybank;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * XinfuPayBank pay properties.
 *
 * <AUTHOR> Wang
 */
@Data
@ConfigurationProperties(prefix = "pay.xinfupaybank")
public class XinfuPayBankProperties {

    /**
     * 商户号
     */
    private String machId;

    /**
     * 商户appId
     */
    private String machAppId;

    /**
     * 支付key
     */
    private String paySecret;

    /**
     * 代付key
     */
    private String withdrawalSecret;

    /**
     * 支付回调地址
     */
    private String payReturnUrl;

    /**
     * 代付回调地址
     */
    private String withdrawalReturnUrl;

}