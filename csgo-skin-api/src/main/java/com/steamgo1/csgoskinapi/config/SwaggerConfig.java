package com.steamgo1.csgoskinapi.config;

import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;
import springfox.documentation.builders.*;
import springfox.documentation.oas.annotations.EnableOpenApi;
import springfox.documentation.schema.Annotations;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.*;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.schema.ModelPropertyBuilderPlugin;
import springfox.documentation.spi.schema.contexts.ModelPropertyContext;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * swagger配置
 *
 * <AUTHOR>
 * @vesion 1.0
 * @date 2020/08/28
 * @since jdk1.8
 */
@Configuration
@EnableOpenApi
@Slf4j
//@ConditionalOnProperty(name = "spring.profiles.active", havingValue = "prod", matchIfMissing = true)
public class SwaggerConfig implements ModelPropertyBuilderPlugin {
    @Value("${swagger.enable: true}")
    private boolean swaggerEnable;
    @Value("${swagger.host}")
    private String host;
    @Value("${swagger.paths}")
    private String paths;
    private String version = "1.0";

    @Bean
    public Docket defaultApi() {

        List<Parameter> parameters = new ArrayList<>();
        parameters.add(new ParameterBuilder()
                .name("Accept-Language")
                .description("Bearer token")
                .modelRef(new ModelRef("string"))
                .parameterType("header")
                .required(false)
                .build());


        return new Docket(DocumentationType.OAS_30)
                .host(host)
                .pathMapping(paths)
                .groupName("默认")
                .apiInfo(defaultApiInfo())
                .enable(swaggerEnable)
                .securitySchemes(Arrays.asList(tokenScheme()))
                .securityContexts(Arrays.asList(tokenContext()))
                .select()
                .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
                .paths(PathSelectors.any())
                .build()
                .globalOperationParameters(parameters);
                /*.forCodeGeneration(true)*/

    }

    private ApiInfo defaultApiInfo() {
        return new ApiInfoBuilder()
                .title("csgo-skin-接口文档")
                .description("用户端接口")
                //服务条款网址
                .version(version)
                .build();
    }

    private HttpAuthenticationScheme tokenScheme() {
        return HttpAuthenticationScheme.JWT_BEARER_BUILDER.name("Authorization").build();
    }


    private SecurityContext tokenContext() {
        return SecurityContext.builder()
                .securityReferences(Arrays.asList(SecurityReference.builder()
                        .scopes(new AuthorizationScope[0])
                        .reference("Authorization")
                        .build()))
                .operationSelector(o -> o.requestMappingPattern().matches("/.*"))
                .build();
    }

    @Override
    public void apply(ModelPropertyContext context) {
        //为枚举字段设置注释
        descForEnumFields(context);
    }

    @Override
    public boolean supports(DocumentationType documentationType) {
        return true;
    }

    /**
     * 为枚举字段设置注释
     */
    private void descForEnumFields(ModelPropertyContext context) {
        Optional<ApiModelProperty> annotation = Optional.empty();

        // 找到 @ApiModelProperty 注解修饰的枚举类
        if (context.getBeanPropertyDefinition().isPresent()) {
            annotation = Annotations.findPropertyAnnotation(context.getBeanPropertyDefinition().get(), ApiModelProperty.class);
        }

        //没有@ApiModelProperty 或者 notes 属性没有值，直接返回
        if (!annotation.isPresent() || StringUtils.isEmpty((annotation.get()).notes())) {
            return;
        }
        //@ApiModelProperties中的notes指定的class类型
        Class rawPrimaryType;
        try {
            rawPrimaryType = Class.forName((annotation.get()).notes());
        } catch (ClassNotFoundException e) {
            //如果指定的类型无法转化，直接忽略
            return;
        }

        Object[] subItemRecords = null;
        // 判断 rawPrimaryType 是否为枚举，且实现了 SwaggerDisplayEnum 接口
        if (Enum.class.isAssignableFrom(rawPrimaryType) && CodeValueBaseEnum.class.isAssignableFrom(rawPrimaryType)) {
            // 拿到枚举的所有的值
            subItemRecords = rawPrimaryType.getEnumConstants();
        } else {
            return;
        }

        final List<String> displayValues = Arrays.stream(subItemRecords).filter(Objects::nonNull)
                // 调用枚举类的 description 方法
                .map(p -> ((CodeValueBaseEnum) p).description()).filter(Objects::nonNull).collect(Collectors.toList());

        String joinText = " (" + String.join("; ", displayValues) + ")";
        try {
            // 拿到字段上原先的描述
            Field mField = PropertySpecificationBuilder.class.getDeclaredField("description");
            mField.setAccessible(true);
            // context 中的 builder 对象保存了字段的信息
            joinText = mField.get(context.getSpecificationBuilder()) + joinText;
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        // 设置新的字段说明并且设置字段类型
        context.getSpecificationBuilder().description(joinText);
    }
}

