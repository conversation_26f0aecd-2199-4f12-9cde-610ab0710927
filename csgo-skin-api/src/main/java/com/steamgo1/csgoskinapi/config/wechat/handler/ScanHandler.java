package com.steamgo1.csgoskinapi.config.wechat.handler;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinapi.service.AsyncTaskService;
import com.steamgo1.csgoskinapi.vo.BindWxVO;
import com.steamgo1.csgoskincommon.dao.UserRepository;
import com.steamgo1.csgoskincommon.entity.UserEntity;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageMethod;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageType;
import com.steamgo1.csgoskincommon.vo.websocket.MessageVO;
import io.netty.util.internal.StringUtil;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@Component
public class ScanHandler extends AbstractHandler {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public WxMpXmlOutMessage handle(WxMpXmlMessage wxMpXmlMessage, Map<String, Object> map,
                                    WxMpService wxMpService, WxSessionManager wxSessionManager) throws WxErrorException {
        // 扫码事件处理
        AsyncTaskService asyncTaskService = null;
        String[] beanNames = applicationContext.getBeanNamesForType(AsyncTaskService.class);
        for (String beanName : beanNames) {
            System.out.println(beanName);
            asyncTaskService = applicationContext.getBean(beanName, AsyncTaskService.class);
        }
        String openId = wxMpXmlMessage.getFromUser();
        String eventKey = wxMpXmlMessage.getEventKey();
        if (!StringUtil.isNullOrEmpty(eventKey)) {
            Long userId = Long.valueOf(eventKey);
            logger.info("用户： {}, 扫码： {}扫码加入公众号", userId, openId);
            BindWxVO bindWxVO = new BindWxVO();
            if (userRepository.existsByWxOpenid(openId)) {
                logger.error("该微信号已绑定");
                bindWxVO.setIsSuccess(false);
                bindWxVO.setMessage("该微信号已绑定");
            } else {
                UserEntity user = userRepository.findById(userId);
                if (user == null) {
                    logger.error("用户不存在");
                    return null;
                } else {
                    bindWxVO.setIsSuccess(true);
                    bindWxVO.setMessage("");
                    user.setWxOpenid(openId);
                    userRepository.save(user);
                }
            }
            MessageVO messageResultVO = new MessageVO(WebSocketMessageMethod.SEND_ONE, WebSocketMessageType.BIND_WX, bindWxVO);
            asyncTaskService.sendWebSocketMessageToOne(userId, JSONObject.toJSONString(messageResultVO));
        }
        return null;
    }
}
