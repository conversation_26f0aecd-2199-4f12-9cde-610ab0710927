package com.steamgo1.csgoskinapi.config.pay.ali;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * wxpay pay properties.
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "pay.ali")
public class AliPayProperties {
    /**
     * appid
     */
    private String appId;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 私钥
     */
    private String privateKey;

    private String notifyUrl;
    private String returnUrl;

    private String signType;
    private String charset;
    private String gatewayUrl;
}