package com.steamgo1.csgoskinapi.config.websocket;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinapi.service.UserService;
import com.steamgo1.csgoskinapi.utils.SecurityUtils;
import com.steamgo1.csgoskincommon.entity.enums.DailyActivityType;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageMethod;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageType;
import com.steamgo1.csgoskincommon.utils.Json;
import com.steamgo1.csgoskincommon.utils.SpringContextUtils;
import com.steamgo1.csgoskincommon.vo.websocket.MessageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

@Component
@Slf4j
@ServerEndpoint("/websocket")  // 接口路径 ws://localhost:8087/webSocket/userId;
public class WebSocket {

    private static UserService userService = SpringContextUtils.getBean(UserService.class); //关键点2
    //concurrent包的线程安全Set，用来存放每个客户端对应的MyWebSocket对象。
    //虽然@Component默认是单例模式的，但springboot还是会为每个websocket连接初始化一个bean，所以可以用一个静态set保存起来。
    //  注：底下WebSocket是当前类名
    private static CopyOnWriteArraySet<WebSocket> webSockets = new CopyOnWriteArraySet<>();
    // 用来存在线连接用户信息
    private static ConcurrentHashMap<Long, Session> sessionPool = new ConcurrentHashMap<Long, Session>();
    //与某个客户端的连接会话，需要通过它来给客户端发送数据
    private Session session;
    /**
     * 用户ID
     */
    private Long userId;

    @PostConstruct
    public void init() {
        log.info("WebSocket端点已初始化: /websocket");
    }

    /**
     * 链接成功调用的方法
     */
    /*@OnOpen
    public void onOpen(Session session, @PathParam(value="userId") Long userId) {
        try {
            this.session = session;
            this.userId = userId;
            webSockets.add(this);
            sessionPool.put(userId, session);
            log.info("【websocket消息】有新的连接，总数为:"+webSockets.size());
        } catch (Exception e) {
        }
    }*/

    /**
     * 链接成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session) {
        try {
            log.info("WebSocket连接开始建立，sessionId: {}", session.getId());
            
            userId = SecurityUtils.getUserId(session);
            log.info("websocket 登录用户： {}", userId);
            
            this.session = session;
            if (userId != null) {
                sessionPool.put(userId, session);
                userService.DailyActivity(userId, DailyActivityType.ONLINE);
            } else {
                log.warn("WebSocket连接用户ID为空，可能是匿名用户");
            }
            
            webSockets.add(this);
            log.info("【websocket消息】有新的连接，总数为:" + webSockets.size());
            
        } catch (Exception e) {
            log.error("WebSocket连接失败: sessionId={}, error={}", session.getId(), e.getMessage(), e);
            try {
                session.close();
            } catch (Exception closeException) {
                log.error("关闭WebSocket连接失败", closeException);
            }
        }
    }


    /**
     * 链接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        try {
            webSockets.remove(this);
            if (this.userId != null) {
                sessionPool.remove(this.userId);
            }
            log.info("【websocket消息】连接断开，总数为:" + webSockets.size());
        } catch (Exception e) {
        }
    }

    /**
     * 收到客户端消息后调用的方法
     *
     * @param message
     */
    @OnMessage
    public void onMessage(String message) {
        log.info("【websocket消息】收到客户端消息:" + message);
    }

    /**
     * 发送错误时的处理
     *
     * @param session
     * @param error
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("WebSocket错误: sessionId={}, userId={}, 原因:{}", 
            session.getId(), this.userId, error.getMessage(), error);
        
        // 清理连接
        try {
            webSockets.remove(this);
            if (this.userId != null) {
                sessionPool.remove(this.userId);
            }
        } catch (Exception e) {
            log.error("清理WebSocket连接失败", e);
        }
    }


    // 此为广播消息
    public void sendAllMessage(MessageVO messageVO) {
        String message = Json.toJsonString(messageVO);
        log.info("【websocket消息】广播消息:" + message);
        for (WebSocket webSocket : webSockets) {
            try {
                if (webSocket.session.isOpen()) {
                    webSocket.session.getAsyncRemote().sendText(message);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void sendAllMessage(String message) {
//        log.info("【websocket消息】广播消息:"+ message);
        for (WebSocket webSocket : webSockets) {
            try {
                if (webSocket.session.isOpen()) {
                    webSocket.session.getAsyncRemote().sendText(message);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    // 此为单点消息
    public void sendOneMessage(Long userId, String message) {
        Session session = sessionPool.get(userId);
        log.info("发送： {}", userId);
        if (session != null && session.isOpen()) {
            try {
                log.info("【websocket消息】 单点消息:" + message);
                session.getAsyncRemote().sendText(message);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    // 此为单点消息(多人)
    public void sendMoreMessage(String[] userIds, String message) {
        for (String userId : userIds) {
            Session session = sessionPool.get(userId);
            if (session != null && session.isOpen()) {
                try {
                    log.info("【websocket消息】 单点消息:" + message);
                    session.getAsyncRemote().sendText(message);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

    }

    /**
     * 服务端群发消息-心跳包
     *
     * @return int
     * <AUTHOR> Ruichuan
     * @date 2022/9/24 10:54
     */
    public int sendPing() {
        if (webSockets.size() <= 0) {
            return 0;
        }
        MessageVO messageVO = new MessageVO(WebSocketMessageMethod.SEND_ALL, WebSocketMessageType.PING, System.currentTimeMillis());
        String message = JSONObject.toJSONString(messageVO);
        webSockets.forEach(item -> {
            try {
                if (item.session.isOpen()) {
                    item.session.getAsyncRemote().sendText(message);
                }
            } catch (Exception e) {
                webSockets.remove(item);
                if (item.userId != null) {
                    sessionPool.remove(item.userId);
                }
                e.printStackTrace();
            }
        });
        return webSockets.size();
    }

}
