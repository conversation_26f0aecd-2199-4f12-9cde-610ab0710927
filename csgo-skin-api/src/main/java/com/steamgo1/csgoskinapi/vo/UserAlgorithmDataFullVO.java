package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("用户算法数据")
public class UserAlgorithmDataFullVO {
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("私密哈希")
    private String secretHash = "重置算法数据后公开";

    @ApiModelProperty("私密盐值")
    private String secretSalt = "重置算法数据后公开";

    @ApiModelProperty("公共哈希")
    private String publicHash;

    @ApiModelProperty("回合数")
    private Integer rounds;

    @ApiModelProperty("用户种子")
    private String clientSeed;
}
