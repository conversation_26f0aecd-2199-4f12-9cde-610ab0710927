package com.steamgo1.csgoskinapi.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("对战房查询条件")
public class BattleHomeQueryParamVO {

    @ApiModelProperty("状态")
    List<Status> statusList;

    @ApiModelProperty("模式")
    List<Method> methodList;

    @Data
    public static class Status {
        @ApiModelProperty("标识码")
        Integer code;
        @ApiModelProperty("值")
        String value;
    }

    @Data
    public static class Method {
        @ApiModelProperty("标识码")
        Integer code;
        @ApiModelProperty("值")
        String value;
    }

}
