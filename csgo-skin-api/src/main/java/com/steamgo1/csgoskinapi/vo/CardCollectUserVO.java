package com.steamgo1.csgoskinapi.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel("集卡用户卡片")
public class CardCollectUserVO {
    @ApiModelProperty("卡片ID")
    @JsonIgnore
    private Long id;

    @ApiModelProperty("卡片名")
    private String name;

    @ApiModelProperty("卡片图片")
    private String picture;

    @ApiModelProperty("数量")
    private Integer total;
}
