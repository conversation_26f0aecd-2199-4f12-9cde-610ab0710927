package com.steamgo1.csgoskinapi.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("用户公共数据")
public class UserPubicInfoVO {
    @ApiModelProperty("用户ID")
    private Long id;

    @ApiModelProperty("昵称")
    private String nickname;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("是否机器人")
    @JsonIgnore
    private Boolean isRobot;

    @ApiModelProperty("用户类型")
    private Integer type;

}
