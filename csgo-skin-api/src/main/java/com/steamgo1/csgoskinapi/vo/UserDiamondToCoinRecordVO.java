package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("用户钻石兑换记录")
public class UserDiamondToCoinRecordVO {
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("变更时间")
    private Date updateTime;

    @ApiModelProperty("消耗钻石总额")
    private BigDecimal diamondAmount;

    @ApiModelProperty("兑换金币总额")
    private BigDecimal coinAmount;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("状态值")
    private String statusValue;
}
