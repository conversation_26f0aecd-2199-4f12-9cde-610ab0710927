package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("用户金币流水")
public class UserCoinRecordVO {
    @ApiModelProperty("变更时间")
    private Date time;
    @ApiModelProperty("变更来源")
    private Integer sourceCode;

    @ApiModelProperty("变更来源值")
    private String sourceValue;

    @ApiModelProperty("是否正增长")
    private Boolean isPositive;

    @ApiModelProperty("变更金额")
    private BigDecimal amount;

    @ApiModelProperty("变更后余额")
    private BigDecimal afterAmount;
}
