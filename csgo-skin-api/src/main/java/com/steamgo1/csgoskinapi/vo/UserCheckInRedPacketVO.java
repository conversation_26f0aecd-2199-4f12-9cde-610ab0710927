package com.steamgo1.csgoskinapi.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("用户日常签到红包")
public class UserCheckInRedPacketVO {
    @ApiModelProperty("用户当月签到次数")
    private Integer current;

    @ApiModelProperty("今日是否签到")
    private Boolean isCheckIn;

    @ApiModelProperty("详情")
    private List<CheckInRedPacketDetail> checkInRedPacketDetailList;

    @Data
    public static class CheckInRedPacketDetail {
        @ApiModelProperty("ID")
        private Long id;

        @ApiModelProperty("红包名称")
        private String name;

        @ApiModelProperty("领取阈值")
        private Integer threshold;

        @ApiModelProperty("状态0不可领取,1可领取,2已领取")
        private Integer status;
    }

}
