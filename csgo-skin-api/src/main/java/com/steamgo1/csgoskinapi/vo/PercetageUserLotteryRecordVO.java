package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("用户追梦记录")
public class PercetageUserLotteryRecordVO {

    @ApiModelProperty("目标饰品信息")
    private SkinInfoVO skinInfo;

    @ApiModelProperty("最小Roll点")
    private Integer minRoll;

    @ApiModelProperty("最大Roll点")
    private Integer maxRoll;

    @ApiModelProperty("算法数据")
    private UserAlgorithmDataFullVO userAlgorithmData;

    @ApiModelProperty("roll点")
    private Integer roll;

    @ApiModelProperty("本轮算法回合数")
    private Integer rounds;

    @ApiModelProperty("是否中奖")
    private Boolean isWin = false;
}
