package com.steamgo1.csgoskinapi.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("对战房信息")
public class BattleHomeBaseInfoVO {
    @ApiModelProperty("房间ID")
    private Long id;

    @ApiModelProperty("模式")
    private Integer method;

    @ApiModelProperty("模式名")
    private String methodName;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("状态名")
    private String statusName;

    @ApiModelProperty("人数")
    private Integer totalPlayer;

    @ApiModelProperty("箱子信息")
    private List<CaseInfoVO> caseInfoList;

    @ApiModelProperty("参与人员")
    private List<UserInfo> userList;

    @ApiModelProperty("总合数")
    private Integer roundsTotal;

    @ApiModelProperty("当前合数")
    private Integer rounds;


    @Data
    public static class UserInfo {
        @ApiModelProperty("用户信息")
        UserPubicInfoVO user;

        @ApiModelProperty("是否房主")
        Boolean isOwner;

        @ApiModelProperty("是否胜利")
        Boolean isWin;
    }
}
