package com.steamgo1.csgoskinapi.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@ApiModel("Roll房基本信息")
public class RollHomeBaseInfoVO {
    @ApiModelProperty("房间ID")
    private Long id;

    @ApiModelProperty("房间名称")
    private String name;

    @ApiModelProperty("房间名称（多语言）")
    private I18nField i18nFieldName;

    @ApiModelProperty("房间类型")
    private Integer rollHomeType;

    @ApiModelProperty("房间类型描述")
    private String rollHomeTypeValue;


    @ApiModelProperty("开奖方式")
    private Integer rollHomeLotteryMethod;

    @ApiModelProperty("开奖方式描述")
    private String rollHomeLotteryMethodValue;

    @ApiModelProperty("是否需要密码")
    private Boolean password;

    @ApiModelProperty("开奖时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date lotteryTime;

    @ApiModelProperty("参入阈值")
    private BigDecimal consumeThreshold;

    @ApiModelProperty("参入人数")
    private Integer totalParticipants;

    @ApiModelProperty("最大人数")
    private Integer maxPeople;

    @ApiModelProperty("饰品数量")
    private Integer totalSkin;

    @ApiModelProperty("Roll房备注")
    private String remarks;
    
    @ApiModelProperty("备注")
    private I18nField i18nFieldRemarks;

//    @ApiModelProperty("饰品信息")
//    private List<SkinInfoVO> skinInfo;

    @ApiModelProperty("饰品信息前三")
    private List<SkinInfoVO> skinInfo;


    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("状态描述")
    private String statusValue;

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getName() {
        return I18nUtils.getI18nFieldValue(i18nFieldName, I18nUtils.getCurrentLanguageEnum());
    }

    /*
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getRemarks() {
        return I18nUtils.getI18nFieldValue(i18nFieldRemarks, I18nUtils.getCurrentLanguageEnum());
    }
}
