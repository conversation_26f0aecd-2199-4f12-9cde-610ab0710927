package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("单个箱子开箱记录")
public class CaseUserLotteryRecordOfCaseVO {
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("用户信息")
    private UserPubicInfoVO userInfo;


    @ApiModelProperty("中奖饰品信息")
    private SkinInfoVO skinInfo;

    @ApiModelProperty("roll点")
    private Integer roll;
}
