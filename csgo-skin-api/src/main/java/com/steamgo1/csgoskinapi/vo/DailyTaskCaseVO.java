package com.steamgo1.csgoskinapi.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;

import java.math.BigDecimal;

@Data
@ApiModel("日常任务箱子")
public class DailyTaskCaseVO {

    @ApiModelProperty("箱子详情")
    private CaseFullInfoVO caseInfo;

    @ApiModelProperty("任务名")
    private String name;

    @ApiModelProperty("状态(0：充值未达标 1：消费未达标 2：可开启 3：已开启)")
    private Integer status;

    @ApiModelProperty("差额")
    private BigDecimal shortfall = BigDecimal.ZERO;


    public enum Status {
        CHARGE_SUBSTANDARD(0, "充值未达标"),
        CONSUME_SUBSTANDARD(1, "消费未达标"),
        UNOPENED(2, "未开启"),
        OPENED(3, "已开启");

        @Getter
        private Integer code;
        @Getter
        private String type;

        Status(Integer code, String type) {
            this.code = code;
            this.type = type;
        }
    }

}
