package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("用户当局Battle结果")
public class BattleLotterResultVO {
    @ApiModelProperty("房间ID")
    private Long battleHomeId;

    @ApiModelProperty("箱子信息")
    private CaseInfoVO caseInfo;

    @ApiModelProperty("总合数")
    private Integer roundsTotal;

    @ApiModelProperty("当前合数")
    private Integer rounds;

    @ApiModelProperty("参与人员本局结果")
    private List<BattleLotterResultVO.LotteryResult> lotteryResult;

    @Data
    public static class LotteryResult {
        @ApiModelProperty("用户信息")
        UserPubicInfoVO user;

        @ApiModelProperty("Roll点")
        Integer roll;

        @ApiModelProperty("获得的饰品")
        SkinInfoVO skinInfo;
    }
}
