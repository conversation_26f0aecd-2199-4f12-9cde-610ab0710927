package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
@ApiModel("饰品掉落来源")
public class UserPackageSkinSourceVO {
    @ApiModelProperty("来源")
    List<UserPackageSkinSourceVO.Source> sourceList;

    @Data
    public static class Source {
        @ApiModelProperty("标识码")
        Integer code;
        @ApiModelProperty("值")
        String value;
    }
}
