package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("汇率表")
public class SysExchangeRateVO {
    @ApiModelProperty("金币兑人民币")
    private BigDecimal coinTocny;

    @ApiModelProperty("金币兑钻石")
    private BigDecimal coinToDiamond;

    @ApiModelProperty("扎比特兑人民币")
    private BigDecimal zbtToCny;

    @ApiModelProperty("扎比特到人民币溢价")
    private BigDecimal zbtToCnyPremium;

    @ApiModelProperty("追梦平台溢价")
    private BigDecimal percentagePremium;
}
