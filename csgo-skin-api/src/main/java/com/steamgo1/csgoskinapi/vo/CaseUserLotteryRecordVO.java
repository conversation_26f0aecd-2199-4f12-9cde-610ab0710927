package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("用户开箱记录")
public class CaseUserLotteryRecordVO {

    @ApiModelProperty("箱子信息")
    private CaseFullInfoVO caseInfo;

    @ApiModelProperty("中奖饰品信息")
    private SkinInfoVO skinInfo;

    @ApiModelProperty("算法数据")
    private UserAlgorithmDataFullVO userAlgorithmData;

    @ApiModelProperty("roll点")
    private Integer roll;

    @ApiModelProperty("本轮算法回合数")
    private Integer rounds;
}
