package com.steamgo1.csgoskinapi.vo;

import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("箱子信息")
public class CaseInfoVO {
    @ApiModelProperty("箱子ID")
    private Long id;
    /**
     * 新手或者老手
     */
    @ApiModelProperty("箱子类型 1 新手箱子 2 普通箱子")
    private String type;

    @ApiModelProperty("箱子名（多语言）")
    private I18nField i18nFieldName;

    @ApiModelProperty("箱子名")
    private String name;

    @ApiModelProperty("箱子价格")
    private BigDecimal price;

    @ApiModelProperty("箱子前景图")
    private String foregroundPicture;

    @ApiModelProperty("箱子背景图")
    private String backgroundPicture;

    @Column(name = "skin_total")
    @ApiModelProperty("箱子包含皮肤数量")
    private Integer skinTotal;

    @Column(name = "probability")
    @ApiModelProperty("箱子总概率")
    private BigDecimal probability;

    @Column(name = "probability_overview")
    @ApiModelProperty("箱子分布详情")
    private List<BigDecimal> probabilityOverview;

    @ApiModelProperty("箱子类别名")
    private String category;

    @ApiModelProperty("箱子优先级(用作排序，越大越靠前)")
    private Integer gradle;

    @Column(name = "is_recommend")
    @ApiModelProperty("箱子是否推荐")
    private Boolean isRecommend;

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getName() {
        return I18nUtils.getI18nFieldValue(i18nFieldName, I18nUtils.getCurrentLanguageEnum());
    }

}
