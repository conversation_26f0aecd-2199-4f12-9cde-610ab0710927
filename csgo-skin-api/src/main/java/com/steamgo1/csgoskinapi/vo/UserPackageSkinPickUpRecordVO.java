package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("用户饰品取回记录")
public class UserPackageSkinPickUpRecordVO {
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("变更时间")
    private Date updateTime;

    @ApiModelProperty("饰品名")
    private String skinName;

    @ApiModelProperty("饰品图片")
    private String skinPicture;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("状态值")
    private String statusValue;

    @ApiModelProperty("备注")
    private String remarks;
}
