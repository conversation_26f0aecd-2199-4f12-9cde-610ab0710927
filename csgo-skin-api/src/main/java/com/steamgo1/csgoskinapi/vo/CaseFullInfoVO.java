package com.steamgo1.csgoskinapi.vo;

import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel("箱子里面的奖品信息")
public class CaseFullInfoVO {
    @ApiModelProperty("箱子ID")
    private Long id;
    /**
     * 新手或者老手
     */
    @ApiModelProperty("箱子类型 1 新手箱子 2 普通箱子 3 寻宝箱子")
    private Integer type;

    @ApiModelProperty("箱子类型 1 新手箱子 2 普通箱子")
    private String typeName;

    @ApiModelProperty("箱子名（多语言）")
    private I18nField i18nFieldName;

    @ApiModelProperty("箱子名")
    private String name;

    @ApiModelProperty("箱子价格")
    private BigDecimal price;

    @ApiModelProperty("箱子前景图")
    private String foregroundPicture;

    @ApiModelProperty("箱子背景图")
    private String backgroundPicture;


    @ApiModelProperty("箱子包含皮肤数量")
    private Integer skinTotal;

    @ApiModelProperty("箱子总概率")
    private BigDecimal probability;

    @ApiModelProperty("箱子类别")
    private Long categoryId;

    @ApiModelProperty("箱子类别名")
    private String categoryName;

    @ApiModelProperty("箱子优先级(用作排序，越大越靠前)")
    private Integer gradle;

    @ApiModelProperty("箱子中饰品信息")
    private List<CaseSkinVO> skins = new ArrayList<>();

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getName() {
        return I18nUtils.getI18nFieldValue(i18nFieldName, I18nUtils.getCurrentLanguageEnum());
    }

    //    @ApiModel("箱子中饰品信息")
    @Data
    public static class CaseSkinVO {
        @ApiModelProperty("ID")
        private Long caseSkinId;

        @ApiModelProperty("饰品信息")
        private SkinInfoVO skinInfo;
        @ApiModelProperty("中奖概率")
        private BigDecimal probability;

        @ApiModelProperty("饰品颜色")
        private Integer color;

        @ApiModelProperty("最小Roll点")
        private Integer minRoll;
        @ApiModelProperty("最大Roll点")
        private Integer maxRoll;

    }
}
