package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("追梦记录")
public class PercentageLotteryRecordVO {
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("用户信息")
    private UserPubicInfoVO userInfo;

    @ApiModelProperty("中奖饰品信息")
    private SkinInfoVO skinInfo;

    @ApiModelProperty("最大roll点")
    private Integer maxRoll;

    @ApiModelProperty("最小roll点")
    private Integer minRoll;

    @ApiModelProperty("roll点")
    private Integer roll;

    @ApiModelProperty("是否成功")
    private Boolean isWin;
}
