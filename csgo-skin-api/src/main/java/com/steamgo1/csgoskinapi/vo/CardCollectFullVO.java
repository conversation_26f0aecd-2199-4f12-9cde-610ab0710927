package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


@Data
@ApiModel("集卡")
public class CardCollectFullVO {
    @ApiModelProperty("活动ID")
    private Long id;
    @ApiModelProperty("活动名")
    private String name;

    @ApiModelProperty("按钮图")
    private String buttonPicture;

    @ApiModelProperty("背景图")
    private String backgroundPicture;

    @ApiModelProperty("详情")
    private String details;

    @ApiModelProperty("当前集齐人数")
    private Integer totalPeople;


    @ApiModelProperty("卡片信息")
    private List<CardCollectCard> cardList;

    @Data
    public static class CardCollectCard {
        @ApiModelProperty("ID")
        private Long id;

        @ApiModelProperty("卡片名")
        private String name;

        @ApiModelProperty("卡片图片")
        private String picture;

        @ApiModelProperty("掉落概率")
        private BigDecimal probability;

        @ApiModelProperty("最小Roll点")
        private Integer minRoll;

        @ApiModelProperty("最大Roll点")
        private Integer maxRoll;
    }
}
