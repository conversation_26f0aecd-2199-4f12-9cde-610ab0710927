package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("用户背包数据")
public class UserPackageVO {

    @ApiModelProperty("获取时间")
    private Date createTime;

    @ApiModelProperty("背包ID")
    private Long packageId;
    @ApiModelProperty("饰品名称")
    private String name;

    @ApiModelProperty("饰品唯一英文")
    private String englishName;

    @ApiModelProperty("饰品图片")
    private String picture;

    @ApiModelProperty("价格")
    private BigDecimal diamond;

    @ApiModelProperty("原型")
    private Long prototype;

    @ApiModelProperty("原型名")
    private String prototypeValue;

    @ApiModelProperty("稀有度")
    private Long rarity;

    @ApiModelProperty("稀有度名")
    private String rarityValue;

    @ApiModelProperty("质量")
    private Long quality;

    @ApiModelProperty("质量名")
    private String qualityValue;

    @ApiModelProperty("外观")
    private Long exterior;

    @ApiModelProperty("外观")
    private String exteriorValue;

    @ApiModelProperty("是否锁定")
    private Boolean isLocked;
}
