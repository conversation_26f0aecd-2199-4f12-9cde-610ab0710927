package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("用户当前算法数据")
public class UserAlgorithmDataVO {
    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("公共哈希")
    private String publicHash;

    @ApiModelProperty("回合数")
    private Integer rounds;

    @ApiModelProperty("用户种子")
    private String clientSeed;
}
