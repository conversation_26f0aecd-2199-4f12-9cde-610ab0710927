package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("用户开箱结果")
public class CaseLotterResultVO {
    @ApiModelProperty("Roll点")
    private Integer roll;

    @ApiModelProperty("抽中饰品")
    private SkinInfoVO skinInfoVO;

    @ApiModelProperty("背包ID")
    private Long packageId;

    @ApiModelProperty("额外卡片")
    private CardVO card;
}
