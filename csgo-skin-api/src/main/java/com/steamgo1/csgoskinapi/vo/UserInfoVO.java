package com.steamgo1.csgoskinapi.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("用户信息")
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserInfoVO {
    @ApiModelProperty("用户ID")
    private Long id;

    @ApiModelProperty("昵称")
    private String nickname;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("微信ID")
    @JsonIgnore
    private String wxOpenid;

    @ApiModelProperty("是否绑定微信")
//    @JsonIgnore
    private Boolean isBandingWx;

    @ApiModelProperty("是否实名")
    private Boolean isReal;

    @ApiModelProperty("steam交易链接")
    private String tradeOfferAccessUrl;

    @ApiModelProperty("钻石")
    private BigDecimal diamond;

    @ApiModelProperty("金币")
    private BigDecimal coin;

    @ApiModelProperty("等级")
    @JsonIgnore
    private Integer lavel;

    @ApiModelProperty("经验")
    @JsonIgnore
    private Integer experience;

    @ApiModelProperty("公共哈希")
    private String publicHash;

    @ApiModelProperty("回合数")
    @JsonIgnore
    private Integer rounds;

    @ApiModelProperty("用户种子")
    @JsonIgnore
    private String clientSeed;

    @ApiModelProperty("用户类型")
    private Integer type;

    @ApiModelProperty("用户类型")
    @JsonIgnore
    private String typeValue;


    @ApiModelProperty("是否新用户")
    private Boolean isNewUser;

    @ApiModelProperty("剩余宝箱开启次数")
    private Integer totalFreeCase;

    @ApiModelProperty("今日消耗")
    private BigDecimal todayConsume;

    @ApiModelProperty("当月消耗")
    private BigDecimal monthConsume;

    @ApiModelProperty("绑定steam")
    private Boolean isBandingSteam;


}
