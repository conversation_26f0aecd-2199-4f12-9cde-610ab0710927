package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel("用户购买金币创建订单")
@Data
public class OrderChargeCreateVO {
    @ApiModelProperty("付款金额")
    private BigDecimal amount;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("二维码链接")
    private String payUrl;

}
