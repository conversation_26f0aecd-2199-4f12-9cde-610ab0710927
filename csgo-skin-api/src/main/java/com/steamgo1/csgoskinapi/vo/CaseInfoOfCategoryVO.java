package com.steamgo1.csgoskinapi.vo;

import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("箱子信息(按分类)")
public class CaseInfoOfCategoryVO {
    @ApiModelProperty("箱子分类ID")
    private Long categoryId;
    @ApiModelProperty("箱子分类名")
    private String name;
    @ApiModelProperty("字段名（多语言）")
    private I18nField i18nFieldName;

    @ApiModelProperty("箱子信息")
    private List<CaseInfoVO> caseInfo;

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getName() {
        return I18nUtils.getI18nFieldValue(i18nFieldName, I18nUtils.getCurrentLanguageEnum());
    }
}
