package com.steamgo1.csgoskinapi.vo;

import com.steamgo1.csgoskincommon.entity.enums.PayType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("支付渠道")
public class PayChannelVO {
    
    @ApiModelProperty("主键")
    private Long id;
    
    @ApiModelProperty("名字")
    private String name;
    
    @ApiModelProperty("类型")
    private PayType payType;
    
    @ApiModelProperty("汇率")
    private BigDecimal exchangeRate;
    
    @ApiModelProperty("状态（1=可用,0=禁用）")
    private Integer status;
    
    @ApiModelProperty("图标")
    private String image;
}