package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


@Data
@ApiModel("用户推广信息")
public class UserInviteVO {
    @ApiModelProperty("邀请码")
    private String code;

    @ApiModelProperty("推广链接")
    private String url;


    @ApiModelProperty("注册用户")
    private Integer totalRegister;

//    @ApiModelProperty("充值总额")
//    private BigDecimal totalCharge;

    @ApiModelProperty("奖励总额")
    private BigDecimal totalEncourage;

    @ApiModelProperty("免费宝箱额外开箱次数")
    private Integer freeCase;
}
