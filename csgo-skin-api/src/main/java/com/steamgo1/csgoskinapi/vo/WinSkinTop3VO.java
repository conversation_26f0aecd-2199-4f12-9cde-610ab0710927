package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel("历史最高价值饰品top3")
public class WinSkinTop3VO {
    @ApiModelProperty("用户消息")
    private UserPubicInfoVO userInfo;

    @ApiModelProperty("饰品消息")
    private SkinInfoVO skinInfoVO;

    @ApiModelProperty("来源")
    private Integer source;

    @ApiModelProperty("箱子ID")
    private Long boxId;
}
