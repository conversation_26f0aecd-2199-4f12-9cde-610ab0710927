package com.steamgo1.csgoskinapi.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


@Data
@ApiModel("集卡")
public class CardCollectVO {
    @ApiModelProperty("活动ID")
    private Long id;
    @ApiModelProperty("活动名")
    private String name;

    @ApiModelProperty("按钮图")
    private String buttonPicture;

    @ApiModelProperty("背景图")
    private String backgroundPicture;

    @ApiModelProperty("详情")
    private String details;

    @ApiModelProperty("卡片信息")
    private List<CardCollectCard> cardList;

    @ApiModelProperty("当前集齐人数")
    private Integer totalPeople;

    @Data
    public static class CardCollectCard {
        @ApiModelProperty("ID")
        @JsonIgnore
        private Long id;

        @ApiModelProperty("卡片名")
        private String name;

        @ApiModelProperty("卡片图片")
        private String picture;

        @ApiModelProperty("掉落概率")
        @JsonIgnore
        private BigDecimal probability;

        @ApiModelProperty("最小Roll点")
        @JsonIgnore
        private Integer minRoll;

        @ApiModelProperty("最大Roll点")
        @JsonIgnore
        private Integer maxRoll;
    }
}
