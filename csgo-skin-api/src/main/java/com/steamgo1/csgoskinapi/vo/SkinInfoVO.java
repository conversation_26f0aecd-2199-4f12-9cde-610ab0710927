package com.steamgo1.csgoskinapi.vo;

import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("饰品信息")
public class SkinInfoVO {
    @ApiModelProperty("饰品ID")
    private Long id;

    @ApiModelProperty("饰品名称（多语言）")
    private I18nField i18nFieldName;

    @ApiModelProperty("饰品名称")
    private String name;

    @ApiModelProperty("饰品唯一英文")
    private String englishName;

    @ApiModelProperty("短名称（多语言）")
    private I18nField i18nFieldShortName;

    @ApiModelProperty("短名称，去掉前缀")
    private String shortName;

    @ApiModelProperty("饰品图片")
    private String picture;


    @ApiModelProperty("原型")
    private Long prototype;

    @ApiModelProperty("原型名")
    private String prototypeName;

    @ApiModelProperty("原型名（多语言）")
    private I18nField i18nFieldPrototypeName;

    @ApiModelProperty("饰品csgo类型")
    private Long csgoType;

    @ApiModelProperty("饰品csgo类型名")
    private String csgoTypeName;

    @ApiModelProperty("饰品csgo类型名（多语言）")
    private I18nField i18nFieldCsgoTypeName;

    @ApiModelProperty("稀有度")
    private Long rarity;

    @ApiModelProperty("稀有度名")
    private String rarityName;

    @ApiModelProperty("稀有度颜色")
    private String rarityColor;

    @ApiModelProperty("质量")
    private Long quality;

    @ApiModelProperty("质量颜色")
    private String qualityColor;

    @ApiModelProperty("质量名")
    private String qualityName;

    @ApiModelProperty("外观")
    private Long exterior;

    @ApiModelProperty("外观名")
    private String exteriorName;

    @ApiModelProperty("在售数量")
    private Integer quantity;

    @ApiModelProperty("在售最低价")
    private BigDecimal price;

    @ApiModelProperty("自动发货在售数量")
    private Integer autoDeliverQuantity;

    @ApiModelProperty("自动发货在售最低价")
    private BigDecimal autoDeliverPrice;

    @ApiModelProperty("人工发货在售数量")
    private Integer manualQuantity;

    @ApiModelProperty("人工发货在售最低价")
    private BigDecimal manualDeliverPrice;

    @ApiModelProperty("价值钻石")
    private BigDecimal diamond;

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getName() {
        return I18nUtils.getI18nFieldValue(i18nFieldName, I18nUtils.getCurrentLanguageEnum());
    }

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getShortName() {
        return I18nUtils.getI18nFieldValue(i18nFieldShortName, I18nUtils.getCurrentLanguageEnum());
    }

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getPrototypeName() {
        return I18nUtils.getI18nFieldValue(i18nFieldPrototypeName, I18nUtils.getCurrentLanguageEnum());
    }
    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getCsgoTypeName() {
        return I18nUtils.getI18nFieldValue(i18nFieldCsgoTypeName, I18nUtils.getCurrentLanguageEnum());
    }
}
