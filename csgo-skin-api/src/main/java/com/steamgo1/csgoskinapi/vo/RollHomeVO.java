package com.steamgo1.csgoskinapi.vo;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("Roll房信息")
public class RollHomeVO {

    @ApiModelProperty("roll房饰品")
    List<RollHomeSkinVO> rollHomeSkin;
    @ApiModelProperty("roll房用户")
    List<RollHomeUserVO> rollHomeUser;
    @ApiModelProperty("房间ID")
    private Long id;
    @ApiModelProperty("房间名称")
    private String name;
    @ApiModelProperty("房间名称（多语言）")
    private I18nField i18nFieldName;
    @ApiModelProperty("房间类型")
    private Integer rollHomeType;
    @ApiModelProperty("房间类型")
    private String rollHomeTypeName;
    @ApiModelProperty("开奖方式")
    private Integer rollHomeLotteryMethod;
    @ApiModelProperty("开奖方式")
    private String rollHomeLotteryMethodName;
    @ApiModelProperty("是否需要密码")
    private Boolean password;
    @ApiModelProperty("最高人数")
    private Integer maxPeople;
    @ApiModelProperty("开奖时间")
    private Date lotteryTime;
    @ApiModelProperty("参入阈值")
    private BigDecimal consumeThreshold;
    @ApiModelProperty("Roll房备注")
    private String remarks;
    @ApiModelProperty("Roll房备注（多语言）")
    private I18nField i18nFieldRemarks;
    @Column(name = "secret_hash")
    @JsonIgnore
    private String secretHash;
    @Column(name = "secret_salt")
    @JsonIgnore
    private String secretSalt;
    @Column(name = "public_hash")
    @JsonIgnore
    private String publicHash;
    @Column(name = "client_seed", length = 128)
    @JsonIgnore
    private String clientSeed;
    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("状态描述")
    private String statusValue;

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getName() {
        return I18nUtils.getI18nFieldValue(i18nFieldName, I18nUtils.getCurrentLanguageEnum());
    }

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getRemarks() {
        return I18nUtils.getI18nFieldValue(i18nFieldRemarks, I18nUtils.getCurrentLanguageEnum());
    }
}
