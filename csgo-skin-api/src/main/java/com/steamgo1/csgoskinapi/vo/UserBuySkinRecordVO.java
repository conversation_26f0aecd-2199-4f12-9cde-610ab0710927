package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("用户商场购买饰品记录")
public class UserBuySkinRecordVO {
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("变更时间")
    private Date updateTime;
    @ApiModelProperty("饰品")
    private SkinInfoVO skinInfo;
    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("状态值")
    private String statusValue;
}
