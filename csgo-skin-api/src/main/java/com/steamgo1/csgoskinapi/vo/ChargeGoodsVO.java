package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class ChargeGoodsVO {
    @ApiModelProperty("商品ID")
    private Long id;
    @ApiModelProperty("金额")
    private BigDecimal price;

    @ApiModelProperty("金币数量")
    private BigDecimal coin;

    @ApiModelProperty("背景图片")
    private String backgroundUrl;

    @ApiModelProperty("首冲赠送")
    private BigDecimal firstChargeFreeCoin;

    @ApiModelProperty("仅新用户可买")
    private Boolean isNewUser;
}
