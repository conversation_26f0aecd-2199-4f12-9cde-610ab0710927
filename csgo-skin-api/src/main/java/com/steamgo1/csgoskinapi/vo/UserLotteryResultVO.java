package com.steamgo1.csgoskinapi.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("用户中奖结果")
public class UserLotteryResultVO {
    @ApiModelProperty("用户消息")
    private UserPubicInfoVO userInfo;

    @ApiModelProperty("饰品消息")
    private SkinInfoVO skinInfoVO;

    @ApiModelProperty("来源")
    private Integer source;

    @ApiModelProperty("箱子")
    private Long boxId;
}
