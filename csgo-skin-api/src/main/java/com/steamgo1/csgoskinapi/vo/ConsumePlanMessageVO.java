package com.steamgo1.csgoskinapi.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("消费计划")
public class ConsumePlanMessageVO {
    //    获得的免费次数
    @ApiModelProperty("获取的免费次数")
    private Integer total = 0;
    @ApiModelProperty("再消耗X次可以获取")
    private BigDecimal reConsume = BigDecimal.ZERO;
    @ApiModelProperty("再消耗X次可以获取的次数")
    private Integer reTotal = 0;
}
