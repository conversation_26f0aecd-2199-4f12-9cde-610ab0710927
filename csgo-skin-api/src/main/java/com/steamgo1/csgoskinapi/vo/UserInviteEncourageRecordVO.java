package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("用户邀请奖励记录")
public class UserInviteEncourageRecordVO {
    @ApiModelProperty("充值时间")
    private Date createTime;
    @ApiModelProperty("充值金币")
    private BigDecimal chargeCoin;

    @ApiModelProperty("奖励金币")
    private BigDecimal encourageCoin;

    @ApiModelProperty("是否首充")
    private Boolean isFirstCharge;
}
