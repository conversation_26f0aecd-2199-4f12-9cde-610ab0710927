package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("用户百分比抽奖结果")
public class PercentageResultVO {
    @ApiModelProperty("最终roll点")
    private Integer roll;

    @ApiModelProperty("是否中")
    private Boolean isWin = false;

    @ApiModelProperty("饰品")
    private SkinInfoVO skinInfo;

    @ApiModelProperty("背包ID")
    private Long packageId;

    @ApiModelProperty("额外卡片")
    private CardVO card;
}
