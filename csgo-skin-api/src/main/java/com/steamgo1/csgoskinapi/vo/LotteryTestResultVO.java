package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("单次抽奖测试")
public class LotteryTestResultVO {
//    @Column(name = "secret_hash" )
//    @ApiModelProperty("私密哈希")
//    private String secretHash;
//
//    @Column(name = "secret_salt" )
//    @ApiModelProperty("私密盐值")
//    private String secretSalt;

    @ApiModelProperty("公共哈希")
    private String publicHash;

    @ApiModelProperty("回合数")
    private Integer rounds;

    @ApiModelProperty("用户种子")
    private String clientSeed;


    @ApiModelProperty("最终rool点")
    private Integer roll;
}
