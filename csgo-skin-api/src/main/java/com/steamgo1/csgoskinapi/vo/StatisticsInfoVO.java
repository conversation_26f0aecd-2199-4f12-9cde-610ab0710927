package com.steamgo1.csgoskinapi.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
@Api(tags = "统计数据")
public class StatisticsInfoVO {
    @ApiModelProperty("在线用户数")
    private Integer onlineCount;

    @ApiModelProperty("总对战")
    private long battleCount;

    @ApiModelProperty("总开箱")
    private long openCaseCount;

    @ApiModelProperty("总追梦")
    private long percentageCount;
}
