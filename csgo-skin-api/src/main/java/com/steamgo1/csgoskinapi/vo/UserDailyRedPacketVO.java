package com.steamgo1.csgoskinapi.vo;


import com.steamgo1.csgoskincommon.converter.I18nFieldConverter;
import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Convert;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("用户日常充值红包")
public class UserDailyRedPacketVO {

    @ApiModelProperty("用户当前充值")
    private BigDecimal current;


    @ApiModelProperty("详情")
    private List<DailyRedPacketDetail> dailyRedPacketDetailList;


    @Data
    public static class DailyRedPacketDetail {
        @ApiModelProperty("ID")
        private Long id;

        @ApiModelProperty("红包名称")
        private String name;

        @ApiModelProperty("红包名称（多语言）")
        private I18nField i18nFieldName;

        @ApiModelProperty("金币数量")
        private BigDecimal coin;

        @ApiModelProperty("领取阈值")
        private BigDecimal threshold;

        @ApiModelProperty("状态0不可领取,1可领取,2已领取")
        private Integer status;

        /**
         * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
         * 使用LanguageEnum枚举进行语言判断
         */
        public String getName() {
            return I18nUtils.getI18nFieldValue(i18nFieldName, I18nUtils.getCurrentLanguageEnum());
        }
    }

}
