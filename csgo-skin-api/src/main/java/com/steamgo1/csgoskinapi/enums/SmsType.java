package com.steamgo1.csgoskinapi.enums;

public enum SmsType {

    /**
     * 发送验证码
     */
    VALID(0, "SMS_243493192", "您的验证码为${code}，请勿把验证码泄漏给第三方。");

    private Integer num;

    private String templateCode;

    private String content;

    SmsType(Integer num, String templateCode, String content) {
        this.num = num;
        this.templateCode = templateCode;
        this.content = content;
    }

    public static SmsType instance(Integer value) {
        SmsType[] enums = values();
        for (SmsType statusEnum : enums) {
            if (statusEnum.value().equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }

    public Integer value() {
        return num;
    }

    public String getTemplateCode() {
        return this.templateCode;
    }

    public String getContent() {
        return this.content;
    }
}
