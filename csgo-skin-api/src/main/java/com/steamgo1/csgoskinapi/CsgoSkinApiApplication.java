package com.steamgo1.csgoskinapi;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;

import java.net.InetAddress;

@Slf4j
@EnableAsync    //开启异步注解功能
@SpringBootApplication
@EntityScan(basePackages = {"com.steamgo1.csgoskincommon.entity"})
@EnableJpaRepositories(basePackages = {"com.steamgo1.csgoskincommon.dao"})
@ComponentScan(basePackages = {"com.steamgo1.csgoskincommon", "com.steamgo1.csgoskinapi"})
public class CsgoSkinApiApplication implements ApplicationRunner {
    @Value("${server.port}")
    private String port;

    public static void main(String[] args) {
        SpringApplication.run(CsgoSkinApiApplication.class, args);
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        String ip = InetAddress.getLocalHost().getHostAddress();
        log.info("启动完成");
        log.info("swagger3访问地址：http://{}:{}/csgo/api/swagger-ui/index.html#/", "127.0.0.1", port);
        log.info("swagger3访问地址：http://{}:{}/csgo/api/swagger-ui/index.html#/", ip, port);
    }
}
