package com.steamgo1.csgoskinapi.utils;

import org.springframework.util.DigestUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

public class XinfuPayBankSignUtils {

    /**
     * 生成签名
     *
     * @param sourceMap 参数map
     * @param key       商户密钥
     * @return 添加签名后的参数map
     */
    public static Map<String, String> signMap(Map<String, String> sourceMap, String key) {
        String str = generateSignString(sourceMap, key);
        String md5code = DigestUtils.md5DigestAsHex(str.getBytes());
        HashMap<String, String> newMap = new HashMap<>(sourceMap);
        newMap.put("sign", md5code);
        return newMap;
    }

    /**
     * 将Map中的key按Ascii码进行升序排序，拼接成 key1=val1&key2=val2&key3=val3....&key=密钥 格式
     */
    public static String generateSignString(Map<String, String> sourceMap, String key) {
        StringBuilder sb = new StringBuilder();
        Map<String, String> treeMap = new TreeMap<>(String::compareTo);
        if (sourceMap != null) {
            treeMap.putAll(sourceMap);
            for (Map.Entry<String, String> entry : treeMap.entrySet()) {
                if (entry.getValue() != null && !entry.getValue().isEmpty()) {
                    sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
                }
            }
        }
        sb.append("key=").append(key);
        return sb.toString();
    }
}