package com.steamgo1.csgoskinapi.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.jwt.JWT;
import com.steamgo1.csgoskinapi.config.jwt.JwtProvider;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import javax.servlet.http.HttpServletRequest;
import javax.websocket.Session;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 安全框架工具类封装
 *
 * <AUTHOR>
 */
public class SecurityUtils {

    private static final BCryptPasswordEncoder PASSWORD_ENCODER = new BCryptPasswordEncoder();

    /**
     * 获取登录者的信息
     */
    public static JWT getInfo() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated()) {
            return (JWT) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        }
        // throw new CsgoSkinException("未登录");
        throw new CsgoSkinException(I18nUtils.getMessage("exception.security.not.logged.in"));
    }

    /**
     * 获取登录者的id
     */
    public static Long getUserId() {
        JWT info = getInfo();
        return Long.valueOf(String.valueOf(info.getPayload("userId")));
    }

    /**
     * 获取登录者的id
     */
    public static Long getUserId(Session session) {
        Authentication authentication = (Authentication) session.getUserPrincipal();
        if (authentication != null && authentication.isAuthenticated()) {
            JWT info = (JWT) authentication.getPrincipal();
            return Long.valueOf(String.valueOf(info.getPayload("userId")));
        }
        return null;

    }

    public static Boolean isAnonymousUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return authentication.getPrincipal().toString().contains("anonymousUser");
    }

    /**
     * 获取登录者的权限
     */
    public static String getAuths() {
        return (String) getInfo().getPayload(JwtProvider.AUTHORITY);
    }

    /**
     * 密码加密
     *
     * @param password 明文密码
     * @return 加密后的密码
     */
    public static String passwordEncoder(String password) {
        return PASSWORD_ENCODER.encode(password);
    }

    /**
     * 密码比对
     *
     * @param rawPassword     明文密码
     * @param encodedPassword 加密后的密码
     * @return 是否通过
     */
    public static boolean passwordMatches(CharSequence rawPassword, String encodedPassword) {
        return PASSWORD_ENCODER.matches(rawPassword, encodedPassword);
    }


    public static void main(String[] args) {
        System.out.println(SecurityUtils.passwordEncoder("yuanfang"));
    }


    public static String getToken(HttpServletRequest request) {
        String authorization = request.getHeader(JwtProvider.TOKEN_HEADER);
        if (StrUtil.isEmpty(authorization)) {
            Map<String, String[]> parameterMap = request.getParameterMap();
            if(parameterMap.containsKey(JwtProvider.TOKEN_HEADER)) {
                authorization = parameterMap.get(JwtProvider.TOKEN_HEADER)[0];
            }
            if(parameterMap.containsKey(JwtProvider.TOKEN_HEADER.toLowerCase())) {
                authorization = parameterMap.get(JwtProvider.TOKEN_HEADER.toLowerCase())[0];
            }
            if (StrUtil.isNotEmpty(authorization)) {
                authorization = "Bearer " + authorization;
            }
        }
        Pattern authorizationPattern = Pattern.compile("^Bearer (?<token>[a-zA-Z0-9-:._~+/]+=*)$", Pattern.CASE_INSENSITIVE);
        Matcher matcher = authorizationPattern.matcher(authorization);
        if (matcher.matches()) {
            return matcher.group("token");
        }
        return null;
    }
}
