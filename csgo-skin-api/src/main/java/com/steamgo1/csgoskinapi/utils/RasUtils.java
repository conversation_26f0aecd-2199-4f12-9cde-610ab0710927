package com.steamgo1.csgoskinapi.utils;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

public class RasUtils {

    // 公钥
    public static final String PUBLIC_KEY = "";
    // 密钥
    public static final String PRIVATE_KEY = "";


    public static void main(String[] args) throws Exception {

        // 使用RAS加解密
        String encrypt = rsaEncryptByPublicKey("使用RAS加密的内容", PUBLIC_KEY);
        System.out.println(encrypt);
        String decrypt = rsaDecryptByPrivateKey(encrypt, PRIVATE_KEY);
        System.out.println(decrypt);


        // 生成签名
        String sign = rsaSignByPrivateKey("生成数字签名", PRIVATE_KEY);
        System.out.println(sign);

        // 校验签名
        verifySign("生成数字签名", sign, PUBLIC_KEY);
    }

    /**
     * 使用RSA公钥加密
     */
    public static String rsaEncryptByPublicKey(String content, String publicKey) throws Exception {
        PublicKey pubKey = getRSAPublicKey(publicKey);

        // 创建加密对象
        Cipher cipher = Cipher.getInstance("RSA");

        //对加密进行初始化
        cipher.init(Cipher.ENCRYPT_MODE, pubKey);

        // 使用公钥进行加密
        byte[] enBytes = cipher.doFinal(content.getBytes("utf-8"));
        return Base64.encodeBase64String(enBytes);
    }


    /**
     * 使用RSA私钥解密
     */
    public static String rsaDecryptByPrivateKey(String content, String privateKey) throws Exception {
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, getRSAPrivateKey(privateKey));
        byte[] deBytes = cipher.doFinal(Base64.decodeBase64(content.getBytes("utf-8")));
        return new String(deBytes);
    }


    /**
     * 获取RSA公钥对象
     */
    public static PublicKey getRSAPublicKey(String publicKey) throws Exception {
        byte[] keyBytes = Base64.decodeBase64(publicKey.getBytes("utf-8"));

        // 创建公钥key的规则
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);

        // 创建key的工厂
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePublic(keySpec);
    }

    /**
     * 获取RSA私钥对象
     */
    public static PrivateKey getRSAPrivateKey(String privateKey) throws Exception {
        byte[] keyBytes = Base64.decodeBase64(privateKey.getBytes("utf-8"));

        // 创建私钥key的规则
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);

        // 创建key的工厂
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePrivate(keySpec);
    }


    /**
     * RSA私钥签名  生成数字签名
     *
     * @param content    原文
     * @param privateKey
     * @return
     * @throws Exception
     */
    public static String rsaSignByPrivateKey(String content, String privateKey) throws Exception {
        PrivateKey priKey = getRSAPrivateKey(privateKey);

        // 获取签名对象
        Signature signature = Signature.getInstance("SHA1WithRSA");

        // 初始化签名
        signature.initSign(priKey);

        // 传入原文
        signature.update(content.getBytes("utf-8"));
        // 开始签名
        byte[] signed = signature.sign();
        return new String(Base64.encodeBase64URLSafe(signed), "utf-8");
    }

    /**
     * 验签
     *
     * @param content   原文
     * @param sign      签名的密文
     * @param publicKey 公钥
     * @throws Exception
     */
    public static void verifySign(String content, String sign, String publicKey)
            throws Exception {

        // 获取签名对象
        Signature signature = Signature.getInstance("SHA1WithRSA");

        // 初始化检验
        signature.initVerify(getRSAPublicKey(publicKey));
        signature.update(content.getBytes("utf-8"));

        // 校验数据
        boolean verifyResult = signature.verify(Base64.decodeBase64(sign.getBytes("utf-8")));
        if (!verifyResult) {
            throw new Exception("验签失败");
        }
    }

}

