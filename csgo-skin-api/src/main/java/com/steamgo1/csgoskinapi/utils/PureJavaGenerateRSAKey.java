package com.steamgo1.csgoskinapi.utils;


import java.io.FileWriter;
import java.io.IOException;
import java.security.Key;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.util.Base64;

/**
 * 原生Java生成RSA公私钥文件
 */
public class PureJavaGenerateRSAKey {
    public static void main(String[] args) throws Exception {
        generateRSAPemKeyFiles();
    }

    /**
     * 生成RSA pem格式的公私钥文件
     *
     * @throws Exception
     */
    private static void generateRSAPemKeyFiles() throws Exception {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(4096);
        KeyPair keyPair = keyPairGenerator.generateKeyPair();

        savePrivatePemFile(keyPair.getPrivate(), "C:\\Users\\<USER>\\Desktop\\ras\\private_key.pem");
        savePublicPemFile(keyPair.getPublic(), "C:\\Users\\<USER>\\Desktop\\ras\\public_key.pem");
    }

    private static void savePrivatePemFile(Key key, String filename)
            throws IOException {
        String encoded = Base64.getEncoder().encodeToString(key.getEncoded());

        FileWriter fileWriter = null;
        try {
            fileWriter = new FileWriter(filename);
            fileWriter.write("-----BEGIN PRIVATE KEY-----\n");
            fileWriter.write(encoded);
            fileWriter.write("\n-----END PRIVATE KEY-----");
        } finally {
            if (null != fileWriter) {
                fileWriter.close();
            }
        }
    }

    private static void savePublicPemFile(Key key, String filename)
            throws IOException {
        String encoded = Base64.getEncoder().encodeToString(key.getEncoded());

        FileWriter fileWriter = null;
        try {
            fileWriter = new FileWriter(filename);
            fileWriter.write("-----BEGIN PUBLIC KEY-----\n");
            fileWriter.write(encoded);
            fileWriter.write("\n-----END PUBLIC KEY-----");
        } finally {
            if (null != fileWriter) {
                fileWriter.close();
            }
        }
    }
}