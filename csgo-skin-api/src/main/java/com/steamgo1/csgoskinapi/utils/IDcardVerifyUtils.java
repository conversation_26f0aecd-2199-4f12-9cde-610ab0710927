package com.steamgo1.csgoskinapi.utils;

import java.util.regex.Pattern;

public class IDcardVerifyUtils {
    // 加权因子数组
    private static final int[] WEIGHT_FACTORS = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};

    // 校验码数组
    private static final String CHECK_CODES = "10X98765432";

    /**
     * 校验身份证号码是否合法
     *
     * @param idCard 身份证号码
     * @return 是否合法
     */
    public static boolean isValidIdCard(String idCard) {
        // 检查身份证长度是否为18位
        if (idCard == null || idCard.length() != 18) {
            return false;
        }

        // 检查前17位是否全是数字，第18位是否是数字或'X'
        if (!Pattern.matches("\\d{17}[0-9Xx]", idCard)) {
            return false;
        }

        // 计算加权因子和
        int sum = 0;
        for (int i = 0; i < 17; i++) {
            char ch = idCard.charAt(i);
            if (!Character.isDigit(ch)) {
                return false;
            }
            sum += Character.getNumericValue(ch) * WEIGHT_FACTORS[i];
        }

        // 计算校验码
        int mod = sum % 11;
        char expectedCheckCode = CHECK_CODES.charAt(mod);

        // 比较实际校验码和计算得到的校验码
        char actualCheckCode = idCard.toUpperCase().charAt(17);
        return expectedCheckCode == actualCheckCode;
    }
}
