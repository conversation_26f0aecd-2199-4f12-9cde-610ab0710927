package com.steamgo1.csgoskinapi.dto.xinfupaybank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 代付回调通知请求
 * XinfuPayBank withdrawal callback notification request DTO
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@ApiModel(value = "WithdrawalNotifyRequest", description = "代付回调通知请求")
public class WithdrawalNotifyRequest {
    
    @ApiModelProperty(value = "商户订单编号", notes = "商户系统的唯一订单编号", required = true, example = "M76571987896")
    private String merchantOrderNo;
    
    @ApiModelProperty(value = "代付金额", notes = "代付的金额，单位：元", required = true, example = "100.00")
    private String withdrawalMoney;
    
    @ApiModelProperty(value = "代付时间", notes = "代付完成的时间", required = true, example = "Thu Nov 23 03:27:41 UTC 2023")
    private String withdrawalTime;
    
    @ApiModelProperty(value = "订单状态", notes = "代付订单状态：2-成功，4-失败", required = true, example = "2", allowableValues = "2,4")
    private String status;
    
    @ApiModelProperty(value = "签名", notes = "使用代付密钥生成的签名，用于验证回调的真实性", required = true, example = "7e150e278ef701cc2615f56db0930a50")
    private String sign;
    
    @ApiModelProperty(value = "描述信息", notes = "代付结果的描述信息，不参与签名加密", example = "success")
    private String msg;
}