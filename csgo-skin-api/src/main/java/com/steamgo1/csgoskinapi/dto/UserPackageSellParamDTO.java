package com.steamgo1.csgoskinapi.dto;

import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

@Data
@ApiModel("用户饰品出售参数")
public class UserPackageSellParamDTO {
    @ApiModelProperty("背包ID")
    List<Long> packageIds;

    public List<Long> getPackageIds() {
        return packageIds.stream().filter(ObjectUtil::isNotNull).collect(Collectors.toList());
    }

}
