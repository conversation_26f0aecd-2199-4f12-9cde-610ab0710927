package com.steamgo1.csgoskinapi.dto.xinfupaybank;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * 收款订单创建请求
 * XinfuPayBank payment order creation request DTO
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@ApiModel(value = "PayOrderRequest", description = "收款订单创建请求")
public abstract class PayOrderRequest {

    @ApiModelProperty(value = "商户号", notes = "XinfuPayBank分配的商户标识", required = true, example = "143d123456548669fae8b0fad5d0fdf")
    private String machId;

    @ApiModelProperty(value = "商户订单编号", notes = "商户系统的唯一订单编号", required = true, example = "M20210919210649238840")
    private String merchantOrderNo;

    @ApiModelProperty(value = "订单金额", notes = "支付金额，单位：元", required = true, example = "100.00")
    private Double amount;

    @ApiModelProperty(value = "用户姓名", notes = "付款用户的真实姓名", example = "张三")
    private String name;

    @ApiModelProperty(value = "用户邮箱", notes = "付款用户的邮箱地址", example = "<EMAIL>")
    private String email;

    @ApiModelProperty(value = "用户手机号", notes = "付款用户的手机号码", example = "***********")
    private String phone;

    @ApiModelProperty(value = "银行代码", notes = "指定银行的代码标识", example = "ICBC")
    private String bankCode;

    @ApiModelProperty(value = "订单备注", notes = "订单的备注信息", example = "充值订单")
    private String remark;

    @ApiModelProperty(value = "支付成功跳转地址", notes = "支付成功后用户跳转的页面地址", example = "http://example.com/success")
    private String successUrl;

    @ApiModelProperty(value = "回调通知地址", notes = "支付结果异步通知的回调地址", required = true, example = "http://example.com/callback")
    private String returnUrl;

    @ApiModelProperty(value = "签名", notes = "使用支付密钥生成的签名", required = true, example = "7e150e278ef701cc2615f56db0930a50")
    private String sign;

    /**
     * 校验参数
     *
     * @return
     */
    public Boolean validate() {
        return StrUtil.isAllNotEmpty(machId, merchantOrderNo, returnUrl, sign) && ObjectUtil.isNotNull(amount) && amount > 0;
    }

    /**
     * 转换成Map<String, String>
     *
     * @Return Map<String, String>
     */
    public Map<String, String> toSignMapIgnoreNullValue() {
        // 转换成Map<String, String>
        return BeanUtil.beanToMap(this).entrySet()
                .stream()
                .filter(entry -> ObjectUtil.isNotEmpty(entry.getValue()) && !"sign".equals(entry.getKey()))  // 过滤掉value为null的条目
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().toString()
                ));
    }

    public abstract String paySubPath();

}