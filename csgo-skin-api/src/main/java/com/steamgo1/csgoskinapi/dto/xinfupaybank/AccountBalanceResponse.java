package com.steamgo1.csgoskinapi.dto.xinfupaybank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询账户余额响应
 * XinfuPayBank account balance query response DTO
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@ApiModel(value = "AccountBalanceResponse", description = "查询账户余额响应")
public class AccountBalanceResponse {
    
    @ApiModelProperty(value = "商户号", notes = "XinfuPayBank分配的商户标识", example = "143d123456548669fae8b0fad5d0fdf", required = true)
    private String machId;
    
    @ApiModelProperty(value = "应用ID", notes = "XinfuPayBank分配的应用标识", example = "de3d187556548669fae8b0fad5d0fdf", required = true)
    private String appId;
    
    @ApiModelProperty(value = "账户余额", notes = "当前账户可用余额，单位：元", example = "10.00", required = true)
    private String money;
}