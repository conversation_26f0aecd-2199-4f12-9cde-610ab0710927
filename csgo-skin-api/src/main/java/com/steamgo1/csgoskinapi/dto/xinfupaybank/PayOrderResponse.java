package com.steamgo1.csgoskinapi.dto.xinfupaybank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 收款订单创建响应
 * XinfuPayBank payment order creation response DTO
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@ApiModel(value = "PayOrderResponse", description = "收款订单创建响应")
public class PayOrderResponse {
    
    @ApiModelProperty(value = "响应代码", notes = "接口调用结果代码，200表示成功", example = "200", required = true)
    private String code;
    
    @ApiModelProperty(value = "订单编号", notes = "XinfuPayBank系统生成的订单编号", example = "XF202109192106492388", required = true)
    private String orderNo;
    
    @ApiModelProperty(value = "支付链接", notes = "用户进行支付的页面链接", example = "http://pay.xinfupaybank.com/pay/12345", required = true)
    private String paymentUrl;
    
    @ApiModelProperty(value = "二维码", notes = "支付二维码内容，可用于生成二维码图片", example = "http://pay.xinfupaybank.com/qr/12345")
    private String qrcode;
}