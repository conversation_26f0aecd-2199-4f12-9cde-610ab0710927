package com.steamgo1.csgoskinapi.dto.xinfupaybank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 收款回调通知请求
 * XinfuPayBank payment callback notification request DTO
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@ApiModel(value = "PayNotifyRequest", description = "收款回调通知请求")
public class PayNotifyRequest {
    
    @ApiModelProperty(value = "商户订单编号", notes = "商户系统的唯一订单编号", required = true, example = "M20210919210649238840")
    private String merchantOrderNo;
    
    @ApiModelProperty(value = "支付金额", notes = "实际支付的金额，单位：元", required = true, example = "100.00")
    private String payMoney;
    
    @ApiModelProperty(value = "支付时间", notes = "支付完成的时间", required = true, example = "2021-09-19 21:10:30")
    private String payTime;
    
    @ApiModelProperty(value = "订单状态", notes = "支付订单状态：2-支付成功", required = true, example = "2", allowableValues = "2")
    private String status;
    
    @ApiModelProperty(value = "签名", notes = "使用支付密钥生成的签名，用于验证回调的真实性", required = true, example = "7e150e278ef701cc2615f56db0930a50")
    private String sign;
}