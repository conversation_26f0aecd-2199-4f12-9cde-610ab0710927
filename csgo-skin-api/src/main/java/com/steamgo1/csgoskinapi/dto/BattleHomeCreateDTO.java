package com.steamgo1.csgoskinapi.dto;


import com.steamgo1.csgoskincommon.entity.enums.BattleHomeMethod;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@ApiModel("创建对战房")
public class BattleHomeCreateDTO {
    @ApiModelProperty(value = "对战房人数", allowableValues = "2,3")
    @Min(2)
    @Max(3)
    private Integer totalPlayer;

    @ApiModelProperty(value = "房间模式", notes = "com.steamgo1.csgoskincommon.entity.enums.BattleHomeMethod", allowableValues = "1,2")
    private Integer method = BattleHomeMethod.PVE.getCode();

    @ApiModelProperty(value = "包含箱子")
    @Size(min = 1, max = 6)
    private List<Case> caseList;

    @Data
    public static class Case {
        @ApiModelProperty("箱子ID")
        Long caseId;
    }
}
