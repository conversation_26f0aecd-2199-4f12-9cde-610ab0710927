package com.steamgo1.csgoskinapi.dto.xinfupaybank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 代付订单创建响应
 * XinfuPayBank withdrawal order creation response DTO
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@ApiModel(value = "WithdrawalResponse", description = "代付订单创建响应")
public class WithdrawalResponse {
    
    @ApiModelProperty(value = "处理状态", notes = "代付请求处理状态", example = "success", required = true)
    private String status;
    
    @ApiModelProperty(value = "响应代码", notes = "接口调用结果代码，200表示成功", example = "200", required = true)
    private String code;
}