package com.steamgo1.csgoskinapi.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@Data
@ApiModel("开箱参数")
public class CaseLotteryDTO {
    @ApiModelProperty("数量")
    private Integer number;

    @ApiModelProperty("箱子ID")
    private Long caseId;

    @ApiModelProperty("回合数")
    @Max(6)
    @Min(1)
    private Integer rounds;
}
