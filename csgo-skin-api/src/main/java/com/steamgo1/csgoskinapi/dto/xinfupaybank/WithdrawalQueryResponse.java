package com.steamgo1.csgoskinapi.dto.xinfupaybank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 代付订单查询响应
 * XinfuPayBank withdrawal order query response DTO
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@ApiModel(value = "WithdrawalQueryResponse", description = "代付订单查询响应")
public class WithdrawalQueryResponse {
    
    @ApiModelProperty(value = "订单编号", notes = "XinfuPayBank系统生成的订单编号", example = "XF202109192106492388", required = true)
    private String orderNo;
    
    @ApiModelProperty(value = "订单状态", notes = "订单当前状态：1-支付中，2-支付成功，4-失败", example = "2", required = true, allowableValues = "1,2,4")
    private String status;
    
    @ApiModelProperty(value = "订单金额", notes = "代付订单的原始金额，单位：元", example = "100.00", required = true)
    private String orderMoney;
    
    @ApiModelProperty(value = "扣款金额", notes = "实际扣款金额，单位：元", example = "100.00", required = true)
    private String payMoney;
    
    @ApiModelProperty(value = "服务费", notes = "平台收取的服务费用，单位：元", example = "5.00")
    private String serviceMoney;
    
    @ApiModelProperty(value = "账号类型", notes = "收款账号类型：1-CPF，2-CNPJ，3-EMAIL，4-PHONE，5-EVP", example = "1", allowableValues = "1,2,3,4,5")
    private String type;
    
    @ApiModelProperty(value = "创建时间", notes = "订单创建时间", example = "2021-09-19 21:06:49", required = true)
    private String createTime;
    
    @ApiModelProperty(value = "支付时间", notes = "订单支付完成时间，未支付时为空", example = "2021-09-19 21:10:30")
    private String updateTime;
    
    @ApiModelProperty(value = "收款账号", notes = "收款人的账号信息", example = "<EMAIL>", required = true)
    private String account;
    
    @ApiModelProperty(value = "商户订单编号", notes = "商户系统的订单编号", example = "M20210919210649238840", required = true)
    private String merchantOrderNo;
    
    @ApiModelProperty(value = "收款人姓名", notes = "收款人的真实姓名", example = "张三", required = true)
    private String name;
    
    @ApiModelProperty(value = "CPF/身份证号", notes = "收款人的CPF号码或身份证号", example = "***********")
    private String bankCode;
    
    @ApiModelProperty(value = "回调跳转地址", notes = "支付完成后的回调通知地址", example = "http://example.com/callback")
    private String returnUrl;
}