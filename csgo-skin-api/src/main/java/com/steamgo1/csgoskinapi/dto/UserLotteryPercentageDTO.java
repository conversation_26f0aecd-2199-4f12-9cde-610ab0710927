package com.steamgo1.csgoskinapi.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@Data
@ApiModel("用户百分比抽奖")
public class UserLotteryPercentageDTO {
    @ApiModelProperty("百分比下限")
    @Max(100)
    @Min(0)
    private Integer min;
    @Max(100)
    @Min(0)
    @ApiModelProperty("百分比上限")
    private Integer max;

    @ApiModelProperty("皮肤ID")
    private Long skinId;
}
