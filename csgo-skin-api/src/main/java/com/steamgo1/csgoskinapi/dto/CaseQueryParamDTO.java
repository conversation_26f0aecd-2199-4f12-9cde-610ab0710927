package com.steamgo1.csgoskinapi.dto;


import com.google.common.collect.Lists;
import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@ApiModel("箱子查询参数")
public class CaseQueryParamDTO {
    @ApiModelProperty("类型 0 开箱 1 对战箱子")
    private Integer type;

    public enum Type implements CodeValueBaseEnum {
        OPEN_CASE(0, "开箱"),
        BATTLE(1, "对战");

        private Integer code;

        private String value;

        Type(int code, String value) {
            this.code = code;
            this.value = value;
        }

        //讲枚举转换成list格式，这样前台遍历的时候比较容易，列如 下拉框 后台调用toList方法，你就可以得到code 和name了
        public static List toList() {
            List list = Lists.newArrayList();//Lists.newArrayList()其实和new ArrayList()几乎一模
            //  一样, 唯一它帮你做的(其实是javac帮你做的), 就是自动推导(不是"倒")尖括号里的数据类型.

            for (com.steamgo1.csgoskincommon.entity.enums.ThresholdType rollHomeType : com.steamgo1.csgoskincommon.entity.enums.ThresholdType.values()) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("code", rollHomeType.getCode());
                map.put("name", rollHomeType.getValue());
                list.add(map);
            }
            return list;
        }

        public static Type instance(Integer code) {
            Type[] enums = values();
            for (Type type : enums) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }

        @Override
        public Integer getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String description() {
            return code + "-" + value;
        }
    }

}
