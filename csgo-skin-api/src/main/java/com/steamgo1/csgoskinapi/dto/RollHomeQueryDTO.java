package com.steamgo1.csgoskinapi.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel("用戶饰品查询参数")
public class RollHomeQueryDTO {
    @ApiModelProperty("分页大小")
    @NotBlank(message = "分页大小必填")
    private int size;

    @ApiModelProperty("页数")
    @NotBlank(message = "页数必填")
    private int page;

    @ApiModelProperty("是否推荐")
    private Boolean isRecommend;

    @ApiModelProperty("Roll房状态(true 已开奖 false 进行中(包含开奖中和未开奖))")
//    @NotBlank(message = "isEnd参数不能为空")
    private Boolean isEnd;
}
