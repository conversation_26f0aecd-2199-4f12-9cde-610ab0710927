package com.steamgo1.csgoskinapi.dto;

import lombok.Data;

/**
 * Gmail用户信息数据传输对象
 * 用于封装从Google OAuth2 API获取的用户基本信息
 *
 * 对应Google OAuth2 userinfo端点返回的JSON结构：
 * {
 *   "id": "123456789",                    // Google用户唯一标识符
 *   "email": "<EMAIL>",            // 用户邮箱地址
 *   "verified_email": true,               // 邮箱验证状态
 *   "name": "User Name",                  // 用户显示名称
 *   "given_name": "User",                 // 名字
 *   "family_name": "Name",                // 姓氏
 *   "picture": "https://lh3.googleusercontent.com/...", // 头像URL
 *   "locale": "en"                        // 用户语言偏好
 * }
 *
 * 注意事项：
 * 1. 只有verified_email为true的邮箱才应该用于登录，确保邮箱真实性
 * 2. picture字段提供的是Google托管的头像URL，可以直接使用
 * 3. name字段是用户在Google账号中设置的显示名称
 * 4. 所有字段都可能为null，使用时需要进行空值检查
 *
 * <AUTHOR>
 */
@Data
public class GmailUserInfo {

    /** 
     * 用户邮箱地址
     * 这是用户身份识别的主要标识符，用于：
     * 1. 查找或创建用户账号
     * 2. 作为用户的唯一标识
     * 3. 发送系统通知邮件
     */
    private String email;

    /** 
     * 用户显示名称
     * Google账号中用户设置的公开显示名称，用于：
     * 1. 作为新用户的默认昵称
     * 2. 在用户界面中显示用户姓名
     * 注意：可能包含特殊字符，使用前需要进行安全过滤
     */
    private String name;

    /** 
     * 用户头像URL
     * Google提供的用户头像图片链接，特点：
     * 1. 由Google CDN托管，访问速度快
     * 2. 支持通过URL参数调整大小（如：?sz=200）
     * 3. 可以直接在前端使用，无需下载存储
     * 4. 链接长期有效，但用户可能会更换头像
     */
    private String picture;

    /** 
     * 邮箱验证状态
     * 表示该邮箱是否已通过Google的验证，重要性：
     * 1. 只有验证过的邮箱才应该用于登录注册
     * 2. 验证过的邮箱确保了邮箱地址的真实性和可达性
     * 3. 这是防止恶意注册的重要安全检查
     * 4. 如果为false，应该拒绝登录并提示用户验证邮箱
     */
    private Boolean verifiedEmail;
}