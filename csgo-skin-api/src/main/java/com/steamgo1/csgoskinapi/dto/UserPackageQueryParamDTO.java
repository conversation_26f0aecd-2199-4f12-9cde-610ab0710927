package com.steamgo1.csgoskinapi.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel("用户背包查询参数")
public class UserPackageQueryParamDTO {
    @ApiModelProperty("分页大小")
    @NotBlank(message = "分页大小必填")
    private int size;

    @ApiModelProperty("页数")
    @NotBlank(message = "页数必填")
    private int page;

    @ApiModelProperty("掉落来源")
    private Integer source;

    @ApiModelProperty("是否价格升序")
    private Boolean priceAsc = true;

    @ApiModelProperty("是否取回(false: 当前库存 true: 已取回)")
    private Boolean isReceived = false;
}
