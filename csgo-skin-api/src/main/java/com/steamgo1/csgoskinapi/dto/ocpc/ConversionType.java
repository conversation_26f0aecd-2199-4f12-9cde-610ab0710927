package com.steamgo1.csgoskinapi.dto.ocpc;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;


// 百度
public class ConversionType implements Serializable {

    /*
        落地页Url
     */
    private String logidUrl;
    /*
        转化类型
     */
    private Integer newType;

    public String getLogidUrl() {
        return logidUrl;
    }

    public void setLogidUrl(String logidUrl) {
        this.logidUrl = logidUrl;
    }

    public Integer getNewType() {
        return newType;
    }

    public void setNewType(Integer newType) {
        this.newType = newType;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
