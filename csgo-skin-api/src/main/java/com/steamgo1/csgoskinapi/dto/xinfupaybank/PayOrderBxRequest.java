package com.steamgo1.csgoskinapi.dto.xinfupaybank;

import com.steamgo1.csgoskincommon.entity.enums.PayType;
import com.steamgo1.csgoskincommon.entity.enums.XinFuPayCountySubPath;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 巴西-收款订单创建请求
 * XinfuPayBank payment order creation request DTO
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@ApiModel(value = "PayOrderRequest", description = "巴西-收款订单创建请求")
public class PayOrderBxRequest extends PayOrderRequest {
    @ApiModelProperty(value = "银行代码", notes = "指定银行的代码标识", example = "ICBC")
    private String bankCode;

    @Override
    public String paySubPath() {
        return XinFuPayCountySubPath.BX.getValue();
    }
}