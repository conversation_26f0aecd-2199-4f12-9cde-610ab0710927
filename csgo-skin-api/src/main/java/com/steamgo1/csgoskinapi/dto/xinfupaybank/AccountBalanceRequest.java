package com.steamgo1.csgoskinapi.dto.xinfupaybank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询账户余额请求
 * XinfuPayBank account balance query request DTO
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@ApiModel(value = "AccountBalanceRequest", description = "查询账户余额请求")
public class AccountBalanceRequest {
    
    @ApiModelProperty(value = "应用ID", notes = "XinfuPayBank分配的应用标识", required = true, example = "de3d187556548669fae8b0fad5d0fdf")
    private String appId;
    
    @ApiModelProperty(value = "商户号", notes = "XinfuPayBank分配的商户标识", required = true, example = "143d123456548669fae8b0fad5d0fdf")
    private String machId;
    
    @ApiModelProperty(value = "签名", notes = "使用支付密钥加密生成的签名", required = true, example = "7e150e278ef701cc2615f56db0930a50")
    private String sign;
}