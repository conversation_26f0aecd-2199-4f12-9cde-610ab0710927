package com.steamgo1.csgoskinapi.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel("在线验证参数")
public class LotteryVerifyDTO {
    @ApiModelProperty("私密哈希")
    private String secretHash;

    @ApiModelProperty("私密盐值")
    private String secretSalt;

    @ApiModelProperty("公共哈希")
    private String publicHash;

    @ApiModelProperty("回合数")
    private Integer rounds;

    @ApiModelProperty("用户种子")
    private String clientSeed;

}
