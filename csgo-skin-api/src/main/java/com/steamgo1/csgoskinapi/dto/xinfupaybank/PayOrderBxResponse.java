package com.steamgo1.csgoskinapi.dto.xinfupaybank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 巴西-收款订单创建响应
 * XinfuPayBank payment order creation response DTO
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@ApiModel(value = "PayOrderResponse", description = "巴西-收款订单创建响应")
public class PayOrderBxResponse extends PayOrderResponse {
    @ApiModelProperty(value = "二维码", notes = "支付二维码内容，可用于生成二维码图片", example = "http://pay.xinfupaybank.com/qr/12345")
    private String qrcode;
}