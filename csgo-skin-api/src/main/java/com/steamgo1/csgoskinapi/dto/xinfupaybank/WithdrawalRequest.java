package com.steamgo1.csgoskinapi.dto.xinfupaybank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 代付订单创建请求
 * XinfuPayBank withdrawal order creation request DTO
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@ApiModel(value = "WithdrawalRequest", description = "代付订单创建请求")
public class WithdrawalRequest {
    
    @ApiModelProperty(value = "商户号", notes = "XinfuPayBank分配的商户标识", required = true, example = "143d123456548669fae8b0fad5d0fdf")
    private String machId;
    
    @ApiModelProperty(value = "代付金额", notes = "代付的金额，单位：元", required = true, example = "100.00")
    private Double amount;
    
    @ApiModelProperty(value = "收款人姓名", notes = "收款人的真实姓名", required = true, example = "张三")
    private String name;
    
    @ApiModelProperty(value = "收款账号", notes = "收款人的账号信息", required = true, example = "<EMAIL>")
    private String account;
    
    @ApiModelProperty(value = "账号类型", notes = "收款账号类型：1-CPF，2-CNPJ，3-EMAIL，4-PHONE，5-EVP", required = true, example = "1", allowableValues = "1,2,3,4,5")
    private String type;
    
    @ApiModelProperty(value = "回调通知地址", notes = "代付结果异步通知的回调地址", required = true, example = "http://example.com/callback")
    private String returnUrl;
    
    @ApiModelProperty(value = "身份证号/CPF", notes = "收款人的身份证号或CPF号码", example = "***********")
    private String idCode;
    
    @ApiModelProperty(value = "商户订单编号", notes = "商户系统的唯一订单编号", required = true, example = "M20210919210649238840")
    private String merchantOrderNo;
    
    @ApiModelProperty(value = "签名", notes = "使用代付密钥生成的签名", required = true, example = "7e150e278ef701cc2615f56db0930a50")
    private String sign;
}