package com.steamgo1.csgoskinapi.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel("参加对战房参数")
public class BattleHomeQueryDTO {
    @ApiModelProperty("分页大小")
    @NotBlank(message = "分页大小必填")
    private int size;

    @ApiModelProperty("页数")
    @NotBlank(message = "页数必填")
    private int page;

//    @ApiModelProperty(value = "对战房状态", notes = "com.steamgo1.csgoskincommon.entity.enums.BattleHomeStatus", allowableValues = "1,2,3")
//    Integer status;

}
