package com.steamgo1.csgoskinapi.dto.epay;


import lombok.Data;

@Data
public class CreateOrderParmas {
    private String type;
    private String outTradeNo;
    private String name;
    private String money;
    private String clientip;
}


//
//            parameters.put("pid", this.pid);
//            parameters.put("timestamp", String.valueOf(System.currentTimeMillis() / 1000));
//            parameters.put("sign_type", this.signType);
//            parameters.put("pid", "1004");
//            parameters.put("method", "web");
//            parameters.put("device", "pc");
//            parameters.put("type", "alipay");
//            parameters.put("out_trade_no", "20160806151343349");
//            parameters.put("notify_url", "http://www.pay.com/notify_url.php");
//            parameters.put("return_url", "http://www.pay.com/return_url.php");
//            parameters.put("name", "VIP会员");
//            parameters.put("money", "1.00");
//            parameters.put("clientip", "*************");
//            parameters.put("timestamp", System.currentTimeMillis()/1000 + "");
//            parameters.put("sign", ""); // 你需要根据签名规则生成sign
//            parameters.put("sign_type", "RSA");