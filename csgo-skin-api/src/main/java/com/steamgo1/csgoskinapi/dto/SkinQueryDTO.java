package com.steamgo1.csgoskinapi.dto;

import com.steamgo1.csgoskincommon.utils.I18nField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

@Data
@ApiModel("饰品查询参数")
public class SkinQueryDTO {
    @ApiModelProperty("分页大小")
    @NotBlank(message = "分页大小必填")
    private int size;

    @ApiModelProperty("页数")
    @NotBlank(message = "页数必填")
    private int page;

    @ApiModelProperty("是否追梦专属")
    private Boolean enablePercentage;

    @ApiModelProperty("饰品原型ID")
    private Long prototypeId;

    @ApiModelProperty("饰品稀有度ID")
    private Long skinRarityId;

    @ApiModelProperty("饰品外观ID")
    private Long skinExteriorId;

    @ApiModelProperty("饰品质量ID")
    private Long skinQualityId;

    @ApiModelProperty("饰品名称（多语言）")
    private I18nField i18nFieldName;

    @ApiModelProperty("饰品名")
    private String name;

    @ApiModelProperty("最大价格")
    private BigDecimal maxPrice;

    @ApiModelProperty("最小价格")
    private BigDecimal minPrice;

    @ApiModelProperty("是否价格升序")
    private Boolean priceAsc = true;


}
