package com.steamgo1.csgoskinapi.dto.xinfupaybank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 收款订单查询响应
 * XinfuPayBank payment order query response DTO
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@ApiModel(value = "PayOrderQueryResponse", description = "收款订单查询响应")
public class PayOrderQueryResponse {
    
    @ApiModelProperty(value = "订单编号", notes = "XinfuPayBank系统生成的订单编号", example = "XF202109192106492388", required = true)
    private String orderNo;
    
    @ApiModelProperty(value = "商户订单编号", notes = "商户系统的订单编号", example = "M20210919210649238840", required = true)
    private String merchantOrderNo;
    
    @ApiModelProperty(value = "订单状态", notes = "订单当前状态：0-等待，1-支付中，2-成功，3-失败", example = "2", required = true, allowableValues = "0,1,2,3")
    private String status;
    
    @ApiModelProperty(value = "订单金额", notes = "订单的原始金额，单位：元", example = "100.00", required = true)
    private String orderMoney;
    
    @ApiModelProperty(value = "营收金额", notes = "扣除手续费后的实际到账金额，单位：元", example = "95.00")
    private String payMoney;
    
    @ApiModelProperty(value = "服务费", notes = "平台收取的服务费用，单位：元", example = "5.00")
    private String serviceMoney;
    
    @ApiModelProperty(value = "订单备注", notes = "创建订单时的备注信息", example = "充值订单")
    private String remark;
    
    @ApiModelProperty(value = "用户姓名", notes = "付款用户的真实姓名", example = "张三")
    private String name;
    
    @ApiModelProperty(value = "用户邮箱", notes = "付款用户的邮箱地址", example = "<EMAIL>")
    private String email;
    
    @ApiModelProperty(value = "用户手机号", notes = "付款用户的手机号码", example = "13800138000")
    private String phone;
    
    @ApiModelProperty(value = "支付成功跳转地址", notes = "支付成功后用户跳转的页面地址", example = "http://example.com/success")
    private String successUrl;
    
    @ApiModelProperty(value = "回调通知地址", notes = "支付结果异步通知的回调地址", example = "http://example.com/callback")
    private String returnUrl;
    
    @ApiModelProperty(value = "创建时间", notes = "订单创建时间", example = "2021-09-19 21:06:49", required = true)
    private String createTime;
    
    @ApiModelProperty(value = "付款时间", notes = "订单支付完成时间，未支付时为空", example = "2021-09-19 21:10:30")
    private String payTime;
}