package com.steamgo1.csgoskinapi.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel("分页查询参数")
public class CaseUserLotteryRecordOfCaseQueryDTO {
    @ApiModelProperty("箱子ID")
    @NotBlank(message = "箱子ID不能为空")
    private Long caseId;

    @ApiModelProperty("分页大小")
    @NotBlank(message = "分页大小必填")
    private int size;

    @ApiModelProperty("页数")
    @NotBlank(message = "页数必填")
    private int page;

}
