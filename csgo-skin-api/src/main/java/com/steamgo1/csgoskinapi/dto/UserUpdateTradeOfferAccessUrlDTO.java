package com.steamgo1.csgoskinapi.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel("用户更新steam交易链接")
public class UserUpdateTradeOfferAccessUrlDTO {
    @NotBlank(message = "tradeOfferAccessUrl参数不能为空")
    @ApiModelProperty("tradeOfferAccessUrl")
    String tradeOfferAccessUrl;
}
