package com.steamgo1.csgoskinapi.controller;

import com.steamgo1.csgoskincommon.entity.StorageEntity;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import com.steamgo1.csgoskincommon.service.StorageService;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.ImageVO;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Path;

@Slf4j
@RequestMapping("/storage")
@RestController
@Api(tags = "存贮服务")
public class StorageController {
    @Autowired
    private StorageService storageService;


    @ApiOperation(value = "文件上传", notes = "返回URL")
    @PostMapping("/upload/ui")
    @ResponseBody
    public ImageVO uploadUi(@ApiParam(name = "file", value = "要上传的图片") @RequestPart("file") MultipartFile file) {
        StorageEntity storage;

        try {
            storage = storageService.store(file.getInputStream(), file.getSize(), file.getContentType(), file.getOriginalFilename());
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            // todo 国际化
            // throw new CsgoSkinException("上传失败");
            throw new CsgoSkinException(I18nUtils.getMessage("exception.storage.upload.fail"));
        }
        return storageService.toImage(storage);
    }


    @ApiOperation("查看")
    @GetMapping("/show/{key:.+}")
    @ResponseBody
    public Response<?> show(@PathVariable("key") String key, HttpServletResponse response) throws Exception {
        StorageEntity storageEntity = storageService.queryByKey(key);
        Path path = storageService.load(key);
        if (path == null) {
            return ResponseUtil.fail(I18nUtils.getMessage("exception.storage.file.not.exist"));
        }
        try {
            FileInputStream file = new FileInputStream(path.toString());
            int size = file.available();
            byte[] buffer = new byte[size];
            file.read(buffer, 0, size);
            file.close();
            response.setBufferSize(size);
            response.setContentType(storageEntity.getType());
            ServletOutputStream outputStream = response.getOutputStream();
            outputStream.write(buffer);
            outputStream.close();
            return null;
        } catch (FileNotFoundException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            return ResponseUtil.fail(I18nUtils.getMessage("exception.storage.file.not.exist"));
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            return ResponseUtil.fail(I18nUtils.getMessage("exception.storage.io.error"));
        }

    }

    @ApiOperation("下载")
    @ResponseBody
    @GetMapping("/download/{key:.+}")
    public Response<?> download(@PathVariable("key") String key, HttpServletResponse response) {
        StorageEntity storageEntity = storageService.queryByKey(key);
        Path path = storageService.load(key);
        if (path == null) {
            return ResponseUtil.fail(I18nUtils.getMessage("exception.storage.file.not.exist"));
        }
        try {
            FileInputStream file = new FileInputStream(path.toString());
            int size = file.available();
            byte[] buffer = new byte[size];
            file.read(buffer, 0, size);
            file.close();
            response.setContentType("application/force-download");// 设置强制下载不打开
            response.addHeader("Content-Disposition", "attachment;fileName=" + storageEntity.getFileName());// 设置文件名
            ServletOutputStream outputStream = response.getOutputStream();
            outputStream.write(buffer);
            outputStream.close();
            return null;
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            return ResponseUtil.fail(I18nUtils.getMessage("exception.storage.file.not.exist"));
        } catch (IOException e) {
            e.printStackTrace();
            return ResponseUtil.fail(I18nUtils.getMessage("exception.storage.io.error"));
        }
    }

}
