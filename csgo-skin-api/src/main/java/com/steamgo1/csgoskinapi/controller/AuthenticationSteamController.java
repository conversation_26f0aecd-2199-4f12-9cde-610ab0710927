package com.steamgo1.csgoskinapi.controller;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.steamgo1.csgoskinapi.config.jwt.JwtProvider;
import com.steamgo1.csgoskinapi.service.UserService;
import com.steamgo1.csgoskinapi.utils.SecurityUtils;
import com.steamgo1.csgoskincommon.dao.UserRepository;
import com.steamgo1.csgoskincommon.entity.UserEntity;
import com.steamgo1.csgoskincommon.utils.Checker;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import com.steamgo1.csgoskincommon.vo.Response;
import com.steamgo1.csgoskincommon.vo.TokenVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import org.springframework.web.util.UriUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Steam 登录控制器
 *
 * <AUTHOR>
 */
@Api(tags = "Steam鉴权")
@Slf4j
@RequestMapping("/auth/steam")
@Controller
public class AuthenticationSteamController {
    @Value("${steam.openid.return_to}")
    private String openIdReturnTo;
    @Value("${steam.bind_user.return_to}")
    private String bindUserReturnTo;
    @Value("${site.domain}")
    private String siteDomain;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private UserService userService;

    @Resource
    private UserRepository userRepository;


    private static final String STEAM_OPENID_URL = "https://steamcommunity.com/openid/login";

    /**
     * 登录
     *
     * @param request
     * @return
     */
    @ApiOperation("登录")
    @GetMapping("/login")
    public String login(HttpServletRequest request) {
        String domainName = request.getHeader("HTTP_X_FORWARDED_HOST");
        log.info(domainName);
        if (domainName == null) {
            domainName = siteDomain;
        }
        return getReturnUrl(request, openIdReturnTo + "?domain_name="+domainName);
    }

    @ApiOperation("登录回调")
    @RequestMapping(value = "/callback")
    @ResponseBody
    public Response<TokenVO> callback(HttpServletRequest request) {
        String steamId = getSteamId(request);
        if (StrUtil.isNotEmpty(steamId)) {
            TokenVO tokenVO = userService.loginBySteamId(steamId, request);
            return ResponseUtil.ok(tokenVO);
        }
        return ResponseUtil.fail();
    }

    /**
     * 登录
     *
     * @param request
     * @return
     */
    @ApiOperation("去绑定用户")
    @GetMapping("/toBind/user")
    public String toBindUser(HttpServletRequest request) {
        String domainName = request.getHeader("HTTP_X_FORWARDED_HOST");
        log.info(domainName);
        if (domainName == null) {
            domainName = siteDomain;
        }
        String fullBindUserReturnTo = bindUserReturnTo + "?domain_name=" + domainName + "&" + JwtProvider.TOKEN_HEADER.toLowerCase() + "=" + SecurityUtils.getToken(request);
        return getReturnUrl(request, fullBindUserReturnTo);
    }

    /**
     * 用户绑定steamId，不允许重复绑定
     * @param request
     * @return
     */
    @ApiOperation("用户绑定steamId回调")
    @GetMapping(value = "/bind/user")
    @ResponseBody
    public Response<Boolean> bindUser(HttpServletRequest request) {
        // 获取当前登录用户
        Long userId = SecurityUtils.getUserId();
        UserEntity user = userRepository.findById(userId);
        if (user == null) {
            return ResponseUtil.fail(I18nUtils.getMessage("exception.user.not.found"));
        }
        
        // 检查用户是否已经绑定Steam ID
        if (StrUtil.isNotEmpty(user.getSteamId())) {
            return ResponseUtil.fail(I18nUtils.getMessage("exception.steam.already.bound"));
        }
        
        // 从Steam回调中获取Steam ID
        String steamId = getSteamId(request);
        if (StrUtil.isEmpty(steamId)) {
            return ResponseUtil.fail(I18nUtils.getMessage("exception.steam.verification.failed"));
        }
        
        // 检查该Steam ID是否已被其他用户绑定
        UserEntity existingUser = userRepository.findBySteamId(steamId);
        if (existingUser != null) {
            return ResponseUtil.fail(I18nUtils.getMessage("exception.steam.bound.to.other.user"));
        }
        
        // 绑定Steam ID到当前用户
        user.setSteamId(steamId);
        userRepository.save(user);

        return ResponseUtil.ok();
    }

    private String getSteamId(HttpServletRequest request) {
        String parameter = UriUtils.decode(request.getQueryString(), StandardCharsets.UTF_8);
        
        // 移除authorization参数
        if (StrUtil.isNotEmpty(parameter)) {
            parameter = parameter.replaceAll("&?authorization=[^&]*", "").replaceAll("^&", "");
        }
        // 移除authorization参数
        if (StrUtil.isNotEmpty(parameter)) {
            parameter = parameter.replaceAll("&?domain=[^&]*", "").replaceAll("^&", "");
        }
        
        ResponseEntity<String> response = restTemplate.postForEntity(STEAM_OPENID_URL + "?" + parameter, null, String.class);
        log.info("{},返回状态：{}", STEAM_OPENID_URL, response.getStatusCode().name());
        if (!(response.getStatusCode().is5xxServerError() || response.getStatusCode().is4xxClientError())) {
            // 提取 Steam ID
            String[] claimedIds = request.getParameterMap().get("openid.claimed_id");
            if (claimedIds != null && claimedIds.length > 0) {
                String claimedId = claimedIds[0];
                String steamId = claimedId.substring(claimedId.lastIndexOf('/') + 1);
                if (StrUtil.isNotEmpty(steamId)) {
                   return steamId;
                }
            }

        }
        return null;
    }


    @NotNull
    private String getReturnUrl(HttpServletRequest request, String returnTo) {
        return "redirect:" + STEAM_OPENID_URL + "?openid.mode=checkid_setup&openid.ns=http://specs.openid.net/auth/2.0&openid.return_to=" + UriUtils.encode(returnTo, "utf-8") + "&openid.ns.sreg=http://openid.net/extensions/sreg/1.1&openid.identity=http://specs.openid.net/auth/2.0/identifier_select&openid.claimed_id=http://specs.openid.net/auth/2.0/identifier_select";
    }

}
