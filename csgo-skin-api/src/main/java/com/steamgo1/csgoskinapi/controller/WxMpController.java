package com.steamgo1.csgoskinapi.controller;


import com.steamgo1.csgoskinapi.config.wechat.WxMpProperties;
import com.steamgo1.csgoskinapi.enums.WxMsgType;
import com.steamgo1.csgoskinapi.service.AsyncTaskService;
import com.steamgo1.csgoskinapi.utils.SecurityUtils;
import com.steamgo1.csgoskinapi.vo.WxMpQrVO;
import com.steamgo1.csgoskincommon.dao.UserRepository;
import com.steamgo1.csgoskincommon.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpMessageRouter;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import me.chanjar.weixin.mp.bean.result.WxMpQrCodeTicket;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;

@Slf4j
@RequestMapping("/wx")
@RestController
@RequiredArgsConstructor
@Api(tags = "微信公众号接口")
public class WxMpController {
    private final WxMpService wxService;
    private final WxMpMessageRouter messageRouter;
    private final WxMpProperties properties;
    private final UserRepository userRepository;
    private final AsyncTaskService asyncTaskService;
    @Value("${wx.mp.msgTemplate.pickUp}")
    private String pickUp;
    @Value("${wx.mp.msgTemplate.rollHome}")
    private String rollHome;

    @ApiOperation("微信事件推送")
    @GetMapping(value = "/portal/{appid}", produces = "text/plain;charset=utf-8")
    public String authGet(@PathVariable String appid,
                          @RequestParam(name = "signature", required = false) String signature,
                          @RequestParam(name = "timestamp", required = false) String timestamp,
                          @RequestParam(name = "nonce", required = false) String nonce,
                          @RequestParam(name = "echostr", required = false) String echostr) {

        log.info("\n接收到来自微信服务器的认证消息：[{}, {}, {}, {}]", signature,
                timestamp, nonce, echostr);
        if (StringUtils.isAnyBlank(signature, timestamp, nonce, echostr)) {
            throw new IllegalArgumentException("请求参数非法，请核实!");
        }

        if (!this.wxService.switchover(appid)) {
            throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", appid));
        }

        if (wxService.checkSignature(timestamp, nonce, signature)) {
            return echostr;
        }

        return "非法请求";
    }

    @ApiOperation("微信事件推送")
    @PostMapping(value = "/portal/{appid}", produces = "application/xml; charset=UTF-8")
    public String post(@PathVariable String appid,
                       @RequestBody String requestBody,
                       @RequestParam("signature") String signature,
                       @RequestParam("timestamp") String timestamp,
                       @RequestParam("nonce") String nonce,
                       @RequestParam("openid") String openid,
                       @RequestParam(name = "encrypt_type", required = false) String encType,
                       @RequestParam(name = "msg_signature", required = false) String msgSignature) {
        log.info("\n接收微信请求：[openid=[{}], [signature=[{}], encType=[{}], msgSignature=[{}],"
                        + " timestamp=[{}], nonce=[{}], requestBody=[\n{}\n] ",
                openid, signature, encType, msgSignature, timestamp, nonce, requestBody);

        if (!this.wxService.switchover(appid)) {
            throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", appid));
        }

        if (!wxService.checkSignature(timestamp, nonce, signature)) {
            throw new IllegalArgumentException("非法请求，可能属于伪造的请求！");
        }

        String out = null;
        if (encType == null) {
            // 明文传输的消息
            WxMpXmlMessage inMessage = WxMpXmlMessage.fromXml(requestBody);
            WxMpXmlOutMessage outMessage = this.route(inMessage);
            if (outMessage == null) {
                return "";
            }

            out = outMessage.toXml();
        } else if ("aes".equalsIgnoreCase(encType)) {
            // aes加密的消息
            WxMpXmlMessage inMessage = WxMpXmlMessage.fromEncryptedXml(requestBody, wxService.getWxMpConfigStorage(),
                    timestamp, nonce, msgSignature);
            log.debug("\n消息解密后内容为：\n{} ", inMessage.toString());
            WxMpXmlOutMessage outMessage = this.route(inMessage);
            if (outMessage == null) {
                return "";
            }

            out = outMessage.toEncryptedXml(wxService.getWxMpConfigStorage());
        }

        log.debug("\n组装回复信息：{}", out);
        return out;
    }

    private WxMpXmlOutMessage route(WxMpXmlMessage message) {
        try {
            return this.messageRouter.route(message);
        } catch (Exception e) {
            log.error("路由消息时出现异常！", e);
        }
        return null;
    }

    @ApiOperation("发送模板信息")
    @GetMapping("/portal/send/{templateId}")
    public String greetUser(@PathVariable String templateId, Long userId) throws WxErrorException {
        if (!this.wxService.switchover(properties.getConfigs().get(0).getAppId())) {
            return "未找到对应appid=[%s]的配置，请核实！";
        }
        UserEntity user = userRepository.findById(userId);
        if (user == null || user.getWxOpenid() == null) {
            return "用户未绑定微信";
        }
        if (templateId.equals("rollhome")) {
            asyncTaskService.sendMessageToWechat(WxMsgType.ROLLHOME, user, "恭喜Roll房中奖,快去官网查看吧");
        } else if (templateId.equals("pickup")) {
            asyncTaskService.sendMessageToWechat(WxMsgType.PICKUP, user, "已发货,及时前往steam接受交易报价", "PICKUP000000000000000");
        }
        return "ok";
    }

    @ApiOperation("获取公众号二维码")
    @GetMapping("/getQr")
    public WxMpQrVO getQr() throws WxErrorException {
        Long userId = SecurityUtils.getUserId();
        if (!this.wxService.switchover(properties.getConfigs().get(0).getAppId())) {
            throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", properties.getConfigs().get(0).getAppId()));
        }
        HashMap<String, Long> data = new HashMap<>();
        WxMpQrCodeTicket ticket = wxService.getQrcodeService().qrCodeCreateTmpTicket(userId.toString(), 20000);
        return new WxMpQrVO() {{
            setUrl(ticket.getUrl());
        }};
    }

}
