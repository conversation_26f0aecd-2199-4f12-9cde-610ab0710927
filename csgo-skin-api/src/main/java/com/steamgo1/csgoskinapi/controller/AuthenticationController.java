package com.steamgo1.csgoskinapi.controller;


import com.steamgo1.csgoskinapi.config.oauth.GmailOAuthConfig;
import com.steamgo1.csgoskinapi.dto.SendEmailParam;
import com.steamgo1.csgoskinapi.dto.SendSmsParam;
import com.steamgo1.csgoskinapi.dto.UserLoginByEmailAndCaptchaDTO;
import com.steamgo1.csgoskinapi.dto.UserLoginByPhoneAndCaptchaDTO;
import com.steamgo1.csgoskinapi.service.EmailService;
import com.steamgo1.csgoskinapi.service.SmsService;
import com.steamgo1.csgoskinapi.service.UserService;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import com.steamgo1.csgoskincommon.vo.TokenVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.security.Principal;

@Slf4j
@RequestMapping("/auth")
@RestController
@RequiredArgsConstructor
@Api(tags = "用户鉴权")
public class  AuthenticationController {

    @Autowired
    private SmsService smsService;

    @Autowired
    private UserService userService;

    @Autowired
    private EmailService emailService;

    @Autowired
    private GmailOAuthConfig gmailOAuthConfig;

    @ApiOperation("登录(手机验证码)")
    @PostMapping(value = "/login")
    public Response<TokenVO> login(@RequestBody UserLoginByPhoneAndCaptchaDTO loginParam, HttpServletRequest request) {
        return ResponseUtil.ok(userService.login(loginParam.getPhone(), loginParam.getCode(), loginParam.getInviteCode(), request));
    }

    @ApiOperation("登录(邮箱验证码)")
    @PostMapping(value = "/email/login")
    public Response<TokenVO> emailLogin(@RequestBody UserLoginByEmailAndCaptchaDTO loginParam, HttpServletRequest request) {
        return ResponseUtil.ok(userService.loginByEmail(loginParam.getEmail(), loginParam.getCode(), loginParam.getInviteCode(), request));
    }


    /**
     * 获取验证码
     */
    @ApiOperation("获取验证码")
    @PostMapping("/sms")
    public Response sendSms(@RequestBody @Valid SendSmsParam sendSmsParam) {
        String code = smsService.sendLoginSmsBydanmi(sendSmsParam.getMobile());
        log.info("用户登录手机号： {} 验证码: {}", sendSmsParam.getMobile(), code);
        return ResponseUtil.ok();
    }

    /**
     * 获取验证码
     */
    @ApiOperation("获取验证码")
    @PostMapping("/email")
    public Response sendEmail(@RequestBody @Valid SendEmailParam sendEmailParam) {
        Boolean result = emailService.sendLoginCode(sendEmailParam.getEmail());
        return result ? ResponseUtil.ok() :ResponseUtil.fail();
    }

    @GetMapping("/loginSuccess")
    public String loginSuccess(Principal principal, Model model) {
        // Get the user's details from the Principal object
        String username = principal.getName();
        model.addAttribute("username", username);
        return "loginSuccess";
    }

    @GetMapping("/loginFailure")
    public String loginFailure() {
        return "loginFailure";
    }

    @ApiOperation("Gmail授权登录 - 重定向到Google授权页面")
    @GetMapping(value = "/gmail")
    public void gmailLogin(@RequestParam(required = false) String inviteCode, HttpServletResponse response) throws IOException {
        // 构建Google OAuth2授权URL
        // 根据OAuth2标准，授权URL需要包含以下参数：
        // - response_type: 固定值"code"，表示使用授权码模式
        // - client_id: 应用的客户端ID
        // - redirect_uri: 授权后的回调地址
        // - scope: 请求的权限范围
        // - state: 防CSRF攻击的随机字符串，同时用于传递邀请码

        StringBuilder authUrl = new StringBuilder("https://accounts.google.com/o/oauth2/v2/auth");
        authUrl.append("?response_type=code");
        authUrl.append("&client_id=").append(gmailOAuthConfig.getClientId());
        authUrl.append("&redirect_uri=").append(gmailOAuthConfig.getRedirectUri());
        authUrl.append("&scope=openid email profile"); // 请求用户基本信息权限

        // 如果有邀请码，将其编码到state参数中
        // state参数有两个作用：
        // 1. 防止CSRF攻击（OAuth2标准要求）
        // 2. 传递邀请码到回调接口
        if (inviteCode != null && !inviteCode.isEmpty()) {
            authUrl.append("&state=").append(inviteCode);
        }

        log.info("重定向到Gmail授权页面: {}", authUrl.toString());

        // 发送302重定向响应，将用户浏览器重定向到Google授权页面
        response.sendRedirect(authUrl.toString());
    }

    @ApiOperation("Gmail授权登录回调")
    @GetMapping(value = "/gmail/login")
    public Response<TokenVO> gmailCallback(@RequestParam String code, @RequestParam(required = false) String state, HttpServletRequest request) {
        // 解析state参数获取邀请码
        // state参数是OAuth2标准中用于防止CSRF攻击的参数，我们同时用它来传递邀请码
        String inviteCode = null;
        if (state != null && !state.isEmpty()) {
            // 支持两种state格式：
            // 1. "invite_code=xxx" - 键值对格式
            // 2. "xxx" - 直接是邀请码
            if (state.startsWith("invite_code=")) {
                inviteCode = state.substring("invite_code=".length());
            } else {
                inviteCode = state;
            }
        }
        
        // 调用用户服务完成Gmail登录流程
        // code参数是Google返回的授权码，用于换取访问令牌
        return ResponseUtil.ok(userService.loginByGmail(code, inviteCode, request));
    }
}
