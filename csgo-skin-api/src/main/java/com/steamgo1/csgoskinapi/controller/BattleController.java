package com.steamgo1.csgoskinapi.controller;


import com.steamgo1.csgoskinapi.dto.BattleHomeCreateDTO;
import com.steamgo1.csgoskinapi.dto.BattleHomeJoinDTO;
import com.steamgo1.csgoskinapi.dto.BattleHomeQueryDTO;
import com.steamgo1.csgoskinapi.service.BattleHomeService;
import com.steamgo1.csgoskinapi.vo.BattleHomeBaseInfoVO;
import com.steamgo1.csgoskinapi.vo.BattleHomeVO;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RequestMapping("/battle")
@RestController
@RequiredArgsConstructor
@Api(tags = "对战房")
public class BattleController {

    @Autowired
    private BattleHomeService battleHomeService;

    @PostMapping("/create")
    @ApiOperation(value = "对战房间创建", notes = "")
    public Response<BattleHomeVO> battleHomeCreate(@RequestBody BattleHomeCreateDTO battleHomeCreateDTO) {
        return ResponseUtil.ok(battleHomeService.createBattleHome(battleHomeCreateDTO));
    }

    @GetMapping("/join")
    @ApiOperation(value = "参加对战房间", notes = "")
    public Response battleJoin(BattleHomeJoinDTO battleHomeJoinDTO) {
        battleHomeService.joinBattleHome(battleHomeJoinDTO.getBattleHomeId());
        return ResponseUtil.ok();
    }

    @GetMapping("/myBattleHome")
    @ApiOperation("参与过的对战房")
    public Response<Page<BattleHomeBaseInfoVO>> queryBattleHomeBaseInfo(BattleHomeQueryDTO battleHomeQueryDTO) {
        return ResponseUtil.ok(battleHomeService.queryMyBattleHomeBaseInfo(battleHomeQueryDTO));
    }
}

