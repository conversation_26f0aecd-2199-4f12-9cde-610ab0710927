package com.steamgo1.csgoskinapi.controller;

import com.steamgo1.csgoskinapi.dto.xinfupaybank.*;
import com.steamgo1.csgoskinapi.service.OrderService;
import com.steamgo1.csgoskinapi.service.XinfuPayBankService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

@RestController
@RequestMapping("/xinfupay")
@AllArgsConstructor
@ApiIgnore
@Slf4j
public class XinfuPayController {
    
    @Autowired
    private XinfuPayBankService xinfuPayBankService;

    @ApiOperation(value = "创建收款订单")
    @PostMapping("/createPayOrder")
    public PayOrderResponse createPayOrder(@RequestBody PayOrderRequest request) {
        return xinfuPayBankService.createPayOrder(request, PayOrderResponse.class);
    }

    @ApiOperation(value = "查询收款订单")
    @PostMapping("/queryPayOrder")
    public PayOrderQueryResponse queryPayOrder(@RequestParam String merchantOrderNo) {
        return xinfuPayBankService.queryPayOrder(merchantOrderNo);
    }

    @ApiOperation(value = "创建代付订单")
    @PostMapping("/createWithdrawal")
    public WithdrawalResponse createWithdrawal(@RequestBody WithdrawalRequest request) {
        return xinfuPayBankService.createWithdrawal(request);
    }

    @ApiOperation(value = "查询代付订单")
    @PostMapping("/queryWithdrawal")
    public WithdrawalQueryResponse queryWithdrawal(@RequestParam String merchantOrderNo) {
        return xinfuPayBankService.queryWithdrawalOrder(merchantOrderNo);
    }

    @ApiOperation(value = "查询账户余额")
    @PostMapping("/queryBalance")
    public AccountBalanceResponse queryBalance() {
        return xinfuPayBankService.queryAccountBalance();
    }

}