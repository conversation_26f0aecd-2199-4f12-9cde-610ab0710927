package com.steamgo1.csgoskinapi.controller;

import com.steamgo1.csgoskinapi.dto.CaseLotteryDTO;
import com.steamgo1.csgoskinapi.dto.UserCaseLotteryRecordQueryDTO;
import com.steamgo1.csgoskinapi.service.CaseService;
import com.steamgo1.csgoskinapi.utils.SecurityUtils;
import com.steamgo1.csgoskinapi.vo.CaseLotterResultVO;
import com.steamgo1.csgoskinapi.vo.CaseUserLotteryRecordVO;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Api(tags = "开箱")
@RestController
@RequestMapping("/case")
@RequiredArgsConstructor
public class CaseController {
    @Autowired
    private CaseService caseService;

    @ApiOperation("开箱")
    @PostMapping(value = "/lottery")
    public Response<List<CaseLotterResultVO>> lottery(@RequestBody @Valid CaseLotteryDTO caseLotteryDTO) {
        return ResponseUtil.ok(caseService.getCaseLotterResult(caseLotteryDTO.getCaseId(), caseLotteryDTO.getRounds(), SecurityUtils.getUserId()));
    }


    @ApiOperation("用户开箱记录")
    @GetMapping(value = "/recored")
    public Response<Page<CaseUserLotteryRecordVO>> lotteryRecord(UserCaseLotteryRecordQueryDTO userCaseLotteryRecordQueryDTO) {
        return ResponseUtil.ok(caseService.getUserLotterRecord(userCaseLotteryRecordQueryDTO, SecurityUtils.getUserId()));
    }
}
