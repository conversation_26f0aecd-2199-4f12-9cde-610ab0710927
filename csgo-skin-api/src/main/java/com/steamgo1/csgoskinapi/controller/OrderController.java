package com.steamgo1.csgoskinapi.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.steamgo1.csgoskinapi.dto.OrderChargeCreateDTO;
import com.steamgo1.csgoskinapi.dto.xinfupaybank.PayNotifyRequest;
import com.steamgo1.csgoskinapi.dto.xinfupaybank.WithdrawalNotifyRequest;
import com.steamgo1.csgoskinapi.service.BoxPayService;
import com.steamgo1.csgoskinapi.service.OrderService;
import com.steamgo1.csgoskinapi.service.XinfuPayBankService;
import com.steamgo1.csgoskinapi.utils.BoxPayUtils;
import com.steamgo1.csgoskinapi.vo.OrderChargeCreateVO;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Map;

@Api(tags = "订单")
@RestController
@RequestMapping("/order")
@RequiredArgsConstructor
@Slf4j
public class OrderController {
    @Autowired
    private OrderService orderService;

    @Autowired
    private BoxPayService boxPayService;

    @Resource
    private XinfuPayBankService xinfuPayBankService;


    @PostMapping("/charge/create")
    @ApiOperation(value = "充值订单创建", notes = "")
    public Response<OrderChargeCreateVO> chargeOrderCreate(@RequestBody OrderChargeCreateDTO orderChargeCreateDTO, HttpServletRequest request) {
        return ResponseUtil.ok(orderService.createOrderCharge(orderChargeCreateDTO.getChargeGoodsId(), orderChargeCreateDTO.getPayType(), request));
    }

    @ApiOperation(value = "微信支付回调通知处理")
    @PostMapping("/wechat/notify")
    public String parseOrderNotifyResult(@RequestBody String xmlData) throws WxPayException {
        log.info("开始回调, 微信：{}", xmlData);
        orderService.wxPayNotify(xmlData);
        // TODO 根据自己业务场景需要构造返回对象
        return WxPayNotifyResponse.success("成功");
    }

    @ApiOperation(value = "支付宝回调通知处理")
    @PostMapping("/ali/notify")
    public String aliOrderNotifyResult(HttpServletRequest request) throws WxPayException {
        Map<String, String[]> requestParams = request.getParameterMap();
        log.info("开始回调, 支付宝: {}", requestParams.toString());
        orderService.aliPayNotify(requestParams);
        // TODO 根据自己业务场景需要构造返回对象
        return WxPayNotifyResponse.success("成功");
    }

    @ApiOperation(value = "盒子支付同步通知处理")
    @RequestMapping("/boxpay/notify")
    public String notifyFunc(HttpServletRequest request) throws IOException {
        log.info("盒子支付同步通知处理");
        String userKey = boxPayService.getApiKey();
        String sign_str = "customerid=" + request.getParameter("customerid")
                + "&status=" + request.getParameter("status")
                + "&sdpayno=" + request.getParameter("sdpayno")
                + "&sdorderno=" + request.getParameter("sdorderno")
                + "&total_fee=" + request.getParameter("total_fee")
                + "&realmoney=" + request.getParameter("realmoney")
                + "&paytype=" + request.getParameter("paytype")
                + "&" + userKey;
        String sign = BoxPayUtils.encryptToMD5(sign_str);
        if (request.getParameter("status").equals("1") && sign.equals(request.getParameter("sign"))) {
            orderService.boxPayNotify(request.getParameter("sdorderno"));
            return "success";
        } else {
            return "error";
        }
    }

    @ApiOperation(value = "盒子支付异步通知处理")
    @RequestMapping("/boxpay/return")
    public String returnFunc(HttpServletRequest request) throws IOException {
        log.info("盒子支付异步通知处理");
        String userKey = boxPayService.getApiKey();
        String sign_str = "customerid=" + request.getParameter("customerid")
                + "&status=" + request.getParameter("status")
                + "&sdpayno=" + request.getParameter("sdpayno")
                + "&sdorderno=" + request.getParameter("sdorderno")
                + "&total_fee=" + request.getParameter("total_fee")
                + "&realmoney=" + request.getParameter("realmoney")
                + "&paytype=" + request.getParameter("paytype")
                + "&" + userKey;
        String sign = BoxPayUtils.encryptToMD5(sign_str);
        if (request.getParameter("status").equals("1") && sign.equals(request.getParameter("sign"))) {
            orderService.boxPayNotify(request.getParameter("sdorderno"));
            return "success";
        } else {
            return "error";
        }
    }

    /**
     * 处理服务器异步通知（notify_url）
     */
    @GetMapping("/epay/notify")
    public String handleNotifyUrl(HttpServletRequest request) throws Exception {
        log.info("收到回调易支付");
//        out_trade_no
        if (request.getParameter("trade_status").equals("TRADE_SUCCESS")) {
            orderService.boxPayNotify(request.getParameter("out_trade_no"));
            return "success";
        } else {
            return "error";
        }
    }


    @ApiOperation(value = "xinfuPay收款回调通知处理")
    @PostMapping("/xinfuPay/notify")
    public String payNotify(@RequestBody PayNotifyRequest request) {
        log.info("开始回调, 信付支付: {}", JSONObject.toJSONString(request));

        // 验证签名
        if (!xinfuPayBankService.verifyNotifySign(request)) {
            log.error("信付支付回调签名验证失败");
            return "FAIL";
        }

        // 检查支付状态
        if ("2".equals(request.getStatus())) {
            orderService.xinfuPayNotify(request.getMerchantOrderNo());
            return "SUCCESS";
        } else {
            log.error("信付支付回调状态异常: {}", request.getStatus());
            return "FAIL";
        }
    }

    @ApiOperation(value = "xinfuPay代付回调通知处理")
    @PostMapping("/xinfuPay/withdrawal/notify")
    public String withdrawalNotify(@RequestBody WithdrawalNotifyRequest request) {
        log.info("开始回调, 信付支付代付: {}", JSONObject.toJSONString(request));

        // 验证签名
        if (!xinfuPayBankService.verifyWithdrawalNotifySign(request)) {
            log.error("信付支付代付回调签名验证失败");
            return "FAIL";
        }

        // 检查代付状态
        if ("2".equals(request.getStatus())) {
            // 代付成功处理逻辑
            log.info("代付成功: {}", request.getMerchantOrderNo());
            return "SUCCESS";
        } else if ("4".equals(request.getStatus())) {
            // 代付失败处理逻辑
            log.error("代付失败: {}, 原因: {}", request.getMerchantOrderNo(), request.getMsg());
            return "SUCCESS";
        } else {
            log.error("信付支付代付回调状态异常: {}", request.getStatus());
            return "FAIL";
        }
    }
}
