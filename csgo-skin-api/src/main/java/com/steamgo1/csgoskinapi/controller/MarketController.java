package com.steamgo1.csgoskinapi.controller;

import com.steamgo1.csgoskinapi.dto.*;
import com.steamgo1.csgoskinapi.service.*;
import com.steamgo1.csgoskinapi.vo.*;
import com.steamgo1.csgoskincommon.contant.CsgoContants;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import com.steamgo1.csgoskincommon.vo.RollHomeFilterParamVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "市场")
@RestController
@RequestMapping("/market")
@RequiredArgsConstructor
public class MarketController {
    @Autowired
    private CaseService caseService;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private SkinService skinService;

    @Autowired
    private RollHomeService rollHomeService;

    @Autowired
    private BattleHomeService battleHomeService;

    @Autowired
    private PercentageService percentageService;

    @Autowired
    private ActivityService activityService;

    @Autowired
    private UserService userService;

    @ApiOperation("获取饰品查询条件")
    @GetMapping("/skin/filters")
    public Response<SkinQueryParamVO> skinQueryParamVOResponse() {
        return ResponseUtil.ok(skinService.skinQueryParam());
    }

    @GetMapping("/battleHome/filters")
    @ApiOperation("获取对战房查询条件")
    public Response<BattleHomeQueryParamVO> getBattleHomeQueryParam() {
        return ResponseUtil.ok(battleHomeService.getBattleHomequeryParam());
    }

    @GetMapping("/battleHome")
    @ApiOperation("查询对战房")
    public Response<Page<BattleHomeBaseInfoVO>> queryBattleHomeBaseInfo(BattleHomeQueryDTO battleHomeQueryDTO) {
        return ResponseUtil.ok(battleHomeService.queryBattleHomeBaseInfo(battleHomeQueryDTO));
    }

    @GetMapping("/battleHome/{id}")
    @ApiOperation("获取对战房详情")
    public Response<BattleHomeVO> queryBattleHomeById(@PathVariable("id") Long battleHomeId) {
        return ResponseUtil.ok(battleHomeService.queryBattleHomeBaseInfoById(battleHomeId));
    }


    @ApiOperation("查询饰品")
    @GetMapping("/skin")
    public Response<Page<SkinInfoVO>> querySkinInfoResponse(SkinQueryDTO skinQueryDTO) {
        return ResponseUtil.ok(skinService.querySkins(skinQueryDTO));
    }

    @ApiOperation("查询随机奖励饰品")
    @GetMapping("/random/skin")
    public Response<Page<SkinInfoVO>> queryRandomSkinInfoResponse(SkinQueryDTO skinQueryDTO) {
        return ResponseUtil.ok(skinService.queryRandomSkins(skinQueryDTO));
    }

    @ApiOperation("ID查询饰品")
    @GetMapping("/skin/{id}")
    public Response<SkinInfoVO> querySkinInfoByIdResponse(@PathVariable("id") Long skinId) {
        return ResponseUtil.ok(skinService.querySkinInfoById(skinId));
    }


    @GetMapping
    @ApiOperation("获取所有充值商品")
    public Response<List<ChargeGoodsVO>> queryChargeGoods() {
        return ResponseUtil.ok(goodsService.queryChargeGoods());
    }

    @GetMapping(value = "/case/cagegory")
    @ApiOperation("获取箱子类别")
    public Response<List<CaseCategoryVO>> queryCaseCategory() {
        return ResponseUtil.ok(caseService.getCaseCategory());
    }

    @GetMapping(value = "/case")
    @ApiOperation("获取所有箱子")
    public Response<List<CaseInfoOfCategoryVO>> queryCaseInfoOfCategory(CaseQueryParamDTO caseQueryParamDTO) {
        return ResponseUtil.ok(caseService.getCaseInfoOfCategory(caseQueryParamDTO));
    }

    @ApiOperation("获取箱子详情")
    @GetMapping(value = "/case/{id}")
    public Response<CaseFullInfoVO> getCase(@PathVariable Long id) {
        return ResponseUtil.ok(caseService.getCaseFullInfo(id, CsgoContants.levelNumber.LEVEL_1));
    }

    @ApiOperation("获取箱子开箱记录")
    @GetMapping(value = "/case/{id}/record")
    public Response<Page<CaseUserLotteryRecordOfCaseVO>> queryCaseUserLotteryRecordOfCase(PageQueryDTO pageQueryDTO, @PathVariable Long id) {
        return ResponseUtil.ok(caseService.queryCaseUserLotteryRecordOfCase(pageQueryDTO, id));
    }

    @ApiOperation("获取Roll房查询条件")
    @GetMapping(value = "/rollhome/filters")
    public Response<RollHomeFilterParamVO> qeuryRollHomeFilterParam() {
        return ResponseUtil.ok(rollHomeService.getRollHomeFilterParam());
    }

    @ApiOperation("查询roll房")
    @GetMapping(value = "/rollhome")
    public Response<Page<RollHomeBaseInfoVO>> queryRollhomes(RollHomeQueryDTO rollHomeQueryDTO) {
        return ResponseUtil.ok(rollHomeService.queryRollHomes(rollHomeQueryDTO));
    }

    @ApiOperation("查询单个Roll房")
    @GetMapping(value = "/rollhome/{id}")
    public Response<RollHomeVO> queryRollhomeById(@PathVariable("id") Long id) {
        return ResponseUtil.ok(rollHomeService.queryRollHomeById(id));
    }

    @ApiOperation("查询单个Roll房饰品信息")
    @GetMapping(value = "/rollhome/skin/{id}")
    public Response<Page<RollHomeSkinVO>> queryRollhomeSkinById(@PathVariable("id") Long id, PageQueryDTO pageQueryDTO) {
        return ResponseUtil.ok(rollHomeService.queryRollHomeSkins(id, pageQueryDTO));
    }


    @ApiOperation("查询单个Roll房用户信息")
    @GetMapping(value = "/rollhome/user/{id}")
    public Response<Page<RollHomeUserVO>> queryRollhomeUserById(@PathVariable("id") Long id, PageQueryDTO pageQueryDTO) {
        return ResponseUtil.ok(rollHomeService.queryRollHomeUsers(id, pageQueryDTO));
    }


    @ApiOperation("查询追梦记录")
    @GetMapping(value = "/percentage/record")
    public Response<Page<PercentageLotteryRecordVO>> queryPercentageLotteryRecord(PageQueryDTO pageQueryDTO) {
        return ResponseUtil.ok(percentageService.queryPercentageLotteryRecord(pageQueryDTO));
    }


    @ApiOperation("查询用户背包")
    @GetMapping("/package")
    public Response<Page<UserPackageVO>> queryUserPackage(OtherUserPackageQueryParamDTO otherUserPackageQueryParamDTO) {
        return ResponseUtil.ok(userService.queryOtherUserPackageVO(otherUserPackageQueryParamDTO));
    }

    @ApiOperation("查询用户饰品出售记录")
    @GetMapping("/package/sell/record")
    public Response<Page<UserPackageSkinSellRecordVO>> queryUserPackageSkinSellRecord(OtherUserPackageSkinSellRecordQueryDTO userPackageSkinSellRecordQueryDTO) {
        return ResponseUtil.ok(userService.queryOtherUserPackageSkinSellRecordVO(userPackageSkinSellRecordQueryDTO));
    }

    @ApiOperation("查询用户饰品取回记录")
    @GetMapping("/skin/pickup/record")
    public Response<Page<UserPackageSkinPickUpRecordVO>> queryUserSkinPickUpRecord(OtherUserPackageSkinPickUpRecordQueryDTO userPackageSkinPickUpRecordQueryDTO) {
        return ResponseUtil.ok(userService.queryOtherUserPickagePickUpRecord(userPackageSkinPickUpRecordQueryDTO));
    }

    @ApiOperation("查询用户兑换记录")
    @GetMapping("/diamond/exchange/record")
    public Response<Page<UserDiamondToCoinRecordVO>> queryUserDiamondToCoinRecordVO(OtherUserDiamondToCoinRecordQueryDTO userDiamondToCoinRecordQueryDTO) {
        return ResponseUtil.ok(userService.queryOtherUserDiamondToCoinRecord(userDiamondToCoinRecordQueryDTO));
    }

    @ApiOperation("查询集卡活动")
    @GetMapping("/activity/cardCollect")
    public Response<CardCollectVO> queryCardCollect() {
        return ResponseUtil.ok(activityService.queryCardCollect());
    }

    @ApiOperation("获取背包饰品查询来源")
    @GetMapping("/package/source")
    public Response<UserPackageSkinSourceVO> userPackageSkinSource() {
        return ResponseUtil.ok(userService.pacekageSkinSource());
    }

    @ApiOperation("获取箱子颜色字典")
    @GetMapping("/case/skinColor")
    public Response<List<SkinRarityColorFilterVO>> querySkinRarityColorFilterVO() {
        return ResponseUtil.ok(caseService.querySkinRarityColorFilterVO());
    }


}
