package com.steamgo1.csgoskinapi.controller;


import com.steamgo1.csgoskinapi.dto.IdParamDTO;
import com.steamgo1.csgoskinapi.dto.RedPicketParamDTO;
import com.steamgo1.csgoskinapi.service.ActivityService;
import com.steamgo1.csgoskinapi.utils.SecurityUtils;
import com.steamgo1.csgoskinapi.vo.CardCollectUserVO;
import com.steamgo1.csgoskinapi.vo.UserCheckInRedPacketVO;
import com.steamgo1.csgoskinapi.vo.UserDailyRedPacketVO;
import com.steamgo1.csgoskinapi.vo.WalletVO;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/activity")
@Api(tags = "活动相关接口")
public class ActivityController {

    @Autowired
    private ActivityService activityService;

    @ApiOperation("领取口令红包")
    @PostMapping("/redpacket/draw")
    public Response<WalletVO> drawRedpackage(@RequestBody @Valid RedPicketParamDTO redPicketParamDTO) {
        return ResponseUtil.ok(activityService.receiveRedPicket(redPicketParamDTO.getKey()));
    }

    @ApiOperation("查询卡片收集")
    @GetMapping("/cardCollect/{id}")
    public Response<List<CardCollectUserVO>> queryCardCollectUser(@PathVariable Long id) {
        return ResponseUtil.ok(activityService.queryUserCard(id));
    }

    @ApiOperation("领取每日红包")
    @PostMapping("/dailyRedpacket/receive")
    public Response<WalletVO> receiveDailyRedpacket(@RequestBody IdParamDTO idParamDTO) {
        Long userId = SecurityUtils.getUserId();
        return ResponseUtil.ok(activityService.receiveUserDailyRedPacket(userId, idParamDTO.getId()));
    }

    @ApiOperation("领取签到奖励")
    @PostMapping("/checkIn/receive")
    public Response<WalletVO> receiveCheckIn(@RequestBody IdParamDTO idParamDTO) {
        Long userId = SecurityUtils.getUserId();
        return ResponseUtil.ok(activityService.receiveUserCheckInRedPacket(userId, idParamDTO.getId()));
    }

    @ApiOperation("查询每日红包详情")
    @GetMapping("/dailyRedpacket/detail")
    public Response<UserDailyRedPacketVO> queryUserDailyRedPacketVO() {
        Long userId = SecurityUtils.getUserId();
        return ResponseUtil.ok(activityService.queryUserDailyRedPacket(userId));
    }

    @ApiOperation("查询签到详情")
    @GetMapping("/checkIn/detail")
    public Response<UserCheckInRedPacketVO> queryUserCheckInRedPacketVO() {
        Long userId = SecurityUtils.getUserId();
        return ResponseUtil.ok(activityService.queryUserCheckInRedPacket(userId));
    }

    @ApiOperation("用户签到")
    @GetMapping("/checkIn")
    public Response userCheckIn() {
        Long userId = SecurityUtils.getUserId();
        activityService.userCheckIn(userId);
        return ResponseUtil.ok();
    }

}
