package com.steamgo1.csgoskinapi.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.LocaleResolver;

import javax.servlet.http.HttpServletRequest;
import java.util.Locale;

@Api(tags = "国际化消息")
@RestController("message")
public class MessageController {

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private LocaleResolver localeResolver;

    @ApiOperation("普通")
    @GetMapping("/")
    public String greeting(HttpServletRequest request, @RequestHeader(value = "Accept-Language", required = false) String acceptLanguage) {
        Locale locale = localeResolver.resolveLocale(request);

        // set language by Accept-Language
        if (acceptLanguage != null && !acceptLanguage.isEmpty()) {
            String[] languages = acceptLanguage.split(",");
            // get the first language
            String language = languages[0].split(";")[0];
            language = language.trim();
            locale = Locale.forLanguageTag(language);
        }

        return messageSource.getMessage("validate.token.error", null, locale);
    }
}
