package com.steamgo1.csgoskinapi.controller;


import com.steamgo1.csgoskinapi.dto.*;
import com.steamgo1.csgoskinapi.service.CaseService;
import com.steamgo1.csgoskinapi.service.LotteryService;
import com.steamgo1.csgoskinapi.service.UserService;
import com.steamgo1.csgoskinapi.utils.SecurityUtils;
import com.steamgo1.csgoskinapi.vo.*;
import com.steamgo1.csgoskincommon.service.ZBTService;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RequestMapping("/user")
@RestController
@RequiredArgsConstructor
@Api(tags = "个人中心")
public class UserController {
    @Autowired
    private UserService userService;

    @Autowired
    private LotteryService lotteryService;

    @Autowired
    private CaseService caseService;

    @Autowired
    private ZBTService zbtService;


    /**
     * 获取用户信息
     */
    @ApiOperation("获取用户信息")
    @GetMapping("/info")
    public Response<UserInfoVO> info() {
        return ResponseUtil.ok(userService.queryUserInfo());
    }

    @ApiOperation("获取用户算法数据")
    @GetMapping("/algorithmData")
    public Response<UserAlgorithmDataVO> getUserAlgorithmDataVO() {
        return ResponseUtil.ok(userService.queryUserAlgorihmData());
    }

    @ApiOperation("获取用户算法数据")
    @GetMapping("/algorithmData/history")
    public Response<Page<UserAlgorithmDataFullVO>> getHistoryUserAlgorithmDataVO(PageQueryDTO pageQueryDTO) {
        return ResponseUtil.ok(userService.queryHistoryUserAlorithmData(pageQueryDTO));
    }

    @ApiOperation("用户重置算法数据")
    @GetMapping("/lottery/reset")
    public Response<UserAlgorithmDataVO> resetUserAlgorithmData() {
        return ResponseUtil.ok(userService.resetUserAlgorihmData());
    }

    @ApiOperation("用户更新个人种子")
    @PutMapping("/lottery/clientSeed")
    public Response<UserAlgorithmDataVO> updateClientSeed(@RequestBody UserUpdateAlgorihmDataDTO updateAlgorihmDataDTO) {
        return ResponseUtil.ok(userService.updateClientSeed(updateAlgorihmDataDTO.getClientSeed()));
    }


    @ApiOperation("用户背包")
    @GetMapping("/package")
    public Response<Page<UserPackageVO>> queryUserPackage(UserPackageQueryParamDTO userPackageQueryParamDTO) {
        return ResponseUtil.ok(userService.queryUserPackageVO(userPackageQueryParamDTO));
    }

    @ApiOperation("配置steam交易连接")
    @PutMapping("tradeOfferAccessUrl")
    public Response<UserInfoVO> updateUserSteamId(@RequestBody UserUpdateTradeOfferAccessUrlDTO updateTradeOfferAccessUrlDTO) {
        return ResponseUtil.ok(userService.updateUserTradeOfferAccessUrl(updateTradeOfferAccessUrlDTO.getTradeOfferAccessUrl()));
    }

    @ApiOperation("配置头像")
    @PutMapping("avatar")
    public Response<UserInfoVO> updateUserAvatar(@RequestBody UserUpdateAvatarDTO updateAvatarDTO) {
        return ResponseUtil.ok(userService.updateUserAvatar(updateAvatarDTO.getAvatar()));
    }

    @ApiOperation("配置用户名")
    @PutMapping("nickname")
    public Response<UserInfoVO> updateUserName(@RequestBody UserUpdateNameDTO updateNameDTO) {
        return ResponseUtil.ok(userService.updateUserName(updateNameDTO.getNickname()));
    }

    @ApiOperation("查询金币流水")
    @GetMapping("/coin/record")
    public Response<Page<UserCoinRecordVO>> queryUserCoinRecord(UserCoinRecordQueryDTO userCoinRecordQueryDTO) {
        return ResponseUtil.ok(userService.queryUserCoinRecord(userCoinRecordQueryDTO));
    }

    @ApiOperation("查询钻石流水")
    @GetMapping("/diamond/record")
    public Response<Page<UserDiamondRecordVO>> queryUserDiamondRecord(UserDiamondRecordQueryDTO userCoinRecordQueryDTO) {
        return ResponseUtil.ok(userService.queryUserDiamondRecord(userCoinRecordQueryDTO));
    }

    @ApiOperation("背包饰品取回")
    @PostMapping("/package/pickup")
    public Response packagePickUP(@RequestBody @Valid UserPackagePickUpParamDTO userPackagePickUpParamDTO) {
        userService.pickUpSkin(userPackagePickUpParamDTO.getPackageIds());
        return ResponseUtil.ok();
    }

    @ApiOperation("查询饰品取回记录")
    @GetMapping("/skin/pickup/record")
    public Response<Page<UserPackageSkinPickUpRecordVO>> queryUserSkinPickUpRecord(UserPackageSkinPickUpRecordQueryDTO userPackageSkinPickUpRecordQueryDTO) {
        return ResponseUtil.ok(userService.queryUserPickagePickUpRecord(userPackageSkinPickUpRecordQueryDTO));
    }

    @ApiOperation("用户饰品出售")
    @PostMapping("/package/sell")
    public Response<WalletVO> packageSell(@RequestBody @Valid UserPackageSellParamDTO userPackageSellParamDTO) {
        return ResponseUtil.ok(userService.sellSkin(userPackageSellParamDTO.getPackageIds()));
    }

    @ApiOperation("用户钻石兑换")
    @PostMapping("/diamond/exchange")
    public Response<WalletVO> diamondExchange(@RequestBody @Valid UserDiamondExchangeParamDTO userDiamondExchangeParamDTO) {
        return ResponseUtil.ok(userService.diamondToCoin(userDiamondExchangeParamDTO.getDiamond()));
    }

    @ApiOperation("查询用户兑换记录")
    @GetMapping("/diamond/exchange/record")
    public Response<Page<UserDiamondToCoinRecordVO>> queryUserDiamondToCoinRecordVO(UserDiamondToCoinRecordQueryDTO userDiamondToCoinRecordQueryDTO) {
        return ResponseUtil.ok(userService.queryUserDiamondToCoinRecord(userDiamondToCoinRecordQueryDTO));
    }

    @ApiOperation("用户商城购买饰品")
    @PostMapping("/buy/skin")
    public Response<WalletVO> diamondExchange(@RequestBody @Valid UserBuySkinParamDTO userBuySkinParamDTO) {
        return ResponseUtil.ok(userService.buySkin(userBuySkinParamDTO.getSkinId()));
    }

    @ApiOperation("查询购买饰品记录")
    @GetMapping("/buy/skin/record")
    public Response<Page<UserBuySkinRecordVO>> queryUserBuySkinRecord(UserBuySkinRecordQueryDTO userBuySkinRecordQueryDTO) {
        return ResponseUtil.ok(userService.buySkinRecord(userBuySkinRecordQueryDTO));
    }

    @ApiOperation("查询用户钱包")
    @GetMapping("/wallet")
    public Response<WalletVO> queryWallet() {
        return ResponseUtil.ok(userService.queryUserWallet());
    }

    @ApiOperation("查询用户推广信息")
    @GetMapping("/invite")
    public Response<UserInviteVO> queryUserInviteInfo() {
        return ResponseUtil.ok(userService.getUserInviteInfo());
    }

    @ApiOperation("查询用户推广记录")
    @GetMapping("/invite/record")
    public Response<Page<UserInviteEncourageRecordVO>> queryUserInviteRecord(UserInviteEncouragRecordQueryDTO userInviteEncouragRecordQueryDTO) {
        return ResponseUtil.ok(userService.queryUserInviteEncourageRecord(userInviteEncouragRecordQueryDTO));
    }

    @ApiOperation("用户饰品出售记录")
    @GetMapping("/package/sell/record")
    public Response<Page<UserPackageSkinSellRecordVO>> queryUserPackageSkinSellRecord(UserPackageSkinSellRecordQueryDTO userPackageSkinSellRecordQueryDTO) {
        return ResponseUtil.ok(userService.queryUserPackageSkinSellRecordVO(userPackageSkinSellRecordQueryDTO));
    }

    @ApiOperation("查询用户库存价值")
    @GetMapping("/package/value")
    public Response<UserPackageValueVO> queryUserPackageValue() {
        Long userId = SecurityUtils.getUserId();
        return ResponseUtil.ok(userService.queryUserPackageValue(userId));
    }

    @ApiOperation("背包饰品锁定/解锁")
    @PostMapping("/package/lock")
    public Response packageSkinLock(@RequestBody @Valid UserPackageLockParamDTO userPackageLockParamDTO) {
        userService.lockSkin(userPackageLockParamDTO.getPackageIds(), userPackageLockParamDTO.getIsLock());
        return ResponseUtil.ok();
    }

    @ApiOperation("查询寻宝详情")
    @PostMapping("/consume/details")
    public Response<ConsumeCaseVO> consumeCase() {
        return ResponseUtil.ok(caseService.queryConsumeCase());
    }

    @ApiOperation("在线验证")
    @PostMapping("/lottery/verify")
    public Response<LotteryVerifyResult> verifyAlgorithmData(@RequestBody LotteryVerifyDTO lotteryVerifyDTO) {
        return ResponseUtil.ok(lotteryService.lottery(lotteryVerifyDTO));
    }

    @ApiOperation("查询日常任务箱子详情")
    @PostMapping("/dailyTaskCase/details")
    public Response<List<DailyTaskCaseVO>> queryDailyTaskCaseVO() {
        return ResponseUtil.ok(caseService.queryDailyTaskCase());
    }

    @ApiOperation("实名认证")
    @PostMapping("/realname/verify")
    public Response idCardValidate(@RequestBody UserVerifyDTO userVerifyDTO) {
        userService.idCardValidate(userVerifyDTO);
        return ResponseUtil.ok();
    }
}
