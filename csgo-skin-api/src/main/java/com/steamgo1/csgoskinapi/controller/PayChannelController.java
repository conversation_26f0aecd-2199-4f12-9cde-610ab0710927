package com.steamgo1.csgoskinapi.controller;

import com.steamgo1.csgoskinapi.service.PayChannelService;
import com.steamgo1.csgoskinapi.vo.PayChannelVO;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "支付渠道")
@RestController
@RequestMapping("/payChannel")
public class PayChannelController {
    
    @Autowired
    private PayChannelService payChannelService;
    
    @ApiOperation("获取所有可用的支付渠道")
    @GetMapping("/available")
    public Response<List<PayChannelVO>> getAvailablePayChannels() {
        return ResponseUtil.ok(payChannelService.getAvailablePayChannels());
    }
}