package com.steamgo1.csgoskinapi.controller;

import com.steamgo1.csgoskinapi.dto.UserLotteryPercentageDTO;
import com.steamgo1.csgoskinapi.dto.UserPercentageLotteryRecordQueryDTO;
import com.steamgo1.csgoskinapi.service.PercentageService;
import com.steamgo1.csgoskinapi.utils.SecurityUtils;
import com.steamgo1.csgoskinapi.vo.PercentageResultVO;
import com.steamgo1.csgoskinapi.vo.PercetageUserLotteryRecordVO;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

@Api(tags = "追梦")
@RestController
@RequestMapping("/percentage")
@RequiredArgsConstructor
public class PercentageController {

    @Autowired
    private PercentageService percentageService;

    @ApiOperation("追梦")
    @PostMapping("/lettory")
    public Response<PercentageResultVO> percentageLettory(@RequestBody UserLotteryPercentageDTO userLotteryPercentageDTO) {
        return ResponseUtil.ok(percentageService.percentageLottery(userLotteryPercentageDTO));
    }

    @ApiOperation("用户追梦记录")
    @GetMapping(value = "/recored")
    public Response<Page<PercetageUserLotteryRecordVO>> lotteryRecord(UserPercentageLotteryRecordQueryDTO userPercentageLotteryRecordQueryDTO) {
        return ResponseUtil.ok(percentageService.queryPercetageUserLotteryRecord(userPercentageLotteryRecordQueryDTO, SecurityUtils.getUserId()));
    }

}
