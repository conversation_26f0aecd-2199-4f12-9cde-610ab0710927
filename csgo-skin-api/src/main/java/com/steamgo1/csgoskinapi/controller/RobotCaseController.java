package com.steamgo1.csgoskinapi.controller;

import com.steamgo1.csgoskinapi.dto.CaseLotteryDTO;
import com.steamgo1.csgoskinapi.service.CaseService;
import com.steamgo1.csgoskinapi.vo.CaseLotterResultVO;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@Api(tags = "机器人开箱")
@RestController
@RequestMapping("/robot/case")
@RequiredArgsConstructor
public class RobotCaseController {
    @Autowired
    private CaseService caseService;

    @ApiOperation("开箱")
    @PostMapping(value = "/lottery")
    public Response<List<CaseLotterResultVO>> lottery(@RequestBody @Valid CaseLotteryDTO caseLotteryDTO) {
        caseService.rorbotOpenCaseByRound(caseLotteryDTO.getNumber(), caseLotteryDTO.getCaseId());
        return ResponseUtil.ok();
    }

}
