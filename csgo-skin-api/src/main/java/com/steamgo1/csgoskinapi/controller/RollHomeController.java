package com.steamgo1.csgoskinapi.controller;


import com.steamgo1.csgoskinapi.dto.RollHomeJoinDTO;
import com.steamgo1.csgoskinapi.dto.RollHomeQueryDTO;
import com.steamgo1.csgoskinapi.service.RollHomeService;
import com.steamgo1.csgoskinapi.vo.RollHomeBaseInfoVO;
import com.steamgo1.csgoskinapi.vo.RollHomeVO;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

@Api(tags = "Roll房")
@RestController
@RequestMapping({"/rollhome", "/roll/home"})
@RequiredArgsConstructor
public class RollHomeController {
    @Autowired
    private RollHomeService rollHomeService;

    @ApiOperation("参与Roll房")
    @PostMapping(value = "/join")
    public Response<RollHomeVO> joinRollhome(@RequestBody RollHomeJoinDTO rollHomeJoinDTO) {
        return ResponseUtil.ok(rollHomeService.joinRollHome(rollHomeJoinDTO));
    }


    @ApiOperation("查询roll房")
    @GetMapping(value = "/myRollHome")
    public Response<Page<RollHomeBaseInfoVO>> queryMyRollHome(RollHomeQueryDTO rollHomeQueryDTO) {
        return ResponseUtil.ok(rollHomeService.queryMyRollHomes(rollHomeQueryDTO));
    }


}
