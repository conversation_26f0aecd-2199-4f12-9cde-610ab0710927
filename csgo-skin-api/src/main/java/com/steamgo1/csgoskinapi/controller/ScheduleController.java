package com.steamgo1.csgoskinapi.controller;

import com.steamgo1.csgoskincommon.service.ZBTService;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Api(tags = "定时任务测试")
@RestController
@RequestMapping("/schedule")
@RequiredArgsConstructor
public class ScheduleController {

    @Autowired
    private ZBTService zbtService;

    @ApiOperation("同步饰品信息")
    @PostMapping("/syncSkin")
    public Response syncSkin() {
        zbtService.SyncSkinInfoByDBNew();
        return ResponseUtil.ok();
    }

}