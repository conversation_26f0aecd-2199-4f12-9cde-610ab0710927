package com.steamgo1.csgoskinapi.controller;

import com.steamgo1.csgoskinapi.service.*;
import com.steamgo1.csgoskinapi.vo.*;
import com.steamgo1.csgoskincommon.utils.ResponseUtil;
import com.steamgo1.csgoskincommon.vo.AnnouncementVO;
import com.steamgo1.csgoskincommon.vo.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/site")
@Api(tags = "站点相关接口")
public class SiteController {
    @Autowired
    private CaseService caseService;

    @Autowired
    private RollHomeService rollHomeService;

    @Autowired
    private PercentageService percentageService;

    @Autowired
    private SiteService siteService;

    @Autowired
    private BattleHomeService battleHomeService;


    @ApiOperation("获取推荐roll房")
    @GetMapping(value = "/case/recommend")
    public Response<List<RollHomeBaseInfoVO>> queryRecommendRollHome() {
        return ResponseUtil.ok(rollHomeService.queryRecommendRollHome());
    }

    @ApiOperation("获取最新Roll房中奖")
    @GetMapping(value = "/lottery/result/rollhome")
    public Response<List<UserLotteryResultVO>> queryRollHomeLotteryResult() {
        return ResponseUtil.ok(rollHomeService.queryRollHomeUserLotteryResult());
    }

    @ApiOperation("获取最新追梦中奖")
    @GetMapping(value = "/lottery/result/percentage")
    public Response<List<UserLotteryResultVO>> queryPercentageUserLotteryResult() {
        return ResponseUtil.ok(percentageService.queryPercentageUserLotteryResult());
    }

    @ApiOperation("获取最新开箱中奖")
    @GetMapping(value = "/lottery/result/case")
    public Response<List<UserLotteryResultVO>> queryOpenCaseUserLotteryResult() {
        return ResponseUtil.ok(caseService.queryOpenCaseUserLotteryResult());
    }

    @ApiOperation("获取汇率")
    @GetMapping(value = "/exchangeRate")
    public Response<SysExchangeRateVO> querySysExchangeRate() {
        return ResponseUtil.ok(siteService.getExchangeRate());
    }

    @ApiOperation("获取联系方式")
    @GetMapping(value = "/contact")
    public Response<ContactInfomationVO> queryContactInfomation() {
        return ResponseUtil.ok(siteService.getContactInfomationVO());
    }

    @ApiOperation("查询昨日对战TOP10")
    @GetMapping(value = "/battle/yesterday/top10")
    public Response<List<BattleTop10VO>> queryBattleTop10VO() {
        return ResponseUtil.ok(battleHomeService.queryBattleTop10VO());
    }

    @ApiOperation("查询公告")
    @GetMapping(value = "/announcement")
    public Response<List<AnnouncementVO>> queryAnnount() {
        return ResponseUtil.ok(siteService.queryAnnountVO());
    }

    @ApiOperation("查询首页Banner")
    @GetMapping(value = "/banner")
    public Response<List<IndexBannerVO>> queryBanner() {
        return ResponseUtil.ok(siteService.queryIndexBanner());
    }

    @ApiOperation("查询历史最高价值掉落饰品top3")
    @GetMapping(value = "/skin/win/top3")
    public Response<List<WinSkinTop3VO>> queryWinSkinTop3VO(HttpServletRequest request) {
        return ResponseUtil.ok(siteService.queryWinSKinTop3(request));
    }

    @ApiOperation("查询统计数据")
    @GetMapping(value = "/statisticsinfo")
    public Response<StatisticsInfoVO> queryStatistics() {
        return ResponseUtil.ok(siteService.getStatistics());
    }
}
