package com.steamgo1.csgoskinapi.schedule.task;

import com.steamgo1.csgoskinapi.enums.WxMsgType;
import com.steamgo1.csgoskinapi.service.AsyncTaskService;
import com.steamgo1.csgoskincommon.config.ConfigurerScheduling;
import com.steamgo1.csgoskincommon.dao.UserPackagePickupRepository;
import com.steamgo1.csgoskincommon.dao.UserPackageRepository;
import com.steamgo1.csgoskincommon.entity.UserPackagePickupEntity;
import com.steamgo1.csgoskincommon.entity.enums.PackagePickupStatus;
import com.steamgo1.csgoskincommon.service.ZBTService;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.time.LocalDateTime;

@Slf4j
//@Configuration
public class ZBTPickUpNotifyTask extends ConfigurerScheduling {
    @Value("${schedule.interval.zbtNotify}")
    private String runInterval;

    @Autowired
    private UserPackagePickupRepository userPackagePickupRepository;

    @Autowired
    private AsyncTaskService asyncTaskService;


    @Override
    protected void processTask() {
        log.debug("取货状态通知 定时任务 {}, 线程名称： {}, 线程ID: {}", LocalDateTime.now(), Thread.currentThread().getName(), Thread.currentThread().getId());
        for (UserPackagePickupEntity userPackagePickupEntity : userPackagePickupRepository.findByIsRemindIsFalse()) {
            log.debug("饰品取回通知：{} 用户：{} 状态：{} {}", userPackagePickupEntity.getId(), userPackagePickupEntity.getUser().getId(), userPackagePickupEntity.getStatus().getValue(), userPackagePickupEntity.getStatus());
            if (StringUtil.isNullOrEmpty(userPackagePickupEntity.getUser().getWxOpenid())) {
                log.error("用户未关注公众号");
                userPackagePickupEntity.setIsRemind(true);
                userPackagePickupRepository.save(userPackagePickupEntity);
                continue;
            }
            if (userPackagePickupEntity.getStatus().equals(PackagePickupStatus.WAIT_USER)) {
                asyncTaskService.sendMessageToWechat(WxMsgType.PICKUP, userPackagePickupEntity.getUser(), "已发货,及时前往steam接受交易报价", userPackagePickupEntity.getOrderNo());
                userPackagePickupEntity.setIsRemind(true);
                userPackagePickupRepository.save(userPackagePickupEntity);
            } else if (userPackagePickupEntity.getStatus().equals(PackagePickupStatus.FAIL)) {
                asyncTaskService.sendMessageToWechat(WxMsgType.PICKUP, userPackagePickupEntity.getUser(), "取回失败,市场暂时缺货", userPackagePickupEntity.getOrderNo());
                userPackagePickupEntity.setIsRemind(true);
                userPackagePickupRepository.save(userPackagePickupEntity);
            } else if (userPackagePickupEntity.getStatus().equals(PackagePickupStatus.FAIL_STEAM)) {
                asyncTaskService.sendMessageToWechat(WxMsgType.PICKUP, userPackagePickupEntity.getUser(), "取回失败,steam交易链接不可用", userPackagePickupEntity.getOrderNo());
                userPackagePickupEntity.setIsRemind(true);
                userPackagePickupRepository.save(userPackagePickupEntity);
            } else if (userPackagePickupEntity.getStatus().equals(PackagePickupStatus.SUCCESS)) {
                userPackagePickupEntity.setIsRemind(true);
                userPackagePickupRepository.save(userPackagePickupEntity);
            } else {
                asyncTaskService.sendMessageToWechat(WxMsgType.PICKUP, userPackagePickupEntity.getUser(), "取回失败,联系客服处理", userPackagePickupEntity.getOrderNo());
                userPackagePickupEntity.setIsRemind(true);
                userPackagePickupRepository.save(userPackagePickupEntity);
            }
        }
    }

    @Override
    protected String getCron() {
        return runInterval;
    }
}
