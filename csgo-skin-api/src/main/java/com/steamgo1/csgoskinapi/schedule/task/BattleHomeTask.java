package com.steamgo1.csgoskinapi.schedule.task;

import com.steamgo1.csgoskinapi.service.AsyncTaskService;
import com.steamgo1.csgoskinapi.service.BattleHomeService;
import com.steamgo1.csgoskincommon.config.ConfigurerScheduling;
import com.steamgo1.csgoskincommon.dao.BattleHomeCaseReposiotry;
import com.steamgo1.csgoskincommon.dao.BattleHomeReposiotry;
import com.steamgo1.csgoskincommon.entity.BattleHomeCaseEntity;
import com.steamgo1.csgoskincommon.entity.BattleHomeEntity;
import com.steamgo1.csgoskincommon.entity.enums.BattleHomeCaseStatus;
import com.steamgo1.csgoskincommon.entity.enums.BattleHomeStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Configuration
public class BattleHomeTask extends ConfigurerScheduling {
    @Value("${schedule.interval.battleHome}")
    private String runInterval;

    @Value("${site.battle-home-robot-join-wait-time}")
    private long robotJoinWaitTime;

    @Autowired
    private BattleHomeReposiotry battleHomeReposiotry;

    @Autowired
    private BattleHomeCaseReposiotry battleHomeCaseReposiotry;


    @Autowired
    private AsyncTaskService asyncTaskService;

    @Autowired
    private BattleHomeService battleHomeService;

    @Override
    protected void processTask() {
        log.debug("battleHome定时任务 {}, 线程名称： {}, 线程ID: {}", LocalDateTime.now(), Thread.currentThread().getName(), Thread.currentThread().getId());
//        List<BattleHomeEntity> battleHomeEntityList = battleHomeReposiotry.findByBattleHomeStatus(BattleHomeStatus.RUNNING);
        List<BattleHomeEntity> battleHomeEntityList = battleHomeReposiotry.findByBattleHomeStatusIn(BattleHomeStatus.unFinishStatus());

        for (BattleHomeEntity battleHome : battleHomeEntityList) {
            // 等待中，过时间自动添加机器人
            switch (battleHome.getBattleHomeStatus()) {
                case WAITING:
                    switch (battleHome.getBattleHomeMethod()) {
                        case PVE:
                            log.debug("对战房: {} PVE对战, 机器人开始加入", battleHome.getId());
                            for (int i = 0; i < battleHome.getTotalPlayer(); i++) {
//                                try {
//                                    battleHomeService.robotJoinBattleHome(battleHome.getId());
//                                    Thread.sleep(1500);
//                                } catch (InterruptedException e) {
//                                    throw new RuntimeException(e);
//                                }
                                battleHomeService.robotJoinBattleHome(battleHome.getId());
                            }
                            break;
                        case PVP:
                            if (System.currentTimeMillis() - battleHome.getUpdateTime().getTime() > robotJoinWaitTime) {
                                log.debug("对战房: {} PVP对战, 过了等待阈值：{} 机器人开始加入", battleHome.getId(), robotJoinWaitTime);
                                battleHomeService.robotJoinBattleHome(battleHome.getId());
                            }
                    }
                    break;
                case RUNNING:
                    if (battleHomeCaseReposiotry.existsByBattleHomeAndStatus(battleHome, BattleHomeCaseStatus.WAIT)) {
                        log.debug("battleHome： {} 当局未完成", battleHome.getId());
                        if (System.currentTimeMillis() - battleHome.getUpdateTime().getTime() > 10000) {
                            asyncTaskService.startBattleHome(battleHome);
                        }
                        break;
                    }
                    BattleHomeCaseEntity battleHomeCase = battleHomeCaseReposiotry.findFirstByBattleHomeAndStatusOrderById(battleHome, BattleHomeCaseStatus.UNOPEN);
                    if (battleHomeCase == null) {
                        battleHome.setBattleHomeStatus(BattleHomeStatus.FINISH);
                        battleHomeReposiotry.save(battleHome);
                    }
                    battleHomeCase.setStatus(BattleHomeCaseStatus.WAIT);
                    battleHomeCaseReposiotry.save(battleHomeCase);
                    asyncTaskService.startBattleHome(battleHome);
            }
        }
    }

    @Override
    protected String getCron() {
        return runInterval;
    }
}
