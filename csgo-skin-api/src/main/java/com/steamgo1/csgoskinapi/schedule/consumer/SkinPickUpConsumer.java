package com.steamgo1.csgoskinapi.schedule.consumer;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskincommon.entity.UserPackagePickupEntity;
import com.steamgo1.csgoskincommon.entity.enums.PackagePickupStatus;
import com.steamgo1.csgoskincommon.service.ZBTService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Timer;
import java.util.TimerTask;

@Slf4j
@Component
public class SkinPickUpConsumer {
    @Autowired
    private ZBTService zbtService;

//    @Autowired
//    private IGXEService igxeService;

//    @Autowired
//    private IO661Service io661Service;

    @RabbitListener(queues = "${rabbitmq.queue.skin-pickup}")
    public void process(JSONObject data) {
        log.info("rabbitmq.queue.skin-pickup:{}", JSONObject.toJSONString(data));

        UserPackagePickupEntity userPackagePickupEntity = JSONObject.toJavaObject(data, UserPackagePickupEntity.class);
        if (userPackagePickupEntity.getStatus().equals(PackagePickupStatus.PICKUPING)) {
            // 放入队列
            new Timer().schedule(new TimerTask() {
                @Override
                public void run() {
                    zbtService.quickBuy(userPackagePickupEntity.getId());
                }
            }, 10 * 1000);
            log.info("用户饰品取回: {}", JSONObject.toJavaObject(data, UserPackagePickupEntity.class));
        }
    }

}
