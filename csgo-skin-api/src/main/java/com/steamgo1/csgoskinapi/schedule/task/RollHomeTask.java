package com.steamgo1.csgoskinapi.schedule.task;

import com.steamgo1.csgoskinapi.service.RollHomeService;
import com.steamgo1.csgoskincommon.config.ConfigurerScheduling;
import com.steamgo1.csgoskincommon.dao.RollHomeRepository;
import com.steamgo1.csgoskincommon.dao.RollHomeUserRepository;
import com.steamgo1.csgoskincommon.entity.RollHomeEntity;
import com.steamgo1.csgoskincommon.entity.enums.RollHomeLotteryMethod;
import com.steamgo1.csgoskincommon.entity.enums.RollHomeStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;

@Slf4j
@Configuration
public class RollHomeTask extends ConfigurerScheduling {
    @Value("${schedule.interval.rollHome}")
    private String runInterval;


    @Autowired
    private RollHomeService rollHomeService;
    @Autowired
    private RollHomeRepository rollHomeRepository;

    @Autowired
    private RollHomeUserRepository rollHomeUserRepository;


    @Override
    protected void processTask() {
        log.debug("rollHome 定时任务 {}, 线程名称： {}, 线程ID: {}", LocalDateTime.now(), Thread.currentThread().getName(), Thread.currentThread().getId());
//        List<BattleHomeEntity> battleHomeEntityList = battleHomeReposiotry.findByBattleHomeStatus(BattleHomeStatus.RUNNING);
        for (RollHomeEntity rollHomeEntity : rollHomeRepository.findByStatusIn(RollHomeStatus.unFinishStatus())) {
            log.debug("Rollhome：{} 模式：{} 状态：{}", rollHomeEntity.getId(), rollHomeEntity.getRollHomeLotteryMethod().getValue(), rollHomeEntity.getStatus().getValue());
            switch (rollHomeEntity.getStatus()) {
                case UNSTART:
                    if (rollHomeEntity.getRollHomeLotteryMethod().equals(RollHomeLotteryMethod.FIXTIME)) {
                        if (System.currentTimeMillis() - rollHomeEntity.getLotteryTime().getTime() > 30000) {
                            rollHomeEntity.setStatus(RollHomeStatus.START);
                            rollHomeRepository.save(rollHomeEntity);
                            rollHomeService.lotteryRollHome(rollHomeEntity.getId());
                            log.debug("固定时间的Roll房过了30秒未开始,补救开始");
                        }
                    }
                    if (rollHomeEntity.getRollHomeLotteryMethod().equals(RollHomeLotteryMethod.FIXPEOPLE)) {
                        if (rollHomeUserRepository.countByRollHome(rollHomeEntity) >= rollHomeEntity.getMaxPeople() && System.currentTimeMillis() - rollHomeEntity.getUpdateTime().getTime() > 20000) {
                            rollHomeEntity.setStatus(RollHomeStatus.START);
                            rollHomeRepository.save(rollHomeEntity);
                            rollHomeService.lotteryRollHome(rollHomeEntity.getId());
                            log.debug("固定人数的Roll房开始,补救开始");
                        }
                    }
                    break;
                case START:
                    if (System.currentTimeMillis() - rollHomeEntity.getUpdateTime().getTime() > 20000) {
                        log.debug("20秒后仍然状态还是开始则重新开始");
                        rollHomeEntity.setStatus(RollHomeStatus.START);
                        rollHomeRepository.save(rollHomeEntity);
                        rollHomeService.lotteryRollHome(rollHomeEntity.getId());
                    }
            }
        }
    }

    @Override
    protected String getCron() {
        return runInterval;
    }

}
