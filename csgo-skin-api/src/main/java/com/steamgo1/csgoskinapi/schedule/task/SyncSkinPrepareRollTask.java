package com.steamgo1.csgoskinapi.schedule.task;

import com.steamgo1.csgoskincommon.config.ConfigurerScheduling;
import com.steamgo1.csgoskincommon.dao.UserPackagePickupRepository;
import com.steamgo1.csgoskincommon.dao.ZbtSyncRecordRepository;
import com.steamgo1.csgoskincommon.service.IGXEService;
import com.steamgo1.csgoskincommon.service.ZBTService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;

@Slf4j
@Configuration
public class SyncSkinPrepareRollTask extends ConfigurerScheduling {
    @Value("${schedule.interval.sync-skin-prepare-roll}")
    private String runInterval;


    @Autowired
    private UserPackagePickupRepository userPackagePickupRepository;

    @Autowired
    private ZBTService zbtService;

    @Autowired
    private IGXEService igxeService;

    @Autowired
    private ZbtSyncRecordRepository zbtSyncRecordRepository;

    @Override
    protected void processTask() {
        log.debug("每天定时同步饰品信息 定时任务 {}, 线程名称： {}, 线程ID: {}", LocalDateTime.now(), Thread.currentThread().getName(), Thread.currentThread().getId());
//        String operationNo = Utils.generateOperationNo("SYNCALLSKIN");
//        ZbtSyncRecordEntity zbtSyncRecordEntity = new ZbtSyncRecordEntity();
//        zbtSyncRecordEntity.setOperationNo(operationNo);
//        zbtSyncRecordEntity.setZbtSyncType(ZbtSyncType.ALL);
//        zbtSyncRecordEntity.setZbtSyncStatus(ZbtSyncStatus.SYNCING);
//        zbtSyncRecordEntity = zbtSyncRecordRepository.save(zbtSyncRecordEntity);
//        zbtService.SyncSkinInfo(zbtSyncRecordEntity.getId());
        zbtService.SyncSkinInfoByDBNew();
    }

    @Override
    protected String getCron() {
        return runInterval;
    }
}
