package com.steamgo1.csgoskinapi.schedule.consumer;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinapi.converter.UserLogConverter;
import com.steamgo1.csgoskinapi.utils.JsonUtils;
import com.steamgo1.csgoskincommon.dao.UserRepository;
import com.steamgo1.csgoskincommon.dto.UserLogVo;
import com.steamgo1.csgoskincommon.entity.UserEntity;
import com.steamgo1.csgoskincommon.entity.UserLogEntity;
import com.steamgo1.csgoskincommon.service.UserLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RabbitListener(queues = "${rabbitmq.queue.userlog}")
public class UserLogConsumer {

    @Autowired
    private UserLogService userLogService;
    @Autowired
    private UserLogConverter userLogConverter;
    @Autowired
    private UserRepository userRepository;

    @RabbitHandler
    public void process(JSONObject data) {
        UserLogVo userLogVo = JSONObject.toJavaObject(data, UserLogVo.class);
        log.info("rabbitmq.queue.userlog::{}", userLogVo != null ? JsonUtils.toJson(userLogVo) : "");
        UserEntity user = userRepository.findById(userLogVo.getUserId());
        UserLogEntity userLogEntity = userLogConverter.toUserLogEntity(userLogVo, user);
        userLogService.save(userLogEntity);
    }
}
