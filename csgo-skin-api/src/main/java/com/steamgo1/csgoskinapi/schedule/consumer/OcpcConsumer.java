package com.steamgo1.csgoskinapi.schedule.consumer;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinapi.service.OcpcService;
import com.steamgo1.csgoskincommon.bo.OcpcTaskBO;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class OcpcConsumer {
    @Autowired
    private OcpcService ocpcService;

    @Value("${spring.redis.prefix.ocpc}")
    private String redisPrefixOcpc;

    @RabbitListener(queues = "${rabbitmq.queue.ocpc}")
    public void process(JSONObject data) {
        log.info("rabbitmq.queue.ocpc:{}", JSONObject.toJSONString(data));
        OcpcTaskBO ocpcTaskBO = JSONObject.toJavaObject(data, OcpcTaskBO.class);
        switch (ocpcTaskBO.getType()) {
            case CLICK:
                log.info("+++++++++++++++++OCPC点击{}", ocpcTaskBO.getIp());
                String clickKey = redisPrefixOcpc + ":logidUrl:" + ocpcTaskBO.getIp();
                if (RedisUtils.hasKey(clickKey)) {
                    String clickLogidUrl = RedisUtils.get(clickKey, String.class);
                    if (clickLogidUrl.contains("fbclid=")) {
                        ocpcService.addClickMetaData(ocpcTaskBO.getIp());
                    } else if (clickLogidUrl.contains("gclid=")) {
                        ocpcService.addClickGoogleData(ocpcTaskBO.getIp());
                    } else {
                        ocpcService.addClickBaiduData(ocpcTaskBO.getIp());
                    }
                }
                break;
            case REGISTER:
                log.info("+++++++++++++++++OCPC注册{}", ocpcTaskBO.getIp());
                String key = redisPrefixOcpc + ":logidUrl:" + ocpcTaskBO.getIp();
                if (!RedisUtils.hasKey(key)) {
                    log.info("用户注册OCPC未记录  ip:{}", ocpcTaskBO.getIp());
                    break;
                }
                String logidUrl = RedisUtils.get(key, String.class);
                if (StringUtil.isNullOrEmpty(logidUrl)) {
                    log.info("用户注册OCPC未记录  ip:{}", ocpcTaskBO.getIp());
                    break;
                }
                log.info("key:{}", key);
                log.info("logidUrl:{}", logidUrl);
                if (logidUrl.contains("fbclid=")) {
                    log.info("Meta OCPC  ip:{}", ocpcTaskBO.getIp());
                    ocpcService.sendRegisterToMeta(ocpcTaskBO.getIp());
                    ocpcService.addRegisterMetaData(ocpcTaskBO.getIp(), ocpcTaskBO.getUserId());
                } else if (logidUrl.contains("gclid=")) {
                    log.info("Google OCPC  ip:{}", ocpcTaskBO.getIp());
                    ocpcService.sendRegisterToGoogle(ocpcTaskBO.getIp());
                    ocpcService.addRegisterGoogleData(ocpcTaskBO.getIp(), ocpcTaskBO.getUserId());
                } else if (logidUrl.contains("bd_vid")) {
                    log.info("百度OCPC  ip:{}", ocpcTaskBO.getIp());
                    ocpcService.sendRegisterToBaidu(ocpcTaskBO.getIp());
                    ocpcService.addRegisterBaiduData(ocpcTaskBO.getIp(), ocpcTaskBO.getUserId());
                } else {
                    ocpcService.sendRegisterTo360(ocpcTaskBO.getIp());
                }
                break;
            case CHARGE:
                log.info("+++++++++++++++++OCPC充值{}", ocpcTaskBO.getIp());
                String chargeKey = redisPrefixOcpc + ":logidUrl:" + ocpcTaskBO.getIp();
                if (RedisUtils.hasKey(chargeKey)) {
                    String chargeLogidUrl = RedisUtils.get(chargeKey, String.class);
                    if (chargeLogidUrl.contains("fbclid=")) {
                        ocpcService.addChangeMetaData(ocpcTaskBO.getIp(), ocpcTaskBO.getUserId(), ocpcTaskBO.getOrderChargeId());
                    } else if (chargeLogidUrl.contains("gclid=")) {
                        ocpcService.addChangeGoogleData(ocpcTaskBO.getIp(), ocpcTaskBO.getUserId(), ocpcTaskBO.getOrderChargeId());
                    } else {
                        ocpcService.addChangeBaiduData(ocpcTaskBO.getIp(), ocpcTaskBO.getUserId(), ocpcTaskBO.getOrderChargeId());
                    }
                }
        }

    }

}
