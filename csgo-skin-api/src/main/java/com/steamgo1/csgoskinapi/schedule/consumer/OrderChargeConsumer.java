package com.steamgo1.csgoskinapi.schedule.consumer;

import com.steamgo1.csgoskincommon.dao.OrderChargeRepository;
import com.steamgo1.csgoskincommon.entity.OrderChargeEntity;
import com.steamgo1.csgoskincommon.entity.enums.OrderStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RabbitListener(queues = "${rabbitmq.queue.charge-order-dead-letter}")
public class OrderChargeConsumer {

    @Autowired
    private OrderChargeRepository orderChargeRepository;

    @RabbitHandler
    public void process(String orderNo) {
        log.info("rabbitmq.queue.charge-order-dead-letter:{}", orderNo);

        log.info("检查关单: {}", orderNo);
        OrderChargeEntity orderChargeEntity = orderChargeRepository.findByOrderNo(orderNo);
        if (orderChargeEntity.getOrderStatus().equals(OrderStatus.UNPAY)) {
            log.info("超时未支付,关闭订单: {}", orderNo);
            orderChargeEntity.setOrderStatus(OrderStatus.CLOSE);
            orderChargeRepository.save(orderChargeEntity);
        }
    }

}
