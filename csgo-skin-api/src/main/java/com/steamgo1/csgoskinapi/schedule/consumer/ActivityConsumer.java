package com.steamgo1.csgoskinapi.schedule.consumer;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinapi.service.ActivityService;
import com.steamgo1.csgoskincommon.dto.RabbitActivityVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RabbitListener(queues = "${rabbitmq.queue.activity-dead-letter}")
public class ActivityConsumer {
    @Autowired
    private ActivityService activityService;

    @RabbitHandler
    public void process(JSONObject data) {
        log.info("{rabbitmq.queue.activity-dead-letter:{}", JSONObject.toJSONString(data));

        RabbitActivityVO rabbitActivityVO = JSONObject.toJavaObject(data, RabbitActivityVO.class);
        log.info("活动结束, 类型: {}, ID: {}", rabbitActivityVO.getType().getValue(), rabbitActivityVO.getId());
        activityService.startCardCollect(rabbitActivityVO.getId(), rabbitActivityVO.getMessageId());
    }

}
