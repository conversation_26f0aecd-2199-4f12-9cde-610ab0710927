package com.steamgo1.csgoskinapi.schedule.task;

import com.steamgo1.csgoskincommon.config.ConfigurerScheduling;
import com.steamgo1.csgoskincommon.dao.UserPackagePickupRepository;
import com.steamgo1.csgoskincommon.service.ZBTService;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;

@Slf4j
@Configuration
public class CleanTask extends ConfigurerScheduling {
    @Value("${schedule.interval.clean}")
    private String runInterval;


    @Value("${spring.redis.prefix.battle-home}")
    private String redisBattleHomePrefix;


    @Autowired
    private UserPackagePickupRepository userPackagePickupRepository;

    @Autowired
    private ZBTService zbtService;

    @Override
    protected void processTask() {
        log.debug("每日清理 定时任务 {}, 线程名称： {}, 线程ID: {}", LocalDateTime.now(), Thread.currentThread().getName(), Thread.currentThread().getId());
        // 清理昨日对战top10
        String redisBattleHomeKey = redisBattleHomePrefix + ":TOP10:" + "YESTERDAY";
        if (RedisUtils.hasKey(redisBattleHomeKey)) {
            RedisUtils.delete(redisBattleHomeKey);
        }
    }

    @Override
    protected String getCron() {
        return runInterval;
    }
}
