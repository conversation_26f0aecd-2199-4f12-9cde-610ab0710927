package com.steamgo1.csgoskinapi.schedule.task;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinapi.service.AsyncTaskService;
import com.steamgo1.csgoskinapi.service.SiteService;
import com.steamgo1.csgoskinapi.vo.StatisticsInfoVO;
import com.steamgo1.csgoskincommon.config.ConfigurerScheduling;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageMethod;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageType;
import com.steamgo1.csgoskincommon.service.ZBTService;
import com.steamgo1.csgoskincommon.vo.websocket.MessageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;


@Slf4j
@Configuration
public class StatisticsTask extends ConfigurerScheduling {
    @Value("${schedule.interval.statistics}")
    private String runInterval;

    @Autowired
    private ZBTService zbtService;

    @Autowired
    private SiteService siteService;

    @Autowired
    private AsyncTaskService asyncTaskService;

    @Override
    protected void processTask() {
        log.debug("上报统计信息 定时任务 {}, 线程名称： {}, 线程ID: {}", LocalDateTime.now(), Thread.currentThread().getName(), Thread.currentThread().getId());
        StatisticsInfoVO statisticsInfoVO = siteService.getStatistics();
        MessageVO messageVO = new MessageVO(WebSocketMessageMethod.SEND_ALL, WebSocketMessageType.STATISTICS, statisticsInfoVO);
        asyncTaskService.sendWebSocketMessageToAll(JSONObject.toJSONString(messageVO));
    }

    @Override
    protected String getCron() {
        return runInterval;
    }
}
