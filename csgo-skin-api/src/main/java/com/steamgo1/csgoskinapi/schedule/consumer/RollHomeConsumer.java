package com.steamgo1.csgoskinapi.schedule.consumer;

import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskinapi.config.websocket.WebSocket;
import com.steamgo1.csgoskinapi.service.RollHomeService;
import com.steamgo1.csgoskinapi.vo.RollHomeVO;
import com.steamgo1.csgoskincommon.dao.RollHomeRepository;
import com.steamgo1.csgoskincommon.dto.RabbitRomeHomeVO;
import com.steamgo1.csgoskincommon.entity.RollHomeEntity;
import com.steamgo1.csgoskincommon.entity.enums.RollHomeStatus;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageMethod;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageType;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import com.steamgo1.csgoskincommon.vo.websocket.MessageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RabbitListener(queues = "${rabbitmq.queue.roll-home-dead-letter}")
public class RollHomeConsumer {

    @Value("${spring.redis.prefix.roll-home}")
    private String redisRollHomePrefix;
    @Autowired
    private RollHomeService rollHomeService;

    @Autowired
    private WebSocket webSocket;

    @Autowired
    private RollHomeRepository rollHomeRepository;

    @RabbitHandler
    public void process(JSONObject data) {
        log.info("Roll房开始抽奖: {}", JSONObject.toJavaObject(data, RabbitRomeHomeVO.class));
        RabbitRomeHomeVO rabbitRomeHomeVO = JSONObject.toJavaObject(data, RabbitRomeHomeVO.class);
        RollHomeEntity rollHomeEntity = rollHomeRepository.findById(rabbitRomeHomeVO.getRollHomeId()).orElse(null);
        if (rollHomeEntity == null) {
            log.error("Roll房已删除 : {}", rabbitRomeHomeVO.getRollHomeId());
            return;
        }
        if (!rabbitRomeHomeVO.getMessageId().equals(rollHomeEntity.getRabbitMessageId())) {
            log.error("Roll房messageId不一致 : {}", rabbitRomeHomeVO.getRollHomeId());
            return;
        }
        if (rollHomeEntity.getStatus().equals(RollHomeStatus.END)) {
            log.info("Roll房{}，已经提前结束", rollHomeEntity.getId());
            return;
        }
        rollHomeEntity.setStatus(RollHomeStatus.START);
        rollHomeService.lotteryRollHome(rollHomeEntity.getId());
        rollHomeRepository.save(rollHomeEntity);
        //删除机器人缓存
        rollHomeService.removeRollHomeRobotSession(rollHomeEntity);
        // 删除roll房缓存
        String redisRollHomeKey = redisRollHomePrefix + ":" + rabbitRomeHomeVO.getRollHomeId();
        RedisUtils.delete(redisRollHomeKey);
        RollHomeVO romeHomeVO = rollHomeService.queryRollHomeById(rabbitRomeHomeVO.getRollHomeId());
        MessageVO messageVO = new MessageVO(WebSocketMessageMethod.SEND_ALL, WebSocketMessageType.ROLL_HOME_RESULT, romeHomeVO);
        webSocket.sendAllMessage(messageVO);
    }

}
