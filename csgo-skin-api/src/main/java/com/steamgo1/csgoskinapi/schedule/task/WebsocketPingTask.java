package com.steamgo1.csgoskinapi.schedule.task;

import com.steamgo1.csgoskinapi.config.websocket.WebSocket;
import com.steamgo1.csgoskincommon.config.ConfigurerScheduling;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;

@Slf4j
@Configuration
public class WebsocketPingTask extends ConfigurerScheduling {
    @Value("${schedule.interval.websocket-ping}")
    private String runInterval;
    @Value("${spring.redis.prefix.user}")
    private String redisUserPrefix;

    @Autowired
    private WebSocket webSocket;

    @Override
    protected void processTask() {
        log.debug("websocket定时任务 {}, 线程名称： {}, 线程ID: {}", LocalDateTime.now(), Thread.currentThread().getName(), Thread.currentThread().getId());
//        List<BattleHomeEntity> battleHomeEntityList = battleHomeReposiotry.findByBattleHomeStatus(BattleHomeStatus.RUNNING);
        int count = webSocket.sendPing();
        RedisUtils.save(redisUserPrefix + ":" + "ONLINE", count);
        log.debug("websocket定时任务结束,当前连接数： {}", count);

    }

    @Override
    protected String getCron() {
        return runInterval;
    }
}
