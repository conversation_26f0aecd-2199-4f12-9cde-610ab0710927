package com.steamgo1.csgoskinapi.schedule.task;

import com.steamgo1.csgoskincommon.config.ConfigurerScheduling;
import com.steamgo1.csgoskincommon.dao.UserPackagePickupRepository;
import com.steamgo1.csgoskincommon.dao.UserPackageRepository;
import com.steamgo1.csgoskincommon.entity.UserPackageEntity;
import com.steamgo1.csgoskincommon.entity.UserPackagePickupEntity;
import com.steamgo1.csgoskincommon.entity.enums.PackagePickupStatus;
import com.steamgo1.csgoskincommon.service.IO661Service;
import com.steamgo1.csgoskincommon.service.ZBTService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;

@Slf4j
@Configuration
public class ZBTPickUpTask extends ConfigurerScheduling {
    @Value("${schedule.interval.zbt}")
    private String runInterval;

    @Autowired
    private UserPackagePickupRepository userPackagePickupRepository;

    @Autowired
    private ZBTService zbtService;

    @Autowired
    private UserPackageRepository userPackageRepository;


    @Override
    protected void processTask() {

        log.debug("取货状态 定时任务 {}, 线程名称： {}, 线程ID: {}", LocalDateTime.now(), Thread.currentThread().getName(), Thread.currentThread().getId());
        for (UserPackagePickupEntity userPackagePickupEntity : userPackagePickupRepository.findByStatusIn(PackagePickupStatus.unFinishStatus())) {
            log.debug("饰品取回：{} 用户：{} 状态：{}", userPackagePickupEntity.getId(), userPackagePickupEntity.getUser().getId(), userPackagePickupEntity.getStatus().getValue());
            if (userPackagePickupEntity.getStatus().equals(PackagePickupStatus.FROZEN)) {
                Duration duration = Duration.between(userPackagePickupEntity.getUpdateTime().toInstant(), new Date().toInstant());
                long differenceInHours = duration.toMinutes();
                log.debug("饰品冻结时间： {}", differenceInHours);
                if (differenceInHours > 10) {
                    // 时间是10分钟之前
                    // 冻结状态一个小时的，给改成失败，并把饰品还原
                    userPackagePickupEntity.setStatus(PackagePickupStatus.FAIL);
                    UserPackageEntity userPackageEntity = userPackagePickupEntity.getUserPackage();
                    userPackageEntity.setIsReceived(false);
                    userPackageRepository.save(userPackageEntity);
                    userPackagePickupRepository.save(userPackagePickupEntity);
                }
                continue;
            }
            if (userPackagePickupEntity.getThirdOrderNo() != null) {
                zbtService.queryOrderStatus(userPackagePickupEntity.getId());
            } else {
                if (userPackagePickupEntity.getStatus().equals(PackagePickupStatus.PICKUPING)) {
                    Duration duration = Duration.between(userPackagePickupEntity.getUpdateTime().toInstant(), new Date().toInstant());
                    long differenceInHours = duration.toMinutes();
                    log.debug("取回中饰品更新时间： {}", differenceInHours);
                    if (differenceInHours > 10) {
                        zbtService.quickBuy(userPackagePickupEntity.getId());
                    }
                }
            }
        }
    }

    @Override
    protected String getCron() {
        return runInterval;
    }
}
