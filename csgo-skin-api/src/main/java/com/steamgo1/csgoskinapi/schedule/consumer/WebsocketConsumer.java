package com.steamgo1.csgoskinapi.schedule.consumer;

import com.steamgo1.csgoskinapi.config.websocket.WebSocket;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class WebsocketConsumer {
    @Autowired
    private WebSocket webSocket;

    @RabbitListener(queues = "${rabbitmq.queue.websocket-notify}")
    public void battleHomeJoinMessage(String data) {
//        log.info("发送websocket信息: {}", data);
        webSocket.sendAllMessage(data);
    }

}