package com.steamgo1.csgoskinapi.schedule.task;

import com.steamgo1.csgoskinapi.service.BattleHomeService;
import com.steamgo1.csgoskinapi.service.CaseService;
import com.steamgo1.csgoskinapi.service.PercentageService;
import com.steamgo1.csgoskincommon.config.ConfigurerScheduling;
import com.steamgo1.csgoskincommon.dao.SysCheatRobotConfigRepository;
import com.steamgo1.csgoskincommon.dao.UserRepository;
import com.steamgo1.csgoskincommon.entity.SysCheatRobotConfigEntity;
import com.steamgo1.csgoskincommon.entity.UserEntity;
import com.steamgo1.csgoskincommon.entity.enums.UserType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Random;

@Slf4j
@Configuration
public class CheatRobotTask extends ConfigurerScheduling {
    @Value("${schedule.interval.cheatRobot}")
    private String runInterval;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private SysCheatRobotConfigRepository sysCheatRobotConfigRepository;

    @Autowired
    private CaseService caseService;

    @Autowired
    private PercentageService percentageService;

    @Autowired
    private BattleHomeService battleHomeService;


    @Override
    protected void processTask() {
        log.debug("全职机器人即将登场 定时任务 {}, 线程名称： {}, 线程ID: {}", LocalDateTime.now(), Thread.currentThread().getName(), Thread.currentThread().getId());
        SysCheatRobotConfigEntity sysCheatRobotConfigEntity = sysCheatRobotConfigRepository.findTopByIsActivateTrue();
        if (sysCheatRobotConfigEntity == null) {
            log.warn("没有生效的配置,全职机器人悻悻退场");
            return;
        }
        Random r = new Random();
        //获取一个3-10的随机数
        int playCount = r.nextInt(1) + 1;
        List<UserEntity> userEntityList = userRepository.randomCheatRobot(UserType.CHEAT_ROBOT.getCode() - 1, sysCheatRobotConfigEntity.getRounds_total());
        for (UserEntity user : userEntityList) {
//            if(sysCheatRobotConfigEntity.getEnableBattle() && Integer.valueOf(r.nextInt(10)) < sysCheatRobotConfigEntity.getBattleProbability()){
//                log.debug("全职机器人： {}, 对战", user.getId());
//                battleHomeService.robotBattle(user.getId());
//                continue;
//            }
            if (sysCheatRobotConfigEntity.getEnableOpenCase() && Integer.valueOf(r.nextInt(10)) < sysCheatRobotConfigEntity.getOpenCaseProbability()) {
                log.debug("全职机器人： {}, 开箱子{}次", user.getId(), playCount);
                for (int i = 0; i < playCount; i++) {
                    caseService.rorbotOpenCase(user.getId());
                }
                continue;
            }
            if (sysCheatRobotConfigEntity.getEnablePercentage() && Integer.valueOf(r.nextInt(10)) < sysCheatRobotConfigEntity.getPercentageProbability()) {
                log.debug("全职机器人： {}, 追梦{}次", user.getId(), playCount);
                for (int i = 0; i < playCount; i++) {
                    percentageService.robotPercentage(user.getId());
                }
            }
        }
    }

    @Override
    protected String getCron() {
        return runInterval;
    }
}
