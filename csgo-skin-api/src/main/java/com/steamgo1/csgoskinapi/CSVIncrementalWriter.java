package com.steamgo1.csgoskinapi;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;

public class CSVIncrementalWriter {
    private final BufferedWriter writer;

    // 初始化写入器（追加模式）
    public CSVIncrementalWriter(String filePath) throws IOException {
        this.writer = new BufferedWriter(new FileWriter(filePath, true));
    }

    // 使用示例
    public static void main(String[] args) {
        try {
            CSVIncrementalWriter csvWriter = new CSVIncrementalWriter("data.csv");

            // 增量写入多批次数据
            csvWriter.writeRecord(new String[]{"ID", "Name", "Address"});
            csvWriter.writeRecord(new String[]{"1", "Alice", "123,\"Main St\""});
            csvWriter.writeRecord(new String[]{"2", "Bob", "456 Oak\nAve"});

            csvWriter.flush(); // 可选：手动刷盘
            csvWriter.close(); // 必须：关闭写入器
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    // 写入单行数据（自动处理转义）
    public void writeRecord(String[] fields) throws IOException {
        StringBuilder csvLine = new StringBuilder();
        for (int i = 0; i < fields.length; i++) {
            String field = fields[i];
            boolean needQuotes = field.contains(",") || field.contains("\"") || field.contains("\n");

            if (needQuotes) {
                csvLine.append("\"");
                csvLine.append(field.replace("\"", "\"\""));
                csvLine.append("\"");
            } else {
                csvLine.append(field);
            }

            if (i < fields.length - 1) {
                csvLine.append(",");
            }
        }
        csvLine.append("\n"); // 使用 \n 保持跨平台兼容性
        writer.write(csvLine.toString());
    }

    // 强制刷盘（确保数据写入磁盘）
    public void flush() throws IOException {
        writer.flush();
    }

    // 关闭资源
    public void close() throws IOException {
        writer.close();
    }
}
