package com.steamgo1.csgoskinapi.converter;


import com.steamgo1.csgoskinapi.vo.IndexBannerVO;
import com.steamgo1.csgoskincommon.entity.SysIndexBannerEntity;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "Spring")
public interface IndexBannerConverter {
    IndexBannerConverter INSTANCE = Mappers.getMapper(IndexBannerConverter.class);

    IndexBannerVO toIndexBannerVO(SysIndexBannerEntity sysIndexBannerEntity);

    List<IndexBannerVO> toIndexBannerVO(List<SysIndexBannerEntity> sysIndexBannerEntityList);
}
