package com.steamgo1.csgoskinapi.converter;


import com.steamgo1.csgoskincommon.config.MapStructConfig;
import com.steamgo1.csgoskincommon.converter.I18nConverter;
import com.steamgo1.csgoskinapi.vo.CaseInfoVO;
import com.steamgo1.csgoskinapi.vo.SkinRarityColorFilterVO;
import com.steamgo1.csgoskincommon.entity.CaseEntity;
import com.steamgo1.csgoskincommon.entity.SkinRarityColorEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(config = MapStructConfig.class)
public interface CaseConverter  extends I18nConverter {
    CaseConverter INSTANCE = Mappers.getMapper(CaseConverter.class);

    @Mapping(source = "caseType.type", target = "type")
    @Mapping(target = "name", source = "i18nFieldName", qualifiedByName = "i18nFieldToCurrent")
    CaseInfoVO toCaseInfoVO(CaseEntity caseEntity);

    List<CaseInfoVO> toCaseInfoVOS(List<CaseEntity> caseEntities);

    List<SkinRarityColorFilterVO> toSkinRarityColorFilterVO(List<SkinRarityColorEntity> skinRarityColorEntityList);
}
