package com.steamgo1.csgoskinapi.converter;

import com.steamgo1.csgoskinapi.vo.UserAlgorithmDataVO;
import com.steamgo1.csgoskinapi.vo.UserInfoVO;
import com.steamgo1.csgoskincommon.entity.AlgorithmDataEntity;
import com.steamgo1.csgoskincommon.entity.UserEntity;
import com.steamgo1.csgoskincommon.entity.UserProfileEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "Spring")
public interface UserInfoConverter {
    UserInfoConverter INSTANCE = Mappers.getMapper(UserInfoConverter.class);

    @Mapping(source = "user.id", target = "id")
    @Mapping(source = "user.type.code", target = "type")
    @Mapping(source = "user.type.value", target = "typeValue")
    UserInfoVO toUserInfoVO(UserEntity user, AlgorithmDataEntity algorithmDataEntity, UserProfileEntity userProfile);

    UserAlgorithmDataVO toUserAlgorithmDataVO(AlgorithmDataEntity algorithmDataEntity);
}
