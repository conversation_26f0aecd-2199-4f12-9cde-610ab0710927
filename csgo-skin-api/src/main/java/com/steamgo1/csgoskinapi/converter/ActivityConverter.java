package com.steamgo1.csgoskinapi.converter;

import com.steamgo1.csgoskinapi.vo.CardCollectFullVO;
import com.steamgo1.csgoskinapi.vo.CardCollectVO;
import com.steamgo1.csgoskinapi.vo.CardVO;
import com.steamgo1.csgoskincommon.entity.CardCollectCardEntity;
import com.steamgo1.csgoskincommon.entity.CardCollectEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "Spring")
public interface ActivityConverter {
    ActivityConverter INSTANCE = Mappers.getMapper(ActivityConverter.class);

    @Mapping(target = "cardList", source = "cardCollectCardEntityList")
    CardCollectVO toCardCollectVO(CardCollectEntity cardCollectEntity, List<CardCollectCardEntity> cardCollectCardEntityList);

    CardCollectFullVO toCardCollectFullVO(CardCollectVO cardCollectVO);

    CardVO toCardVO(CardCollectFullVO.CardCollectCard cardCollectCard);
}
