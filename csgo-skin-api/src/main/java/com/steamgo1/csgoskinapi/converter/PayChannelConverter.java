package com.steamgo1.csgoskinapi.converter;

import com.steamgo1.csgoskinapi.vo.PayChannelVO;
import com.steamgo1.csgoskincommon.entity.PayChannelEntity;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "Spring")
public interface PayChannelConverter {
    PayChannelConverter INSTANCE = Mappers.getMapper(PayChannelConverter.class);

    PayChannelVO toPayChannelVO(PayChannelEntity payChannelEntity);

    List<PayChannelVO> toPayChannelVOList(List<PayChannelEntity> payChannelEntityList);
}