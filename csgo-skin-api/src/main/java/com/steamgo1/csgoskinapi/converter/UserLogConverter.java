package com.steamgo1.csgoskinapi.converter;

import com.steamgo1.csgoskincommon.dto.UserLogVo;
import com.steamgo1.csgoskincommon.entity.UserEntity;
import com.steamgo1.csgoskincommon.entity.UserLogEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "Spring")
public interface UserLogConverter {

    @Mapping(source = "userEntity", target = "user")
    UserLogEntity toUserLogEntity(UserLogVo userLogVo, UserEntity userEntity);

    UserLogVo toUserLogVo(UserLogEntity userLogEntity);
}
