package com.steamgo1.csgoskinapi.converter;


import com.steamgo1.csgoskinapi.vo.ChargeGoodsVO;
import com.steamgo1.csgoskincommon.entity.ChargeGoodsEntity;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "Spring")
public interface ChargeGoodsConverter {
    ChargeGoodsConverter INSTANCE = Mappers.getMapper(ChargeGoodsConverter.class);

    ChargeGoodsVO toChargeGoods(ChargeGoodsEntity chargeGoodsEntity);

    List<ChargeGoodsVO> toChargesList(List<ChargeGoodsEntity> chargeGoodsEntities);

}
