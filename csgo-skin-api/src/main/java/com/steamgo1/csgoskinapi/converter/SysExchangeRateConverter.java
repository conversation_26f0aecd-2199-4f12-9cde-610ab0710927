package com.steamgo1.csgoskinapi.converter;


import com.steamgo1.csgoskinapi.vo.SysExchangeRateVO;
import com.steamgo1.csgoskincommon.entity.SysExchangeRateEntity;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "Spring")
public interface SysExchangeRateConverter {
    SysExchangeRateConverter INSTANCE = Mappers.getMapper(SysExchangeRateConverter.class);

    SysExchangeRateVO toSysExchangeVO(SysExchangeRateEntity sysExchangeRateEntity);
}
