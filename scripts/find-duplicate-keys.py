#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找国际化语言包中的重复键
"""

import os
import re
from collections import defaultdict

def find_duplicate_keys_in_file(file_path):
    """查找单个文件中的重复键"""
    if not os.path.exists(file_path):
        return []
    
    key_lines = defaultdict(list)
    
    with open(file_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            # 跳过空行和注释行
            if not line or line.startswith('#'):
                continue
            
            # 查找键值对
            if '=' in line:
                key = line.split('=', 1)[0].strip()
                if key:
                    key_lines[key].append(line_num)
    
    # 找出重复的键
    duplicates = []
    for key, lines in key_lines.items():
        if len(lines) > 1:
            duplicates.append((key, lines))
    
    return duplicates

def main():
    # 语言包文件路径
    i18n_dir = "csgo-skin-common/src/main/resources/i18n"
    language_files = {
        'default': os.path.join(i18n_dir, 'messages.properties'),
        'zh_CN': os.path.join(i18n_dir, 'messages_zh_CN.properties'),
        'en_US': os.path.join(i18n_dir, 'messages_en_US.properties'),
        'pt_BR': os.path.join(i18n_dir, 'messages_pt_BR.properties')
    }
    
    print("=== 检查重复键 ===\n")
    
    total_duplicates = 0
    
    for lang, file_path in language_files.items():
        print(f"检查 {lang} ({file_path}):")
        
        if not os.path.exists(file_path):
            print(f"  ❌ 文件不存在")
            continue
        
        duplicates = find_duplicate_keys_in_file(file_path)
        
        if duplicates:
            print(f"  ❌ 发现 {len(duplicates)} 个重复键:")
            for key, lines in duplicates:
                print(f"    - {key}: 行 {', '.join(map(str, lines))}")
            total_duplicates += len(duplicates)
        else:
            print(f"  ✅ 无重复键")
        
        print()
    
    if total_duplicates == 0:
        print("🎉 所有语言包都没有重复键！")
    else:
        print(f"⚠️  总共发现 {total_duplicates} 个重复键需要清理")

if __name__ == "__main__":
    main()
