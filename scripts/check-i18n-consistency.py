#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
国际化语言包一致性检查脚本
检查所有语言包是否包含相同的键值对
"""

import os
import re
from collections import defaultdict

def parse_properties_file(file_path):
    """解析 properties 文件，返回键值对字典"""
    keys = set()
    if not os.path.exists(file_path):
        return keys
    
    with open(file_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            # 跳过空行和注释行
            if not line or line.startswith('#'):
                continue
            
            # 查找键值对
            if '=' in line:
                key = line.split('=', 1)[0].strip()
                if key:
                    keys.add(key)
    
    return keys

def main():
    # 语言包文件路径
    i18n_dir = "csgo-skin-common/src/main/resources/i18n"
    language_files = {
        'default': os.path.join(i18n_dir, 'messages.properties'),
        'zh_CN': os.path.join(i18n_dir, 'messages_zh_CN.properties'),
        'en_US': os.path.join(i18n_dir, 'messages_en_US.properties'),
        'pt_BR': os.path.join(i18n_dir, 'messages_pt_BR.properties')
    }
    
    # 解析所有语言包
    language_keys = {}
    for lang, file_path in language_files.items():
        language_keys[lang] = parse_properties_file(file_path)
        print(f"{lang}: {len(language_keys[lang])} keys")
    
    # 获取所有键的并集
    all_keys = set()
    for keys in language_keys.values():
        all_keys.update(keys)
    
    print(f"\n总共有 {len(all_keys)} 个唯一键")
    
    # 检查缺失的键
    missing_keys = defaultdict(list)
    for lang, keys in language_keys.items():
        missing = all_keys - keys
        if missing:
            missing_keys[lang] = sorted(missing)
    
    # 输出缺失的键
    if missing_keys:
        print("\n=== 缺失的键 ===")
        for lang, keys in missing_keys.items():
            print(f"\n{lang} 缺失 {len(keys)} 个键:")
            for key in keys[:10]:  # 只显示前10个
                print(f"  - {key}")
            if len(keys) > 10:
                print(f"  ... 还有 {len(keys) - 10} 个")
    else:
        print("\n✅ 所有语言包的键都是一致的！")
    
    # 检查重复的键
    print("\n=== 重复键检查 ===")
    for lang, file_path in language_files.items():
        if not os.path.exists(file_path):
            continue
            
        seen_keys = set()
        duplicates = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                if '=' in line:
                    key = line.split('=', 1)[0].strip()
                    if key in seen_keys:
                        duplicates.append((key, line_num))
                    else:
                        seen_keys.add(key)
        
        if duplicates:
            print(f"{lang} 有重复键:")
            for key, line_num in duplicates:
                print(f"  - {key} (行 {line_num})")
        else:
            print(f"{lang}: ✅ 无重复键")

if __name__ == "__main__":
    main()
