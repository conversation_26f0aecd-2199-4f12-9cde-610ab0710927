<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.8</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.steamgo1</groupId>
    <artifactId>csgo-skin</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>csgo-skin</name>
    <description>csgo-skin</description>
    <packaging>pom</packaging>
    <modules>
        <module>csgo-skin-common</module>
        <module>csgo-skin-admin</module>
        <module>csgo-skin-api</module>
    </modules>
    <properties>
        <java.version>1.8</java.version>
        <mapstruct.version>1.3.0.Final</mapstruct.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!--    json 工具包 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.80</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.22</version>
        </dependency>
        <!--swagger3-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>6.0.20.Final</version>
        </dependency>
        <!--实体类转换-->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-jdk8</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <!--   redis包     -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.google.guava/guava -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <!--        rabbitmq-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <!--        阿里云OSS-->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.10.2</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>cc.siyecao.uid</groupId>
            <artifactId>uid-spring-boot-starter</artifactId>
            <version>2.1</version>
        </dependency>
        <dependency>
            <groupId>cc.siyecao.uid</groupId>
            <artifactId>uid-database</artifactId>
            <version>2.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>30.1.1-jre</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.8.0</version> <!-- 请根据需要选择适当的版本 -->
        </dependency>
        <!-- Spring Security + OpenID -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <!-- AWS SDK for Java -->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>1.12.720</version> <!-- 使用最新版本 -->
        </dependency>
        <!-- PayPal SDK -->
        <dependency>
            <groupId>com.paypal.sdk</groupId>
            <artifactId>rest-api-sdk</artifactId>
            <version>1.14.0</version> <!-- 使用最新版本 -->
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
            <!-- docker插件-->
            <!--            <plugin>-->
            <!--                <groupId>com.spotify</groupId>-->
            <!--                <artifactId>docker-maven-plugin</artifactId>-->
            <!--                <version>1.2.2</version>-->

            <!--                <configuration>-->
            <!--                    &lt;!&ndash; 远程Docker的地址 &ndash;&gt;-->
            <!--&lt;!&ndash;                    <dockerHost>http://localhost:2375</dockerHost>&ndash;&gt;-->
            <!--                    &lt;!&ndash; 镜像名称、前缀、项目名 &ndash;&gt;-->
            <!--                    <imageName>${project.artifactId}</imageName>-->
            <!--                    &lt;!&ndash; Dockerfile的位置 &ndash;&gt;-->
            <!--                    <dockerDirectory>src/../</dockerDirectory>-->
            <!--                    <resources>-->
            <!--                        <resource>-->
            <!--                            <targetPath>/</targetPath>-->
            <!--                            &lt;!&ndash; 表示的target文件夹 &ndash;&gt;-->
            <!--                            <directory>${project.build.directory}</directory>-->
            <!--                            &lt;!&ndash; 表示打出来的JAR包&ndash;&gt;-->
            <!--                            <include>${project.build.finalName}.jar</include>-->
            <!--                        </resource>-->
            <!--                    </resources>-->
            <!--                </configuration>-->
            <!--            </plugin>-->
        </plugins>
    </build>

</project>
