-- Complete update script for all 100 skin entries with Portuguese translations

UPDATE `skin` SET `i18n_field_name` = '{"en": "Desert Eagle | Bronze Deco (Factory New)", "zh": "沙漠之鹰 | 青铜装饰 (崭新出厂)", "pt": "Desert Eagle | Decoração de Bronze (Nova de Fábrica)"}' WHERE `id` = '1';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Sport Gloves | Bronze Morph (Field-Tested)", "zh": "运动手套（★） | 青铜形态 (久经沙场)", "pt": "★ Luvas Esportivas | Metamorfose Bronze (Testada em Campo)"}' WHERE `id` = '2';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Nova | Windblown (Field-Tested)", "zh": "新星 | 随风 (久经沙场)", "pt": "Nova | Ventania (Testada em Campo)"}' WHERE `id` = '3';
UPDATE `skin` SET `i18n_field_name` = '{"en": "CZ75-Auto | Circaetus (Battle-Scarred)", "zh": "CZ75 | 短趾雕 (战痕累累)", "pt": "CZ75-Auto | Circaetus (Veterana de Guerra)"}' WHERE `id` = '4';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Glock-18 | Bullet Queen (Field-Tested)", "zh": "格洛克 18 型 | 子弹皇后 (久经沙场)", "pt": "Glock-18 | Rainha das Balas (Testada em Campo)"}' WHERE `id` = '5';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Nova | Candy Apple (Factory New)", "zh": "新星 | 红苹果 (崭新出厂)", "pt": "Nova | Maçã do Amor (Nova de Fábrica)"}' WHERE `id` = '6';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Desert Eagle | Mecha Industries (Minimal Wear)", "zh": "沙漠之鹰 | 机械工业 (略有磨损)", "pt": "Desert Eagle | Indústrias Mecha (Pouco Usada)"}' WHERE `id` = '7';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Desert Eagle | Oxide Blaze (Factory New)", "zh": "沙漠之鹰 | 锈蚀烈焰 (崭新出厂)", "pt": "Desert Eagle | Chama Oxidada (Nova de Fábrica)"}' WHERE `id` = '8';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A4 | The Emperor (Field-Tested)", "zh": "M4A4 | 皇帝 (久经沙场)", "pt": "M4A4 | O Imperador (Testada em Campo)"}' WHERE `id` = '9';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Aquamarine Revenge (Minimal Wear)", "zh": "AK-47 | 深海复仇 (略有磨损)", "pt": "AK-47 | Vingança Água-marinha (Pouco Usada)"}' WHERE `id` = '10';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ G3SG1 | Orange Crash (Factory New)", "zh": "G3SG1（StatTrak™） | 橙光冲击 (崭新出厂)", "pt": "StatTrak™ G3SG1 | Impacto Laranja (Nova de Fábrica)"}' WHERE `id` = '11';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Vulcan (Minimal Wear)", "zh": "AK-47 | 火神 (略有磨损)", "pt": "AK-47 | Vulcano (Pouco Usada)"}' WHERE `id` = '12';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Snakebite Case", "zh": "蛇噬武器箱", "pt": "Caixa Mordida de Cobra"}' WHERE `id` = '13';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Sport Gloves | Big Game (Field-Tested)", "zh": "运动手套（★） | 大型猎物 (久经沙场)", "pt": "★ Luvas Esportivas | Caça Grossa (Testada em Campo)"}' WHERE `id` = '14';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ MP7 | Armor Core (Factory New)", "zh": "MP7（StatTrak™） | 装甲核心 (崭新出厂)", "pt": "StatTrak™ MP7 | Núcleo Blindado (Nova de Fábrica)"}' WHERE `id` = '15';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AUG | Aristocrat (Factory New)", "zh": "AUG | 贵族 (崭新出厂)", "pt": "AUG | Aristocrata (Nova de Fábrica)"}' WHERE `id` = '16';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Nova | Gila (Minimal Wear)", "zh": "新星 | 毒蜥 (略有磨损)", "pt": "Nova | Gila (Pouco Usada)"}' WHERE `id` = '17';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | Chromatic Aberration (Field-Tested)", "zh": "AWP | 迷人眼 (久经沙场)", "pt": "AWP | Aberração Cromática (Testada em Campo)"}' WHERE `id` = '18';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | Worm God (Field-Tested)", "zh": "AWP | 蠕虫之神 (久经沙场)", "pt": "AWP | Deus Verme (Testada em Campo)"}' WHERE `id` = '19';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AUG | Syd Mead (Minimal Wear)", "zh": "AUG | 席德.米德 (略有磨损)", "pt": "AUG | Syd Mead (Pouco Usada)"}' WHERE `id` = '20';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | Mortis (Field-Tested)", "zh": "AWP | 死神 (久经沙场)", "pt": "AWP | Mortis (Testada em Campo)"}' WHERE `id` = '21';
UPDATE `skin` SET `i18n_field_name` = '{"en": "UMP-45 | Wild Child (Well-Worn)", "zh": "UMP-45 | 野孩子 (破损不堪)", "pt": "UMP-45 | Criança Selvagem (Bem Desgastada)"}' WHERE `id` = '22';
UPDATE `skin` SET `i18n_field_name` = '{"en": "P90 | Grim (Field-Tested)", "zh": "P90 | 冷血无情 (久经沙场)", "pt": "P90 | Sombrio (Testada em Campo)"}' WHERE `id` = '23';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ StatTrak™ Shadow Daggers | Marble Fade (Factory New)", "zh": "暗影双匕（★ StatTrak™） | 渐变大理石 (崭新出厂)", "pt": "★ StatTrak™ Adagas Sombrias | Mármore Desbotado (Nova de Fábrica)"}' WHERE `id` = '24';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A4 | Neo-Noir (Field-Tested)", "zh": "M4A4 | 黑色魅影 (久经沙场)", "pt": "M4A4 | Neo-Noir (Testada em Campo)"}' WHERE `id` = '25';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Safari Mesh (Minimal Wear)", "zh": "AK-47 | 狩猎网格 (略有磨损)", "pt": "AK-47 | Malha Safari (Pouco Usada)"}' WHERE `id` = '26';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Revolution Case", "zh": "变革武器箱", "pt": "Caixa Revolução"}' WHERE `id` = '27';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | 100 Thieves (Foil) | 2020 RMR", "zh": "印花 | 100 Thieves（闪亮）| 2020 RMR", "pt": "Adesivo | 100 Thieves (Holográfico) | 2020 RMR"}' WHERE `id` = '28';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ AWP | Capillary (Minimal Wear)", "zh": "AWP（StatTrak™） | 毛细血管 (略有磨损)", "pt": "StatTrak™ AWP | Capilar (Pouco Usada)"}' WHERE `id` = '29';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A4 | Spider Lily (Factory New)", "zh": "M4A4 | 彼岸花 (崭新出厂)", "pt": "M4A4 | Lírio Aranha (Nova de Fábrica)"}' WHERE `id` = '30';
UPDATE `skin` SET `i18n_field_name` = '{"en": "USP-S | The Traitor (Field-Tested)", "zh": "USP 消音版 | 倒吊人 (久经沙场)", "pt": "USP-S | O Traidor (Testada em Campo)"}' WHERE `id` = '31';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Aquamarine Revenge (Factory New)", "zh": "AK-47 | 深海复仇 (崭新出厂)", "pt": "AK-47 | Vingança Água-marinha (Nova de Fábrica)"}' WHERE `id` = '32';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ MAC-10 | Oceanic (Field-Tested)", "zh": "MAC-10（StatTrak™） | 海洋 (久经沙场)", "pt": "StatTrak™ MAC-10 | Oceânico (Testada em Campo)"}' WHERE `id` = '33';
UPDATE `skin` SET `i18n_field_name` = '{"en": "PP-Bizon | Facility Sketch (Factory New)", "zh": "PP-野牛 | 设施系列·速写图 (崭新出厂)", "pt": "PP-Bizon | Esboço da Instalação (Nova de Fábrica)"}' WHERE `id` = '34';
UPDATE `skin` SET `i18n_field_name` = '{"en": "SG 553 | Army Sheen (Minimal Wear)", "zh": "SG 553 | 军队之辉 (略有磨损)", "pt": "SG 553 | Brilho Militar (Pouco Usada)"}' WHERE `id` = '35';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Buckshot | NSWC SEAL", "zh": "铅弹 | 海军水面战中心海豹部队", "pt": "Buckshot | NSWC SEAL"}' WHERE `id` = '36';
UPDATE `skin` SET `i18n_field_name` = '{"en": "USP-S | Cyrex (Field-Tested)", "zh": "USP 消音版 | 次时代 (久经沙场)", "pt": "USP-S | Cyrex (Testada em Campo)"}' WHERE `id` = '37';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Sport Gloves | Arid (Field-Tested)", "zh": "运动手套（★） | 干旱 (久经沙场)", "pt": "★ Luvas Esportivas | Árido (Testada em Campo)"}' WHERE `id` = '38';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A4 | In Living Color (Field-Tested)", "zh": "M4A4 | 活色生香 (久经沙场)", "pt": "M4A4 | Em Cores Vivas (Testada em Campo)"}' WHERE `id` = '39';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Case Hardened (Field-Tested)", "zh": "AK-47 | 表面淬火 (久经沙场)", "pt": "AK-47 | Temperado (Testada em Campo)"}' WHERE `id` = '40';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A1-S | Player Two (Factory New)", "zh": "M4A1 消音型 | 二号玩家 (崭新出厂)", "pt": "M4A1-S | Jogador Dois (Nova de Fábrica)"}' WHERE `id` = '41';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Desert Eagle | Mecha Industries (Factory New)", "zh": "沙漠之鹰 | 机械工业 (崭新出厂)", "pt": "Desert Eagle | Indústrias Mecha (Nova de Fábrica)"}' WHERE `id` = '42';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Souvenir Galil AR | Amber Fade (Field-Tested)", "zh": "加利尔 AR（纪念品） | 渐变琥珀 (久经沙场)", "pt": "Souvenir Galil AR | Âmbar Desbotado (Testada em Campo)"}' WHERE `id` = '43';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A4 | Tooth Fairy (Minimal Wear)", "zh": "M4A4 | 齿仙 (略有磨损)", "pt": "M4A4 | Fada do Dente (Pouco Usada)"}' WHERE `id` = '44';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Slate (Minimal Wear)", "zh": "AK-47 | 墨岩 (略有磨损)", "pt": "AK-47 | Ardósia (Pouco Usada)"}' WHERE `id` = '45';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A1-S | Bright Water (Minimal Wear)", "zh": "M4A1 消音型 | 澄澈之水 (略有磨损)", "pt": "M4A1-S | Água Cristalina (Pouco Usada)"}' WHERE `id` = '46';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Specialist Gloves | Field Agent (Field-Tested)", "zh": "专业手套（★） | 一线特工 (久经沙场)", "pt": "★ Luvas Especialistas | Agente de Campo (Testada em Campo)"}' WHERE `id` = '47';
UPDATE `skin` SET `i18n_field_name` = '{"en": "CS20 Case", "zh": "反恐精英20周年武器箱", "pt": "Caixa CS20"}' WHERE `id` = '48';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Horizon Case", "zh": "地平线武器箱", "pt": "Caixa Horizonte"}' WHERE `id` = '49';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Desert Eagle | Ocean Drive (Field-Tested)", "zh": "沙漠之鹰 | 纵横波涛 (久经沙场)", "pt": "Desert Eagle | Ocean Drive (Testada em Campo)"}' WHERE `id` = '50';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Tec-9 | Red Quartz (Minimal Wear)", "zh": "Tec-9 | 晶红石英 (略有磨损)", "pt": "Tec-9 | Quartzo Vermelho (Pouco Usada)"}' WHERE `id` = '51';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Tec-9 | Brother (Minimal Wear)", "zh": "Tec-9 | 兄弟连 (略有磨损)", "pt": "Tec-9 | Irmão (Pouco Usada)"}' WHERE `id` = '52';
UPDATE `skin` SET `i18n_field_name` = '{"en": "MAC-10 | Stalker (Field-Tested)", "zh": "MAC-10 | 潜行者 (久经沙场)", "pt": "MAC-10 | Perseguidor (Testada em Campo)"}' WHERE `id` = '53';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Dual Berettas | Stained (Minimal Wear)", "zh": "双持贝瑞塔 | 人工染色 (略有磨损)", "pt": "Dual Berettas | Manchado (Pouco Usada)"}' WHERE `id` = '54';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A4 | Evil Daimyo (Minimal Wear)", "zh": "M4A4 | 杀意大名 (略有磨损)", "pt": "M4A4 | Daimyo Maligno (Pouco Usada)"}' WHERE `id` = '55';
UPDATE `skin` SET `i18n_field_name` = '{"en": "CZ75-Auto | Xiangliu (Factory New)", "zh": "CZ75 | 相柳 (崭新出厂)", "pt": "CZ75-Auto | Xiangliu (Nova de Fábrica)"}' WHERE `id` = '56';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Neon Revolution (Minimal Wear)", "zh": "AK-47 | 霓虹革命 (略有磨损)", "pt": "AK-47 | Revolução Neon (Pouco Usada)"}' WHERE `id` = '57';
UPDATE `skin` SET `i18n_field_name` = '{"en": "MAC-10 | Malachite (Factory New)", "zh": "MAC-10 | 孔雀石 (崭新出厂)", "pt": "MAC-10 | Malaquita (Nova de Fábrica)"}' WHERE `id` = '58';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Galil AR | Chatterbox (Well-Worn)", "zh": "加利尔 AR | 喧闹骷髅 (破损不堪)", "pt": "Galil AR | Tagarela (Bem Desgastada)"}' WHERE `id` = '59';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ Desert Eagle | Light Rail (Minimal Wear)", "zh": "沙漠之鹰（StatTrak™） | 轻轨 (略有磨损)", "pt": "StatTrak™ Desert Eagle | Trilho Leve (Pouco Usada)"}' WHERE `id` = '60';
UPDATE `skin` SET `i18n_field_name` = '{"en": "XM1014 | Seasons (Minimal Wear)", "zh": "XM1014 | 四季 (略有磨损)", "pt": "XM1014 | Estações (Pouco Usada)"}' WHERE `id` = '61';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Music Kit | Meechy Darko, Gothic Luxury", "zh": "音乐盒 | Meechy Darko - 哥特浮华", "pt": "Kit Musical | Meechy Darko, Luxo Gótico"}' WHERE `id` = '62';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Hand Wraps | Overprint (Field-Tested)", "zh": "裹手（★） | 套印 (久经沙场)", "pt": "★ Ataduras | Sobreimpressão (Testada em Campo)"}' WHERE `id` = '63';
UPDATE `skin` SET `i18n_field_name` = '{"en": "USP-S | Guardian (Field-Tested)", "zh": "USP 消音版 | 守护者 (久经沙场)", "pt": "USP-S | Guardião (Testada em Campo)"}' WHERE `id` = '64';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Dual Berettas | Melondrama (Factory New)", "zh": "双持贝瑞塔 | 瓜瓜 (崭新出厂)", "pt": "Dual Berettas | Drama do Melão (Nova de Fábrica)"}' WHERE `id` = '65';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Ice Coaled (Field-Tested)", "zh": "AK-47 | 可燃冰 (久经沙场)", "pt": "AK-47 | Gelo Carbonizado (Testada em Campo)"}' WHERE `id` = '66';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Safecracker Voltzmann | The Professionals", "zh": "飞贼波兹曼 | 专业人士", "pt": "Safecracker Voltzmann | Os Profissionais"}' WHERE `id` = '67';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A1-S | Leaded Glass (Minimal Wear)", "zh": "M4A1 消音型 | 破碎铅秋 (略有磨损)", "pt": "M4A1-S | Vidro com Chumbo (Pouco Usada)"}' WHERE `id` = '68';
UPDATE `skin` SET `i18n_field_name` = '{"en": "R8 Revolver | Grip (Field-Tested)", "zh": "R8 左轮手枪 | 稳 (久经沙场)", "pt": "R8 Revolver | Punho (Testada em Campo)"}' WHERE `id` = '69';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | The Empress (Field-Tested)", "zh": "AK-47 | 皇后 (久经沙场)", "pt": "AK-47 | A Imperatriz (Testada em Campo)"}' WHERE `id` = '70';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ Tec-9 | Cracked Opal (Minimal Wear)", "zh": "Tec-9（StatTrak™） | 碎蛋白石 (略有磨损)", "pt": "StatTrak™ Tec-9 | Opala Rachada (Pouco Usada)"}' WHERE `id` = '71';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Markus Delrow | FBI HRT", "zh": "马尔库斯·戴劳 | 联邦调查局（FBI）人质营救队", "pt": "Markus Delrow | FBI HRT"}' WHERE `id` = '72';
UPDATE `skin` SET `i18n_field_name` = '{"en": "MAC-10 | Disco Tech (Field-Tested)", "zh": "MAC-10 | 渐变迪斯科 (久经沙场)", "pt": "MAC-10 | Disco Tech (Testada em Campo)"}' WHERE `id` = '73';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ MAC-10 | Rangeen (Factory New)", "zh": "MAC-10（StatTrak™） | 冉吉 (崭新出厂)", "pt": "StatTrak™ MAC-10 | Rangeen (Nova de Fábrica)"}' WHERE `id` = '74';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | Hyper Beast (Field-Tested)", "zh": "AWP | 暴怒野兽 (久经沙场)", "pt": "AWP | Hiper Fera (Testada em Campo)"}' WHERE `id` = '75';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Leet Museo (Minimal Wear)", "zh": "AK-47 | 抽象派 1337 (略有磨损)", "pt": "AK-47 | Leet Museo (Pouco Usada)"}' WHERE `id` = '76';
UPDATE `skin` SET `i18n_field_name` = '{"en": "PP-Bizon | Runic (Field-Tested)", "zh": "PP-野牛 | 神秘碑文 (久经沙场)", "pt": "PP-Bizon | Rúnico (Testada em Campo)"}' WHERE `id` = '77';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Galil AR | Crimson Tsunami (Factory New)", "zh": "加利尔 AR | 深红海啸 (崭新出厂)", "pt": "Galil AR | Tsunami Carmesim (Nova de Fábrica)"}' WHERE `id` = '78';
UPDATE `skin` SET `i18n_field_name` = '{"en": "SSG 08 | Carbon Fiber (Factory New)", "zh": "SSG 08 | 碳素纤维 (崭新出厂)", "pt": "SSG 08 | Fibra de Carbono (Nova de Fábrica)"}' WHERE `id` = '79';
UPDATE `skin` SET `i18n_field_name` = '{"en": "USP-S | Ticket to Hell (Factory New)", "zh": "USP 消音版 | 地狱门票 (崭新出厂)", "pt": "USP-S | Bilhete para o Inferno (Nova de Fábrica)"}' WHERE `id` = '80';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A4 | Cyber Security (Factory New)", "zh": "M4A4 | 赛博 (崭新出厂)", "pt": "M4A4 | Segurança Cibernética (Nova de Fábrica)"}' WHERE `id` = '81';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Maximus | Sabre", "zh": "马克西姆斯 | 军刀", "pt": "Maximus | Sabre"}' WHERE `id` = '82';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Desert Eagle | Directive (Field-Tested)", "zh": "沙漠之鹰 | 指挥 (久经沙场)", "pt": "Desert Eagle | Diretiva (Testada em Campo)"}' WHERE `id` = '83';
UPDATE `skin` SET `i18n_field_name` = '{"en": "SCAR-20 | Outbreak (Factory New)", "zh": "SCAR-20 | 丛林爆发 (崭新出厂)", "pt": "SCAR-20 | Surto (Nova de Fábrica)"}' WHERE `id` = '84';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ P2000 | Imperial (Factory New)", "zh": "P2000（StatTrak™） | 至高帝皇 (崭新出厂)", "pt": "StatTrak™ P2000 | Imperial (Nova de Fábrica)"}' WHERE `id` = '85';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Desert Eagle | Light Rail (Well-Worn)", "zh": "沙漠之鹰 | 轻轨 (破损不堪)", "pt": "Desert Eagle | Trilho Leve (Bem Desgastada)"}' WHERE `id` = '86';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Moto Gloves | 3rd Commando Company (Minimal Wear)", "zh": "摩托手套（★） | 第三特种兵连 (略有磨损)", "pt": "★ Luvas de Motociclista | 3ª Companhia de Comandos (Pouco Usada)"}' WHERE `id` = '87';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Antwerp 2022 Legends Sticker Capsule", "zh": "2022年安特卫普锦标赛传奇组印花胶囊", "pt": "Cápsula de Adesivos Lendas Antuérpia 2022"}' WHERE `id` = '88';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ MP9 | Mount Fuji (Field-Tested)", "zh": "MP9（StatTrak™） | 富士山 (久经沙场)", "pt": "StatTrak™ MP9 | Monte Fuji (Testada em Campo)"}' WHERE `id` = '89';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | BIG (Holo) | 2020 RMR", "zh": "印花 | BIG（全息）| 2020 RMR", "pt": "Adesivo | BIG (Holo) | 2020 RMR"}' WHERE `id` = '90';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | Nemiga (Holo) | 2020 RMR", "zh": "印花 | Nemiga（全息）| 2020 RMR", "pt": "Adesivo | Nemiga (Holo) | 2020 RMR"}' WHERE `id` = '91';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | Ninjas in Pyjamas (Holo) | 2020 RMR", "zh": "印花 | Ninjas in Pyjamas（全息）| 2020 RMR", "pt": "Adesivo | Ninjas in Pyjamas (Holo) | 2020 RMR"}' WHERE `id` = '92';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Frontside Misty (Field-Tested)", "zh": "AK-47 | 前线迷雾 (久经沙场)", "pt": "AK-47 | Névoa Frontal (Testada em Campo)"}' WHERE `id` = '93';
UPDATE `skin` SET `i18n_field_name` = '{"en": "PP-Bizon | Candy Apple (Factory New)", "zh": "PP-野牛 | 红苹果 (崭新出厂)", "pt": "PP-Bizon | Maçã do Amor (Nova de Fábrica)"}' WHERE `id` = '94';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Jungle Spray (Field-Tested)", "zh": "AK-47 | 丛林涂装 (久经沙场)", "pt": "AK-47 | Spray da Selva (Testada em Campo)"}' WHERE `id` = '95';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Desert Eagle | Printstream (Factory New)", "zh": "沙漠之鹰 | 印花集 (崭新出厂)", "pt": "Desert Eagle | Fluxo de Impressão (Nova de Fábrica)"}' WHERE `id` = '96';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ SG 553 | Pulse (Field-Tested)", "zh": "SG 553（StatTrak™） | 电子脉冲 (久经沙场)", "pt": "StatTrak™ SG 553 | Pulso (Testada em Campo)"}' WHERE `id` = '97';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Shadow Daggers | Freehand (Factory New)", "zh": "暗影双匕（★） | 自由之手 (崭新出厂)", "pt": "★ Adagas Sombrias | Mão Livre (Nova de Fábrica)"}' WHERE `id` = '98';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Desert Eagle | Printstream (Field-Tested)", "zh": "沙漠之鹰 | 印花集 (久经沙场)", "pt": "Desert Eagle | Fluxo de Impressão (Testada em Campo)"}' WHERE `id` = '99';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Hand Wraps | Cobalt Skulls (Field-Tested)", "zh": "裹手（★） | 钴蓝骷髅 (久经沙场)", "pt": "★ Ataduras | Caveiras Cobalto (Testada em Campo)"}' WHERE `id` = '100';


-- Complete update script for all 200 skin entries with Portuguese translations

-- Entries 1-100 remain unchanged

-- New entries 101-200 with Portuguese translations added
UPDATE `skin` SET `i18n_field_name` = '{"en": "Desert Eagle | Trigger Discipline (Minimal Wear)", "zh": "沙漠之鹰 | 后发制人 (略有磨损)", "pt": "Desert Eagle | Disciplina de Gatilho (Pouco Usada)"}' WHERE `id` = '101';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Prisma Case", "zh": "棱彩武器箱", "pt": "Caixa Prisma"}' WHERE `id` = '102';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Desert Eagle | Crimson Web (Field-Tested)", "zh": "沙漠之鹰 | 深红之网 (久经沙场)", "pt": "Desert Eagle | Teia Carmesim (Testada em Campo)"}' WHERE `id` = '103';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ Desert Eagle | Code Red (Field-Tested)", "zh": "沙漠之鹰（StatTrak™） | 红色代号 (久经沙场)", "pt": "StatTrak™ Desert Eagle | Código Vermelho (Testada em Campo)"}' WHERE `id` = '104';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Moto Gloves | Eclipse (Field-Tested)", "zh": "摩托手套（★） | 日蚀 (久经沙场)", "pt": "★ Luvas de Motociclista | Eclipse (Testada em Campo)"}' WHERE `id` = '105';
UPDATE `skin` SET `i18n_field_name` = '{"en": "SSG 08 | Mainframe 001 (Field-Tested)", "zh": "SSG 08 | 主机001 (久经沙场)", "pt": "SSG 08 | Mainframe 001 (Testada em Campo)"}' WHERE `id` = '106';
UPDATE `skin` SET `i18n_field_name` = '{"en": "SCAR-20 | Enforcer (Minimal Wear)", "zh": "SCAR-20 | 执行者 (略有磨损)", "pt": "SCAR-20 | Executor (Pouco Usada)"}' WHERE `id` = '107';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Vypa Sista of the Revolution | Guerrilla Warfare", "zh": "薇帕姐（革新派） | 游击队", "pt": "Vypa Sista da Revolução | Guerra de Guerrilha"}' WHERE `id` = '108';
UPDATE `skin` SET `i18n_field_name` = '{"en": "MAG-7 | Petroglyph (Factory New)", "zh": "MAG-7 | 石雕 (崭新出厂)", "pt": "MAG-7 | Petroglifo (Nova de Fábrica)"}' WHERE `id` = '109';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Bayonet | Gamma Doppler (Factory New)", "zh": "刺刀（★） | 伽玛多普勒 (崭新出厂)", "pt": "★ Baioneta | Gamma Doppler (Nova de Fábrica)"}' WHERE `id` = '110';
UPDATE `skin` SET `i18n_field_name` = '{"en": "MAC-10 | Silver (Factory New)", "zh": "MAC-10 | 银质 (崭新出厂)", "pt": "MAC-10 | Prata (Nova de Fábrica)"}' WHERE `id` = '111';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Desert Eagle | Printstream (Minimal Wear)", "zh": "沙漠之鹰 | 印花集 (略有磨损)", "pt": "Desert Eagle | Fluxo de Impressão (Pouco Usada)"}' WHERE `id` = '112';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Glock-18 | Candy Apple (Factory New)", "zh": "格洛克 18 型 | 红苹果 (崭新出厂)", "pt": "Glock-18 | Maçã do Amor (Nova de Fábrica)"}' WHERE `id` = '113';
UPDATE `skin` SET `i18n_field_name` = '{"en": "P90 | Shapewood (Field-Tested)", "zh": "P90 | 精雕木刻 (久经沙场)", "pt": "P90 | Madeira Esculpida (Testada em Campo)"}' WHERE `id` = '114';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AUG | Fleet Flock (Minimal Wear)", "zh": "AUG | 燕群 (略有磨损)", "pt": "AUG | Bando de Andorinhas (Pouco Usada)"}' WHERE `id` = '115';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Glock-18 | Vogue (Field-Tested)", "zh": "格洛克 18 型 | 摩登时代 (久经沙场)", "pt": "Glock-18 | Vogue (Testada em Campo)"}' WHERE `id` = '116';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Nova | Exo (Minimal Wear)", "zh": "新星 | Exo (略有磨损)", "pt": "Nova | Exo (Pouco Usada)"}' WHERE `id` = '117';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Desert Eagle | Code Red (Field-Tested)", "zh": "沙漠之鹰 | 红色代号 (久经沙场)", "pt": "Desert Eagle | Código Vermelho (Testada em Campo)"}' WHERE `id` = '118';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | Chromatic Aberration (Minimal Wear)", "zh": "AWP | 迷人眼 (略有磨损)", "pt": "AWP | Aberração Cromática (Pouco Usada)"}' WHERE `id` = '119';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Baroque Purple (Factory New)", "zh": "AK-47 | 巴洛克之紫 (崭新出厂)", "pt": "AK-47 | Roxo Barroco (Nova de Fábrica)"}' WHERE `id` = '120';
UPDATE `skin` SET `i18n_field_name` = '{"en": "USP-S | Kill Confirmed (Minimal Wear)", "zh": "USP 消音版 | 枪响人亡 (略有磨损)", "pt": "USP-S | Morte Confirmada (Pouco Usada)"}' WHERE `id` = '121';
UPDATE `skin` SET `i18n_field_name` = '{"en": "USP-S | Blueprint (Factory New)", "zh": "USP 消音版 | 蓝图 (崭新出厂)", "pt": "USP-S | Planta Baixa (Nova de Fábrica)"}' WHERE `id` = '122';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | Atheris (Minimal Wear)", "zh": "AWP | 树蝰 (略有磨损)", "pt": "AWP | Atheris (Pouco Usada)"}' WHERE `id` = '123';
UPDATE `skin` SET `i18n_field_name` = '{"en": "SG 553 | Integrale (Minimal Wear)", "zh": "SG 553 | 意式拉力 (略有磨损)", "pt": "SG 553 | Integrale (Pouco Usada)"}' WHERE `id` = '124';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Glock-18 | Moonrise (Field-Tested)", "zh": "格洛克 18 型 | 城里的月光 (久经沙场)", "pt": "Glock-18 | Luar (Testada em Campo)"}' WHERE `id` = '125';
UPDATE `skin` SET `i18n_field_name` = '{"en": "SG 553 | Pulse (Minimal Wear)", "zh": "SG 553 | 电子脉冲 (略有磨损)", "pt": "SG 553 | Pulso (Pouco Usada)"}' WHERE `id` = '126';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Galil AR | Tuxedo (Minimal Wear)", "zh": "加利尔 AR | 燕尾 (略有磨损)", "pt": "Galil AR | Smoking (Pouco Usada)"}' WHERE `id` = '127';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | suNny | Krakow 2017", "zh": "印花 | suNny | 2017年克拉科夫锦标赛", "pt": "Adesivo | suNny | Cracóvia 2017"}' WHERE `id` = '128';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A4 | The Battlestar (Factory New)", "zh": "M4A4 | 战场之星 (崭新出厂)", "pt": "M4A4 | Estrela de Batalha (Nova de Fábrica)"}' WHERE `id` = '129';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ Tec-9 | Ice Cap (Factory New)", "zh": "Tec-9（StatTrak™） | 冰冠 (崭新出厂)", "pt": "StatTrak™ Tec-9 | Capa de Gelo (Nova de Fábrica)"}' WHERE `id` = '130';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Tec-9 | Avalanche (Field-Tested)", "zh": "Tec-9 | 坍雪寒裘 (久经沙场)", "pt": "Tec-9 | Avalanche (Testada em Campo)"}' WHERE `id` = '131';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Moto Gloves | Smoke Out (Battle-Scarred)", "zh": "摩托手套（★） | 小心烟雾弹 (战痕累累)", "pt": "★ Luvas de Motociclista | Fumaça (Veterana de Guerra)"}' WHERE `id` = '132';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Moto Gloves | Polygon (Field-Tested)", "zh": "摩托手套（★） | 多边形 (久经沙场)", "pt": "★ Luvas de Motociclista | Polígono (Testada em Campo)"}' WHERE `id` = '133';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A1-S | Emphorosaur-S (Minimal Wear)", "zh": "M4A1 消音型 | 隐伏帝王龙 (略有磨损)", "pt": "M4A1-S | Emphorosaur-S (Pouco Usada)"}' WHERE `id` = '134';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Ursus Knife | Tiger Tooth (Factory New)", "zh": "熊刀（★） | 虎牙 (崭新出厂)", "pt": "★ Faca Ursus | Dente de Tigre (Nova de Fábrica)"}' WHERE `id` = '135';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Leet Museo (Field-Tested)", "zh": "AK-47 | 抽象派 1337 (久经沙场)", "pt": "AK-47 | Leet Museo (Testada em Campo)"}' WHERE `id` = '136';
UPDATE `skin` SET `i18n_field_name` = '{"en": "CZ75-Auto | Red Astor (Factory New)", "zh": "CZ75 | 红鹰 (崭新出厂)", "pt": "CZ75-Auto | Astor Vermelho (Nova de Fábrica)"}' WHERE `id` = '137';
UPDATE `skin` SET `i18n_field_name` = '{"en": "SG 553 | Colony IV (Field-Tested)", "zh": "SG 553 | 四号栖息地 (久经沙场)", "pt": "SG 553 | Colônia IV (Testada em Campo)"}' WHERE `id` = '138';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A4 | Desolate Space (Field-Tested)", "zh": "M4A4 | 死寂空间 (久经沙场)", "pt": "M4A4 | Espaço Desolado (Testada em Campo)"}' WHERE `id` = '139';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ Music Kit | Blitz Kids, The Good Youth", "zh": "音乐盒（StatTrak™） | Blitz Kids - 有为青年", "pt": "StatTrak™ Kit Musical | Blitz Kids, A Boa Juventude"}' WHERE `id` = '140';
UPDATE `skin` SET `i18n_field_name` = '{"en": "USP-S | Caiman (Minimal Wear)", "zh": "USP 消音版 | 凯门鳄 (略有磨损)", "pt": "USP-S | Caimão (Pouco Usada)"}' WHERE `id` = '141';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | cadiaN (Glitter) | Rio 2022", "zh": "印花 | cadiaN（闪耀）| 2022年里约热内卢锦标赛", "pt": "Adesivo | cadiaN (Brilhante) | Rio 2022"}' WHERE `id` = '142';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Glock-18 | Neo-Noir (Field-Tested)", "zh": "格洛克 18 型 | 黑色魅影 (久经沙场)", "pt": "Glock-18 | Neo-Noir (Testada em Campo)"}' WHERE `id` = '143';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Negev | Lionfish (Factory New)", "zh": "内格夫 | 狮子鱼 (崭新出厂)", "pt": "Negev | Peixe-Leão (Nova de Fábrica)"}' WHERE `id` = '144';
UPDATE `skin` SET `i18n_field_name` = '{"en": "R8 Revolver | Amber Fade (Factory New)", "zh": "R8 左轮手枪 | 渐变琥珀 (崭新出厂)", "pt": "R8 Revolver | Âmbar Desbotado (Nova de Fábrica)"}' WHERE `id` = '145';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | Chromatic Aberration (Factory New)", "zh": "AWP | 迷人眼 (崭新出厂)", "pt": "AWP | Aberração Cromática (Nova de Fábrica)"}' WHERE `id` = '146';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A1-S | Flashback (Factory New)", "zh": "M4A1 消音型 | 闪回 (崭新出厂)", "pt": "M4A1-S | Flashback (Nova de Fábrica)"}' WHERE `id` = '147';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ XM1014 | Slipstream (Field-Tested)", "zh": "XM1014（StatTrak™） | 滑流 (久经沙场)", "pt": "StatTrak™ XM1014 | Esteira de Vento (Testada em Campo)"}' WHERE `id` = '148';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | Stone Scales (Foil)", "zh": "印花 | 石鳞（闪亮）", "pt": "Adesivo | Escamas de Pedra (Holográfico)"}' WHERE `id` = '149';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Music Kit | 3kliksphilip, Heading for the Source", "zh": "音乐盒 | 3kliksphilip - 追溯起源", "pt": "Kit Musical | 3kliksphilip, Rumo à Fonte"}' WHERE `id` = '150';
UPDATE `skin` SET `i18n_field_name` = '{"en": "PP-Bizon | Osiris (Factory New)", "zh": "PP-野牛 | 死亡主宰者 (崭新出厂)", "pt": "PP-Bizon | Osíris (Nova de Fábrica)"}' WHERE `id` = '151';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Shadow Daggers | Rust Coat (Battle-Scarred)", "zh": "暗影双匕（★） | 外表生锈 (战痕累累)", "pt": "★ Adagas Sombrias | Revestimento de Ferrugem (Veterana de Guerra)"}' WHERE `id` = '152';
UPDATE `skin` SET `i18n_field_name` = '{"en": "USP-S | Road Rash (Field-Tested)", "zh": "USP 消音版 | 公路杀手 (久经沙场)", "pt": "USP-S | Escoriação (Testada em Campo)"}' WHERE `id` = '153';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A4 | Griffin (Minimal Wear)", "zh": "M4A4 | 狮鹫 (略有磨损)", "pt": "M4A4 | Grifo (Pouco Usada)"}' WHERE `id` = '154';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | PAW (Minimal Wear)", "zh": "AWP | 猫猫狗狗 (略有磨损)", "pt": "AWP | PAW (Pouco Usada)"}' WHERE `id` = '155';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A4 | 龍王 (Dragon King) (Field-Tested)", "zh": "M4A4 | 龙王 (久经沙场)", "pt": "M4A4 | Rei Dragão (Testada em Campo)"}' WHERE `id` = '156';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ CZ75-Auto | Pole Position (Minimal Wear)", "zh": "CZ75（StatTrak™） | 先驱 (略有磨损)", "pt": "StatTrak™ CZ75-Auto | Pole Position (Pouco Usada)"}' WHERE `id` = '157';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Dreams & Nightmares Case", "zh": "梦魇武器箱", "pt": "Caixa Sonhos e Pesadelos"}' WHERE `id` = '158';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Music Kit | Perfect World, 花脸 Hua Lian (Painted Face)", "zh": "音乐盒 | 完美世界 - 花脸", "pt": "Kit Musical | Perfect World, 花脸 Hua Lian (Rosto Pintado)"}' WHERE `id` = '159';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Operator | FBI SWAT", "zh": "特种兵 | 联邦调查局（FBI）特警", "pt": "Operador | FBI SWAT"}' WHERE `id` = '160';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M249 | System Lock (Factory New)", "zh": "M249 | 系统锁定 (崭新出厂)", "pt": "M249 | Bloqueio do Sistema (Nova de Fábrica)"}' WHERE `id` = '161';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Glock-18 | Winterized (Factory New)", "zh": "格洛克 18 型 | 冬季战术 (崭新出厂)", "pt": "Glock-18 | Preparada para o Inverno (Nova de Fábrica)"}' WHERE `id` = '162';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A4 | Griffin (Field-Tested)", "zh": "M4A4 | 狮鹫 (久经沙场)", "pt": "M4A4 | Grifo (Testada em Campo)"}' WHERE `id` = '163';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Glock-18 | Bunsen Burner (Field-Tested)", "zh": "格洛克 18 型 | 本生灯 (久经沙场)", "pt": "Glock-18 | Bico de Bunsen (Testada em Campo)"}' WHERE `id` = '164';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Desert Eagle | Blue Ply (Minimal Wear)", "zh": "沙漠之鹰 | 蓝色层压板 (略有磨损)", "pt": "Desert Eagle | Camada Azul (Pouco Usada)"}' WHERE `id` = '165';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Glock-18 | High Beam (Factory New)", "zh": "格洛克 18 型 | 远光灯 (崭新出厂)", "pt": "Glock-18 | Farol Alto (Nova de Fábrica)"}' WHERE `id` = '166';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Recoil Case", "zh": "反冲武器箱", "pt": "Caixa Recuo"}' WHERE `id` = '167';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Glock-18 | Grinder (Minimal Wear)", "zh": "格洛克 18 型 | 粉碎者 (略有磨损)", "pt": "Glock-18 | Moedor (Pouco Usada)"}' WHERE `id` = '168';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Five-SeveN | Angry Mob (Factory New)", "zh": "FN57 | 怒氓 (崭新出厂)", "pt": "Five-SeveN | Multidão Furiosa (Nova de Fábrica)"}' WHERE `id` = '169';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Desert Eagle | Fennec Fox (Factory New)", "zh": "沙漠之鹰 | 沙漠之狐 (崭新出厂)", "pt": "Desert Eagle | Raposa do Deserto (Nova de Fábrica)"}' WHERE `id` = '170';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Aquamarine Revenge (Field-Tested)", "zh": "AK-47 | 深海复仇 (久经沙场)", "pt": "AK-47 | Vingança Água-marinha (Testada em Campo)"}' WHERE `id` = '171';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ P2000 | Handgun (Field-Tested)", "zh": "P2000（StatTrak™） | 手炮 (久经沙场)", "pt": "StatTrak™ P2000 | Pistola (Testada em Campo)"}' WHERE `id` = '172';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Negev | Drop Me (Field-Tested)", "zh": "内格夫 | 丢把枪 (久经沙场)", "pt": "Negev | Me Solte (Testada em Campo)"}' WHERE `id` = '173';
UPDATE `skin` SET `i18n_field_name` = '{"en": "MP9 | Black Sand (Field-Tested)", "zh": "MP9 | 黑砂 (久经沙场)", "pt": "MP9 | Areia Preta (Testada em Campo)"}' WHERE `id` = '174';
UPDATE `skin` SET `i18n_field_name` = '{"en": "P90 | Freight (Well-Worn)", "zh": "P90 | 集装箱 (破损不堪)", "pt": "P90 | Carga (Bem Desgastada)"}' WHERE `id` = '175';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Nova | Wild Six (Field-Tested)", "zh": "新星 | 狂野六号 (久经沙场)", "pt": "Nova | Seis Selvagem (Testada em Campo)"}' WHERE `id` = '176';
UPDATE `skin` SET `i18n_field_name` = '{"en": "P90 | Sand Spray (Field-Tested)", "zh": "P90 | 沙漠涂装 (久经沙场)", "pt": "P90 | Spray de Areia (Testada em Campo)"}' WHERE `id` = '177';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A1-S | Icarus Fell (Factory New)", "zh": "M4A1 消音型 | 伊卡洛斯殒落 (崭新出厂)", "pt": "M4A1-S | Queda de Ícaro (Nova de Fábrica)"}' WHERE `id` = '178';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Specialist Gloves | Fade (Field-Tested)", "zh": "专业手套（★） | 渐变之色 (久经沙场)", "pt": "★ Luvas Especialistas | Desbotamento (Testada em Campo)"}' WHERE `id` = '179';
UPDATE `skin` SET `i18n_field_name` = '{"en": "MP9 | Mount Fuji (Factory New)", "zh": "MP9 | 富士山 (崭新出厂)", "pt": "MP9 | Monte Fuji (Nova de Fábrica)"}' WHERE `id` = '180';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ Nova | Windblown (Field-Tested)", "zh": "新星（StatTrak™） | 随风 (久经沙场)", "pt": "StatTrak™ Nova | Ventania (Testada em Campo)"}' WHERE `id` = '181';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | Skull Lil Boney", "zh": "印花 | 骷髅小伯尼", "pt": "Adesivo | Caveirinha Ossuda"}' WHERE `id` = '182';
UPDATE `skin` SET `i18n_field_name` = '{"en": "MAC-10 | Allure (Well-Worn)", "zh": "MAC-10 | 魅惑 (破损不堪)", "pt": "MAC-10 | Fascínio (Bem Desgastada)"}' WHERE `id` = '183';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ Tec-9 | Bamboozle (Minimal Wear)", "zh": "Tec-9（StatTrak™） | 青竹伪装 (略有磨损)", "pt": "StatTrak™ Tec-9 | Bamboozle (Pouco Usada)"}' WHERE `id` = '184';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Gut Knife | Gamma Doppler (Factory New)", "zh": "穿肠刀（★） | 伽玛多普勒 (崭新出厂)", "pt": "★ Faca Gut | Gamma Doppler (Nova de Fábrica)"}' WHERE `id` = '185';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ P250 | See Ya Later (Field-Tested)", "zh": "P250（StatTrak™） | 生化短吻鳄 (久经沙场)", "pt": "StatTrak™ P250 | Até Logo (Testada em Campo)"}' WHERE `id` = '186';
UPDATE `skin` SET `i18n_field_name` = '{"en": "MP9 | Food Chain (Factory New)", "zh": "MP9 | 爆裂食物链 (崭新出厂)", "pt": "MP9 | Cadeia Alimentar (Nova de Fábrica)"}' WHERE `id` = '187';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ Galil AR | Crimson Tsunami (Factory New)", "zh": "加利尔 AR（StatTrak™） | 深红海啸 (崭新出厂)", "pt": "StatTrak™ Galil AR | Tsunami Carmesim (Nova de Fábrica)"}' WHERE `id` = '188';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ Galil AR | Connexion (Minimal Wear)", "zh": "加利尔 AR（StatTrak™） | 凤凰商号 (略有磨损)", "pt": "StatTrak™ Galil AR | Conexão (Pouco Usada)"}' WHERE `id` = '189';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Stiletto Knife | Urban Masked (Field-Tested)", "zh": "短剑（★） | 都市伪装 (久经沙场)", "pt": "★ Faca Stiletto | Camuflagem Urbana (Testada em Campo)"}' WHERE `id` = '190';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | BOOM (Minimal Wear)", "zh": "AWP | *嘣* (略有磨损)", "pt": "AWP | BOOM (Pouco Usada)"}' WHERE `id` = '191';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Nova | Clear Polymer (Field-Tested)", "zh": "新星 | 一见青心 (久经沙场)", "pt": "Nova | Polímero Transparente (Testada em Campo)"}' WHERE `id` = '192';
UPDATE `skin` SET `i18n_field_name` = '{"en": "MAC-10 | Case Hardened (Field-Tested)", "zh": "MAC-10 | 表面淬火 (久经沙场)", "pt": "MAC-10 | Revestimento Endurecido (Testada em Campo)"}' WHERE `id` = '193';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Classic Knife | Crimson Web (Battle-Scarred)", "zh": "海豹短刀（★） | 深红之网 (战痕累累)", "pt": "★ Faca Clássica | Teia Carmesim (Veterana de Guerra)"}' WHERE `id` = '194';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | BOOM (Field-Tested)", "zh": "AWP | *嘣* (久经沙场)", "pt": "AWP | BOOM (Testada em Campo)"}' WHERE `id` = '195';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Uncharted (Minimal Wear)", "zh": "AK-47 | 迷踪秘境 (略有磨损)", "pt": "AK-47 | Inexplorado (Pouco Usada)"}' WHERE `id` = '196';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Moto Gloves | Boom! (Field-Tested)", "zh": "摩托手套（★） | *嘣！* (久经沙场)", "pt": "★ Luvas de Motociclista | Boom! (Testada em Campo)"}' WHERE `id` = '197';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sir Bloody Darryl Royale | The Professionals", "zh": "残酷的达里尔爵士（皇家）| 专业人士", "pt": "Sir Bloody Darryl Royale | Os Profissionais"}' WHERE `id` = '198';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | PAW (Field-Tested)", "zh": "AWP | 猫猫狗狗 (久经沙场)", "pt": "AWP | PAW (Testada em Campo)"}' WHERE `id` = '199';
UPDATE `skin` SET `i18n_field_name` = '{"en": "MP9 | Bioleak (Factory New)", "zh": "MP9 | 生化泄漏 (崭新出厂)", "pt": "MP9 | Vazamento Biológico (Nova de Fábrica)"}' WHERE `id` = '200';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ Tec-9 | Rebel (Field-Tested)", "zh": "Tec-9（StatTrak™） | 叛逆 (久经沙场)", "pt": "StatTrak™ Tec-9 | Rebelde (Testada em Campo)"}' WHERE `id` = '201';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A1-S | Basilisk (Field-Tested)", "zh": "M4A1 消音型 | 翼蜥 (久经沙场)", "pt": "M4A1-S | Basilisco (Testada em Campo)"}' WHERE `id` = '202';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Huntsman Knife | Lore (Minimal Wear)", "zh": "猎杀者匕首（★） | 传说 (略有磨损)", "pt": "★ Faca Huntsman | Tradição (Pouco Usada)"}' WHERE `id` = '203';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Moto Gloves | Smoke Out (Field-Tested)", "zh": "摩托手套（★） | 小心烟雾弹 (久经沙场)", "pt": "★ Luvas de Motociclista | Fumaça (Testada em Campo)"}' WHERE `id` = '204';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Hand Wraps | Arboreal (Field-Tested)", "zh": "裹手（★） | 森林色调 (久经沙场)", "pt": "★ Faixas de Mão | Arbórea (Testada em Campo)"}' WHERE `id` = '205';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Navaja Knife | Rust Coat (Battle-Scarred)", "zh": "折刀（★） | 外表生锈 (战痕累累)", "pt": "★ Faca Navaja | Revestimento de Ferrugem (Veterana de Guerra)"}' WHERE `id` = '206';
UPDATE `skin` SET `i18n_field_name` = '{"en": "MP9 | Goo (Field-Tested)", "zh": "MP9 | 焦油缠绕 (久经沙场)", "pt": "MP9 | Gosma (Testada em Campo)"}' WHERE `id` = '207';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Bayonet | Damascus Steel (Minimal Wear)", "zh": "刺刀（★） | 大马士革钢 (略有磨损)", "pt": "★ Baioneta | Aço de Damasco (Pouco Usada)"}' WHERE `id` = '208';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | Evil Geniuses (Holo) | 2020 RMR", "zh": "印花 | Evil Geniuses（全息）| 2020 RMR", "pt": "Adesivo | Evil Geniuses (Holográfico) | 2020 RMR"}' WHERE `id` = '209';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Nova | Windblown (Minimal Wear)", "zh": "新星 | 随风 (略有磨损)", "pt": "Nova | Ventania (Pouco Usada)"}' WHERE `id` = '210';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A1-S | Decimator (Minimal Wear)", "zh": "M4A1 消音型 | 毁灭者 2000 (略有磨损)", "pt": "M4A1-S | Decimador (Pouco Usada)"}' WHERE `id` = '211';
UPDATE `skin` SET `i18n_field_name` = '{"en": "P90 | Nostalgia (Factory New)", "zh": "P90 | 往日行动 (崭新出厂)", "pt": "P90 | Nostalgia (Nova de Fábrica)"}' WHERE `id` = '212';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Desert Eagle | Blue Ply (Field-Tested)", "zh": "沙漠之鹰 | 蓝色层压板 (久经沙场)", "pt": "Desert Eagle | Camada Azul (Testada em Campo)"}' WHERE `id` = '213';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ AWP | PAW (Minimal Wear)", "zh": "AWP（StatTrak™） | 猫猫狗狗 (略有磨损)", "pt": "StatTrak™ AWP | PAW (Pouco Usada)"}' WHERE `id` = '214';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Ursus Knife | Rust Coat (Battle-Scarred)", "zh": "熊刀（★） | 外表生锈 (战痕累累)", "pt": "★ Faca Ursus | Revestimento de Ferrugem (Veterana de Guerra)"}' WHERE `id` = '215';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Elite Build (Factory New)", "zh": "AK-47 | 精英之作 (崭新出厂)", "pt": "AK-47 | Construção de Elite (Nova de Fábrica)"}' WHERE `id` = '216';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ Music Kit | Beartooth, Aggressive", "zh": "音乐盒（StatTrak™） | Beartooth - 咄咄逼人", "pt": "Kit Musical StatTrak™ | Beartooth, Agressivo"}' WHERE `id` = '217';
UPDATE `skin` SET `i18n_field_name` = '{"en": "MP9 | Ruby Poison Dart (Minimal Wear)", "zh": "MP9 | 红宝石毒镖 (略有磨损)", "pt": "MP9 | Dardo Venenoso Rubi (Pouco Usada)"}' WHERE `id` = '218';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ FAMAS | Meow 36 (Factory New)", "zh": "法玛斯（StatTrak™） | 喵喵36 (崭新出厂)", "pt": "StatTrak™ FAMAS | Miau 36 (Nova de Fábrica)"}' WHERE `id` = '219';
UPDATE `skin` SET `i18n_field_name` = '{"en": "FAMAS | Meow 36 (Battle-Scarred)", "zh": "法玛斯 | 喵喵36 (战痕累累)", "pt": "FAMAS | Miau 36 (Veterana de Guerra)"}' WHERE `id` = '220';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Glock-18 | Oxide Blaze (Field-Tested)", "zh": "格洛克 18 型 | 锈蚀烈焰 (久经沙场)", "pt": "Glock-18 | Chama Oxidada (Testada em Campo)"}' WHERE `id` = '221';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Rezan The Ready | Sabre", "zh": "准备就绪的列赞 | 军刀", "pt": "Rezan O Pronto | Sabre"}' WHERE `id` = '222';
UPDATE `skin` SET `i18n_field_name` = '{"en": "G3SG1 | VariCamo (Minimal Wear)", "zh": "G3SG1 | 多变迷彩 (略有磨损)", "pt": "G3SG1 | VariCamo (Pouco Usada)"}' WHERE `id` = '223';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | Gen.G (Holo) | 2020 RMR", "zh": "印花 | Gen.G（全息）| 2020 RMR", "pt": "Adesivo | Gen.G (Holográfico) | 2020 RMR"}' WHERE `id` = '224';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | Natus Vincere | 2020 RMR", "zh": "印花 | Natus Vincere | 2020 RMR", "pt": "Adesivo | Natus Vincere | 2020 RMR"}' WHERE `id` = '225';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | ropz (Glitter, Champion) | Antwerp 2022", "zh": "印花 | ropz（闪耀，冠军） | 2022年安特卫普锦标赛", "pt": "Adesivo | ropz (Brilhante, Campeão) | Antuérpia 2022"}' WHERE `id` = '226';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | Renegades (Holo) | 2020 RMR", "zh": "印花 | Renegades（全息）| 2020 RMR", "pt": "Adesivo | Renegades (Holográfico) | 2020 RMR"}' WHERE `id` = '227';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ R8 Revolver | Crimson Web (Field-Tested)", "zh": "R8 左轮手枪（StatTrak™） | 深红之网 (久经沙场)", "pt": "StatTrak™ Revólver R8 | Teia Carmesim (Testada em Campo)"}' WHERE `id` = '228';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | rain (Glitter, Champion) | Antwerp 2022", "zh": "印花 | rain（闪耀，冠军）| 2022年安特卫普锦标赛", "pt": "Adesivo | rain (Brilhante, Campeão) | Antuérpia 2022"}' WHERE `id` = '229';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | TYLOO | 2020 RMR", "zh": "印花 | TYLOO | 2020 RMR", "pt": "Adesivo | TYLOO | 2020 RMR"}' WHERE `id` = '230';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sealed Graffiti | Heart (Blood Red)", "zh": "封装的涂鸦 | 心 (血红)", "pt": "Grafite Lacrado | Coração (Vermelho Sangue)"}' WHERE `id` = '231';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Glock-18 | Water Elemental (Factory New)", "zh": "格洛克 18 型 | 水灵 (崭新出厂)", "pt": "Glock-18 | Elemental de Água (Nova de Fábrica)"}' WHERE `id` = '232';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Moto Gloves | Cool Mint (Field-Tested)", "zh": "摩托手套（★） | 清凉薄荷 (久经沙场)", "pt": "★ Luvas de Motociclista | Menta Refrescante (Testada em Campo)"}' WHERE `id` = '233';
UPDATE `skin` SET `i18n_field_name` = '{"en": "USP-S | Monster Mashup (Minimal Wear)", "zh": "USP 消音版 | 小绿怪 (略有磨损)", "pt": "USP-S | Mistura de Monstros (Pouco Usada)"}' WHERE `id` = '234';
UPDATE `skin` SET `i18n_field_name` = '{"en": "P250 | See Ya Later (Field-Tested)", "zh": "P250 | 生化短吻鳄 (久经沙场)", "pt": "P250 | Até Logo (Testada em Campo)"}' WHERE `id` = '235';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ AK-47 | Nightwish (Field-Tested)", "zh": "AK-47（StatTrak™） | 夜愿 (久经沙场)", "pt": "StatTrak™ AK-47 | Desejo Noturno (Testada em Campo)"}' WHERE `id` = '236';
UPDATE `skin` SET `i18n_field_name` = '{"en": "P250 | Asiimov (Field-Tested)", "zh": "P250 | 二西莫夫 (久经沙场)", "pt": "P250 | Asiimov (Testada em Campo)"}' WHERE `id` = '237';
UPDATE `skin` SET `i18n_field_name` = '{"en": "USP-S | Flashback (Factory New)", "zh": "USP 消音版 | 闪回 (崭新出厂)", "pt": "USP-S | Flashback (Nova de Fábrica)"}' WHERE `id` = '238';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Negev | Power Loader (Minimal Wear)", "zh": "内格夫 | 动力装载机 (略有磨损)", "pt": "Negev | Carregador de Energia (Pouco Usada)"}' WHERE `id` = '239';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | sdy | Rio 2022", "zh": "印花 | sdy | 2022年里约热内卢锦标赛", "pt": "Adesivo | sdy | Rio 2022"}' WHERE `id` = '240';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Clutch Case", "zh": "命悬一线武器箱", "pt": "Caixa Clutch"}' WHERE `id` = '241';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Glock-18 | Moonrise (Factory New)", "zh": "格洛克 18 型 | 城里的月光 (崭新出厂)", "pt": "Glock-18 | Nascer da Lua (Nova de Fábrica)"}' WHERE `id` = '242';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | Bad News Eagles (Glitter) | Rio 2022", "zh": "印花 | Bad News Eagles（闪耀）| 2022年里约热内卢锦标赛", "pt": "Adesivo | Bad News Eagles (Brilhante) | Rio 2022"}' WHERE `id` = '243';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | Boom (Holo) | 2020 RMR", "zh": "印花 | Boom（全息）| 2020 RMR", "pt": "Adesivo | Boom (Holográfico) | 2020 RMR"}' WHERE `id` = '244';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | apEX (Glitter) | Antwerp 2022", "zh": "印花 | apEX（闪耀） | 2022年安特卫普锦标赛", "pt": "Adesivo | apEX (Brilhante) | Antuérpia 2022"}' WHERE `id` = '245';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | sh1ro (Glitter) | Rio 2022", "zh": "印花 | sh1ro（闪耀）| 2022年里约热内卢锦标赛", "pt": "Adesivo | sh1ro (Brilhante) | Rio 2022"}' WHERE `id` = '246';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Glock-18 | Vogue (Factory New)", "zh": "格洛克 18 型 | 摩登时代 (崭新出厂)", "pt": "Glock-18 | Vogue (Nova de Fábrica)"}' WHERE `id` = '247';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A4 | Evil Daimyo (Field-Tested)", "zh": "M4A4 | 杀意大名 (久经沙场)", "pt": "M4A4 | Daimyo Maligno (Testada em Campo)"}' WHERE `id` = '248';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ MAC-10 | Allure (Minimal Wear)", "zh": "MAC-10（StatTrak™） | 魅惑 (略有磨损)", "pt": "StatTrak™ MAC-10 | Fascínio (Pouco Usada)"}' WHERE `id` = '249';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Tec-9 | Groundwater (Field-Tested)", "zh": "Tec-9 | 地下水 (久经沙场)", "pt": "Tec-9 | Água Subterrânea (Testada em Campo)"}' WHERE `id` = '250';
UPDATE `skin` SET `i18n_field_name` = '{"en": "CZ75-Auto | Victoria (Field-Tested)", "zh": "CZ75 | 维多利亚 (久经沙场)", "pt": "CZ75-Auto | Victoria (Testada em Campo)"}' WHERE `id` = '251';
UPDATE `skin` SET `i18n_field_name` = '{"en": "P250 | Visions (Factory New)", "zh": "P250 | 迷人幻象 (崭新出厂)", "pt": "P250 | Visões (Nova de Fábrica)"}' WHERE `id` = '252';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Shadow Daggers | Tiger Tooth (Factory New)", "zh": "暗影双匕（★） | 虎牙 (崭新出厂)", "pt": "★ Adagas Sombrias | Dente de Tigre (Nova de Fábrica)"}' WHERE `id` = '253';
UPDATE `skin` SET `i18n_field_name` = '{"en": "USP-S | Printstream (Field-Tested)", "zh": "USP 消音版 | 印花集 (久经沙场)", "pt": "USP-S | Fluxo de Impressão (Testada em Campo)"}' WHERE `id` = '254';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Navaja Knife | Crimson Web (Minimal Wear)", "zh": "折刀（★） | 深红之网 (略有磨损)", "pt": "★ Faca Navaja | Teia Carmesim (Pouco Usada)"}' WHERE `id` = '255';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A1-S | Nitro (Minimal Wear)", "zh": "M4A1 消音型 | 氮化处理 (略有磨损)", "pt": "M4A1-S | Nitro (Pouco Usada)"}' WHERE `id` = '256';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | Spirit (Foil) | 2020 RMR", "zh": "印花 | Spirit（闪亮）| 2020 RMR", "pt": "Adesivo | Spirit (Metálico) | 2020 RMR"}' WHERE `id` = '257';
UPDATE `skin` SET `i18n_field_name` = '{"en": "MP9 | Airlock (Battle-Scarred)", "zh": "MP9 | 气密 (战痕累累)", "pt": "MP9 | Comporta de Ar (Veterana de Guerra)"}' WHERE `id` = '258';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Glock-18 | Gamma Doppler (Minimal Wear)", "zh": "格洛克 18 型 | 伽玛多普勒 (略有磨损)", "pt": "Glock-18 | Gamma Doppler (Pouco Usada)"}' WHERE `id` = '259';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Glock-18 | Night (Field-Tested)", "zh": "格洛克 18 型 | 噩梦之夜 (久经沙场)", "pt": "Glock-18 | Noite (Testada em Campo)"}' WHERE `id` = '260';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Hand Wraps | Cobalt Skulls (Battle-Scarred)", "zh": "裹手（★） | 钴蓝骷髅 (战痕累累)", "pt": "★ Faixas de Mão | Caveiras de Cobalto (Veterana de Guerra)"}' WHERE `id` = '261';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ Dual Berettas | Urban Shock (Field-Tested)", "zh": "双持贝瑞塔（StatTrak™） | 都市冲击 (久经沙场)", "pt": "StatTrak™ Dual Berettas | Choque Urbano (Testada em Campo)"}' WHERE `id` = '262';
UPDATE `skin` SET `i18n_field_name` = '{"en": "USP-S | Cortex (Minimal Wear)", "zh": "USP 消音版 | 脑洞大开 (略有磨损)", "pt": "USP-S | Córtex (Pouco Usada)"}' WHERE `id` = '263';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A4 | In Living Color (Factory New)", "zh": "M4A4 | 活色生香 (崭新出厂)", "pt": "M4A4 | Em Cores Vivas (Nova de Fábrica)"}' WHERE `id` = '264';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ XM1014 | Ziggy (Factory New)", "zh": "XM1014（StatTrak™） | 五彩斑驳 (崭新出厂)", "pt": "StatTrak™ XM1014 | Ziggy (Nova de Fábrica)"}' WHERE `id` = '265';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Desert Eagle | Cobalt Disruption (Factory New)", "zh": "沙漠之鹰 | 钴蓝禁锢 (崭新出厂)", "pt": "Desert Eagle | Perturbação de Cobalto (Nova de Fábrica)"}' WHERE `id` = '266';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | Neo-Noir (Field-Tested)", "zh": "AWP | 黑色魅影 (久经沙场)", "pt": "AWP | Neo-Noir (Testada em Campo)"}' WHERE `id` = '267';
UPDATE `skin` SET `i18n_field_name` = '{"en": "FAMAS | Valence (Minimal Wear)", "zh": "法玛斯 | 元素轮廓 (略有磨损)", "pt": "FAMAS | Valência (Pouco Usada)"}' WHERE `id` = '268';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Special Agent Ava | FBI", "zh": "爱娃特工 | 联邦调查局（FBI）", "pt": "Agente Especial Ava | FBI"}' WHERE `id` = '269';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | b1t | Rio 2022", "zh": "印花 | b1t | 2022年里约热内卢锦标赛", "pt": "Adesivo | b1t | Rio 2022"}' WHERE `id` = '270';
UPDATE `skin` SET `i18n_field_name` = '{"en": "R8 Revolver | Crimson Web (Field-Tested)", "zh": "R8 左轮手枪 | 深红之网 (久经沙场)", "pt": "Revólver R8 | Teia Carmesim (Testada em Campo)"}' WHERE `id` = '271';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Desert Eagle | Light Rail (Field-Tested)", "zh": "沙漠之鹰 | 轻轨 (久经沙场)", "pt": "Desert Eagle | Trilho Leve (Testada em Campo)"}' WHERE `id` = '272';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | FURIA (Gold) | Antwerp 2022", "zh": "印花 | FURIA（金色）| 2022年安特卫普锦标赛", "pt": "Adesivo | FURIA (Dourado) | Antuérpia 2022"}' WHERE `id` = '273';
UPDATE `skin` SET `i18n_field_name` = '{"en": "P90 | Neoqueen (Factory New)", "zh": "P90 | 元女王 (崭新出厂)", "pt": "P90 | Neorrainha (Nova de Fábrica)"}' WHERE `id` = '274';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | IHC Esports (Holo) | Rio 2022", "zh": "印花 | IHC Esports（全息）| 2022年里约热内卢锦标赛", "pt": "Adesivo | IHC Esports (Holográfico) | Rio 2022"}' WHERE `id` = '275';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | NAF (Glitter) | Rio 2022", "zh": "印花 | NAF（闪耀）| 2022年里约热内卢锦标赛", "pt": "Adesivo | NAF (Brilhante) | Rio 2022"}' WHERE `id` = '276';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | Corticera (Minimal Wear)", "zh": "AWP | 珊瑚树 (略有磨损)", "pt": "AWP | Corticeira (Pouco Usada)"}' WHERE `id` = '277';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Lt. Commander Ricksaw | NSWC SEAL", "zh": "海军上尉里克索尔 | 海军水面战中心海豹部队", "pt": "Tenente-Comandante Ricksaw | NSWC SEAL"}' WHERE `id` = '278';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | 00 Nation | Rio 2022", "zh": "印花 | 00 Nation | 2022年里约热内卢锦标赛", "pt": "Adesivo | 00 Nation | Rio 2022"}' WHERE `id` = '279';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ AWP | Atheris (Field-Tested)", "zh": "AWP（StatTrak™） | 树蝰 (久经沙场)", "pt": "StatTrak™ AWP | Atheris (Testada em Campo)"}' WHERE `id` = '280';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A1-S | Nitro (Field-Tested)", "zh": "M4A1 消音型 | 氮化处理 (久经沙场)", "pt": "M4A1-S | Nitro (Testada em Campo)"}' WHERE `id` = '281';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Elite Build (Field-Tested)", "zh": "AK-47 | 精英之作 (久经沙场)", "pt": "AK-47 | Construção de Elite (Testada em Campo)"}' WHERE `id` = '282';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AUG | Stymphalian (Field-Tested)", "zh": "AUG | 湖怪鸟 (久经沙场)", "pt": "AUG | Estinfálida (Testada em Campo)"}' WHERE `id` = '283';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Specialist Gloves | Emerald Web (Field-Tested)", "zh": "专业手套（★） | 翠绿之网 (久经沙场)", "pt": "★ Luvas Especialistas | Teia Esmeralda (Testada em Campo)"}' WHERE `id` = '284';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A4 | X-Ray (Minimal Wear)", "zh": "M4A4 | X 射线 (略有磨损)", "pt": "M4A4 | Raio-X (Pouco Usada)"}' WHERE `id` = '285';
UPDATE `skin` SET `i18n_field_name` = '{"en": "P250 | See Ya Later (Factory New)", "zh": "P250 | 生化短吻鳄 (崭新出厂)", "pt": "P250 | Até Logo (Nova de Fábrica)"}' WHERE `id` = '286';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A4 | Evil Daimyo (Factory New)", "zh": "M4A4 | 杀意大名 (崭新出厂)", "pt": "M4A4 | Daimyo Maligno (Nova de Fábrica)"}' WHERE `id` = '287';
UPDATE `skin` SET `i18n_field_name` = '{"en": "P90 | Asiimov (Minimal Wear)", "zh": "P90 | 二西莫夫 (略有磨损)", "pt": "P90 | Asiimov (Pouco Usada)"}' WHERE `id` = '288';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A1-S | Cyrex (Factory New)", "zh": "M4A1 消音型 | 次时代 (崭新出厂)", "pt": "M4A1-S | Cyrex (Nova de Fábrica)"}' WHERE `id` = '289';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A1-S | Player Two (Field-Tested)", "zh": "M4A1 消音型 | 二号玩家 (久经沙场)", "pt": "M4A1-S | Jogador Dois (Testada em Campo)"}' WHERE `id` = '290';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Glock-18 | Franklin (Factory New)", "zh": "格洛克 18 型 | 富兰克林 (崭新出厂)", "pt": "Glock-18 | Franklin (Nova de Fábrica)"}' WHERE `id` = '291';
UPDATE `skin` SET `i18n_field_name` = '{"en": "USP-S | Neo-Noir (Field-Tested)", "zh": "USP 消音版 | 黑色魅影 (久经沙场)", "pt": "USP-S | Neo-Noir (Testada em Campo)"}' WHERE `id` = '292';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Sport Gloves | Amphibious (Field-Tested)", "zh": "运动手套（★） | 双栖 (久经沙场)", "pt": "★ Luvas Esportivas | Anfíbio (Testada em Campo)"}' WHERE `id` = '293';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Point Disarray (Factory New)", "zh": "AK-47 | 混沌点阵 (崭新出厂)", "pt": "AK-47 | Desordem Pontual (Nova de Fábrica)"}' WHERE `id` = '294';
UPDATE `skin` SET `i18n_field_name` = '{"en": "PP-Bizon | Fuel Rod (Factory New)", "zh": "PP-野牛 | 核燃料棒 (崭新出厂)", "pt": "PP-Bizon | Barra de Combustível (Nova de Fábrica)"}' WHERE `id` = '295';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Specialist Gloves | Mogul (Field-Tested)", "zh": "专业手套（★） | 大腕 (久经沙场)", "pt": "★ Luvas Especialistas | Magnata (Testada em Campo)"}' WHERE `id` = '296';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Music Kit | Denzel Curry, ULTIMATE", "zh": "音乐盒 | Denzel Curry - 终极", "pt": "Kit Musical | Denzel Curry, ULTIMATE"}' WHERE `id` = '297';
UPDATE `skin` SET `i18n_field_name` = '{"en": "SCAR-20 | Bloodsport (Minimal Wear)", "zh": "SCAR-20 | 血腥运动 (略有磨损)", "pt": "SCAR-20 | Esporte Sangrento (Pouco Usada)"}' WHERE `id` = '298';
UPDATE `skin` SET `i18n_field_name` = '{"en": "MP7 | Bloodsport (Minimal Wear)", "zh": "MP7 | 血腥运动 (略有磨损)", "pt": "MP7 | Esporte Sangrento (Pouco Usada)"}' WHERE `id` = '299';
UPDATE `skin` SET `i18n_field_name` = '{"en": "MAC-10 | Candy Apple (Factory New)", "zh": "MAC-10 | 红苹果 (崭新出厂)", "pt": "MAC-10 | Maçã do Amor (Nova de Fábrica)"}' WHERE `id` = '300';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ Swap Tool", "zh": "StatTrak™ 数据互换器", "pt": "Ferramenta de Troca StatTrak™"}' WHERE `id` = '301';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Elite Build (Minimal Wear)", "zh": "AK-47 | 精英之作 (略有磨损)", "pt": "AK-47 | Construção de Elite (Pouco Usada)"}' WHERE `id` = '302';
UPDATE `skin` SET `i18n_field_name` = '{"en": "SG 553 | Aerial (Factory New)", "zh": "SG 553 | 轻空 (崭新出厂)", "pt": "SG 553 | Aéreo (Nova de Fábrica)"}' WHERE `id` = '303';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | Safari Mesh (Battle-Scarred)", "zh": "AWP | 狩猎网格 (战痕累累)", "pt": "AWP | Malha Safari (Veterana de Guerra)"}' WHERE `id` = '304';
UPDATE `skin` SET `i18n_field_name` = '{"en": "SG 553 | Waves Perforated (Minimal Wear)", "zh": "SG 553 | 浪花穿孔 (略有磨损)", "pt": "SG 553 | Ondas Perfuradas (Pouco Usada)"}' WHERE `id` = '305';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Specialist Gloves | Foundation (Field-Tested)", "zh": "专业手套（★） | 元勋 (久经沙场)", "pt": "★ Luvas Especialistas | Fundação (Testada em Campo)"}' WHERE `id` = '306';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ AK-47 | Elite Build (Field-Tested)", "zh": "AK-47（StatTrak™） | 精英之作 (久经沙场)", "pt": "StatTrak™ AK-47 | Construção de Elite (Testada em Campo)"}' WHERE `id` = '307';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A1-S | Mecha Industries (Field-Tested)", "zh": "M4A1 消音型 | 机械工业 (久经沙场)", "pt": "M4A1-S | Indústrias Mecha (Testada em Campo)"}' WHERE `id` = '308';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Souvenir SCAR-20 | Storm (Factory New)", "zh": "SCAR-20（纪念品） | 暴风呼啸 (崭新出厂)", "pt": "Souvenir SCAR-20 | Tempestade (Nova de Fábrica)"}' WHERE `id` = '309';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Prisma 2 Case", "zh": "棱彩2号武器箱", "pt": "Caixa Prisma 2"}' WHERE `id` = '310';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Driver Gloves | Overtake (Minimal Wear)", "zh": "驾驶手套（★） | 超越 (略有磨损)", "pt": "★ Luvas de Motorista | Ultrapassagem (Pouco Usada)"}' WHERE `id` = '311';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Gut Knife | Lore (Minimal Wear)", "zh": "穿肠刀（★） | 传说 (略有磨损)", "pt": "★ Faca Gut | Tradição (Pouco Usada)"}' WHERE `id` = '312';
UPDATE `skin` SET `i18n_field_name` = '{"en": "USP-S | Whiteout (Field-Tested)", "zh": "USP 消音版 | 银装素裹 (久经沙场)", "pt": "USP-S | Branco Total (Testada em Campo)"}' WHERE `id` = '313';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Head Shot (Minimal Wear)", "zh": "AK-47 | 一发入魂 (略有磨损)", "pt": "AK-47 | Tiro na Cabeça (Pouco Usada)"}' WHERE `id` = '314';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Falchion Knife | Ultraviolet (Field-Tested)", "zh": "弯刀（★） | 致命紫罗兰 (久经沙场)", "pt": "★ Faca Falchion | Ultravioleta (Testada em Campo)"}' WHERE `id` = '315';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ Nova | Clear Polymer (Minimal Wear)", "zh": "新星（StatTrak™） | 一见青心 (略有磨损)", "pt": "StatTrak™ Nova | Polímero Transparente (Pouco Usada)"}' WHERE `id` = '316';
UPDATE `skin` SET `i18n_field_name` = '{"en": "SSG 08 | Ghost Crusader (Field-Tested)", "zh": "SSG 08 | 幽灵战士 (久经沙场)", "pt": "SSG 08 | Cruzado Fantasma (Testada em Campo)"}' WHERE `id` = '317';
UPDATE `skin` SET `i18n_field_name` = '{"en": "P250 | Nevermore (Factory New)", "zh": "P250 | 影魔 (崭新出厂)", "pt": "P250 | Nunca Mais (Nova de Fábrica)"}' WHERE `id` = '318';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | POP AWP (Field-Tested)", "zh": "AWP | 复古流行 (久经沙场)", "pt": "AWP | POP AWP (Testada em Campo)"}' WHERE `id` = '319';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A1-S | Flashback (Field-Tested)", "zh": "M4A1 消音型 | 闪回 (久经沙场)", "pt": "M4A1-S | Flashback (Testada em Campo)"}' WHERE `id` = '320';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | Phobos (Minimal Wear)", "zh": "AWP | 火卫一 (略有磨损)", "pt": "AWP | Phobos (Pouco Usada)"}' WHERE `id` = '321';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ Music Kit | Twin Atlantic, GLA", "zh": "音乐盒（StatTrak™） | Twin Atlantic — GLA", "pt": "StatTrak™ Kit Musical | Twin Atlantic, GLA"}' WHERE `id` = '322';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A1-S | Night Terror (Field-Tested)", "zh": "M4A1 消音型 | 夜无眠 (久经沙场)", "pt": "M4A1-S | Terror Noturno (Testada em Campo)"}' WHERE `id` = '323';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Glock-18 | Candy Apple (Minimal Wear)", "zh": "格洛克 18 型 | 红苹果 (略有磨损)", "pt": "Glock-18 | Maçã do Amor (Pouco Usada)"}' WHERE `id` = '324';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Butterfly Knife | Scorched (Minimal Wear)", "zh": "蝴蝶刀（★） | 枯焦之色 (略有磨损)", "pt": "★ Faca Borboleta | Chamuscada (Pouco Usada)"}' WHERE `id` = '325';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Tec-9 | Fuel Injector (Field-Tested)", "zh": "Tec-9 | 燃料喷射器 (久经沙场)", "pt": "Tec-9 | Injetor de Combustível (Testada em Campo)"}' WHERE `id` = '326';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sir Bloody Loudmouth Darryl | The Professionals", "zh": "残酷的达里尔爵士（聒噪）| 专业人士", "pt": "Sir Bloody Loudmouth Darryl | Os Profissionais"}' WHERE `id` = '327';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Chroma 3 Case", "zh": "幻彩 3 号武器箱", "pt": "Caixa Cromática 3"}' WHERE `id` = '328';
UPDATE `skin` SET `i18n_field_name` = '{"en": "PP-Bizon | Space Cat (Field-Tested)", "zh": "PP-野牛 | 太空猫 (久经沙场)", "pt": "PP-Bizon | Gato Espacial (Testada em Campo)"}' WHERE `id` = '329';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Desert Eagle | Kumicho Dragon (Field-Tested)", "zh": "沙漠之鹰 | 大佬龙 (久经沙场)", "pt": "Desert Eagle | Dragão Kumicho (Testada em Campo)"}' WHERE `id` = '330';
UPDATE `skin` SET `i18n_field_name` = '{"en": "SSG 08 | Fever Dream (Minimal Wear)", "zh": "SSG 08 | 浮生如梦 (略有磨损)", "pt": "SSG 08 | Sonho Febril (Pouco Usada)"}' WHERE `id` = '331';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Slate (Factory New)", "zh": "AK-47 | 墨岩 (崭新出厂)", "pt": "AK-47 | Ardósia (Nova de Fábrica)"}' WHERE `id` = '332';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A1-S | Nightmare (Minimal Wear)", "zh": "M4A1 消音型 | 梦魇 (略有磨损)", "pt": "M4A1-S | Pesadelo (Pouco Usada)"}' WHERE `id` = '333';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A1-S | Player Two (Minimal Wear)", "zh": "M4A1 消音型 | 二号玩家 (略有磨损)", "pt": "M4A1-S | Jogador Dois (Pouco Usada)"}' WHERE `id` = '334';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A4 | Global Offensive (Factory New)", "zh": "M4A4 | 全球攻势 (崭新出厂)", "pt": "M4A4 | Ofensiva Global (Nova de Fábrica)"}' WHERE `id` = '335';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Nightwish (Factory New)", "zh": "AK-47 | 夜愿 (崭新出厂)", "pt": "AK-47 | Desejo Noturno (Nova de Fábrica)"}' WHERE `id` = '336';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A4 | The Emperor (Factory New)", "zh": "M4A4 | 皇帝 (崭新出厂)", "pt": "M4A4 | O Imperador (Nova de Fábrica)"}' WHERE `id` = '337';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A4 | Hellfire (Minimal Wear)", "zh": "M4A4 | 地狱烈焰 (略有磨损)", "pt": "M4A4 | Fogo Infernal (Pouco Usada)"}' WHERE `id` = '338';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | POP AWP (Factory New)", "zh": "AWP | 复古流行 (崭新出厂)", "pt": "AWP | POP AWP (Nova de Fábrica)"}' WHERE `id` = '339';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Ursus Knife | Case Hardened (Minimal Wear)", "zh": "熊刀（★） | 表面淬火 (略有磨损)", "pt": "★ Faca Ursus | Revestimento Endurecido (Pouco Usada)"}' WHERE `id` = '340';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Moto Gloves | Polygon (Factory New)", "zh": "摩托手套（★） | 多边形 (崭新出厂)", "pt": "★ Luvas de Motociclista | Polígono (Nova de Fábrica)"}' WHERE `id` = '341';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Classic Knife | Boreal Forest (Field-Tested)", "zh": "海豹短刀（★） | 北方森林 (久经沙场)", "pt": "★ Faca Clássica | Floresta Boreal (Testada em Campo)"}' WHERE `id` = '342';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Hand Wraps | Leather (Field-Tested)", "zh": "裹手（★） | 皮革 (久经沙场)", "pt": "★ Faixas de Mão | Couro (Testada em Campo)"}' WHERE `id` = '343';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Rio 2022 Contenders Sticker Capsule", "zh": "2022年里约热内卢锦标赛竞争组印花胶囊", "pt": "Cápsula de Adesivos Competidores Rio 2022"}' WHERE `id` = '344';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ Desert Eagle | Printstream (Field-Tested)", "zh": "沙漠之鹰（StatTrak™） | 印花集 (久经沙场)", "pt": "StatTrak™ Desert Eagle | Fluxo de Impressão (Testada em Campo)"}' WHERE `id` = '345';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Operation Broken Fang Case", "zh": ""狂牙大行动"武器箱", "pt": "Caixa Operação Presa Quebrada"}' WHERE `id` = '346';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | Asiimov (Field-Tested)", "zh": "AWP | 二西莫夫 (久经沙场)", "pt": "AWP | Asiimov (Testada em Campo)"}' WHERE `id` = '347';
UPDATE `skin` SET `i18n_field_name` = '{"en": "P90 | Elite Build (Factory New)", "zh": "P90 | 精英之作 (崭新出厂)", "pt": "P90 | Construção de Elite (Nova de Fábrica)"}' WHERE `id` = '348';
UPDATE `skin` SET `i18n_field_name` = '{"en": "USP-S | Cortex (Field-Tested)", "zh": "USP 消音版 | 脑洞大开 (久经沙场)", "pt": "USP-S | Córtex (Testada em Campo)"}' WHERE `id` = '349';
UPDATE `skin` SET `i18n_field_name` = '{"en": "P90 | Death by Kitty (Minimal Wear)", "zh": "P90 | 喵之萌杀 (略有磨损)", "pt": "P90 | Morte por Gatinho (Pouco Usada)"}' WHERE `id` = '350';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AUG | Chameleon (Factory New)", "zh": "AUG | 变色龙 (崭新出厂)", "pt": "AUG | Camaleão (Nova de Fábrica)"}' WHERE `id` = '351';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ AWP | Asiimov (Well-Worn)", "zh": "AWP（StatTrak™） | 二西莫夫 (破损不堪)", "pt": "StatTrak™ AWP | Asiimov (Bem Desgastada)"}' WHERE `id` = '352';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Dual Berettas | Cobalt Quartz (Minimal Wear)", "zh": "双持贝瑞塔 | 钴蓝石英 (略有磨损)", "pt": "Dual Berettas | Quartzo Cobalto (Pouco Usada)"}' WHERE `id` = '353';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Point Disarray (Field-Tested)", "zh": "AK-47 | 混沌点阵 (久经沙场)", "pt": "AK-47 | Desordem Pontual (Testada em Campo)"}' WHERE `id` = '354';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Glock-18 | Gamma Doppler (Factory New)", "zh": "格洛克 18 型 | 伽玛多普勒 (崭新出厂)", "pt": "Glock-18 | Gamma Doppler (Nova de Fábrica)"}' WHERE `id` = '355';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Danger Zone Case", "zh": ""头号特训"武器箱", "pt": "Caixa Zona de Perigo"}' WHERE `id` = '356';
UPDATE `skin` SET `i18n_field_name` = '{"en": "MP9 | Mount Fuji (Minimal Wear)", "zh": "MP9 | 富士山 (略有磨损)", "pt": "MP9 | Monte Fuji (Pouco Usada)"}' WHERE `id` = '357';
UPDATE `skin` SET `i18n_field_name` = '{"en": "SG 553 | Aloha (Field-Tested)", "zh": "SG 553 | 阿罗哈 (久经沙场)", "pt": "SG 553 | Aloha (Testada em Campo)"}' WHERE `id` = '358';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Phantom Disruptor (Field-Tested)", "zh": "AK-47 | 幻影破坏者 (久经沙场)", "pt": "AK-47 | Disruptor Fantasma (Testada em Campo)"}' WHERE `id` = '359';
UPDATE `skin` SET `i18n_field_name` = '{"en": "PP-Bizon | Cobalt Halftone (Minimal Wear)", "zh": "PP-野牛 | 钻蓝半调 (略有磨损)", "pt": "PP-Bizon | Meio-tom Cobalto (Pouco Usada)"}' WHERE `id` = '360';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Bloody Darryl The Strapped | The Professionals", "zh": "残酷的达里尔（穷鬼）| 专业人士", "pt": "Bloody Darryl The Strapped | Os Profissionais"}' WHERE `id` = '361';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Galil AR | Sandstorm (Field-Tested)", "zh": "加利尔 AR | 沙尘暴 (久经沙场)", "pt": "Galil AR | Tempestade de Areia (Testada em Campo)"}' WHERE `id` = '362';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Neon Revolution (Factory New)", "zh": "AK-47 | 霓虹革命 (崭新出厂)", "pt": "AK-47 | Revolução Neon (Nova de Fábrica)"}' WHERE `id` = '363';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Driver Gloves | Queen Jaguar (Minimal Wear)", "zh": "驾驶手套（★） | 美洲豹女王 (略有磨损)", "pt": "★ Luvas de Motorista | Rainha Jaguar (Pouco Usada)"}' WHERE `id` = '364';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Butterfly Knife | Ultraviolet (Battle-Scarred)", "zh": "蝴蝶刀（★） | 致命紫罗兰 (战痕累累)", "pt": "★ Faca Borboleta | Ultravioleta (Veterana de Guerra)"}' WHERE `id` = '365';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Souvenir PP-Bizon | Anolis (Battle-Scarred)", "zh": "PP-野牛（纪念品） | 安乐蜥 (战痕累累)", "pt": "Souvenir PP-Bizon | Anolis (Veterana de Guerra)"}' WHERE `id` = '366';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Souvenir SSG 08 | Jungle Dashed (Well-Worn)", "zh": "SSG 08（纪念品） | 丛林虚线 (破损不堪)", "pt": "Souvenir SSG 08 | Traço da Selva (Bem Desgastada)"}' WHERE `id` = '367';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | Man-o\'-war (Field-Tested)", "zh": "AWP | 无畏战神 (久经沙场)", "pt": "AWP | Caravela (Testada em Campo)"}' WHERE `id` = '368';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | PAW (Factory New)", "zh": "AWP | 猫猫狗狗 (崭新出厂)", "pt": "AWP | PAW (Nova de Fábrica)"}' WHERE `id` = '369';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A4 | Desolate Space (Minimal Wear)", "zh": "M4A4 | 死寂空间 (略有磨损)", "pt": "M4A4 | Espaço Desolado (Pouco Usada)"}' WHERE `id` = '370';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Desert Eagle | Mecha Industries (Field-Tested)", "zh": "沙漠之鹰 | 机械工业 (久经沙场)", "pt": "Desert Eagle | Indústrias Mecha (Testada em Campo)"}' WHERE `id` = '371';
UPDATE `skin` SET `i18n_field_name` = '{"en": "MP9 | Starlight Protector (Field-Tested)", "zh": "MP9 | 星使 (久经沙场)", "pt": "MP9 | Protetor Estelar (Testada em Campo)"}' WHERE `id` = '372';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Phantom Disruptor (Factory New)", "zh": "AK-47 | 幻影破坏者 (崭新出厂)", "pt": "AK-47 | Disruptor Fantasma (Nova de Fábrica)"}' WHERE `id` = '373';
UPDATE `skin` SET `i18n_field_name` = '{"en": "USP-S | Cortex (Factory New)", "zh": "USP 消音版 | 脑洞大开 (崭新出厂)", "pt": "USP-S | Córtex (Nova de Fábrica)"}' WHERE `id` = '374';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ M4A4 | Magnesium (Factory New)", "zh": "M4A4（StatTrak™） | 镁元素 (崭新出厂)", "pt": "StatTrak™ M4A4 | Magnésio (Nova de Fábrica)"}' WHERE `id` = '375';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ PP-Bizon | Photic Zone (Factory New)", "zh": "PP-野牛（StatTrak™） | 透光区 (崭新出厂)", "pt": "StatTrak™ PP-Bizon | Zona Fótica (Nova de Fábrica)"}' WHERE `id` = '376';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Driver Gloves | Racing Green (Field-Tested)", "zh": "驾驶手套（★） | 墨绿色调 (久经沙场)", "pt": "★ Luvas de Motorista | Verde Corrida (Testada em Campo)"}' WHERE `id` = '377';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ MAG-7 | Monster Call (Minimal Wear)", "zh": "MAG-7（StatTrak™） | 北冥有鱼 (略有磨损)", "pt": "StatTrak™ MAG-7 | Chamado do Monstro (Pouco Usada)"}' WHERE `id` = '378';
UPDATE `skin` SET `i18n_field_name` = '{"en": "CZ75-Auto | Silver (Factory New)", "zh": "CZ75 | 银质 (崭新出厂)", "pt": "CZ75-Auto | Prata (Nova de Fábrica)"}' WHERE `id` = '379';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | Atheris (Factory New)", "zh": "AWP | 树蝰 (崭新出厂)", "pt": "AWP | Atheris (Nova de Fábrica)"}' WHERE `id` = '380';
UPDATE `skin` SET `i18n_field_name` = '{"en": "John \'Van Healen\' Kask | SWAT", "zh": "约翰 "范·海伦" 卡斯克 | 特警", "pt": "John \'Van Healen\' Kask | SWAT"}' WHERE `id` = '381';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Glock-18 | Vogue (Minimal Wear)", "zh": "格洛克 18 型 | 摩登时代 (略有磨损)", "pt": "Glock-18 | Vogue (Pouco Usada)"}' WHERE `id` = '382';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ Music Kit | AWOLNATION, I Am", "zh": "音乐盒（StatTrak™） | AWOLNATION - 就是我", "pt": "StatTrak™ Kit Musical | AWOLNATION, Eu Sou"}' WHERE `id` = '383';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | Hyper Beast (Minimal Wear)", "zh": "AWP | 暴怒野兽 (略有磨损)", "pt": "AWP | Fera Hiper (Pouco Usada)"}' WHERE `id` = '384';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | Pit Viper (Minimal Wear)", "zh": "AWP | 响尾蛇 (略有磨损)", "pt": "AWP | Víbora do Poço (Pouco Usada)"}' WHERE `id` = '385';
UPDATE `skin` SET `i18n_field_name` = '{"en": "MAG-7 | Petroglyph (Minimal Wear)", "zh": "MAG-7 | 石雕 (略有磨损)", "pt": "MAG-7 | Petroglifo (Pouco Usada)"}' WHERE `id` = '386';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Sticker | Global Elite", "zh": "印花 | 全球精英", "pt": "Adesivo | Elite Global"}' WHERE `id` = '387';
UPDATE `skin` SET `i18n_field_name` = '{"en": "SSG 08 | Turbo Peek (Field-Tested)", "zh": "SSG 08 | 速度激情 (久经沙场)", "pt": "SSG 08 | Espiada Turbo (Testada em Campo)"}' WHERE `id` = '388';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A1-S | VariCamo (Factory New)", "zh": "M4A1 消音型 | 多变迷彩 (崭新出厂)", "pt": "M4A1-S | VariCamo (Nova de Fábrica)"}' WHERE `id` = '389';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Glock-18 | Water Elemental (Minimal Wear)", "zh": "格洛克 18 型 | 水灵 (略有磨损)", "pt": "Glock-18 | Elemental da Água (Pouco Usada)"}' WHERE `id` = '390';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Falchion Knife | Gamma Doppler (Factory New)", "zh": "弯刀（★） | 伽玛多普勒 (崭新出厂)", "pt": "★ Faca Falchion | Gamma Doppler (Nova de Fábrica)"}' WHERE `id` = '391';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ StatTrak™ Karambit | Tiger Tooth (Factory New)", "zh": "爪子刀（★ StatTrak™） | 虎牙 (崭新出厂)", "pt": "★ StatTrak™ Karambit | Dente de Tigre (Nova de Fábrica)"}' WHERE `id` = '392';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ MAG-7 | BI83 Spectrum (Factory New)", "zh": "MAG-7（StatTrak™） | 铋晶体 (崭新出厂)", "pt": "StatTrak™ MAG-7 | Espectro BI83 (Nova de Fábrica)"}' WHERE `id` = '393';
UPDATE `skin` SET `i18n_field_name` = '{"en": "StatTrak™ MP9 | Mount Fuji (Factory New)", "zh": "MP9（StatTrak™） | 富士山 (崭新出厂)", "pt": "StatTrak™ MP9 | Monte Fuji (Nova de Fábrica)"}' WHERE `id` = '394';
UPDATE `skin` SET `i18n_field_name` = '{"en": "M4A1-S | Decimator (Factory New)", "zh": "M4A1 消音型 | 毁灭者 2000 (崭新出厂)", "pt": "M4A1-S | Dizimador (Nova de Fábrica)"}' WHERE `id` = '395';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Falchion Knife | Rust Coat (Battle-Scarred)", "zh": "弯刀（★） | 外表生锈 (战痕累累)", "pt": "★ Faca Falchion | Revestimento de Ferrugem (Veterana de Guerra)"}' WHERE `id` = '396';
UPDATE `skin` SET `i18n_field_name` = '{"en": "★ Stiletto Knife | Ultraviolet (Field-Tested)", "zh": "短剑（★） | 致命紫罗兰 (久经沙场)", "pt": "★ Faca Stiletto | Ultravioleta (Testada em Campo)"}' WHERE `id` = '397';
UPDATE `skin` SET `i18n_field_name` = '{"en": "Tec-9 | Cracked Opal (Factory New)", "zh": "Tec-9 | 碎蛋白石 (崭新出厂)", "pt": "Tec-9 | Opala Rachada (Nova de Fábrica)"}' WHERE `id` = '398';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AK-47 | Point Disarray (Minimal Wear)", "zh": "AK-47 | 混沌点阵 (略有磨损)", "pt": "AK-47 | Desordem Pontual (Pouco Usada)"}' WHERE `id` = '399';
UPDATE `skin` SET `i18n_field_name` = '{"en": "AWP | Fever Dream (Minimal Wear)", "zh": "AWP | 浮生如梦 (略有磨损)", "pt": "AWP | Sonho Febril (Pouco Usada)"}' WHERE `id` = '400';