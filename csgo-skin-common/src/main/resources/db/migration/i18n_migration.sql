-- 国际化字段迁移SQL脚本
-- 将现有的多语言字段迁移到JSON格式

-- 1. 为user_log表添加新的JSON字段
ALTER TABLE user_log 
ADD COLUMN action_name JSON COMMENT '操作名称（多语言）',
ADD COLUMN description JSON COMMENT '描述（多语言）',
ADD COLUMN remark JSON COMMENT '备注（多语言）',
ADD COLUMN user_id BIGINT COMMENT '用户ID',
ADD COLUMN ip_address VARCHAR(45) COMMENT 'IP地址',
ADD COLUMN user_agent TEXT COMMENT '用户代理';

-- 2. 如果存在旧的多语言字段，可以使用以下脚本进行数据迁移
-- 注意：这里假设原来有action_name_zh, action_name_en等字段

-- 示例迁移脚本（根据实际字段调整）
/*
UPDATE user_log 
SET action_name = JSON_OBJECT(
    'zh', COALESCE(action_name_zh, ''),
    'en', COALESCE(action_name_en, '')
),
description = JSON_OBJECT(
    'zh', COALESCE(description_zh, ''),
    'en', COALESCE(description_en, '')
),
remark = JSON_OBJECT(
    'zh', COALESCE(remark_zh, ''),
    'en', COALESCE(remark_en, '')
)
WHERE action_name IS NULL;
*/

-- 3. 为其他需要国际化的表添加类似字段
-- 例如：category表
/*
ALTER TABLE category 
ADD COLUMN name JSON COMMENT '名称（多语言）',
ADD COLUMN description JSON COMMENT '描述（多语言）';

-- 迁移category数据
UPDATE category 
SET name = JSON_OBJECT(
    'zh', COALESCE(name_zh, name, ''),
    'en', COALESCE(name_en, name, '')
),
description = JSON_OBJECT(
    'zh', COALESCE(description_zh, description, ''),
    'en', COALESCE(description_en, description, '')
)
WHERE name IS NULL;
*/

-- 4. 创建索引以提高查询性能
-- CREATE INDEX idx_user_log_user_id ON user_log(user_id);
-- CREATE INDEX idx_user_log_create_time ON user_log(create_time);

-- 5. 验证数据迁移
-- SELECT id, action_name, description, remark FROM user_log LIMIT 10;

-- 6. 清理旧字段（在确认迁移成功后执行）
-- ALTER TABLE user_log DROP COLUMN action_name_zh;
-- ALTER TABLE user_log DROP COLUMN action_name_en;
-- ALTER TABLE user_log DROP COLUMN description_zh;
-- ALTER TABLE user_log DROP COLUMN description_en;
-- ALTER TABLE user_log DROP COLUMN remark_zh;
-- ALTER TABLE user_log DROP COLUMN remark_en;
