-- 创建国际化演示表
CREATE TABLE `i18n_demo` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `text` json DEFAULT NULL COMMENT '文本内容（多语言）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='国际化演示表';

-- 插入示例数据
INSERT INTO `i18n_demo` (`text`) VALUES 
('{"zh": "欢迎使用系统", "en": "Welcome to the system"}'),
('{"zh": "用户登录成功", "en": "User login successful"}'),
('{"zh": "数据保存完成", "en": "Data saved successfully"}'),
('{"zh": "操作执行失败", "en": "Operation failed"}'),
('{"zh": "权限验证通过", "en": "Permission verified"}'),
('{"zh": "文件上传完成", "en": "File upload completed"}'),
('{"zh": "邮件发送成功", "en": "Email sent successfully"}'),
('{"zh": "密码修改完成", "en": "Password changed"}'),
('{"zh": "账户已激活", "en": "Account activated"}'),
('{"zh": "订单处理中", "en": "Order processing"}'),
('{"zh": "支付成功", "en": "Payment successful"}'),
('{"zh": "商品已发货", "en": "Product shipped"}'),
('{"zh": "评价已提交", "en": "Review submitted"}'),
('{"zh": "退款申请", "en": "Refund request"}'),
('{"zh": "客服咨询", "en": "Customer service inquiry"}');

-- 创建索引以提高JSON字段搜索性能
-- MySQL 5.7+ 支持JSON字段的虚拟列索引
ALTER TABLE `i18n_demo` 
ADD COLUMN `text_zh` VARCHAR(500) GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(`text`, '$.zh'))) VIRTUAL,
ADD COLUMN `text_en` VARCHAR(500) GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(`text`, '$.en'))) VIRTUAL;

-- 为虚拟列创建索引
CREATE INDEX `idx_text_zh` ON `i18n_demo` (`text_zh`);
CREATE INDEX `idx_text_en` ON `i18n_demo` (`text_en`);

-- 创建全文索引（如果需要全文搜索）
-- ALTER TABLE `i18n_demo` ADD FULLTEXT(`text_zh`, `text_en`);
