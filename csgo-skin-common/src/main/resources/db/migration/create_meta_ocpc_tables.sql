-- 创建Meta OCPC相关表

-- 1. 创建Meta OCPC Token表
CREATE TABLE `ocpc_meta_token` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `access_token` varchar(512) DEFAULT NULL COMMENT 'Facebook访问令牌',
  `pixel_id` varchar(64) DEFAULT NULL COMMENT 'Facebook像素ID',
  `relm_name` varchar(256) DEFAULT NULL COMMENT '域名',
  `app_id` varchar(64) DEFAULT NULL COMMENT 'Facebook应用ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_relm_name` (`relm_name`),
  KEY `idx_pixel_id` (`pixel_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Meta OCPC Token信息';

-- 2. 创建Meta OCPC数据表
CREATE TABLE `ocpc_meta_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `ocpc_channel_id` bigint(20) DEFAULT NULL COMMENT '渠道ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `order_charge_id` bigint(20) DEFAULT NULL COMMENT '订单ID',
  `type` varchar(20) DEFAULT NULL COMMENT '类型：CLICK-点击，REGISTER-注册，CHARGE-充值',
  `event_id` varchar(64) DEFAULT NULL COMMENT '事件ID',
  `campaign_id` varchar(32) DEFAULT NULL COMMENT '广告系列ID',
  `adset_id` varchar(32) DEFAULT NULL COMMENT '广告组ID',
  `ad_id` varchar(32) DEFAULT NULL COMMENT '广告ID',
  `click_id` varchar(128) DEFAULT NULL COMMENT 'Facebook点击ID (fbclid)',
  `event_value` decimal(10,2) DEFAULT NULL COMMENT '事件价值',
  `ip` varchar(64) DEFAULT NULL COMMENT '用户IP',
  `user_agent` varchar(512) DEFAULT NULL COMMENT '用户代理',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_channel_id` (`ocpc_channel_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_charge_id` (`order_charge_id`),
  KEY `idx_type` (`type`),
  KEY `idx_click_id` (`click_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_event_id` (`event_id`),
  CONSTRAINT `fk_meta_data_channel` FOREIGN KEY (`ocpc_channel_id`) REFERENCES `ocpc_channel` (`id`),
  CONSTRAINT `fk_meta_data_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_meta_data_order` FOREIGN KEY (`order_charge_id`) REFERENCES `order_charge` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Meta OCPC数据';

-- 3. 创建OCPC渠道表（如果不存在）
CREATE TABLE IF NOT EXISTS `ocpc_channel` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(128) DEFAULT NULL COMMENT '渠道名称',
  `relm_name` varchar(32) DEFAULT NULL COMMENT '渠道域名',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_relm_name` (`relm_name`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OCPC渠道信息';

-- 4. 插入示例数据（可选）
-- Meta OCPC Token示例数据
INSERT INTO `ocpc_meta_token` (`access_token`, `pixel_id`, `relm_name`, `app_id`) 
VALUES 
('EAABwzLixnjYBO...', '123456789012345', 'example.com', '123456789012345'),
('EAABwzLixnjYBO...', '234567890123456', 'test.com', '234567890123456');

-- OCPC渠道示例数据
INSERT INTO `ocpc_channel` (`name`, `relm_name`) 
VALUES 
('Meta Facebook渠道', 'example.com'),
('Meta Instagram渠道', 'test.com'),
('百度SEM渠道', 'baidu.example.com'),
('Google Ads渠道', 'google.example.com');

-- 5. 创建索引以提高查询性能
-- 复合索引用于常见查询
CREATE INDEX `idx_meta_data_user_type` ON `ocpc_meta_data` (`user_id`, `type`);
CREATE INDEX `idx_meta_data_channel_type` ON `ocpc_meta_data` (`ocpc_channel_id`, `type`);
CREATE INDEX `idx_meta_data_date_type` ON `ocpc_meta_data` (`create_time`, `type`);

-- 6. 添加注释说明
ALTER TABLE `ocpc_meta_data` COMMENT = 'Meta OCPC数据表，用于存储Facebook/Instagram广告转化数据';
ALTER TABLE `ocpc_meta_token` COMMENT = 'Meta OCPC Token表，用于存储Facebook API访问凭证';

-- 7. 数据验证查询（可选执行）
-- 验证表结构
-- DESCRIBE `ocpc_meta_token`;
-- DESCRIBE `ocpc_meta_data`;

-- 验证数据
-- SELECT COUNT(*) as token_count FROM `ocpc_meta_token`;
-- SELECT COUNT(*) as channel_count FROM `ocpc_channel`;
-- SELECT COUNT(*) as data_count FROM `ocpc_meta_data`;

-- 8. 权限设置（根据需要调整）
-- GRANT SELECT, INSERT, UPDATE, DELETE ON `ocpc_meta_token` TO 'your_app_user'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON `ocpc_meta_data` TO 'your_app_user'@'%';
-- GRANT SELECT ON `ocpc_channel` TO 'your_app_user'@'%';
