-- 创建Google OCPC相关表

-- 1. 创建Google OCPC Token表
CREATE TABLE `ocpc_google_token` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `conversion_id` varchar(64) DEFAULT NULL COMMENT 'Google Ads转化ID',
  `conversion_label` varchar(64) DEFAULT NULL COMMENT 'Google Ads转化标签',
  `relm_name` varchar(256) DEFAULT NULL COMMENT '域名',
  `measurement_id` varchar(64) DEFAULT NULL COMMENT 'Google Analytics测量ID',
  `api_secret` varchar(128) DEFAULT NULL COMMENT 'Google Analytics API密钥',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_relm_name` (`relm_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Google OCPC Token信息';

-- 2. 创建Google OCPC数据表
CREATE TABLE `ocpc_google_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `ocpc_channel_id` bigint(20) DEFAULT NULL COMMENT '渠道ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `order_charge_id` bigint(20) DEFAULT NULL COMMENT '订单ID',
  `type` int(11) DEFAULT NULL COMMENT '类型：0-点击，1-注册，2-充值',
  `event_id` varchar(64) DEFAULT NULL COMMENT '事件ID',
  `client_id` varchar(128) DEFAULT NULL COMMENT '客户端ID',
  `gclid` varchar(128) DEFAULT NULL COMMENT 'Google Click ID',
  `event_value` decimal(10,2) DEFAULT NULL COMMENT '事件价值',
  `ip` varchar(64) DEFAULT NULL COMMENT '用户IP',
  `user_agent` varchar(512) DEFAULT NULL COMMENT '用户代理',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_channel_id` (`ocpc_channel_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_charge_id` (`order_charge_id`),
  KEY `idx_type` (`type`),
  KEY `idx_gclid` (`gclid`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_google_data_channel` FOREIGN KEY (`ocpc_channel_id`) REFERENCES `ocpc_channel` (`id`),
  CONSTRAINT `fk_google_data_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_google_data_order` FOREIGN KEY (`order_charge_id`) REFERENCES `order_charge` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Google OCPC数据';

-- 3. 插入示例数据（可选）
-- INSERT INTO `ocpc_google_token` (`conversion_id`, `conversion_label`, `relm_name`, `measurement_id`, `api_secret`) 
-- VALUES ('AW-123456789', 'abcdefg', 'example.com', 'G-XXXXXXXXXX', 'your_api_secret_here');
