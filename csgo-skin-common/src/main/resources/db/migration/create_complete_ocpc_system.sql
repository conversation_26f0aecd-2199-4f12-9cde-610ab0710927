-- 完整的OCPC系统数据库迁移脚本
-- 包含百度、Meta、Google、360等所有OCPC平台的表结构

-- ========================================
-- 1. 基础渠道表
-- ========================================
CREATE TABLE IF NOT EXISTS `ocpc_channel` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(128) DEFAULT NULL COMMENT '渠道名称',
  `relm_name` varchar(32) DEFAULT NULL COMMENT '渠道域名',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_relm_name` (`relm_name`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OCPC渠道信息';

-- ========================================
-- 2. 百度OCPC相关表
-- ========================================

-- 百度OCPC Token表
CREATE TABLE IF NOT EXISTS `ocpc_baidu_token` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `token` varchar(128) DEFAULT NULL COMMENT '百度API Token',
  `relm_name` varchar(256) DEFAULT NULL COMMENT '域名',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_relm_name` (`relm_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='百度OCPC Token信息';

-- 百度OCPC账号表
CREATE TABLE IF NOT EXISTS `ocpc_baidu_account` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `account` varchar(64) DEFAULT NULL COMMENT '百度账号用户名',
  `access_token` varchar(512) DEFAULT NULL COMMENT '访问令牌',
  `app_id` varchar(64) DEFAULT NULL COMMENT '应用ID',
  `secret_key` varchar(64) DEFAULT NULL COMMENT '密钥',
  `relm_name` varchar(256) DEFAULT NULL COMMENT '域名',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_relm_name` (`relm_name`),
  KEY `idx_account` (`account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='百度OCPC账号信息';

-- 百度OCPC数据表
CREATE TABLE IF NOT EXISTS `ocpc_baidu_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `ocpc_channel_id` bigint(20) DEFAULT NULL COMMENT '渠道ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `order_charge_id` bigint(20) DEFAULT NULL COMMENT '订单ID',
  `type` int(11) DEFAULT NULL COMMENT '类型：0-点击，1-注册，2-充值',
  `key_word` varchar(32) DEFAULT NULL COMMENT '关键词',
  `unit` varchar(32) DEFAULT NULL COMMENT '单元',
  `plan` varchar(32) DEFAULT NULL COMMENT '计划',
  `creative_id` bigint(20) DEFAULT NULL COMMENT '创意ID',
  `price` decimal(10,2) DEFAULT NULL COMMENT '出价',
  `ip` varchar(64) DEFAULT NULL COMMENT '用户IP',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_channel_id` (`ocpc_channel_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_charge_id` (`order_charge_id`),
  KEY `idx_type` (`type`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_baidu_data_channel` FOREIGN KEY (`ocpc_channel_id`) REFERENCES `ocpc_channel` (`id`),
  CONSTRAINT `fk_baidu_data_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_baidu_data_order` FOREIGN KEY (`order_charge_id`) REFERENCES `order_charge` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='百度OCPC数据';

-- ========================================
-- 3. Meta OCPC相关表
-- ========================================

-- Meta OCPC Token表
CREATE TABLE IF NOT EXISTS `ocpc_meta_token` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `access_token` varchar(512) DEFAULT NULL COMMENT 'Facebook访问令牌',
  `pixel_id` varchar(64) DEFAULT NULL COMMENT 'Facebook像素ID',
  `relm_name` varchar(256) DEFAULT NULL COMMENT '域名',
  `app_id` varchar(64) DEFAULT NULL COMMENT 'Facebook应用ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_relm_name` (`relm_name`),
  KEY `idx_pixel_id` (`pixel_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Meta OCPC Token信息';

-- Meta OCPC数据表
CREATE TABLE IF NOT EXISTS `ocpc_meta_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `ocpc_channel_id` bigint(20) DEFAULT NULL COMMENT '渠道ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `order_charge_id` bigint(20) DEFAULT NULL COMMENT '订单ID',
  `type` varchar(20) DEFAULT NULL COMMENT '类型：CLICK-点击，REGISTER-注册，CHARGE-充值',
  `event_id` varchar(64) DEFAULT NULL COMMENT '事件ID',
  `campaign_id` varchar(32) DEFAULT NULL COMMENT '广告系列ID',
  `adset_id` varchar(32) DEFAULT NULL COMMENT '广告组ID',
  `ad_id` varchar(32) DEFAULT NULL COMMENT '广告ID',
  `click_id` varchar(128) DEFAULT NULL COMMENT 'Facebook点击ID (fbclid)',
  `event_value` decimal(10,2) DEFAULT NULL COMMENT '事件价值',
  `ip` varchar(64) DEFAULT NULL COMMENT '用户IP',
  `user_agent` varchar(512) DEFAULT NULL COMMENT '用户代理',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_channel_id` (`ocpc_channel_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_charge_id` (`order_charge_id`),
  KEY `idx_type` (`type`),
  KEY `idx_click_id` (`click_id`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_meta_data_channel` FOREIGN KEY (`ocpc_channel_id`) REFERENCES `ocpc_channel` (`id`),
  CONSTRAINT `fk_meta_data_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_meta_data_order` FOREIGN KEY (`order_charge_id`) REFERENCES `order_charge` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Meta OCPC数据';

-- ========================================
-- 4. Google OCPC相关表
-- ========================================

-- Google OCPC Token表
CREATE TABLE IF NOT EXISTS `ocpc_google_token` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `conversion_id` varchar(64) DEFAULT NULL COMMENT 'Google Ads转化ID',
  `conversion_label` varchar(64) DEFAULT NULL COMMENT 'Google Ads转化标签',
  `relm_name` varchar(256) DEFAULT NULL COMMENT '域名',
  `measurement_id` varchar(64) DEFAULT NULL COMMENT 'Google Analytics测量ID',
  `api_secret` varchar(128) DEFAULT NULL COMMENT 'Google Analytics API密钥',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_relm_name` (`relm_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Google OCPC Token信息';

-- Google OCPC数据表
CREATE TABLE IF NOT EXISTS `ocpc_google_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `ocpc_channel_id` bigint(20) DEFAULT NULL COMMENT '渠道ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `order_charge_id` bigint(20) DEFAULT NULL COMMENT '订单ID',
  `type` int(11) DEFAULT NULL COMMENT '类型：0-点击，1-注册，2-充值',
  `event_id` varchar(64) DEFAULT NULL COMMENT '事件ID',
  `client_id` varchar(128) DEFAULT NULL COMMENT '客户端ID',
  `gclid` varchar(128) DEFAULT NULL COMMENT 'Google Click ID',
  `event_value` decimal(10,2) DEFAULT NULL COMMENT '事件价值',
  `ip` varchar(64) DEFAULT NULL COMMENT '用户IP',
  `user_agent` varchar(512) DEFAULT NULL COMMENT '用户代理',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_channel_id` (`ocpc_channel_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_charge_id` (`order_charge_id`),
  KEY `idx_type` (`type`),
  KEY `idx_gclid` (`gclid`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_google_data_channel` FOREIGN KEY (`ocpc_channel_id`) REFERENCES `ocpc_channel` (`id`),
  CONSTRAINT `fk_google_data_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_google_data_order` FOREIGN KEY (`order_charge_id`) REFERENCES `order_charge` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Google OCPC数据';

-- ========================================
-- 5. 360 OCPC相关表
-- ========================================

-- 360 OCPC Token表
CREATE TABLE IF NOT EXISTS `ocpc_360_token` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `app_key` varchar(128) DEFAULT NULL COMMENT '360应用密钥',
  `secret` varchar(128) DEFAULT NULL COMMENT '360密钥',
  `relm_name` varchar(256) DEFAULT NULL COMMENT '域名',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_relm_name` (`relm_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='360 OCPC Token信息';

-- ========================================
-- 6. 创建复合索引以提高查询性能
-- ========================================

-- 百度OCPC数据复合索引
CREATE INDEX `idx_baidu_data_user_type` ON `ocpc_baidu_data` (`user_id`, `type`);
CREATE INDEX `idx_baidu_data_channel_type` ON `ocpc_baidu_data` (`ocpc_channel_id`, `type`);
CREATE INDEX `idx_baidu_data_date_type` ON `ocpc_baidu_data` (`create_time`, `type`);

-- Meta OCPC数据复合索引
CREATE INDEX `idx_meta_data_user_type` ON `ocpc_meta_data` (`user_id`, `type`);
CREATE INDEX `idx_meta_data_channel_type` ON `ocpc_meta_data` (`ocpc_channel_id`, `type`);
CREATE INDEX `idx_meta_data_date_type` ON `ocpc_meta_data` (`create_time`, `type`);

-- Google OCPC数据复合索引
CREATE INDEX `idx_google_data_user_type` ON `ocpc_google_data` (`user_id`, `type`);
CREATE INDEX `idx_google_data_channel_type` ON `ocpc_google_data` (`ocpc_channel_id`, `type`);
CREATE INDEX `idx_google_data_date_type` ON `ocpc_google_data` (`create_time`, `type`);

-- ========================================
-- 7. 插入示例数据
-- ========================================

-- 插入OCPC渠道示例数据
INSERT INTO `ocpc_channel` (`name`, `relm_name`) VALUES
('百度SEM主渠道', 'baidu.example.com'),
('Meta Facebook渠道', 'meta.example.com'),
('Google Ads渠道', 'google.example.com'),
('360搜索渠道', '360.example.com'),
('测试渠道', 'test.example.com');

-- 插入百度OCPC Token示例数据
INSERT INTO `ocpc_baidu_token` (`token`, `relm_name`) VALUES
('baidu_token_example_123', 'baidu.example.com'),
('baidu_token_test_456', 'test.example.com');

-- 插入百度OCPC账号示例数据
INSERT INTO `ocpc_baidu_account` (`account`, `access_token`, `app_id`, `secret_key`, `relm_name`) VALUES
('baidu_account_1', 'baidu_access_token_123', 'app_123', 'secret_123', 'baidu.example.com'),
('baidu_account_test', 'baidu_access_token_test', 'app_test', 'secret_test', 'test.example.com');

-- 插入Meta OCPC Token示例数据
INSERT INTO `ocpc_meta_token` (`access_token`, `pixel_id`, `relm_name`, `app_id`) VALUES
('EAABwzLixnjYBO...example_token', '*********012345', 'meta.example.com', '*********012345'),
('EAABwzLixnjYBO...test_token', '***************', 'test.example.com', '***************');

-- 插入Google OCPC Token示例数据
INSERT INTO `ocpc_google_token` (`conversion_id`, `conversion_label`, `relm_name`, `measurement_id`, `api_secret`) VALUES
('AW-*********', 'conversion_label_1', 'google.example.com', 'G-XXXXXXXXXX', 'google_api_secret_123'),
('AW-*********', 'conversion_label_test', 'test.example.com', 'G-YYYYYYYYYY', 'google_api_secret_test');

-- 插入360 OCPC Token示例数据
INSERT INTO `ocpc_360_token` (`app_key`, `secret`, `relm_name`) VALUES
('360_app_key_123', '360_secret_123', '360.example.com'),
('360_app_key_test', '360_secret_test', 'test.example.com');

-- ========================================
-- 8. 数据验证和统计查询
-- ========================================

-- 验证表结构创建
SELECT
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_ROWS
FROM
    INFORMATION_SCHEMA.TABLES
WHERE
    TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME LIKE 'ocpc_%'
ORDER BY
    TABLE_NAME;

-- 验证外键约束
SELECT
    TABLE_NAME,
    CONSTRAINT_NAME,
    CONSTRAINT_TYPE
FROM
    INFORMATION_SCHEMA.TABLE_CONSTRAINTS
WHERE
    TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME LIKE 'ocpc_%'
    AND CONSTRAINT_TYPE = 'FOREIGN KEY';

-- 统计各平台配置数量
SELECT
    'OCPC渠道' as platform, COUNT(*) as count FROM ocpc_channel
UNION ALL
SELECT
    '百度Token' as platform, COUNT(*) as count FROM ocpc_baidu_token
UNION ALL
SELECT
    '百度账号' as platform, COUNT(*) as count FROM ocpc_baidu_account
UNION ALL
SELECT
    'Meta Token' as platform, COUNT(*) as count FROM ocpc_meta_token
UNION ALL
SELECT
    'Google Token' as platform, COUNT(*) as count FROM ocpc_google_token
UNION ALL
SELECT
    '360 Token' as platform, COUNT(*) as count FROM ocpc_360_token;

-- ========================================
-- 9. 清理脚本（谨慎使用）
-- ========================================

-- 如果需要重新创建表，可以使用以下脚本清理
-- 注意：这将删除所有数据，请谨慎使用！

/*
-- 删除数据表（按依赖关系顺序）
DROP TABLE IF EXISTS `ocpc_baidu_data`;
DROP TABLE IF EXISTS `ocpc_meta_data`;
DROP TABLE IF EXISTS `ocpc_google_data`;
DROP TABLE IF EXISTS `ocpc_baidu_account`;
DROP TABLE IF EXISTS `ocpc_baidu_token`;
DROP TABLE IF EXISTS `ocpc_meta_token`;
DROP TABLE IF EXISTS `ocpc_google_token`;
DROP TABLE IF EXISTS `ocpc_360_token`;
DROP TABLE IF EXISTS `ocpc_channel`;
*/

-- ========================================
-- 10. 权限设置建议
-- ========================================

-- 为应用用户授予必要权限（根据实际情况调整用户名和主机）
/*
-- 创建专用的OCPC数据库用户
CREATE USER 'ocpc_user'@'%' IDENTIFIED BY 'secure_password_here';

-- 授予OCPC相关表的权限
GRANT SELECT, INSERT, UPDATE, DELETE ON ocpc_channel TO 'ocpc_user'@'%';
GRANT SELECT, INSERT, UPDATE, DELETE ON ocpc_baidu_token TO 'ocpc_user'@'%';
GRANT SELECT, INSERT, UPDATE, DELETE ON ocpc_baidu_account TO 'ocpc_user'@'%';
GRANT SELECT, INSERT, UPDATE, DELETE ON ocpc_baidu_data TO 'ocpc_user'@'%';
GRANT SELECT, INSERT, UPDATE, DELETE ON ocpc_meta_token TO 'ocpc_user'@'%';
GRANT SELECT, INSERT, UPDATE, DELETE ON ocpc_meta_data TO 'ocpc_user'@'%';
GRANT SELECT, INSERT, UPDATE, DELETE ON ocpc_google_token TO 'ocpc_user'@'%';
GRANT SELECT, INSERT, UPDATE, DELETE ON ocpc_google_data TO 'ocpc_user'@'%';
GRANT SELECT, INSERT, UPDATE, DELETE ON ocpc_360_token TO 'ocpc_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;
*/
