-- Meta OCPC 数据迁移脚本
-- 用于将现有的Meta OCPC数据迁移到新的表结构

-- ========================================
-- 1. 备份现有数据（可选）
-- ========================================

-- 创建备份表
CREATE TABLE IF NOT EXISTS `ocpc_meta_token_backup` AS SELECT * FROM `ocpc_meta_token`;
CREATE TABLE IF NOT EXISTS `ocpc_meta_data_backup` AS SELECT * FROM `ocpc_meta_data`;

-- ========================================
-- 2. 检查现有数据结构
-- ========================================

-- 检查Meta Token表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'ocpc_meta_token'
ORDER BY 
    ORDINAL_POSITION;

-- 检查Meta Data表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'ocpc_meta_data'
ORDER BY 
    ORDINAL_POSITION;

-- ========================================
-- 3. 数据完整性检查
-- ========================================

-- 检查Meta Token数据
SELECT 
    COUNT(*) as total_tokens,
    COUNT(DISTINCT relm_name) as unique_domains,
    COUNT(DISTINCT pixel_id) as unique_pixels
FROM 
    ocpc_meta_token;

-- 检查Meta Data数据
SELECT 
    type,
    COUNT(*) as count,
    MIN(create_time) as earliest_date,
    MAX(create_time) as latest_date
FROM 
    ocpc_meta_data 
GROUP BY 
    type;

-- 检查外键关联数据
SELECT 
    'ocpc_channel关联' as check_type,
    COUNT(*) as total_records,
    COUNT(DISTINCT ocpc_channel_id) as unique_channels
FROM 
    ocpc_meta_data 
WHERE 
    ocpc_channel_id IS NOT NULL

UNION ALL

SELECT 
    'user关联' as check_type,
    COUNT(*) as total_records,
    COUNT(DISTINCT user_id) as unique_users
FROM 
    ocpc_meta_data 
WHERE 
    user_id IS NOT NULL

UNION ALL

SELECT 
    'order_charge关联' as check_type,
    COUNT(*) as total_records,
    COUNT(DISTINCT order_charge_id) as unique_orders
FROM 
    ocpc_meta_data 
WHERE 
    order_charge_id IS NOT NULL;

-- ========================================
-- 4. 数据清理和标准化
-- ========================================

-- 清理Meta Token表中的重复数据
DELETE t1 FROM ocpc_meta_token t1
INNER JOIN ocpc_meta_token t2 
WHERE 
    t1.id > t2.id 
    AND t1.relm_name = t2.relm_name 
    AND t1.pixel_id = t2.pixel_id;

-- 清理Meta Data表中的无效外键关联
DELETE FROM ocpc_meta_data 
WHERE 
    ocpc_channel_id IS NOT NULL 
    AND ocpc_channel_id NOT IN (SELECT id FROM ocpc_channel);

DELETE FROM ocpc_meta_data 
WHERE 
    user_id IS NOT NULL 
    AND user_id NOT IN (SELECT id FROM user);

DELETE FROM ocpc_meta_data 
WHERE 
    order_charge_id IS NOT NULL 
    AND order_charge_id NOT IN (SELECT id FROM order_charge);

-- 标准化type字段（如果使用的是数字类型，转换为字符串）
UPDATE ocpc_meta_data 
SET type = CASE 
    WHEN type = '0' OR type = 'CLICK' THEN 'CLICK'
    WHEN type = '1' OR type = 'REGISTER' THEN 'REGISTER'
    WHEN type = '2' OR type = 'CHARGE' THEN 'CHARGE'
    ELSE type
END;

-- ========================================
-- 5. 添加缺失的索引
-- ========================================

-- 为Meta Token表添加索引
CREATE INDEX IF NOT EXISTS `idx_meta_token_relm_name` ON `ocpc_meta_token` (`relm_name`);
CREATE INDEX IF NOT EXISTS `idx_meta_token_pixel_id` ON `ocpc_meta_token` (`pixel_id`);

-- 为Meta Data表添加索引
CREATE INDEX IF NOT EXISTS `idx_meta_data_channel_id` ON `ocpc_meta_data` (`ocpc_channel_id`);
CREATE INDEX IF NOT EXISTS `idx_meta_data_user_id` ON `ocpc_meta_data` (`user_id`);
CREATE INDEX IF NOT EXISTS `idx_meta_data_order_charge_id` ON `ocpc_meta_data` (`order_charge_id`);
CREATE INDEX IF NOT EXISTS `idx_meta_data_type` ON `ocpc_meta_data` (`type`);
CREATE INDEX IF NOT EXISTS `idx_meta_data_click_id` ON `ocpc_meta_data` (`click_id`);
CREATE INDEX IF NOT EXISTS `idx_meta_data_create_time` ON `ocpc_meta_data` (`create_time`);
CREATE INDEX IF NOT EXISTS `idx_meta_data_event_id` ON `ocpc_meta_data` (`event_id`);

-- 复合索引
CREATE INDEX IF NOT EXISTS `idx_meta_data_user_type` ON `ocpc_meta_data` (`user_id`, `type`);
CREATE INDEX IF NOT EXISTS `idx_meta_data_channel_type` ON `ocpc_meta_data` (`ocpc_channel_id`, `type`);
CREATE INDEX IF NOT EXISTS `idx_meta_data_date_type` ON `ocpc_meta_data` (`create_time`, `type`);

-- ========================================
-- 6. 数据验证
-- ========================================

-- 验证迁移后的数据完整性
SELECT 
    'Meta Token表' as table_name,
    COUNT(*) as record_count,
    COUNT(DISTINCT relm_name) as unique_domains,
    MIN(create_time) as earliest_record,
    MAX(create_time) as latest_record
FROM 
    ocpc_meta_token

UNION ALL

SELECT 
    'Meta Data表' as table_name,
    COUNT(*) as record_count,
    COUNT(DISTINCT ocpc_channel_id) as unique_channels,
    MIN(create_time) as earliest_record,
    MAX(create_time) as latest_record
FROM 
    ocpc_meta_data;

-- 验证外键约束
SELECT 
    'Meta Data -> Channel' as relation,
    COUNT(*) as total_records,
    SUM(CASE WHEN c.id IS NULL THEN 1 ELSE 0 END) as orphaned_records
FROM 
    ocpc_meta_data md
    LEFT JOIN ocpc_channel c ON md.ocpc_channel_id = c.id
WHERE 
    md.ocpc_channel_id IS NOT NULL

UNION ALL

SELECT 
    'Meta Data -> User' as relation,
    COUNT(*) as total_records,
    SUM(CASE WHEN u.id IS NULL THEN 1 ELSE 0 END) as orphaned_records
FROM 
    ocpc_meta_data md
    LEFT JOIN user u ON md.user_id = u.id
WHERE 
    md.user_id IS NOT NULL

UNION ALL

SELECT 
    'Meta Data -> Order' as relation,
    COUNT(*) as total_records,
    SUM(CASE WHEN o.id IS NULL THEN 1 ELSE 0 END) as orphaned_records
FROM 
    ocpc_meta_data md
    LEFT JOIN order_charge o ON md.order_charge_id = o.id
WHERE 
    md.order_charge_id IS NOT NULL;

-- ========================================
-- 7. 性能优化建议
-- ========================================

-- 分析表以更新统计信息
ANALYZE TABLE ocpc_meta_token;
ANALYZE TABLE ocpc_meta_data;

-- 优化表结构
OPTIMIZE TABLE ocpc_meta_token;
OPTIMIZE TABLE ocpc_meta_data;

-- ========================================
-- 8. 迁移完成报告
-- ========================================

SELECT 
    '=== Meta OCPC 数据迁移完成报告 ===' as report_title
UNION ALL
SELECT 
    CONCAT('Meta Token记录数: ', COUNT(*)) as report_line
FROM 
    ocpc_meta_token
UNION ALL
SELECT 
    CONCAT('Meta Data记录数: ', COUNT(*)) as report_line
FROM 
    ocpc_meta_data
UNION ALL
SELECT 
    CONCAT('最早数据时间: ', MIN(create_time)) as report_line
FROM 
    ocpc_meta_data
UNION ALL
SELECT 
    CONCAT('最新数据时间: ', MAX(create_time)) as report_line
FROM 
    ocpc_meta_data
UNION ALL
SELECT 
    '迁移状态: 成功完成' as report_line;
