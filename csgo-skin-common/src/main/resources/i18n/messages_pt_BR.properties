validate.token.error=Erro de token
response.success=Solicitação bem-sucedida
response.fail=Solicitação falhou
response.http_200=Solicitação bem-sucedida
response.http_400=Solicitação falhou
response.http_401=Não autorizado
response.http_403=Proibido
response.http_500=Erro do servidor
response.logout.success=Logout bem-sucedido

# Respostas de operações do gerenciador
response.manager.case.cache.deleted=Cache de caixa excluído com sucesso
response.manager.skin.cache.deleted=Cache de skin excluído com sucesso
response.manager.case.skin.refresh.success=Atualização de skin da caixa bem-sucedida

# Mensagens de resposta de exceção
response.exception.resource.not.found=Recurso não encontrado

# Nomes das Salas ROLL de Recarga Diária
roll.home.name.50=Sala ROLL Bônus Recarga Diária 50
roll.home.name.100=Sala ROLL Bônus Recarga Diária 100
roll.home.name.500=Sala ROLL Bônus Recarga Diária 500
roll.home.name.1000=Sala ROLL Bônus Recarga Diária 1000
roll.home.name.2000=Sala ROLL Bônus Recarga Diária 2000
roll.home.name.5000=Sala ROLL Bônus Recarga Diária 5000
# Nomes das Salas ROLL de Recarga Semanal
week.roll.home.name.500=Sala ROLL Bônus Recarga Semanal 500
week.roll.home.name.2000=Sala ROLL Bônus Recarga Semanal 2000
week.roll.home.name.5000=Sala ROLL Bônus Recarga Semanal 5000
week.roll.home.name.10000=Sala ROLL Bônus Recarga Semanal 10000

# Mensagens de Exceção
exception.too.many.requests=Solicitação falhou! Muitas solicitações! Entre em contato com o atendimento ao cliente!
exception.parent.field.not.exist.or.deleted=Campo pai não existe ou foi excluído
exception.field.not.exist.or.deleted=Campo não existe ou foi excluído
exception.case.not.exist.or.offline=Caixa não existe ou está offline
exception.case.not.openable=Esta caixa não pode ser aberta
exception.insufficient.coin=Moedas insuficientes
exception.sms.send.fail.retry.later=Falha ao enviar SMS, tente novamente mais tarde
exception.captcha.not.expired.retry.later=Captcha não expirou, tente novamente mais tarde

exception.novice.case.no.remaining.chances=Sem chances restantes para caixas novatas, convide usuários para ganhar oportunidades extras
exception.novice.case.remaining.chances=Apenas {0} chances restantes para caixas novatas
exception.consumeplan.case.no.remaining.chances=Sem chances restantes para caixas do tesouro
exception.consumeplan.case.remaining.chances=Apenas {0} chances restantes para caixas do tesouro
exception.daily.task.insufficient.charge=Recarregue {0} moedas para participar
exception.daily.task.insufficient.consume=Gaste {0} moedas para participar
exception.daily.task.already.opened.today=Você já abriu {0} hoje, volte amanhã
exception.insufficient.coins=Moedas insuficientes
exception.operation.too.frequent=Operação muito frequente
exception.daily.task.config.missing=Configuração de tarefa diária ausente, entre em contato com o atendimento ao cliente

exception.battle.home.cannot.join=Não é possível entrar nesta sala de batalha
exception.battle.home.already.joined=Já entrou, não pode entrar novamente
exception.battle.home.room.full=Falha ao entrar na sala, está cheia
exception.battle.home.not.exist=Sala de batalha não existe
exception.case.not.battle.box=Não é uma caixa de batalha

exception.percentage.need.charge=Precisa recarregar para participar da loteria percentual
exception.percentage.skin.not.supported=Não é uma skin de loteria percentual
exception.invalid.parameters=Parâmetros inválidos
exception.skin.not.exist.or.offline=A skin não existe ou foi retirada de linha
exception.percentage.min.coin.requirement=Mínimo de 3 moedas necessárias para loteria percentual

exception.csgo.skin.quality.not.exist=Qualidade da skin não existe.
exception.csgo.skin.rarity.not.exist=Raridade da skin não existe.
exception.csgo.skin.exterior.not.exist=Exterior da skin não existe.
exception.csgo.prototype.not.exist=Protótipo não existe.

exception.storage.upload.fail=Falha no upload do arquivo.
exception.storage.file.notfound=O arquivo não existe.
exception.storage.io.error=Ocorreu um erro de IO.

exception.case.category.not.found=Categoria da caixa não existe.
exception.skin.not.found=Skin com ID {0} não existe.
exception.skin.color.not.found=Cor com ID {0} não existe.
exception.custom.color.required=Cor personalizada não pode estar vazia.
exception.case.level.probability.invalid=Probabilidade total do nível {0} na caixa {1} não é igual a 1.
exception.case.skin.duplicate=Skin {0} no nível {1} da caixa {2} é diferente das outras.
exception.case.no.levels=Caixa não tem níveis associados.
exception.resource.not.exist=Recurso não existe.
exception.case.not.found=Caixa não existe ou foi excluída.
exception.case.not.exist.create.one=Ainda não há caixas! Crie uma agora!

# Enum de tipo de caixa
case.type.beginner=Caixa Iniciante
case.type.normal=Caixa Normal
case.type.novice=Caixa Novata
case.type.ordinary=Caixa Comum
case.type.consumeplan=Caixa do Plano de Consumo
case.type.daily.tasks=Caixa de Tarefas Diárias

# Exceções relacionadas ao usuário
exception.user.not.found=Usuário não encontrado ou foi excluído.
exception.user.charge.amount.invalid=Valor de recarga inválido.
exception.user.type.invalid=Tipo de usuário inválido.
exception.user.robot.add.limit.exceeded=Não é possível adicionar mais de 10 robôs de uma vez.
exception.skin.not.exist=Skin não existe

# Exceções relacionadas ao usuário administrador
exception.admin.login.invalid.credentials=Nome de usuário ou senha inválidos

# Exceções relacionadas ao pacote vermelho
exception.redpacket.code.already.exists=Código do pacote vermelho já existe

# Exceções relacionadas à segurança
exception.security.not.logged.in=Não logado

# Exceções relacionadas à skin
exception.skin.bound.to.case.cannot.delete=Esta skin já está vinculada a uma caixa e não pode ser excluída

# Exceções relacionadas à caixa
exception.case.level.not.exist.or.offline=Nível da caixa não existe ou está offline
exception.case.skins.not.exist.or.offline=Skins da caixa não existem ou estão offline

contact.admin=Entre em contato com o atendimento ao cliente.

exception.user.name.empty=Nome de usuário não pode estar vazio
exception.user.name.change.limit=Pode ser modificado após
exception.trade.url.empty=URL de troca não pode estar vazia
exception.trade.url.invalid=URL de troca é inválida
exception.trade.url.same=URL de troca é a mesma que a atual
exception.trade.url.bound=URL de troca já está vinculada a outra conta
exception.trade.url.change.limit=Pode ser modificada após
exception.avatar.empty=Avatar não pode estar vazio
exception.user.banned=Usuário banido, entre em contato com o atendimento ao cliente
exception.algorithm.data.error=Erro nos dados do algoritmo, redefina
exception.user.data.error=Erro nos dados do usuário, entre em contato com o atendimento ao cliente
exception.verification.code.error=Erro no código de verificação
exception.inventory.not.found=Item do inventário não encontrado
exception.inventory.locked=Inventário insuficiente
exception.diamond.required=Diamante deve ser maior que 0
exception.diamond.insufficient=Diamantes insuficientes
exception.real.name.error=Falha na verificação do nome real

exception.operation.invalid=Operação inválida
exception.function.charge.required=Função disponível após recarga
exception.inventory.not.owned=Item não possuído
exception.inventory.out.of.stock=Fora de estoque
exception.inventory.already.processed=Item já vendido ou recuperado
exception.trade.url.not.set=URL de troca não definida
exception.user.data.error.contact=Erro nos dados do usuário, entre em contato com o atendimento ao cliente
exception.operation.failed=Operação falhou
system.disable.exchange.unpaid=Função de troca desabilitada para usuários não pagos
system.remark.trade.url.change=URL de troca do Steam alterada

email.login.verify.template=Seu código de verificação é {0}, não compartilhe com outros.
email.login.subject=Verificação de Login da Conta

exception.admin.add.roll.home.failed=Falha ao adicionar sala Roll pelo administrador
exception.roll.home.type.not.exist=Tipo de sala Roll não existe
exception.roll.home.lottery.method.not.exist=Método de loteria da sala Roll não existe
exception.admin.update.roll.home.failed=Falha ao atualizar sala Roll pelo administrador
exception.roll.home.not.exist=Sala Roll não existe
exception.roll.home.only.unstarted.can.be.updated=Apenas salas Roll não iniciadas podem ser atualizadas
exception.user.type.not.supported=Tipo de usuário não suportado
exception.roll.home.has.ended=Sala Roll terminou
exception.user.already.joined.roll.home=Usuário já entrou na sala Roll
exception.roll.home.is.full=Sala Roll está cheia
exception.roll.home.can.not.be.modified.after.draw=Sala Roll foi sorteada, modificação não permitida
exception.user.not.join.roll.home=Usuário não entrou na sala Roll
exception.create.roll.home.failed=Falha ao criar sala Roll
exception.roll.home.id.cannot.be.empty=ID da sala Roll não pode estar vazio

exception.roll_home.room_not_found=Sala Roll não existe
exception.roll_home.password_incorrect=Senha incorreta
exception.roll_home.novice_already_joined=Usuário já entrou em uma sala Roll novata
exception.roll_home.insufficient_charge=Recarregue %s moedas para entrar
exception.roll_home.insufficient_consume=Gaste %s moedas para entrar
exception.roll_home.already_ended=Sala Roll já terminou
exception.roll_home.user_already_joined=Usuário já entrou
exception.roll_home.full=Sala Roll está cheia

exception.product.not.exist=Produto não existe
exception.real.name.auth.required=Autenticação de nome real é necessária
exception.payment.method.invalid=Método de pagamento inválido
exception.order.creation.failed=Falha ao criar pedido

exception.dictionary.id.not.exist=Categoria do dicionário selecionada é inválida
exception.exchange.rate.not.configured=Taxa de câmbio não configurada no backend
exception.contact.info.not.configured=Informações de contato não configuradas
exception.user.benefit.not.configured=Benefícios do usuário não configurados

exception.algorithm.data.abnormal.please.reset=Dados do algoritmo do usuário são anômalos, tente redefinir e tentar novamente
exception.public.hash.mismatch.secret.hash=Erro nos dados do algoritmo: hash público não corresponde ao hash secreto

exception.storage.file.not.exist=Arquivo não existe
exception.skin.info.not.exist=Informações da skin não existem
exception.card.collect.probability.not.one=A probabilidade da caixa de coleta de cartas não é igual a 1

# Relacionado à categoria da caixa
case.category.treasure.box=Caixa do Tesouro
case.category.daily.task.box=Caixa de Tarefa Diária
case.category.battle.exclusive=Exclusivo de Batalha
case.category.newbie.welfare=Bem-estar do Novato

exception.steam.already.bound=Conta Steam já vinculada, vinculação duplicada não permitida
exception.steam.verification.failed=Verificação do Steam falhou
exception.steam.bound.to.other.user=Esta conta Steam já está vinculada a outro usuário
exception.steam.not.bound=Conta Steam não vinculada

# Modo de Batalha
battle.home.method.pve=Batalha de Robôs
battle.home.method.pvp=Batalha de Jogadores

# Tipo de Sala Roll
roll.home.type.novice=Sala Roll Novato
roll.home.type.day=Sala Roll Diária
roll.home.type.week=Sala Roll Semanal
roll.home.type.month=Sala Roll Mensal

# Tipo de Usuário
user.type.actual=Usuário Real
user.type.robot=Robô
user.type.insider=Usuário Interno
user.type.cheat.robot=Robô Integral
user.type.anchor=Âncora

# Tipo de Limite
threshold.type.charge=Recarga
threshold.type.consume=Consumo

# Tipo de Sincronização ZBT
zbt.sync.type.all=Sincronização Completa
zbt.sync.type.appoint=Sincronização Designada

# Tipo de Pagamento
pay.type.wechatpay=WeChat Pay
pay.type.alipay=Alipay
pay.type.easy.alipay=Alipay 2
pay.type.easy.wechatpay=WeChat Pay 2
pay.type.box.alipay=Alipay 1
pay.type.box.wechatpay=WeChat Pay 1
pay.type.xinfu.bx.pay=Pagamento Xinfu Brasil

# Fonte do Pacote do Usuário
user.package.source.open.case=Abrir Caixa
user.package.source.percentage=Porcentagem
user.package.source.roll.home=Batalha da Sala Roll
user.package.source.battle=Batalha
user.package.source.buy=Comprar
user.package.source.system=Sistema
user.package.source.other=Outros

# Fonte de Mudança de Moeda do Usuário
user.coin.change.source.charge=Recarga
user.coin.change.source.open.case=Abrir Caixa
user.coin.change.source.percentage=Porcentagem
user.coin.change.source.roll.home=Sala Roll
user.coin.change.source.battle=Batalha
user.coin.change.source.buy=Comprar
user.coin.change.source.system=Sistema
user.coin.change.source.activity=Atividade
user.coin.change.source.other=Outros
user.coin.change.source.red.packet=Envelope Vermelho
user.coin.change.source.register=Presente de Registro
user.coin.change.source.first.charge=Presente de Primeira Recarga
user.coin.change.source.invite=Recompensa de Convite
user.coin.change.source.activity.card=Coleta de Cartas
user.coin.change.source.daily.charge.red.packet=Envelope Vermelho de Recarga Diária
user.coin.change.source.check.in=Recompensa de Check-in
user.coin.change.source.exchange=Troca de Diamante

# Status do Pedido
order.status.unpay=Não Pago
order.status.paid=Pago
order.status.cancel=Cancelado
order.status.refund=Reembolsado

# Tipo de Atividade Diária
daily.activity.type.sign.in=Check-in
daily.activity.type.open.case=Abrir Caixa
daily.activity.type.charge=Recarga
daily.activity.type.invite.friend=Convidar Amigo
daily.activity.type.online=Online
daily.activity.type.percentage=Perseguir Sonhos
daily.activity.type.battle=Batalha
daily.activity.type.pick.up=Retirar
daily.activity.type.other=Outros

# Fonte de Coleta de Cartas
card.collect.source.sign.in=Check-in
card.collect.source.open.case=Abrir Caixa
card.collect.source.charge=Recarga
card.collect.source.activity=Atividade
card.collect.source.percentage=Perseguir Sonhos
card.collect.source.battle=Batalha

# Status
status.disable=Desabilitado
status.enable=Habilitado

# Status da Sala de Batalha
battle.home.status.waiting=Aguardando
battle.home.status.running=Em Andamento
battle.home.status.finish=Finalizado

# Status da Caixa da Sala de Batalha
battle.home.case.status.unopen=Não Aberto
battle.home.case.status.wait=Preparando
battle.home.case.status.open=Aberto

# Status de Retirada do Pacote
package.pickup.status.pickuping=Retirando
package.pickup.status.todo=A Fazer
package.pickup.status.doing=Processando
package.pickup.status.wait.user=Aguardando Usuário
package.pickup.status.success=Sucesso
package.pickup.status.fail=Falhou
package.pickup.status.cancel=Cancelado
package.pickup.status.checking=Verificando
package.pickup.status.frozen=Congelado
package.pickup.status.fail.steam=Falha no Steam
package.pickup.status.wait.send=Aguardando Envio

# Método de Envelope Vermelho
red.packet.method.novice=Envelope Vermelho Novato
red.packet.method.ordinary=Envelope Vermelho Comum

# Status de Sincronização ZBT
zbt.sync.status.syncing=Sincronizando
zbt.sync.status.success=Sucesso
zbt.sync.status.fail=Falhou

# Status de Venda do Pacote
package.sell.status.selling=Vendendo
package.sell.status.success=Sucesso
package.sell.status.fail=Falhou

# Tipo de Envelope Vermelho
red.packet.type.day=Envelope Vermelho Diário
red.packet.type.week=Envelope Vermelho Semanal
red.packet.type.month=Envelope Vermelho Mensal

# Método de Sorteio da Sala Roll
roll.home.lottery.method.fixtime=Tempo Fixo
roll.home.lottery.method.fixpeople=Pessoas Fixas

# Método da Sala Roll
roll.home.method.novice=Sala Roll Novato
roll.home.method.ordinary=Sala Roll Comum
roll.home.method.day=Sala Roll Diária
roll.home.method.week=Sala Roll Semanal
roll.home.method.month=Sala Roll Mensal

# Pessoas da Sala Roll
roll.home.people.one=Batalha de Dois Jogadores
roll.home.people.two=Batalha de Três Jogadores

# Status da Sala Roll
roll.home.status.unstart=Não Iniciado
roll.home.status.start=Sorteando
roll.home.status.end=Finalizado

# Status de Compra de Skin do Usuário
user.buy.skin.status.buying=Comprando
user.buy.skin.status.success=Sucesso
user.buy.skin.status.fail=Falhou

# Fonte de Mudança de Diamante do Usuário
user.diamond.change.source.exchange=Troca
user.diamond.change.source.sell=Vender
user.diamond.change.source.buy=Comprar
user.diamond.change.source.other=Outros

# Tipo de Desabilitação do Usuário
user.disable.type.exchange=Troca
user.disable.type.pick.up=Retirar
user.disable.type.open.case=Abrir Caixa
user.disable.type.percentage=Perseguir Sonhos

# Status de Troca do Usuário
user.exchange.status.exchanging=Trocando
user.exchange.status.success=Sucesso
user.exchange.status.fail=Falhou

# Tipo de Mudança de Informação do Usuário
user.info.change.type.nickname=Apelido
user.info.change.type.avatar=Avatar
user.info.change.type.trade.url=URL de Troca

# Tipo de Dados OCPC Baidu
ocpc.baidu.data.type.click=Clique
ocpc.baidu.data.type.register=Registrar
ocpc.baidu.data.type.charge=Recarga

response.exception.pay.channel.not.found=Canal de pagamento não encontrado para o país atual



# OCPC Meta Data Types
ocpc.meta.data.type.click=Clique
ocpc.meta.data.type.register=Registrar
ocpc.meta.data.type.charge=Recarga
