validate.token.error=Token??
response.success=请求成功
response.fail=请求失败
response.http_200=请求成功
response.http_400=请求失败
response.http_401=未授权
response.http_403=禁止访问
response.http_500=服务器错误
response.logout.success=退出登录成功

# 管理操作响应
response.manager.case.cache.deleted=箱子缓存删除成功
response.manager.skin.cache.deleted=饰品缓存删除成功
response.manager.case.skin.refresh.success=箱子饰品刷新成功

# 异常响应消息
response.exception.resource.not.found=资源不存在

# 每日充值ROLL房名称
roll.home.name.50=每日充值50福利ROLL房
roll.home.name.100=每日充值100福利ROLL房
roll.home.name.500=每日充值500福利ROLL房
roll.home.name.1000=每日充值1000福利ROLL房
roll.home.name.2000=每日充值2000福利ROLL房
roll.home.name.5000=每日充值5000福利ROLL房
# 本周充值ROLL房名称
week.roll.home.name.500=本周充值500福利ROLL房
week.roll.home.name.2000=本周充值2000福利ROLL房
week.roll.home.name.5000=本周充值5000福利ROLL房
week.roll.home.name.10000=本周充值10000福利ROLL房
# 异常消息
exception.too.many.requests=请求失败！请求次数过多！请联系客服！
exception.parent.field.not.exist.or.deleted=父字段不存在或已删除
# 字段不存在或已删除
exception.field.not.exist.or.deleted=字段不存在或已删除
exception.case.not.exist.or.offline=箱子不存在或已下架
exception.case.not.openable=非开箱箱子
exception.insufficient.coin=金币不足

exception.sms.send.fail.retry.later=发送短信失败，请稍后再试
exception.captcha.not.expired.retry.later=验证码未到期,稍后重试
exception.novice.case.no.remaining.chances=新手箱子开启次数已用完,邀请用户可赚额外机会
exception.novice.case.remaining.chances=新手箱子仅剩{0}次抽取机会
exception.consumeplan.case.no.remaining.chances=寻宝箱子开启次数已用完
exception.consumeplan.case.remaining.chances=寻宝箱子仅剩{0}次抽取机会
exception.daily.task.insufficient.charge=再充值{0}金币即可参与
exception.daily.task.insufficient.consume=再消耗{0}金币即可参与
exception.daily.task.already.opened.today=今天已经开过{0},明天再来吧
exception.insufficient.coins=金币不足
exception.operation.too.frequent=操作频繁

# OCPC 数据类型
ocpc.baidu.data.type.click=点击
ocpc.baidu.data.type.register=注册
ocpc.baidu.data.type.charge=充值

ocpc.google.data.type.click=点击
ocpc.google.data.type.register=注册
ocpc.google.data.type.charge=充值

# Roll房相关
roll.home.secret.after.lottery=开奖后公开

# 响应消息
response.create.success=创建成功
response.update.success=更新成功
response.delete.success=删除成功
response.operation.success=操作成功

# 存储相关
exception.storage.upload.fail=上传失败

# 更多异常消息
exception.red.packet.not.exist=红包不存在
exception.red.packet.already.received=红包已领取
exception.activity.not.exist.or.ended=活动不存在或已结束
exception.need.charge.to.receive=再充值{0}金币即可领取
exception.checkin.reward.already.claimed=签到奖励已领取
exception.daily.task.config.missing=日常任务状态异常, 请联系客服
exception.battle.home.cannot.join=无法参与该竞技房
exception.battle.home.already.joined=已参与，无法重复参与
exception.battle.home.room.full=参加箱子失败，房间已满
exception.battle.home.not.exist=竞技房不存在
exception.case.not.battle.box=非对战箱子
exception.percentage.need.charge=充值后可参加追梦
exception.percentage.skin.not.supported=非追梦奖池饰品
exception.invalid.parameters=参数错误
exception.skin.not.exist.or.offline=皮肤不存在或已下架
exception.percentage.min.coin.requirement=追梦最小需消耗3金币

exception.storage.file.notfound=文件不存在。
exception.storage.io.error=发生IO错误。

exception.case.category.not.found=箱子分类不存在。
exception.skin.not.found=皮肤ID {0}不存在。
exception.skin.color.not.found=颜色ID{0}不存在。
exception.custom.color.required=自定义颜色不能为空。
exception.case.level.probability.invalid=箱子 {0}的等级{1}总概率不等于1。
exception.case.skin.duplicate=箱子{0}等级{1}的皮肤{2}与其他不同。
exception.case.no.levels=箱子没有关联等级。
exception.resource.not.exist=资源不存在。
exception.case.not.found=箱子不存在或已被删除。
exception.case.not.exist.create.one=现在还没有箱子！快去创建一个吧!!

# 箱子类型枚举
case.type.beginner=新手箱子
case.type.normal=普通箱子
case.type.novice=新手箱子
case.type.ordinary=普通箱子
case.type.consumeplan=消费计划箱子
case.type.daily.tasks=日常任务箱子

# 用户相关异常
exception.user.not.found=用户不存在或已被删除。
exception.user.charge.amount.invalid=充值金额错误。
exception.user.type.invalid=用户类型错误。
exception.user.robot.add.limit.exceeded=一次最多只能添加10个机器人。
exception.skin.not.exist=饰品不存在

# 管理员用户相关异常
exception.admin.login.invalid.credentials=用户名或密码错误

# 红包相关异常
exception.redpacket.code.already.exists=口令已存在

# 安全相关异常
exception.security.not.logged.in=未登录

# 饰品相关异常
exception.skin.bound.to.case.cannot.delete=该饰品已经绑定箱子，不支持删除

# 箱子相关异常
exception.case.level.not.exist.or.offline=箱子等级不存在或已下架
exception.case.skins.not.exist.or.offline=箱子饰品不存在或已下架

contact.admin=联系管理员

exception.user.name.empty=用户名不能为空
exception.user.name.change.limit=后可修改用户名
exception.trade.url.empty=交易链接不能为空
exception.trade.url.invalid=交易链接不可用
exception.trade.url.same=交易链接与当前一致
exception.trade.url.bound=交易链接已绑定其它账号
exception.trade.url.change.limit=后可修改交易链接
exception.avatar.empty=头像不能为空
exception.user.banned=用户禁用,联系客服处理
exception.algorithm.data.error=算法数据异常,重置后再试
exception.user.data.error=用户数据异常,联系客服
exception.verification.code.error=验证码错误
exception.inventory.not.found=背包饰品不存在
exception.inventory.locked=库存不足
exception.diamond.required=钻石必须大于0
exception.diamond.insufficient=钻石不足
exception.real.name.error=实名信息错误

exception.operation.invalid=操作异常
exception.function.charge.required=功能充值后可用
exception.inventory.not.owned=饰品未拥有
exception.inventory.out.of.stock=商品缺货
exception.inventory.already.processed=饰品已出售或已取回
exception.trade.url.not.set=未配置交易链接
exception.user.data.error.contact=用户数据异常,联系客服
exception.operation.failed=操作失败
system.disable.exchange.unpaid=未充值用户,禁用兑换功能
system.remark.trade.url.change=steam交易链接更改

email.login.verify.template=您的验证码为{0}，请勿泄露给他人。
email.login.subject=邮箱登录验证

exception.admin.add.roll.home.failed=管理员添加 Roll 房失败
exception.roll.home.type.not.exist=rollHomeType 不存在
exception.roll.home.lottery.method.not.exist=rollHomeLotteryMethod 不存在
exception.admin.update.roll.home.failed=管理员更新 Roll 房失败
exception.roll.home.not.exist=Roll 房不存在
exception.roll.home.only.unstarted.can.be.updated=更新 Roll 房失败，只支持未开始的房间
exception.user.type.not.supported=不支持的用户类型
exception.roll.home.has.ended=Roll 房已结束
exception.user.already.joined.roll.home=用户已经参与过 Roll 房
exception.roll.home.is.full=Roll 房已满
exception.roll.home.can.not.be.modified.after.draw=Roll 房已开奖，不支持修改
exception.user.not.join.roll.home=用户未参加 Roll 房
exception.create.roll.home.failed=创建 Roll 房失败
exception.roll.home.id.cannot.be.empty=rollHomeId 不能为空

exception.roll_home.room_not_found=roll房不存在
exception.roll_home.password_incorrect=密码错误
exception.roll_home.novice_already_joined=已参与过新手Roll房
exception.roll_home.insufficient_charge=再充值 %s 金币即可参与
exception.roll_home.insufficient_consume=再消费 %s 金币即可参与
exception.roll_home.already_ended=已经结束
exception.roll_home.user_already_joined=已经参与过
exception.roll_home.full=Roll房已满

exception.product.not.exist=商品不存在
exception.real.name.auth.required=未完成实名认证
exception.payment.method.invalid=错误的支付方式
exception.order.creation.failed=创建订单失败

exception.dictionary.id.not.exist=所选类别错误
exception.exchange.rate.not.configured=后台未配置汇率
exception.contact.info.not.configured=未配置联系方式
exception.user.benefit.not.configured=未配置用户福利

exception.algorithm.data.abnormal.please.reset=用户算法数据异常，请尝试重置后重试
exception.public.hash.mismatch.secret.hash=算法数据异常，公共哈希与私密哈希不匹配

exception.storage.file.not.exist=文件不存在
exception.skin.info.not.exist=饰品信息不存在
exception.card.collect.probability.not.one=箱子概率不等于1

# 箱子分类相关
case.category.treasure.box=寻宝箱子
case.category.daily.task.box=日常任务箱子
case.category.battle.exclusive=对战专属
case.category.newbie.welfare=新人福利

exception.steam.already.bound=用户已绑定Steam账号，不允许重复绑定
exception.steam.verification.failed=Steam验证失败
exception.steam.bound.to.other.user=该Steam账号已被其他用户绑定
exception.steam.not.bound=未绑定steam账号

# 战斗模式
battle.home.method.pve=机器人对战
battle.home.method.pvp=玩家对战

# Roll房类型
roll.home.type.novice=新人Roll房
roll.home.type.day=日Roll房
roll.home.type.week=周Roll房
roll.home.type.month=月Roll房

# 用户类型
user.type.actual=真实用户
user.type.robot=机器人
user.type.insider=内部用户
user.type.cheat.robot=全职机器人
user.type.anchor=主播

# 阈值类型
threshold.type.charge=充值
threshold.type.consume=消费

# ZBT同步类型
zbt.sync.type.all=全量同步
zbt.sync.type.appoint=指定同步

# 支付类型
pay.type.wechatpay=微信支付
pay.type.alipay=支付宝
pay.type.easy.alipay=支付宝2
pay.type.easy.wechatpay=微信支付2
pay.type.box.alipay=支付宝1
pay.type.box.wechatpay=微信支付1
pay.type.xinfu.bx.pay=巴西信付支付

# 用户包裹来源
user.package.source.open.case=开箱子
user.package.source.percentage=追梦
user.package.source.roll.home=Roll房对战
user.package.source.battle=对战
user.package.source.buy=购买
user.package.source.system=系统
user.package.source.other=其它

# 用户金币变动来源
user.coin.change.source.charge=充值
user.coin.change.source.open.case=开箱子
user.coin.change.source.percentage=追梦
user.coin.change.source.roll.home=Roll房
user.coin.change.source.battle=对战
user.coin.change.source.buy=购买
user.coin.change.source.system=系统
user.coin.change.source.activity=活动
user.coin.change.source.other=其它
user.coin.change.source.red.packet=口令红包
user.coin.change.source.register=注册赠送
user.coin.change.source.first.charge=首充赠送
user.coin.change.source.invite=邀请奖励
user.coin.change.source.activity.card=集卡
user.coin.change.source.daily.charge.red.packet=每日充值红包
user.coin.change.source.check.in=签到奖励
user.coin.change.source.exchange=钻石兑换

# 订单状态
order.status.unpay=未支付
order.status.paid=已支付
order.status.cancel=订单关闭
order.status.refund=已退款

# 每日活动类型
daily.activity.type.sign.in=签到
daily.activity.type.open.case=开箱
daily.activity.type.charge=充值
daily.activity.type.invite.friend=邀请好友
daily.activity.type.online=上线
daily.activity.type.percentage=追梦
daily.activity.type.battle=对战
daily.activity.type.pick.up=取回
daily.activity.type.other=其它

# 卡片收集来源
card.collect.source.sign.in=签到
card.collect.source.open.case=开箱
card.collect.source.charge=充值
card.collect.source.activity=活动
card.collect.source.percentage=追梦
card.collect.source.battle=对战

# 状态
status.disable=禁用
status.enable=启用

# 战斗房状态
battle.home.status.waiting=等待中
battle.home.status.running=进行中
battle.home.status.finish=已结束

# 战斗房箱子状态
battle.home.case.status.unopen=未开启
battle.home.case.status.wait=准备中
battle.home.case.status.open=已打开

# 包裹取回状态
package.pickup.status.pickuping=取回中
package.pickup.status.todo=待处理
package.pickup.status.doing=处理中
package.pickup.status.wait.user=等待接受报价
package.pickup.status.success=成功
package.pickup.status.fail=失败
package.pickup.status.cancel=取消
package.pickup.status.checking=待处理
package.pickup.status.frozen=处理中
package.pickup.status.fail.steam=失败
package.pickup.status.wait.send=等待发货

# 包裹出售状态
package.sell.status.selling=出售中
package.sell.status.success=成功
package.sell.status.fail=失败

# 红包方式
red.packet.method.novice=新人红包
red.packet.method.ordinary=普通红包

# 红包类型
red.packet.type.day=每日红包
red.packet.type.week=每周红包
red.packet.type.month=每月红包

# Roll房开奖方式
roll.home.lottery.method.fixtime=固定时间
roll.home.lottery.method.fixpeople=固定人数

# Roll房方式
roll.home.method.novice=新人Roll房
roll.home.method.ordinary=普通Roll房
roll.home.method.day=每日Roll房
roll.home.method.week=每周Roll房
roll.home.method.month=每月Roll房

# Roll房人数
roll.home.people.one=双人对战
roll.home.people.two=三人对战

# Roll房状态
roll.home.status.unstart=未开始
roll.home.status.start=开奖中
roll.home.status.end=已结束

# ZBT同步状态
zbt.sync.status.syncing=同步中
zbt.sync.status.success=同步成功
zbt.sync.status.fail=同步失败

# 用户购买饰品状态
user.buy.skin.status.buying=购买中
user.buy.skin.status.success=成功
user.buy.skin.status.fail=失败

# 用户钻石变动来源
user.diamond.change.source.exchange=钻石兑换
user.diamond.change.source.sell=出售
user.diamond.change.source.buy=购买
user.diamond.change.source.other=其它

# 用户禁用类型
user.disable.type.exchange=兑换
user.disable.type.pick.up=取回
user.disable.type.open.case=开箱
user.disable.type.percentage=追梦

# 用户兑换状态
user.exchange.status.exchanging=兑换中
user.exchange.status.success=成功
user.exchange.status.fail=失败

# 用户信息变更类型
user.info.change.type.nickname=昵称
user.info.change.type.avatar=头像
user.info.change.type.trade.url=交易链接

# 支付渠道相关异常
response.exception.pay.channel.not.found=未找到当前国家对应的支付渠道



# OCPC Meta 数据类型
ocpc.meta.data.type.click=点击
ocpc.meta.data.type.register=注册
ocpc.meta.data.type.charge=充值

