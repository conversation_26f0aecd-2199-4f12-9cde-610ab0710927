validate.token.error=Token error
response.success=Request succeeded
response.fail=Request failed
response.http_200=Request succeeded
response.http_400=Request failed
response.http_401=Unauthorized
response.http_403=Forbidden
response.http_500=Server error
response.logout.success=Logout successful

# Manager operation responses
response.manager.case.cache.deleted=Case cache deleted successfully
response.manager.skin.cache.deleted=Skin cache deleted successfully
response.manager.case.skin.refresh.success=Case skin refresh successful

# Exception response messages
response.exception.resource.not.found=Resource not found

# Daily Recharge ROLL Room Names
roll.home.name.50=Daily Recharge 50 Bonus ROLL Room
roll.home.name.100=Daily Recharge 100 Bonus ROLL Room
roll.home.name.500=Daily Recharge 500 Bonus ROLL Room
roll.home.name.1000=Daily Recharge 1000 Bonus ROLL Room
roll.home.name.2000=Daily Recharge 2000 Bonus ROLL Room
roll.home.name.5000=Daily Recharge 5000 Bonus ROLL Room
# Weekly Recharge ROLL Room Names
week.roll.home.name.500=Weekly Recharge 500 Bonus ROLL Room
week.roll.home.name.2000=Weekly Recharge 2000 Bonus ROLL Room
week.roll.home.name.5000=Weekly Recharge 5000 Bonus ROLL Room
week.roll.home.name.10000=Weekly Recharge 10000 Bonus ROLL Room
# Exception Messages
exception.too.many.requests=Request failed! Too many requests! Please contact customer service!
exception.parent.field.not.exist.or.deleted = Parent field does not exist or has been deleted
exception.field.not.exist.or.deleted=Field does not exist or has been deleted
exception.case.not.exist.or.offline=Case does not exist or is offline
exception.case.not.openable=This case is not openable
exception.insufficient.coin=Insufficient coin
exception.sms.send.fail.retry.later=Failed to send SMS, please try again later
exception.captcha.not.expired.retry.later=Captcha not expired, please try again later

exception.novice.case.no.remaining.chances=No remaining chances for novice cases, invite users to earn extra opportunities
exception.novice.case.remaining.chances=Only {0} chances left for novice cases
exception.consumeplan.case.no.remaining.chances=No remaining chances for treasure cases
exception.consumeplan.case.remaining.chances=Only {0} chances left for treasure cases
exception.daily.task.insufficient.charge=Recharge {0} coins to participate
exception.daily.task.insufficient.consume=Spend {0} coins to participate
exception.daily.task.already.opened.today=You have already opened {0} today, come back tomorrow
exception.insufficient.coins=Insufficient coins
exception.operation.too.frequent=Operation too frequent
exception.daily.task.config.missing=Daily task configuration missing, please contact customer service

exception.battle.home.cannot.join=Cannot join this battle room
exception.battle.home.already.joined=Already joined, cannot join again
exception.battle.home.room.full=Failed to join the room, it is full
exception.battle.home.not.exist=Battle home does not exist
exception.case.not.battle.box=Not a battle box

exception.percentage.need.charge=Need to recharge to participate in percentage lottery
exception.percentage.skin.not.supported=Not a percentage lottery skin
exception.invalid.parameters=Invalid parameters
exception.skin.not.exist.or.offline=The skin does not exist or has been taken offline
exception.percentage.min.coin.requirement=Minimum 3 coins required for percentage lottery

exception.csgo.skin.quality.not.exist=Skin quality does not exist.
exception.csgo.skin.rarity.not.exist=Skin rarity does not exist.
exception.csgo.skin.exterior.not.exist=Skin exterior does not exist.
exception.csgo.prototype.not.exist=Prototype does not exist.

exception.storage.file.notfound=The file does not exist.
exception.storage.io.error=An IO error occurred.

exception.case.category.not.found=Case category does not exist.
exception.skin.not.found=Skin with ID {0} does not exist.
exception.skin.color.not.found=Color with ID {0} does not exist.
exception.custom.color.required=Custom color cannot be empty.
exception.case.level.probability.invalid=Total probability of level {0} in case {1} is not equal to 1.
exception.case.skin.duplicate=Skin {0} in level {1} of case {2} is different from others.
exception.case.no.levels=Case has no associated levels.
exception.resource.not.exist=Resource does not exist.
exception.case.not.found=Case does not exist or has been deleted.
exception.case.not.exist.create.one=There are no cases yet! Please create one now!



# User related exceptions
exception.user.not.found=User not found or has been deleted.
exception.user.charge.amount.invalid=Invalid charge amount.
exception.user.type.invalid=Invalid user type.
exception.user.robot.add.limit.exceeded=Cannot add more than 10 robots at once.
exception.skin.not.exist=Skin does not exist

# Admin user related exceptions
exception.admin.login.invalid.credentials=Invalid username or password

# Red packet related exceptions
exception.redpacket.code.already.exists=Red packet code already exists

# Security related exceptions
exception.security.not.logged.in=Not logged in

# Skin related exceptions
exception.skin.bound.to.case.cannot.delete=This skin is already bound to a case and cannot be deleted

# Case related exceptions
exception.case.level.not.exist.or.offline=Case level does not exist or is offline
exception.case.skins.not.exist.or.offline=Case skins do not exist or are offline

contact.admin=Please contact customer service.

exception.user.name.empty=Username cannot be empty
exception.user.name.change.limit=Can be modified after
exception.trade.url.empty=Trade URL cannot be empty
exception.trade.url.invalid=Trade URL is invalid
exception.trade.url.same=Trade URL is the same as current
exception.trade.url.bound=Trade URL is already bound to another account
exception.trade.url.change.limit=Can be modified after
exception.avatar.empty=Avatar cannot be empty
exception.user.banned=User banned, contact customer service
exception.algorithm.data.error=Algorithm data error, please reset
exception.user.data.error=User data error, contact customer service
exception.verification.code.error=Verification code error
exception.inventory.not.found=Inventory item not found
exception.inventory.locked=Insufficient inventory
exception.diamond.required=Diamond must be greater than 0
exception.diamond.insufficient=Insufficient diamonds
exception.real.name.error=Real name verification failed

exception.operation.invalid=Invalid operation
exception.function.charge.required=Function available after recharge
exception.inventory.not.owned=Item not owned
exception.inventory.out.of.stock=Out of stock
exception.inventory.already.processed=Item already sold or retrieved
exception.trade.url.not.set=Trade URL not set
exception.user.data.error.contact=User data error, contact customer service
exception.operation.failed=Operation failed
system.disable.exchange.unpaid=Exchange function disabled for unpaid users
system.remark.trade.url.change=Steam trade URL changed

email.login.verify.template=Your verification code is {0}, do not share with others.
email.login.subject=Account Login Verification

exception.admin.add.roll.home.failed=Failed to add Roll room by admin
exception.roll.home.type.not.exist=Roll home type does not exist
exception.roll.home.lottery.method.not.exist=Roll home lottery method does not exist
exception.admin.update.roll.home.failed=Failed to update Roll room by admin
exception.roll.home.not.exist=Roll room does not exist
exception.roll.home.only.unstarted.can.be.updated=Only unstarted roll rooms can be updated
exception.user.type.not.supported=User type is not supported
exception.roll.home.has.ended=Roll room has ended
exception.user.already.joined.roll.home=User has already joined the Roll room
exception.roll.home.is.full=Roll room is full
exception.roll.home.can.not.be.modified.after.draw=Roll room has been drawn, modification is not allowed
exception.user.not.join.roll.home=User has not joined the Roll room
exception.create.roll.home.failed=Failed to create Roll room
exception.roll.home.id.cannot.be.empty=Roll home ID cannot be empty

exception.roll_home.room_not_found=Roll home does not exist
exception.roll_home.password_incorrect=Password is incorrect
exception.roll_home.novice_already_joined=User has already joined a novice Roll room
exception.roll_home.insufficient_charge=Please recharge %s coins to join
exception.roll_home.insufficient_consume=Please spend %s coins to join
exception.roll_home.already_ended=Roll home has already ended
exception.roll_home.user_already_joined=User has already joined
exception.roll_home.full=Roll home is full

exception.product.not.exist=Product does not exist
exception.real.name.auth.required=Real name authentication is required
exception.payment.method.invalid=Invalid payment method
exception.order.creation.failed=Failed to create order

exception.dictionary.id.not.exist=Selected dictionary category is invalid
exception.exchange.rate.not.configured=Exchange rate is not configured in the backend
exception.contact.info.not.configured=Contact information is not configured
exception.user.benefit.not.configured=User benefits are not configured

exception.algorithm.data.abnormal.please.reset=User algorithm data is abnormal, please try resetting and retrying
exception.public.hash.mismatch.secret.hash=Algorithm data error: public hash does not match secret hash

exception.storage.file.not.exist=File does not exist
exception.skin.info.not.exist=Skin information does not exist
exception.card.collect.probability.not.one=The probability of the card collect box is not equal to 1

# Case category related
case.category.treasure.box=Treasure Box
case.category.daily.task.box=Daily Task Box
case.category.battle.exclusive=Battle Exclusive
case.category.newbie.welfare=Newbie Welfare

# Case type enum
case.type.beginner=Beginner Case
case.type.normal=Normal Case
case.type.novice=Novice Case
case.type.ordinary=Ordinary Case
case.type.consumeplan=Consumption Plan Case
case.type.daily.tasks=Daily Tasks Case

exception.steam.already.bound=Steam account already bound, duplicate binding not allowed
exception.steam.verification.failed=Steam verification failed
exception.steam.bound.to.other.user=This Steam account is already bound to another user

# Battle Home Method
battle.home.method.pve=Robot Battle
battle.home.method.pvp=Player Battle

# Roll Home Type
roll.home.type.novice=Novice Roll Room
roll.home.type.day=Daily Roll Room
roll.home.type.week=Weekly Roll Room
roll.home.type.month=Monthly Roll Room

# User Type
user.type.actual=Real User
user.type.robot=Robot
user.type.insider=Insider
user.type.cheat.robot=Full-time Robot
user.type.anchor=Anchor

# Threshold Type
threshold.type.charge=Charge
threshold.type.consume=Consume

# ZBT Sync Type
zbt.sync.type.all=Full Sync
zbt.sync.type.appoint=Appointed Sync

# Pay Type
pay.type.wechatpay=WeChat Pay
pay.type.alipay=Alipay
pay.type.easy.alipay=Alipay 2
pay.type.easy.wechatpay=WeChat Pay 2
pay.type.box.alipay=Alipay 1
pay.type.box.wechatpay=WeChat Pay 1
pay.type.xinfu.bx.pay=Brazil Xinfu Pay

# User Package Source
user.package.source.open.case=Open Case
user.package.source.percentage=Percentage
user.package.source.roll.home=Roll Room Battle
user.package.source.battle=Battle
user.package.source.buy=Purchase
user.package.source.system=System
user.package.source.other=Other

# User Coin Change Source
user.coin.change.source.charge=Charge
user.coin.change.source.open.case=Open Case
user.coin.change.source.percentage=Percentage
user.coin.change.source.roll.home=Roll Room
user.coin.change.source.battle=Battle
user.coin.change.source.buy=Purchase
user.coin.change.source.system=System
user.coin.change.source.activity=Activity
user.coin.change.source.other=Other
user.coin.change.source.red.packet=Red Packet
user.coin.change.source.register=Registration Gift
user.coin.change.source.first.charge=First Charge Gift
user.coin.change.source.invite=Invite Reward
user.coin.change.source.activity.card=Card Collection
user.coin.change.source.daily.charge.red.packet=Daily Charge Red Packet
user.coin.change.source.check.in=Check-in Reward
user.coin.change.source.exchange=Diamond Exchange

# Order Status
order.status.unpay=Unpaid
order.status.paid=Paid
order.status.cancel=Cancelled
order.status.refund=Refunded

# Daily Activity Type
daily.activity.type.sign.in=Sign In
daily.activity.type.open.case=Open Case
daily.activity.type.charge=Charge
daily.activity.type.invite.friend=Invite Friend
daily.activity.type.online=Online
daily.activity.type.percentage=Dream Chase
daily.activity.type.battle=Battle
daily.activity.type.pick.up=Pick Up
daily.activity.type.other=Other

# Card Collect Source
card.collect.source.sign.in=Sign In
card.collect.source.open.case=Open Case
card.collect.source.charge=Charge
card.collect.source.activity=Activity
card.collect.source.percentage=Dream Chase
card.collect.source.battle=Battle

# Status
status.disable=Disabled
status.enable=Enabled

# Battle Home Status
battle.home.status.waiting=Waiting
battle.home.status.running=Running
battle.home.status.finish=Finished

# Battle Home Case Status
battle.home.case.status.unopen=Unopened
battle.home.case.status.wait=Preparing
battle.home.case.status.open=Opened

# Package Pickup Status
package.pickup.status.pickuping=Picking Up
package.pickup.status.todo=To Do
package.pickup.status.doing=Processing
package.pickup.status.wait.user=Waiting for User
package.pickup.status.success=Success
package.pickup.status.fail=Failed
package.pickup.status.cancel=Cancelled
package.pickup.status.checking=Checking
package.pickup.status.frozen=Frozen
package.pickup.status.fail.steam=Steam Failed
package.pickup.status.wait.send=Waiting to Send

# Red Packet Method
red.packet.method.novice=Novice Red Packet
red.packet.method.ordinary=Ordinary Red Packet

# ZBT Sync Status
zbt.sync.status.syncing=Syncing
zbt.sync.status.success=Success
zbt.sync.status.fail=Failed

# Package Sell Status
package.sell.status.selling=Selling
package.sell.status.success=Success
package.sell.status.fail=Failed

# Red Packet Type
red.packet.type.day=Daily Red Packet
red.packet.type.week=Weekly Red Packet
red.packet.type.month=Monthly Red Packet

# Roll Home Lottery Method
roll.home.lottery.method.fixtime=Fixed Time
roll.home.lottery.method.fixpeople=Fixed People

# Roll Home Method
roll.home.method.novice=Novice Roll Room
roll.home.method.ordinary=Ordinary Roll Room
roll.home.method.day=Daily Roll Room
roll.home.method.week=Weekly Roll Room
roll.home.method.month=Monthly Roll Room

# Roll Home People
roll.home.people.one=Two Players Battle
roll.home.people.two=Three Players Battle

# Roll Home Status
roll.home.status.unstart=Not Started
roll.home.status.start=Drawing
roll.home.status.end=Finished

# User Buy Skin Status
user.buy.skin.status.buying=Purchasing
user.buy.skin.status.success=Success
user.buy.skin.status.fail=Failed

# User Diamond Change Source
user.diamond.change.source.exchange=Exchange
user.diamond.change.source.sell=Sell
user.diamond.change.source.buy=Purchase
user.diamond.change.source.other=Other

# User Disable Type
user.disable.type.exchange=Exchange
user.disable.type.pick.up=Pick Up
user.disable.type.open.case=Open Case
user.disable.type.percentage=Dream Chase

# User Exchange Status
user.exchange.status.exchanging=Exchanging
user.exchange.status.success=Success
user.exchange.status.fail=Failed

# User Info Change Type
user.info.change.type.nickname=Nickname
user.info.change.type.avatar=Avatar
user.info.change.type.trade.url=Trade URL

exception.steam.not.bound=Steam account not bound

# Roll Home Related
roll.home.secret.after.lottery=Revealed after lottery

# Response Messages
response.create.success=Created successfully
response.update.success=Updated successfully
response.delete.success=Deleted successfully
response.operation.success=Operation successful

# Storage Related
exception.storage.upload.fail=Upload failed

# More Exception Messages
exception.red.packet.not.exist=Red packet does not exist
exception.red.packet.already.received=Red packet already received
exception.activity.not.exist.or.ended=Activity does not exist or has ended
exception.need.charge.to.receive=Recharge {0} coins to receive
exception.checkin.reward.already.claimed=Check-in reward already claimed

# OCPC Data Types
ocpc.baidu.data.type.click=Click
ocpc.baidu.data.type.register=Register
ocpc.baidu.data.type.charge=Charge

ocpc.google.data.type.click=Click
ocpc.google.data.type.register=Register
ocpc.google.data.type.charge=Charge

# OCPC Meta Data Types
ocpc.meta.data.type.click=Click
ocpc.meta.data.type.register=Register
ocpc.meta.data.type.charge=Charge

# Payment Channel
response.exception.pay.channel.not.found=Payment channel not found for current country

