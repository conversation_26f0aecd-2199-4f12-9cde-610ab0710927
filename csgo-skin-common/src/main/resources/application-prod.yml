spring:
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  # 上传的单个文件的大小最大为3MB
  # 单次请求的所有文件的总大小最大为9MB
  # 如果是想要不限制文件上传的大小,那么就把两个值都设置为-1

  messages:
    basename: i18n/messages
    encoding: UTF-8
    cache-duration: 3600

  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: hxhoddbkzkpsbgji
    protocol: smtp
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
          connectiontimeout: 5000
          timeout: 3000
          writetimeout: 5000

  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    password: 890705liuxin
    url: **********************************************************************************************************************************************
    username: admin
  # redis 配置
  redis:
    database: 0
    host: **********
    port: 6379
    #连接超时时间z
    timeout: 3000
    password: redis_dHfFSJ
    #连接池配置
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1
    #    登录验证码前缀
    prefix:
      login-captcha: LOGIN
      token-admin: TOKEN_ADMIN
      token-front: TOKEN_FRONT
      data-dictionary: DICTIONARTY
      case: CASE
      skin: SKIN
      roll-home: ROLLHOME
      user: USER
      sys: SYS
      battle-home: BATTLE_HOME
      ocpc: OCPC
      activity: ACTIVITY
      black: BLACK
    expire:
      login-captcha: 300
      disable-pickup: 86400
      ocpc-logid-url: 259200
      ocpc-baidu-keyword: 86400

  jackson:
    serialization:
      fail-on-empty-beans: false
    #    time-zone: GMT +8
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
  jpa:
    database: MYSQL
    show-sql: false
  main:
    allow-circular-references: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  rabbitmq:
    host: **********
    port: 5672
    username: admin
    password: mq@6zAi89Vs
    connection-timeout: 15000
    listener:
      simple:
        retry:
          max-attempts: 3
          enabled: true
          initial-interval: 5000
    # 发送确认
    #    publisher-confirms: true
    # 路由失败回调
#    publisher-returns: true
#    template:
#      # 必须设置成true 消息路由失败通知监听者，而不是将消息丢弃
#      mandatory: true
#    listener:
#      simple:
#        # 每次从RabbitMQ获取的消息数量
#        prefetch: 1
#        default-requeue-rejected: false
#        # 每个队列启动的消费者数量
#        concurrency: 1
#        # 每个队列最大的消费者数量
#        max-concurrency: 1
#        # 签收模式为手动签收-那么需要在代码中手动ACK
#        acknowledge-mode: manual
swagger:
  host: 127.0.0.1:5000
  enable: True
  paths: /
jwt:
  expire: 720000
  secret: Yjkdk*#@$sd12cd
logging:
  config: classpath:logback-spring.xml
  file:
    #    path: C:\Users\<USER>\Desktop\log
    path: /home/<USER>/csgo/log
aliyun:
  sms:
    accessKeyId: LTAI5t7aWkyQTcb6U7NVc5Jx
    accesskeySecret: ******************************
    signName: 蒸汽go
    templateCode: SMS_243493192


#roll点范围
roll:
  min: 1
  max: 1000000

rabbitmq:
  # 队列定义
  queue:
    # Roll房队列(不被消费的队列)
    roll-home: roll-home-queue
    # Roll房死信队列
    roll-home-dead-letter: roll-home-dead-letter-queue
    # 饰品取回队列
    skin-pickup: skin-pickup-queue
    # websocket通知队列
    websocket-notify: websocket-notify-queue
    # ocpc 上报
    ocpc: ocpc-queue
    # 订单房队列(不被消费的队列)
    charge-order: charge-order-queue
    # 订单死信队列
    charge-order-dead-letter: charge-order-dead-letter-queue
    # 活动队列(不被消费的队列)
    activity: activity-queue
    # 活动死信队列
    activity-dead-letter: activity-dead-letter-queue
    # 登录日志记录
    userlog: userlog-queue
    # 用户绑定roll房
    roll-home-user-h1: roll-home-user-h1-queue
    # 用户绑定roll房
    roll-home-user-h2: roll-home-user-h2-queue
    # 用户绑定roll房
    roll-home-user-h3: roll-home-user-h3-queue
    # 用户绑定roll房
    roll-home-user-h4: roll-home-user-h4-queue
    # 用户绑定roll房
    roll-home-user-h5: roll-home-user-h5-queue
    # 用户绑定roll房
    roll-home-user-h6: roll-home-user-h6-queue
    # 用户绑定roll房死信
    roll-home-user-dead-letter: roll-home-user-dead-letter-queue
  # 交换机定义
  exchange:
    # Roll房交换机
    csgo: csgo-exchange
    # 死信交换机
    csgo-dead-letter: csgo-dead-letter-exchange

# 微信支付
pay:
  wx:
    appId: ??
    mchId: ??
    mchKey: ??
    notifyUrl: http://127.0.0.1:5002/csgo/api/order/wechat/notify
  #    subAppId: #服务商模式下的子商户公众账号ID
  #    subMchId: #服务商模式下的子商户号
  #    keyPath: # p12证书的位置，可以指定绝对路径，也可以指定类路径（以classpath:开头）
  ali:
    appId: ??
    privateKey: ??
    publicKey: ??
    notifyUrl: http://127.0.0.1:5002/csgo/api/order/ali/notify
    returnUrl: http://127.0.0.1:5002/csgo/api/order/ali/return
    # 签名方式
    signType: RSA2
    # 字符编码格式
    charset: utf-8
    # 支付宝网关，在沙箱应用中获取
    gatewayUrl: openapi.alipaydev.com
  xinfupaybank:
    # 商户号
    machId: 255d87a759af4b1bae76a3a6c820ce87
    # 商户appId
    machAppId: 68970e2c7d2f4625a6451cb46620ffa6
    # 支付key
    paySecret: 0be30f340df34deb9fc4b0a21211c925
    # 代付key
    withdrawalSecret: 0be30f340df34deb9fc4b0a21211c925
    # 收款回调
    payReturnUrl: https://csgo.nova-auto.asia/api/csgo/api/order/xinfuPay/notify
    # 代付回调
    withdrawalReturnUrl: https://csgo.nova-auto.asia/apicsgo/api/order/xinfuPay/withdrawal/notify


schedule:
  interval:
    battleHome: 0/5 * * * * ?
    rollHome: 0/30 * * * * ?
    websocket-ping: 0/10 * * * * ?
    # 0/15 * * * * ?
    zbt: 0/15 * * * * ?
    # 0/15 * * * * ?
    zbtNotify: 0/15 * * * * ?
    sync-skin: 0 0 16 * * ?
    sync-skin-prepare-roll: 0 55 23 * * ?
    clean: 0 0 0 * * ?
    cheatRobot: 0/3 * * * * ?
    statistics: 0/30 * * * * ?
    rollHomeAdd: 0 10 0 * * ?
    weekRollHomeAdd: 0 10 0 * * 1
    refreshCaseSkin: 0 10 16 * * ?


site:
  battle-home-robot-join-wait-time: 30000
  # 皮肤同步时间
  skin-sync-time: 3600000
  # 默认头像
  default-user-avator: https://csgo-go.s3.us-west-2.amazonaws.com/img/default.jpg
  # 订单超时时间
  order-outtime: 900000
  roll:
    min: 1
    max: 1000000
  domain: csgo.nova-auto.asia


# 对象存储配置
storage:
  # 当前工作的对象存储模式，分别是local、aliyun、tencent、qiniu
  active: aws
  # 本地对象存储配置信息
  local:
    storagePath: /home/<USER>/csgo/storage
    address: http://127.0.0.1：5002/api/csgo/admin/storage/show/
  # 阿里云对象存储配置信息
  aliyun:
    endpoint: ??
    accessKeyId: ??
    accessKeySecret: ??
    bucketName: ??
  aws:
    accessKey: ********************
    secretKey: krCz8audc10qKPxv5VLXxDB/87IZ9/PC7+3PhDbB
    region: us-west-2
    bucketName: csgo-go
    endpoint: s3.us-west-2.amazonaws.com

#---------------------- uid-generator   -----------------------
uid-generator:
  #workerId获取方式：none：随机数获取；db：数据库；redis：redis（集群或单机）；zk：（zk集群或单机）
  assigner-mode: db
  #time-bits: 28 #可选配置, 如未指定将采用默认值
  #worker-bits: 22 #可选配置, 如未指定将采用默认值
  #seq-bits: 13 #可选配置, 如未指定将采用默认值
  #epoch-str: 2016-05-20 #可选配置, 如未指定将采用默认值
  #boost-power: 3 #可选配置, 如未指定将采用默认值
  #padding-factor: 50 #可选配置, 如未指定将采用默认值
  #schedule-interval:  #可选配置, 如未指定则不启用此功能
app:
  # 首页地址，用于推广链接
  index-url: http://csgo.nova-auto.asia

# 百度OCPC
ocpc:
  baidu:
    BAIDU_OCPC_URL: https://ocpc.baidu.com/ocpcapi/api/uploadConvertData
    RETRY_TIMES: 3
  qihu:
    BASE_OCPC_URL: https://convert.dop.360.cn/uploadWebConvert
    RETRY_TIMES: 3
  meta:
    BASE_OCPC_URL: https://graph.facebook.com/v19.0/%s/events
    RETRY_TIMES: 3
  google:
    BASE_OCPC_URL: https://www.google-analytics.com/mp/collect?measurement_id=%s&api_secret=%s
    RETRY_TIMES: 3


steam:
  openid:
    return_to: https://csgo.nova-auto.asia/rollRoom
  bind_user:
    return_to: https://csgo.nova-auto.asia/user/recharge


wx:
  mp:
    useRedis: true
    redisConfig:
      host: **********
      port: 6379
      timeout: 3000
      database: 1
      password: redis_dHfFSJ
    configs:
      - appId: wx446446c6d2eae200                  # 第一个公众号的appid
        secret: 84d0999e09ed6b507dd647747811d126   # 公众号的appsecret
        token: hGoDY5qMO3eZamGIjPWPQGk3h53IENw8    # 接口配置里的Token值
        aesKey: 111                          # 接口配置里的EncodingAESKey值
    msgTemplate:
      rollHome: nDk84KJlWZkiw_VRlFJ-a5hJBxdiI04XqVPDfW9Ucm0
      pickUp: nGawRtED7VEemr-6iR4zwMxJGXl3aRapkfG8z1tyxks

danmi:
  sms:
    accountSid: ??
    authtoken: ??
    accountId: ??
    templateid: ??

oauth:
  gmail:
    client-id: ***********-lthncegv9lgon71htetuan3uknkt6e8e.apps.googleusercontent.com
    client-secret: 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
    redirect-uri: https://csgo.nova-auto.asia/rollRoom?authType=gmail
