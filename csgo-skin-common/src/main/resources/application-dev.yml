spring:
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  # 上传的单个文件的大小最大为3MB
  # 单次请求的所有文件的总大小最大为9MB
  # 如果是想要不限制文件上传的大小,那么就把两个值都设置为-1

  messages:
    basename: i18n/messages
    encoding: UTF-8
    cache-duration: 3600

  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    password: 1qaz@WSX
    url: ****************************************************************************************************
    username: csgo
  # redis 配置
  redis:
    database: 0
    host: localhost
    port: 6379
    #连接超时时间
    timeout: 3000
    password:
    #连接池配置
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1
    #    登录验证码前缀
    prefix:
      login-captcha: LOGIN
      token-admin: TOKEN_ADMIN
      token-front: TOKEN_FRONT
      data-dictionary: DICTIONARTY
      case: CASE
      skin: SKIN
      roll-home: ROLLHOME
      user: USER
      sys: SYS
      battle-home: BATTLE_HOME
      ocpc: OCPC
      activity: ACTIVITY
      black: BLACK
    expire:
      login-captcha: 60
      disable-pickup: 86400
      ocpc-logid-url: 259200
      ocpc-baidu-keyword: 86400

  jackson:
    serialization:
      fail-on-empty-beans: false
    #    time-zone: GMT +8
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
  jpa:
    database: MYSQL
    show-sql: false
  main:
    allow-circular-references: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  rabbitmq:
    host: **************
    port: 5672
    username: admin
    password: Isuperone#123
    connection-timeout: 15000
    listener:
      simple:
        retry:
          max-attempts: 3
          enabled: true
          initial-interval: 5000
    # 发送确认
    #    publisher-confirms: true
    # 路由失败回调
  web:
    locale: zh_CN
    locale-resolver: accept_header
#    publisher-returns: true
#    template:
#      # 必须设置成true 消息路由失败通知监听者，而不是将消息丢弃
#      mandatory: true
#    listener:
#      simple:
#        # 每次从RabbitMQ获取的消息数量
#        prefetch: 1
#        default-requeue-rejected: false
#        # 每个队列启动的消费者数量
#        concurrency: 1
#        # 每个队列最大的消费者数量
#        max-concurrency: 1
#        # 签收模式为手动签收-那么需要在代码中手动ACK
#        acknowledge-mode: manual
swagger:
  host: 127.0.0.1:5000
  enable: True
  paths: /
jwt:
  expire: 720000
  secret: Yjkdk*#@$sd12cd
logging:
  config: classpath:logback-spring.xml
  file:
    #    path: C:\Users\<USER>\Desktop\log
    path: /home/<USER>/csgo/log
aliyun:
  sms:
    accessKeyId: LTAI5t7aWkyQTcb6U7NVc5Jx
    accesskeySecret: ******************************
    signName: 蒸汽go
    templateCode: SMS_243493192


#roll点范围
roll:
  min: 1
  max: 1000000

rabbitmq:
  # 队列定义
  queue:
    # Roll房队列(不被消费的队列)
    roll-home: roll-home-queue
    # Roll房死信队列
    roll-home-dead-letter: roll-home-dead-letter-queue
    # 饰品取回队列
    skin-pickup: skin-pickup-queue
    # websocket通知队列
    websocket-notify: websocket-notify-queue
    # ocpc 上报
    ocpc: ocpc-queue
    # 订单房队列(不被消费的队列)
    charge-order: charge-order-queue
    # 订单死信队列
    charge-order-dead-letter: charge-order-dead-letter-queue
    # 活动队列(不被消费的队列)
    activity: activity-queue
    # 活动死信队列
    activity-dead-letter: activity-dead-letter-queue
    # 登录日志记录
    userlog: userlog-queue
    # 用户绑定roll房
    roll-home-user-h1: roll-home-user-h1-queue
    # 用户绑定roll房
    roll-home-user-h2: roll-home-user-h2-queue
    # 用户绑定roll房
    roll-home-user-h3: roll-home-user-h3-queue
    # 用户绑定roll房
    roll-home-user-h4: roll-home-user-h4-queue
    # 用户绑定roll房
    roll-home-user-h5: roll-home-user-h5-queue
    # 用户绑定roll房
    roll-home-user-h6: roll-home-user-h6-queue
    # 用户绑定roll房死信
    roll-home-user-dead-letter: roll-home-user-dead-letter-queue
  # 交换机定义
  exchange:
    # Roll房交换机
    csgo: csgo-exchange
    # 死信交换机
    csgo-dead-letter: csgo-dead-letter-exchange

# 微信支付
pay:
  wx:
    appId: wx2e6f792f6f7152e7
    mchId: 1523061261
    mchKey: 3F5DBd6LqFuhkjul5d5cGUUX3xtXRP06
    notifyUrl: http://client.test.steamgo1.com:8001/csgo/api/order/wechat/notify
  #    subAppId: #服务商模式下的子商户公众账号ID
  #    subMchId: #服务商模式下的子商户号
  #    keyPath: # p12证书的位置，可以指定绝对路径，也可以指定类路径（以classpath:开头）
  ali:
    appId: 2021000122630829
    privateKey: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCSi47sFwKMpXMwQqb4sBiXmf1ppAE73BpuIGq3JUmc0b+3zBG8KGcX4HbfNYvVvk+N/418tQTma+hIixCTAqmbuaQA6+Qul3P54Y45DjoZuTtNVa3Eb9+cuowHA+QyTrBLkzBQ+JlEGf7l184B2i9oowoQY9EMWyxp4vCQhR6HZK6driTJQ+CPy1OWe8qxEx1ppPDSPVgaiITeHvQwzxbq9iU4MYY0LtPoAngzAU/X2h2D5LJv9VE6Nv3FptCN8Pq7vRYVZMNcwVjnsehwUuxZRz/nK+EJMSXEgwsSUX5FDcMlZ2tnulKpYPqF1t1g5V3IXnRVgFFK1aSiuDurg/MPAgMBAAECggEAUtim8f2IKW8Xkb78mjcjkAE0bFaVg/vIzV1yNxvTY96IXiX2GMNWlP8G9OVWqn2uh/5jRI8puAJlDS3hLtsge7MVbpftJhHlNO049DSA0C4RwoSfeiAs8HPd2dFvXqlxrSgG9p+0NfXkTpgUFKP+jU8uz1k+w5d/olTz3yACxFNgNAOMB/HJLJoSVNhOcd1M97SHHyWmwaW2m7XXZOKAljKBIuxSOjCL1RIy08qztHfcb0n1EwkfxQPi/Vnjmw4VzGTSQvvRbd6KAZtLP2K0nDKcdG0HnuWTx7ZodDfuiI7k9RcwSjKKHTs72YcQkBwvls+14w2TAENxToRjgkld4QKBgQDcgw8yqlnZLJF475wbnjUxwz+pe6bPY5QfYHV8yXBl1ksVh6sUhYKHdz2jiD+8NVx9F53MwJTHrnUbgl5TWB920h5+VT84AHe38vnVRsNWC19zVr6Ji2P0+DsOgm23rvNpePdTm1GBJ/t7Wqfvk5FQyV4xIRHgpJ0mcrvlCgEL1wKBgQCqIR4cApmX9gSy/AlvvUJIiRMo0+ie25mPstXDVcdgIxZ+/jhir1vQzZeLyOmu8ncZB1bM8SCXKCa1WxZm3zdCh+LXUfAzFfnWdnVLdPjuPkR+Vy2J0kGHi7d+BAJvbOz/x7AwxiE7o2HeYKEoUW8xlRT8h0DdMy/YWAyvsNSriQKBgFg1dRbnXhmp4mdmMiQ6HT1SxVyewhOj2dTTWSwZyIR0PITYSG9lGNnnsRiBoD1Be0Xu9Ii7YMif2OiwNdtRHQUC+H/AyhE03q8O2zGdpPX+hqg0wvNydwG/aqBbPFiF+4jtgsJHF0t7GvY1RYhol3ChnLQ6blRWhSakLtVoMxLxAoGAWfrmF95wKeUemyOeQhERS5fkIEXj1JFE6LGnSYbGylwEe+otHFSu9QKn0nDS14q53uz4xbAKkqfTwTuJ36LTcUa5fTSDHCHVX86LPvuA5VDdnQyysyAXNH967xVjzFcKCkYtM762uPoyuxDUa6SWcNzumplWfyeheIUpQIsAEDECgYEApq/ELbmeZGZnoSVPTpVhQIy4ZjicFveyR1VFION/NFrhByemuwrGiE6s8fVU73b/RwtAc3jeQQlzH4Sxy0gjqyQKrW8vG3ZyAYI8vXwV4MLRrIilQ8n9aJ9a4y/Q+1VHi8pWzuA56iIQobKk0JYr5ncJu6v8d/hamxYWyEpqYKI=
    publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmUAazR+2fHK7ll2XygyjlEuoGjfgCNVEfw1vEHH11hZnRcwivXS4WSWF+REmUXDR3xFbCGFp2jmp7PMwok8FpuJVxXFzgMBmX7aDogSU4HsBniUPq7Qy+fN85YG4bOjRuJMEpQ9vrqJw2hmeX1xwY0KNrUG+uz5+vzSvNBsuUlSAvHStGEJMIJ+qVWUkgf++nzQeGmX6XhUHn4IMeYl+eeGC+9ZGBgKSZ8sox7taQ2eJ9avPKz4z3yf3qMo2OQJ6oPyAKy6HUizazUOyQx7mggpcKhbkymYKBlwx2lSyh8z1NRaJmRhzGDA9iQTXxlkDyWIjrijO1MaJc2Q5PM7lRQIDAQAB
    notifyUrl: http://test.client.steamgo1.com/csgo/api/order/ali/notify
    returnUrl: http://test.client.steamgo1.com/csgo/api/order/ali/return
    # 签名方式
    signType: RSA2
    # 字符编码格式
    charset: utf-8
    # 支付宝网关，在沙箱应用中获取
    gatewayUrl: openapi.alipaydev.com

schedule:
  interval:
    battleHome: 0/5 * * * * ?
    rollHome: 0/10 * * * * ?
    websocket-ping: 0/10 * * * * ?
    zbt: 0/8 * * * * ?
    zbtNotify: 0/5 * * * * ?
    sync-skin: 0 0 16 * * ?
    sync-skin-prepare-roll: 0 55 23 * * ?
    clean: 0 0 0 * * ?
    cheatRobot: 0/3 * * * * ?
    statistics: 0/30 * * * * ?
    rollHomeAdd: 0 10 0 * * ?
    #    rollHomeAdd: 0 * * * * ?
    weekRollHomeAdd: 0 10 0 * * 1
    #    weekRollHomeAdd: 0 * * * * ?
    refreshCaseSkin: 0 10 16 * * ?


site:
  battle-home-robot-join-wait-time: 30000
  skin-sync-time: 3600000
  default-user-avator: https://kk8skins.oss-cn-hangzhou.aliyuncs.com/img/default.jpg
  order-outtime: 900000
  roll:
    min: 1
    max: 1000000


# 对象存储配置
storage:
  # 当前工作的对象存储模式，分别是local、aliyun、tencent、qiniu
  active: aliyun
  # 本地对象存储配置信息
  local:
    storagePath: /home/<USER>/csgo/storage
    address: http://************:81/api/csgo/admin/storage/show/
  # 阿里云对象存储配置信息
  aliyun:
    endpoint: oss-cn-hangzhou.aliyuncs.com
    accessKeyId: LTAI5tJ4vipPjuiVzNuo8CvK
    accessKeySecret: ******************************
    bucketName: kk8skins

#---------------------- uid-generator   -----------------------
uid-generator:
  #workerId获取方式：none：随机数获取；db：数据库；redis：redis（集群或单机）；zk：（zk集群或单机）
  assigner-mode: db
  #time-bits: 28 #可选配置, 如未指定将采用默认值
  #worker-bits: 22 #可选配置, 如未指定将采用默认值
  #seq-bits: 13 #可选配置, 如未指定将采用默认值
  #epoch-str: 2016-05-20 #可选配置, 如未指定将采用默认值
  #boost-power: 3 #可选配置, 如未指定将采用默认值
  #padding-factor: 50 #可选配置, 如未指定将采用默认值
  #schedule-interval:  #可选配置, 如未指定则不启用此功能
app:
  index-url: http://127.0.0.1

ocpc:
  baidu:
    BAIDU_OCPC_URL: https://ocpc.baidu.com/ocpcapi/api/uploadConvertData
    RETRY_TIMES: 3
  qihu:
    BASE_OCPC_URL: https://convert.dop.360.cn/uploadWebConvert
    RETRY_TIMES: 3
  meta:
    BASE_OCPC_URL: https://graph.facebook.com/v18.0/%s/events
    RETRY_TIMES: 3
  google:
    BASE_OCPC_URL: https://www.google-analytics.com/mp/collect?measurement_id=%s&api_secret=%s
    RETRY_TIMES: 3

steam:
  openid:
    return_to: http://127.0.0.1:5002/csgo/api/auth/steam/callback?domain_name=


wx:
  mp:
    useRedis: true
    redisConfig:
      host: localhost
      port: 6379
      timeout: 3000
      database: 1
      password:
    configs:
      - appId: wx446446c6d2eae200                  # 第一个公众号的appid
        secret: 84d0999e09ed6b507dd647747811d126   # 公众号的appsecret
        token: hGoDY5qMO3eZamGIjPWPQGk3h53IENw8    # 接口配置里的Token值
        aesKey: 111                          # 接口配置里的EncodingAESKey值
    msgTemplate:
      rollHome: nDk84KJlWZkiw_VRlFJ-a5hJBxdiI04XqVPDfW9Ucm0
      pickUp: nGawRtED7VEemr-6iR4zwMxJGXl3aRapkfG8z1tyxks

danmi:
  sms:
    accountSid: 5f110f732edfb4a324066a595319cb52
    authtoken: 63b128fd48bb001dc6e1ece19116ef52
    accountId: ************
    templateid: *************

oauth:
  gmail:
    client-id: your-gmail-client-id
    client-secret: your-gmail-client-secret
    redirect-uri: http://localhost:5002/csgo/api/auth/gmail/callback
