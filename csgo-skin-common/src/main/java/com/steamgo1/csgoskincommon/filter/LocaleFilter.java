package com.steamgo1.csgoskincommon.filter;

import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.servlet.LocaleResolver;

import javax.servlet.*;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.Locale;
import java.util.logging.Logger;

/**
 * 国际化过滤器，用于从请求头、请求参数或 Cookie 中获取语言信息并设置 Locale。
 */
public class LocaleFilter implements Filter {

    private static final Logger logger = Logger.getLogger(LocaleFilter.class.getName());

    private final LocaleResolver localeResolver;

    public LocaleFilter(LocaleResolver localeResolver) {
        this.localeResolver = localeResolver;
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        if (request instanceof HttpServletRequest && response instanceof HttpServletResponse) {
            HttpServletRequest httpRequest = (HttpServletRequest) request;
            HttpServletResponse httpResponse = (HttpServletResponse) response;

            // 尝试从请求参数获取语言信息
            String langParam = httpRequest.getParameter("lang");
            // 尝试从请求头获取语言信息
            String langHeader = httpRequest.getHeader("Accept-Language");
            // 尝试从 Cookie 获取语言信息
            String langCookie = getLangFromCookie(httpRequest);

            Locale locale = determineLocale(langParam, langHeader, langCookie);

            if (locale != null) {
                localeResolver.setLocale(httpRequest, httpResponse, locale);
                //logger.info(" <========> Current Language = " + locale.toString());
                LocaleContextHolder.setLocale(locale);
            } else {
                LocaleContextHolder.setDefaultLocale(Locale.CHINESE);
                //logger.info(" <#########> Current Language = " + LocaleContextHolder.getLocale().toString());
            }
        }

        try {
            chain.doFilter(request, response);
        } finally {
            LocaleContextHolder.resetLocaleContext();
        }
    }

    /**
     * 从 Cookie 中获取语言信息
     *
     * @param request HttpServletRequest 对象
     * @return 语言信息，如果没有则返回 null
     */
    private String getLangFromCookie(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if ("lang".equals(cookie.getName())) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }

    /**
     * 确定最终使用的 Locale，优先级：请求参数 > 请求头 > Cookie
     *
     * @param langParam  请求参数中的语言信息
     * @param langHeader 请求头中的语言信息
     * @param langCookie Cookie 中的语言信息
     * @return 最终确定的 Locale，如果都没有则返回 null
     */
    private Locale determineLocale(String langParam, String langHeader, String langCookie) {
        if (langParam != null && !langParam.isEmpty()) {
            // 替换下划线为连字符
            langParam = langParam.replace("_", "-");
            return Locale.forLanguageTag(langParam);
        } else if (langHeader != null && !langHeader.isEmpty()) {
            // 替换下划线为连字符
            langHeader = langHeader.replace("_", "-");
            return Locale.lookup(Locale.LanguageRange.parse(langHeader), Arrays.asList(Locale.getAvailableLocales()));
        } else if (langCookie != null && !langCookie.isEmpty()) {
            // 替换下划线为连字符
            langCookie = langCookie.replace("_", "-");
            return Locale.forLanguageTag(langCookie);
        }
        return null;
    }

    public static void main(String[] args) {
        //Locale locale = Locale.lookup(Locale.LanguageRange.parse("en-US;q=0.9,zh-CN,zh;q=0.8,en;q=0.7,en-GB;q=0.6"), Arrays.asList(Locale.getAvailableLocales()));
        Locale locale = Locale.lookup(Locale.LanguageRange.parse("en-US"), Arrays.asList(Locale.getAvailableLocales()));
        System.out.println(locale.getDisplayLanguage());
    }
}
