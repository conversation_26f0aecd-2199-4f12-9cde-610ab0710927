package com.steamgo1.csgoskincommon.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 多语言字段注解
 * 用于标记实体类中的多语言字段
 * 
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface I18nField {
    
    /**
     * 字段基础名称（不包含语言后缀）
     * 例如：name（对应name_zh, name_en）
     */
    String baseName();
    
    /**
     * 是否为默认字段（兼容字段）
     * 默认字段在多语言字段为空时作为fallback
     */
    boolean isDefault() default false;
    
    /**
     * 字段描述
     */
    String description() default "";
}
