package com.steamgo1.csgoskincommon.config;

import org.springframework.amqp.core.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * roll房 rabbit配置
 */
@Configuration
public class RabbitConfig {
    @Value("${rabbitmq.queue.roll-home-user-h1}")
    private String rollHomeUserH1;
    @Value("${rabbitmq.queue.roll-home-user-h2}")
    private String rollHomeUserH2;
    @Value("${rabbitmq.queue.roll-home-user-h3}")
    private String rollHomeUserH3;
    @Value("${rabbitmq.queue.roll-home-user-h4}")
    private String rollHomeUserH4;
    @Value("${rabbitmq.queue.roll-home-user-h5}")
    private String rollHomeUserH5;
    @Value("${rabbitmq.queue.roll-home-user-h6}")
    private String rollHomeUserH6;

    @Value("${rabbitmq.queue.roll-home-user-dead-letter}")
    private String rollHomeUserDeadLetter;

    @Value("${rabbitmq.queue.userlog}")
    private String userlog;

    @Value("${rabbitmq.queue.roll-home}")
    private String rollHomeQueueName;

    @Value("${rabbitmq.queue.roll-home-dead-letter}")
    private String rollHomeDeadLetterQueueName;

    @Value("${rabbitmq.exchange.csgo}")
    private String exchangeName;

    @Value("${rabbitmq.exchange.csgo-dead-letter}")
    private String deadLetterExchangeName;

    @Value("${rabbitmq.queue.skin-pickup}")
    private String skinPickUpQueueName;

    @Value("${rabbitmq.queue.websocket-notify}")
    private String websocketNotifyQueueName;

    @Value("${rabbitmq.queue.ocpc}")
    private String ocpcQueueName;

    @Value("${rabbitmq.queue.charge-order}")
    private String chargeOrderQueueName;

    @Value("${rabbitmq.queue.charge-order-dead-letter}")
    private String chargeOrderDeadLetterQueueName;

    @Value("${rabbitmq.queue.activity}")
    private String activityQueueName;

    @Value("${rabbitmq.queue.activity-dead-letter}")
    private String activityDeadLetterQueueName;

    /**
     * 普通交换机定义
     *
     * @return
     */
    @Bean
    public Exchange exchange() {
        return ExchangeBuilder
                .topicExchange(exchangeName)
                .durable(true)
                .build();
    }

    /**
     * 死信交换机定义
     *
     * @return
     */
    @Bean
    public Exchange deadLetterExchange() {
        return ExchangeBuilder
                .topicExchange(deadLetterExchangeName)
                .durable(true)
                .build();
    }


    /**
     * Roll房队列定义
     *
     * @return
     */
    @Bean
    public Queue rollHomeQueue() {
        return QueueBuilder
                .durable(rollHomeQueueName)
                //声明该队列的死信消息发送到的 交换机 （队列添加了这个参数之后会自动与该交换机绑定，并设置路由键，不需要开发者手动设置)
                .withArgument("x-dead-letter-exchange", deadLetterExchangeName)
                //声明该队列死信消息在交换机的 路由键
                .withArgument("x-dead-letter-routing-key", "rollhome-dead-letter-routing-key")
                .build();
    }

    /**
     * Roll房队列绑定
     *
     * @param rollHomeQueue
     * @param exchange
     * @return
     */
    @Bean
    public Binding rollHomeBinDing(Queue rollHomeQueue, Exchange exchange) {
        return BindingBuilder
                .bind(rollHomeQueue)
                .to(exchange)
                .with("rollhome.*")
                .noargs();
    }

    /**
     * Roll房死信队列定义
     * 用户队列user-queue的死信投递到死信交换机`common-dead-letter-exchange`后再投递到该队列
     * 用这个队列来接收user-queue的死信消息
     *
     * @return
     */
    @Bean
    public Queue rollHomeLetterQueue() {
        return QueueBuilder
                .durable(rollHomeDeadLetterQueueName)
                .build();
    }


    /**
     * Roll房死信队列绑定交换机
     *
     * @param rollHomeLetterQueue
     * @param deadLetterExchange
     * @return
     */
    @Bean
    public Binding rollHomeDeadLetterBinding(Queue rollHomeLetterQueue, Exchange deadLetterExchange) {
        return BindingBuilder
                .bind(rollHomeLetterQueue)
                .to(deadLetterExchange)
                .with("rollhome-dead-letter-routing-key")
                .noargs();
    }


    /**
     * 饰品取回队列定义
     *
     * @return
     */
    @Bean
    public Queue skinPickUpQueue() {
        return QueueBuilder
                .durable(skinPickUpQueueName)
                .build();
    }

    /**
     * 饰品取回队列绑定
     *
     * @param skinPickUpQueue
     * @param exchange
     * @return
     */
    @Bean
    public Binding skinPickUpBinDing(Queue skinPickUpQueue, Exchange exchange) {
        return BindingBuilder
                .bind(skinPickUpQueue)
                .to(exchange)
                .with("skin.pickup")
                .noargs();
    }


    /**
     * websocket通知队列定义
     *
     * @return
     */
    @Bean
    public Queue websocketNotifyQueue() {
        return QueueBuilder
                .durable(websocketNotifyQueueName)
                .build();
    }

    /**
     * websocket通知队列绑定
     *
     * @param websocketNotifyQueue
     * @param exchange
     * @return
     */
    @Bean
    public Binding websocketNotifyBinDing(Queue websocketNotifyQueue, Exchange exchange) {
        return BindingBuilder
                .bind(websocketNotifyQueue)
                .to(exchange)
                .with("websocket.*")
                .noargs();
    }

    /**
     * ocpc队列定义
     *
     * @return
     */
    @Bean
    public Queue ocpcQueue() {
        return QueueBuilder
                .durable(ocpcQueueName)
                .build();
    }

    /**
     * ocpc队列绑定
     *
     * @param ocpcQueue
     * @param exchange
     * @return
     */
    @Bean
    public Binding ocpcBinDing(Queue ocpcQueue, Exchange exchange) {
        return BindingBuilder
                .bind(ocpcQueue)
                .to(exchange)
                .with("ocpc.*.*")
                .noargs();
    }

    /**
     * 订单队列定义
     *
     * @return
     */
    @Bean
    public Queue chargeOrderQueue() {
        return QueueBuilder
                .durable(chargeOrderQueueName)
                //声明该队列的死信消息发送到的 交换机 （队列添加了这个参数之后会自动与该交换机绑定，并设置路由键，不需要开发者手动设置)
                .withArgument("x-dead-letter-exchange", deadLetterExchangeName)
                //声明该队列死信消息在交换机的 路由键
                .withArgument("x-dead-letter-routing-key", "charge-order-dead-letter-routing-key")
                .build();
    }

    /**
     * 订单队列绑定
     *
     * @param chargeOrderQueue
     * @param exchange
     * @return
     */
    @Bean
    public Binding chargeOrderBinDing(Queue chargeOrderQueue, Exchange exchange) {
        return BindingBuilder
                .bind(chargeOrderQueue)
                .to(exchange)
                .with("order.*")
                .noargs();
    }

    /**
     * 订单死信队列定义
     * 用户队列user-queue的死信投递到死信交换机`common-dead-letter-exchange`后再投递到该队列
     * 用这个队列来接收user-queue的死信消息
     *
     * @return
     */
    @Bean
    public Queue chargeOrderLetterQueue() {
        return QueueBuilder
                .durable(chargeOrderDeadLetterQueueName)
                .build();
    }


    /**
     * 订单死信队列绑定交换机
     *
     * @param chargeOrderLetterQueue
     * @param deadLetterExchange
     * @return
     */
    @Bean
    public Binding chargeOrderDeadLetterBinding(Queue chargeOrderLetterQueue, Exchange deadLetterExchange) {
        return BindingBuilder
                .bind(chargeOrderLetterQueue)
                .to(deadLetterExchange)
                .with("charge-order-dead-letter-routing-key")
                .noargs();
    }

    /**
     * 活动队列定义
     *
     * @return
     */
    @Bean
    public Queue activityQueue() {
        return QueueBuilder
                .durable(activityQueueName)
                //声明该队列的死信消息发送到的 交换机 （队列添加了这个参数之后会自动与该交换机绑定，并设置路由键，不需要开发者手动设置)
                .withArgument("x-dead-letter-exchange", deadLetterExchangeName)
                //声明该队列死信消息在交换机的 路由键
                .withArgument("x-dead-letter-routing-key", "activity-dead-letter-routing-key")
                .build();
    }

    /**
     * 活动队列绑定
     *
     * @param activityQueue
     * @param exchange
     * @return
     */
    @Bean
    public Binding activityBinDing(Queue activityQueue, Exchange exchange) {
        return BindingBuilder
                .bind(activityQueue)
                .to(exchange)
                .with("activity.*")
                .noargs();
    }

    /**
     * 活动死信队列定义
     * 用户队列user-queue的死信投递到死信交换机`common-dead-letter-exchange`后再投递到该队列
     * 用这个队列来接收user-queue的死信消息
     *
     * @return
     */
    @Bean
    public Queue activityDeadLetterQueueName() {
        return QueueBuilder
                .durable(activityDeadLetterQueueName)
                .build();
    }


    /**
     * 活动死信队列绑定交换机
     *
     * @param activityDeadLetterQueueName
     * @param deadLetterExchange
     * @return
     */
    @Bean
    public Binding activityDeadLetterBinding(Queue activityDeadLetterQueueName, Exchange deadLetterExchange) {
        return BindingBuilder
                .bind(activityDeadLetterQueueName)
                .to(deadLetterExchange)
                .with("activity-dead-letter-routing-key")
                .noargs();
    }

    @Bean
    public Queue userLoginQueue() {
        return QueueBuilder
                .durable(userlog)
                .build();
    }

    @Bean
    public Binding userLoginBinDing(Queue userLoginQueue, Exchange exchange) {
        return BindingBuilder
                .bind(userLoginQueue)
                .to(exchange)
                .with("userlog.*")
                .noargs();
    }

    @Bean
    public Queue rollHomeUserOneQueue() {
        return QueueBuilder
                .durable(rollHomeUserH1)
                //声明该队列的死信消息发送到的 交换机 （队列添加了这个参数之后会自动与该交换机绑定，并设置路由键，不需要开发者手动设置)
                .withArgument("x-dead-letter-exchange", deadLetterExchangeName)
                //声明该队列死信消息在交换机的 路由键
                .withArgument("x-dead-letter-routing-key", "roll-home-user-dead-letter-routing-key")
                .build();
    }

    @Bean
    public Binding rollHomeUserOneBinDing(Queue rollHomeUserOneQueue, Exchange exchange) {
        return BindingBuilder
                .bind(rollHomeUserOneQueue)
                .to(exchange)
                .with("rollhomeuser.1h.*")
                .noargs();
    }

    @Bean
    public Queue rollHomeUserTwoQueue() {
        return QueueBuilder
                .durable(rollHomeUserH2)
                //声明该队列的死信消息发送到的 交换机 （队列添加了这个参数之后会自动与该交换机绑定，并设置路由键，不需要开发者手动设置)
                .withArgument("x-dead-letter-exchange", deadLetterExchangeName)
                //声明该队列死信消息在交换机的 路由键
                .withArgument("x-dead-letter-routing-key", "roll-home-user-dead-letter-routing-key")
                .build();
    }

    @Bean
    public Binding rollHomeUserTwoBinDing(Queue rollHomeUserTwoQueue, Exchange exchange) {
        return BindingBuilder
                .bind(rollHomeUserTwoQueue)
                .to(exchange)
                .with("rollhomeuser.2h.*")
                .noargs();
    }

    @Bean
    public Queue rollHomeUserThreeQueue() {
        return QueueBuilder
                .durable(rollHomeUserH3)
                //声明该队列的死信消息发送到的 交换机 （队列添加了这个参数之后会自动与该交换机绑定，并设置路由键，不需要开发者手动设置)
                .withArgument("x-dead-letter-exchange", deadLetterExchangeName)
                //声明该队列死信消息在交换机的 路由键
                .withArgument("x-dead-letter-routing-key", "roll-home-user-dead-letter-routing-key")
                .build();
    }

    @Bean
    public Binding rollHomeUserThreeBinDing(Queue rollHomeUserThreeQueue, Exchange exchange) {
        return BindingBuilder
                .bind(rollHomeUserThreeQueue)
                .to(exchange)
                .with("rollhomeuser.3h.*")
                .noargs();
    }

    @Bean
    public Queue rollHomeUserfourQueue() {
        return QueueBuilder
                .durable(rollHomeUserH4)
                //声明该队列的死信消息发送到的 交换机 （队列添加了这个参数之后会自动与该交换机绑定，并设置路由键，不需要开发者手动设置)
                .withArgument("x-dead-letter-exchange", deadLetterExchangeName)
                //声明该队列死信消息在交换机的 路由键
                .withArgument("x-dead-letter-routing-key", "roll-home-user-dead-letter-routing-key")
                .build();
    }

    @Bean
    public Binding rollHomeUserfourBinDing(Queue rollHomeUserfourQueue, Exchange exchange) {
        return BindingBuilder
                .bind(rollHomeUserfourQueue)
                .to(exchange)
                .with("rollhomeuser.4h.*")
                .noargs();
    }

    @Bean
    public Queue rollHomefiveUserQueue() {
        return QueueBuilder
                .durable(rollHomeUserH5)
                //声明该队列的死信消息发送到的 交换机 （队列添加了这个参数之后会自动与该交换机绑定，并设置路由键，不需要开发者手动设置)
                .withArgument("x-dead-letter-exchange", deadLetterExchangeName)
                //声明该队列死信消息在交换机的 路由键
                .withArgument("x-dead-letter-routing-key", "roll-home-user-dead-letter-routing-key")
                .build();
    }

    @Bean
    public Binding rollHomefiveUserBinDing(Queue rollHomefiveUserQueue, Exchange exchange) {
        return BindingBuilder
                .bind(rollHomefiveUserQueue)
                .to(exchange)
                .with("rollhomeuser.5h.*")
                .noargs();
    }

    @Bean
    public Queue rollHomeSixUserQueue() {
        return QueueBuilder
                .durable(rollHomeUserH6)
                //声明该队列的死信消息发送到的 交换机 （队列添加了这个参数之后会自动与该交换机绑定，并设置路由键，不需要开发者手动设置)
                .withArgument("x-dead-letter-exchange", deadLetterExchangeName)
                //声明该队列死信消息在交换机的 路由键
                .withArgument("x-dead-letter-routing-key", "roll-home-user-dead-letter-routing-key")
                .build();
    }

    @Bean
    public Binding rollHomeSixUserBinDing(Queue rollHomeSixUserQueue, Exchange exchange) {
        return BindingBuilder
                .bind(rollHomeSixUserQueue)
                .to(exchange)
                .with("rollhomeuser.6h.*")
                .noargs();
    }

    @Bean
    public Queue rollHomeUserDeadLetterQueue() {
        return QueueBuilder
                .durable(rollHomeUserDeadLetter)
                .build();
    }

    @Bean
    public Binding rollHomeUserDeadLetterBinDing(Queue rollHomeUserDeadLetterQueue, Exchange deadLetterExchange) {
        return BindingBuilder
                .bind(rollHomeUserDeadLetterQueue)
                .to(deadLetterExchange)
                .with("roll-home-user-dead-letter-routing-key")
                .noargs();
    }
}
