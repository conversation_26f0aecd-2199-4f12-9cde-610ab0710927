package com.steamgo1.csgoskincommon.config.storage;

import com.steamgo1.csgoskincommon.service.StorageService;
import com.steamgo1.csgoskincommon.service.impl.AliyunStorage;
import com.steamgo1.csgoskincommon.service.impl.LocalStorage;
import com.steamgo1.csgoskincommon.service.impl.S3Storage;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties(StorageProperties.class)
public class StorageAutoConfiguration {

    private final StorageProperties properties;

    public StorageAutoConfiguration(StorageProperties properties) {
        this.properties = properties;
    }

    @Bean
    public StorageService storageService() {
        StorageService storageService = new StorageService();
        String active = this.properties.getActive();
        storageService.setActive(active);
        if (active.equals("local")) {
            storageService.setStorage(localStorage());
        } else if (active.equals("aliyun")) {
            storageService.setStorage(aliyunStorage());
        } else if (active.equals("aws")) {
            storageService.setStorage(s3Storage());
        } else {
            throw new RuntimeException("当前存储模式 " + active + " 不支持");
        }

        return storageService;
    }

    @Bean
    public LocalStorage localStorage() {
        LocalStorage localStorage = new LocalStorage();
        StorageProperties.Local local = this.properties.getLocal();
        localStorage.setAddress(local.getAddress());
        localStorage.setStoragePath(local.getStoragePath());
        return localStorage;
    }

    @Bean
    public AliyunStorage aliyunStorage() {
        AliyunStorage aliyunStorage = new AliyunStorage();
        StorageProperties.Aliyun aliyun = this.properties.getAliyun();
        aliyunStorage.setAccessKeyId(aliyun.getAccessKeyId());
        aliyunStorage.setAccessKeySecret(aliyun.getAccessKeySecret());
        aliyunStorage.setBucketName(aliyun.getBucketName());
        aliyunStorage.setEndpoint(aliyun.getEndpoint());
        return aliyunStorage;
    }

    @Bean
    public S3Storage s3Storage() {

        S3Storage s3Storage = new S3Storage();
        StorageProperties.Aws aws = this.properties.getAws();
        s3Storage.setBucketName(aws.getBucketName());
        s3Storage.setRegion(aws.getRegion());
        s3Storage.setAccessKey(aws.getAccessKey());
        s3Storage.setSecretKey(aws.getSecretKey());
        s3Storage.setEndpoint(aws.getEndpoint());
        return s3Storage;
    }
}
