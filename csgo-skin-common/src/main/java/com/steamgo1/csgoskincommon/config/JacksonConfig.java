package com.steamgo1.csgoskincommon.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.steamgo1.csgoskincommon.converter.LanguageEnumDeserializer;
import com.steamgo1.csgoskincommon.converter.LanguageEnumKeyDeserializer;
import com.steamgo1.csgoskincommon.converter.LanguageEnumKeySerializer;
import com.steamgo1.csgoskincommon.converter.LanguageEnumSerializer;
import com.steamgo1.csgoskincommon.enums.LanguageEnum;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.text.SimpleDateFormat;

/**
 * Jackson配置类
 * 配置LanguageEnum的序列化和反序列化
 *
 * <AUTHOR>
 */
@Configuration
public class JacksonConfig {

    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();

        mapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

        // 创建自定义模块
        SimpleModule module = new SimpleModule("LanguageEnumModule");

        // 注册LanguageEnum的序列化器和反序列化器
        module.addSerializer(LanguageEnum.class, new LanguageEnumSerializer());
        module.addDeserializer(LanguageEnum.class, new LanguageEnumDeserializer());

        // 注册LanguageEnum的Key序列化器和反序列化器（用于Map的key）
        module.addKeySerializer(LanguageEnum.class, new LanguageEnumKeySerializer());
        module.addKeyDeserializer(LanguageEnum.class, new LanguageEnumKeyDeserializer());

        // 注册模块
        mapper.registerModule(module);

        return mapper;
    }
}
