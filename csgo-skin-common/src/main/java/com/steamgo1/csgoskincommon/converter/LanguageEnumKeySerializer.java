package com.steamgo1.csgoskincommon.converter;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.steamgo1.csgoskincommon.enums.LanguageEnum;

import java.io.IOException;

/**
 * LanguageEnum Key序列化器
 * 用于支持Map<LanguageEnum, String>的JSON序列化
 * 将LanguageEnum作为Map的key时序列化为其code值
 * 
 * <AUTHOR>
 */
public class LanguageEnumKeySerializer extends JsonSerializer<LanguageEnum> {
    
    @Override
    public void serialize(LanguageEnum value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value == null) {
            gen.writeFieldName(LanguageEnum.getDefault().getCode());
        } else {
            gen.writeFieldName(value.getCode());
        }
    }
}
