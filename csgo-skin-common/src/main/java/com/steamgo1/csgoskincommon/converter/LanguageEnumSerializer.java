package com.steamgo1.csgoskincommon.converter;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.steamgo1.csgoskincommon.enums.LanguageEnum;

import java.io.IOException;

/**
 * LanguageEnum 序列化器
 * 用于将LanguageEnum序列化为其code值
 * 
 * <AUTHOR>
 */
public class LanguageEnumSerializer extends JsonSerializer<LanguageEnum> {
    
    @Override
    public void serialize(LanguageEnum value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value == null) {
            gen.writeString(LanguageEnum.getDefault().getCode());
        } else {
            gen.writeString(value.getCode());
        }
    }
}
