package com.steamgo1.csgoskincommon.converter;

import com.steamgo1.csgoskincommon.utils.I18nField;
import org.mapstruct.Context;
import org.mapstruct.Named;

/**
 * 国际化演示转换器
 * 使用MapStruct进行对象映射
 *
 * <AUTHOR>
 */
public interface I18nConverter {
    /**
     * I18nField转换为当前语言的字符串
     */
    @Named("i18nFieldToCurrent")
    default String i18nFieldToCurrent(I18nField i18nField) {
        if (i18nField == null) {
            return "";
        }
        return i18nField.getCurrent();
    }

    /**
     * 处理更新时的ID映射
     */
    @Named("updateId")
    default Long updateId(Long sourceId, @Context Long targetId) {
        return targetId != null ? targetId : sourceId;
    }

}
