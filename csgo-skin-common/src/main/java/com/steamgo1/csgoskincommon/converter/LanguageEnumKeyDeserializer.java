package com.steamgo1.csgoskincommon.converter;

import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.KeyDeserializer;
import com.steamgo1.csgoskincommon.enums.LanguageEnum;

import java.io.IOException;

/**
 * LanguageEnum Key反序列化器
 * 用于支持Map<LanguageEnum, String>的JSON反序列化
 * 
 * <AUTHOR>
 */
public class LanguageEnumKeyDeserializer extends KeyDeserializer {
    
    @Override
    public Object deserializeKey(String key, DeserializationContext ctxt) throws IOException {
        if (key == null || key.trim().isEmpty()) {
            return LanguageEnum.getDefault();
        }
        
        try {
            return LanguageEnum.fromCode(key);
        } catch (Exception e) {
            // 如果转换失败，返回默认语言
            return LanguageEnum.getDefault();
        }
    }
}
