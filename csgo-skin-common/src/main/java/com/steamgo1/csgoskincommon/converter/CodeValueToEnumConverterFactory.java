package com.steamgo1.csgoskincommon.converter;

import com.google.common.collect.Maps;
import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.convert.converter.ConverterFactory;

import java.util.Map;

public class CodeValueToEnumConverterFactory implements ConverterFactory<String, CodeValueBaseEnum> {
    private static final Map<Class, Converter> CONVERTERS = Maps.newHashMap();

    @Override
    public <T extends CodeValueBaseEnum> Converter<String, T> getConverter(Class<T> targetType) {
        Converter<String, T> converter = CONVERTERS.get(targetType);
        if (converter == null) {
            converter = new CodeValueToEnumConverter<>(targetType);
            CONVERTERS.put(targetType, converter);
        }
        return converter;
    }
}
