package com.steamgo1.csgoskincommon.converter;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.steamgo1.csgoskincommon.enums.LanguageEnum;

import java.io.IOException;

/**
 * LanguageEnum 反序列化器
 * 用于将字符串反序列化为LanguageEnum
 * 
 * <AUTHOR>
 */
public class LanguageEnumDeserializer extends JsonDeserializer<LanguageEnum> {
    
    @Override
    public LanguageEnum deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String value = p.getValueAsString();
        if (value == null || value.trim().isEmpty()) {
            return LanguageEnum.getDefault();
        }
        
        try {
            return LanguageEnum.fromCode(value);
        } catch (Exception e) {
            // 如果转换失败，返回默认语言
            return LanguageEnum.getDefault();
        }
    }
}
