package com.steamgo1.csgoskincommon.converter;

import com.steamgo1.csgoskincommon.entity.StorageEntity;
import com.steamgo1.csgoskincommon.vo.ImageVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "Spring")
public interface StorageConverter {
    StorageConverter INSTANCE = Mappers.getMapper(StorageConverter.class);

    ImageVO toConverImageVO(StorageEntity storage);
}
