package com.steamgo1.csgoskincommon.converter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.steamgo1.csgoskincommon.enums.LanguageEnum;
import com.steamgo1.csgoskincommon.utils.I18nField;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.Map;

/**
 * I18nField与数据库JSON字段的转换器
 * 
 * <AUTHOR>
 */
@Converter
public class I18nFieldConverter implements AttributeConverter<I18nField, String> {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public String convertToDatabaseColumn(I18nField i18nField) {
        if (i18nField == null || i18nField.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(i18nField.getTranslations());
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting I18nField to JSON", e);
        }
    }
    
    @Override
    public I18nField convertToEntityAttribute(String dbData) {
        if (dbData == null || dbData.trim().isEmpty()) {
            return new I18nField();
        }
        try {
            TypeReference<Map<LanguageEnum, String>> typeRef = new TypeReference<Map<LanguageEnum, String>>() {};
            Map<LanguageEnum, String> translations = objectMapper.readValue(dbData, typeRef);
            return new I18nField(translations);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting JSON to I18nField", e);
        }
    }
}
