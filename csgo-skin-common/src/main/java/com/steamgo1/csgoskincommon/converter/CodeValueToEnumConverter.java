package com.steamgo1.csgoskincommon.converter;

import com.google.common.collect.Maps;
import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;
import com.steamgo1.csgoskincommon.exception.CsgoSkinException;
import org.springframework.core.convert.converter.Converter;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;

public class CodeValueToEnumConverter<T extends CodeValueBaseEnum> implements Converter<String, T> {
    private final Map<String, T> codeEnumMap = Maps.newHashMap();
    private final Map<String, T> valueEnumMap = Maps.newHashMap();

    public CodeValueToEnumConverter(Class<T> enumType) {
        Arrays.stream(enumType.getEnumConstants())
                .forEach(x -> {
                    codeEnumMap.put(x.getCode().toString(), x);
                    valueEnumMap.put(x.getValue(), x);
                });
    }

    @Override
    public T convert(String source) {
        return Optional.of(source)
                .map(codeEnumMap::get)
                .orElseGet(() -> Optional.of(source)
                        .map(codeEnumMap::get)
                        .orElseThrow(() -> new CsgoSkinException("参数不匹配")));
    }
}
