package com.steamgo1.csgoskincommon.converter;

import com.steamgo1.csgoskincommon.enums.LanguageEnum;
import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 基础国际化转换器
 * 提供通用的转换方法
 * 
 * <AUTHOR>
 */
public abstract class BaseI18nConverter {
    
    /**
     * 安全获取I18nField的当前语言值
     */
    protected String getCurrentValue(I18nField field) {
        return I18nUtils.safeGetCurrent(field);
    }
    
    /**
     * 安全获取I18nField的指定语言值
     */
    protected String getValue(I18nField field, LanguageEnum locale) {
        return I18nUtils.safeGet(field, locale);
    }
    
    /**
     * 批量转换 - 模板方法
     */
    protected <T, R> List<R> convertList(List<T> sourceList, Function<T, R> converter) {
        if (sourceList == null || sourceList.isEmpty()) {
            return new ArrayList<>();
        }
        return sourceList.stream()
                .map(converter)
                .collect(Collectors.toList());
    }

    /**
     * 安全转换 - 处理null值
     */
    protected <T, R> R safeConvert(T source, Function<T, R> converter) {
        if (source == null) {
            return null;
        }
        return converter.apply(source);
    }
}
