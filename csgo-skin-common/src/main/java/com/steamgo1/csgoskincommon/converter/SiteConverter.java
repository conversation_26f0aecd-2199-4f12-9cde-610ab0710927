package com.steamgo1.csgoskincommon.converter;

import com.steamgo1.csgoskincommon.entity.AnnouncementEntity;
import com.steamgo1.csgoskincommon.vo.AnnouncementVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper(componentModel = "Spring")
public interface SiteConverter {
    SiteConverter INSTANCE = Mappers.getMapper(SiteConverter.class);

    AnnouncementVO toAnnouncementVO(AnnouncementEntity announcementEntity);

    List<AnnouncementVO> toAnnouncementVOList(List<AnnouncementEntity> announcementEntityList);

}
