package com.steamgo1.csgoskincommon.dao;

import cn.hutool.core.util.StrUtil;
import com.steamgo1.csgoskincommon.enums.LanguageEnum;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.NoRepositoryBean;

import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;

/**
 * 基础多语言Repository接口
 * 提供通用的多语言字段搜索功能
 * 
 * @param <T> 实体类型
 * @param <ID> 主键类型
 * <AUTHOR>
 */
@NoRepositoryBean
public interface BaseI18nRepository<T, ID> extends JpaRepository<T, ID>, JpaSpecificationExecutor<T> {

    /**
     * 根据当前语言搜索文本
     */
    default List<T> searchInCurrentLanguage(String fieldName, String keyword) {
        // 使用Specification进行动态查询
        Specification<T> spec = createI18nSearchSpecification(fieldName, I18nUtils.getCurrentLanguageEnum(), keyword);
        return findAll(spec);
    }

    /**
     * 根据当前语言搜索文本
     */
    default T findInCurrentLanguage(String fieldName, String value) {
        // 使用Specification进行动态查询
        Specification<T> spec = createI18nSearchSpecification(fieldName, I18nUtils.getCurrentLanguageEnum(), value);
        return findOne(spec).orElse(null);
    }
    
    /**
     * 根据语言搜索文本
     */
    default List<T> searchInLanguage(String fieldName, LanguageEnum language, String keyword) {
        // 使用Specification进行动态查询
        Specification<T> spec = createI18nSearchSpecification(fieldName, language, keyword);
        return findAll(spec);
    }

    /**
     * 根据语言搜索文本
     */
    default T findInLanguage(String fieldName, LanguageEnum language, String value) {
        // 使用Specification进行动态查询
        Specification<T> spec = createI18nSearchSpecification(fieldName, language, value);
        return findOne(spec).orElse(null);
    }

    /**
     * 创建多语言搜索的Specification
     */
    default Specification<T> createI18nSearchSpecification(String fieldName, LanguageEnum language, String keyword) {
        return (root, query, criteriaBuilder) -> {
            if (keyword == null || keyword.trim().isEmpty()) {
                return criteriaBuilder.conjunction();
            }

            String likePattern = "%" + keyword.trim() + "%";

            try {
                // 确保字段存在于实体中
                Path<Object> fieldPath = root.get(StrUtil.toCamelCase(fieldName));
                
                return criteriaBuilder.like(
                        criteriaBuilder.function("JSON_EXTRACT", String.class,
                                fieldPath, criteriaBuilder.literal("$." + language.getCode())),
                        likePattern
                );
            } catch (IllegalArgumentException e) {
                // 如果字段不存在，返回false条件
                return criteriaBuilder.disjunction(); // 返回false
            }
        };
    }

    /**
     * 分页搜索（使用Specification）
     */
    default Page<T> searchWithPagination(String fieldName, LanguageEnum language, String keyword, Pageable pageable) {
        Specification<T> spec = createI18nSearchSpecification(fieldName, language, keyword);
        return findAll(spec, pageable);
    }

    /**
     * 复合条件搜索的Specification
     */
    default Specification<T> createComplexSearchSpecification(String fieldName, LanguageEnum language, String keyword, Long minId, Long maxId) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 添加文本搜索条件
            if (keyword != null && !keyword.trim().isEmpty()) {
                Specification<T> textSpec = createI18nSearchSpecification(fieldName, language, keyword);
                Predicate textPredicate = textSpec.toPredicate(root, query, criteriaBuilder);
                if (textPredicate != null) {
                    predicates.add(textPredicate);
                }
            }

            // 添加ID范围条件
            if (minId != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("id"), minId));
            }
            if (maxId != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("id"), maxId));
            }

            // 添加排序
            query.orderBy(criteriaBuilder.desc(root.get("createTime")));

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 复合条件搜索
     */
    default List<T> searchWithComplexConditions(String fieldName, LanguageEnum language, String keyword, Long minId, Long maxId) {
        Specification<T> spec = createComplexSearchSpecification(fieldName, language, keyword, minId, maxId);
        return findAll(spec);
    }

    /**
     * 创建多语言字段的模糊匹配Specification（支持多个关键词）
     */
    default Specification<T> createI18nMultiKeywordSpecification(String fieldName, LanguageEnum language, List<String> keywords) {
        return (root, query, criteriaBuilder) -> {
            if (keywords == null || keywords.isEmpty()) {
                return criteriaBuilder.conjunction();
            }

            List<Predicate> keywordPredicates = new ArrayList<>();
            for (String keyword : keywords) {
                if (keyword != null && !keyword.trim().isEmpty()) {
                    keywordPredicates.add(
                        criteriaBuilder.like(
                            criteriaBuilder.function("JSON_EXTRACT", String.class,
                                root.get(fieldName), criteriaBuilder.literal("$." + language)),
                            "%" + keyword.trim() + "%"
                        )
                    );
                }
            }

            return keywordPredicates.isEmpty() ? 
                criteriaBuilder.conjunction() : 
                criteriaBuilder.or(keywordPredicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 创建检查多语言字段是否为空的Specification
     */
    default Specification<T> createI18nNotEmptySpecification(String fieldName, LanguageEnum language) {
        return (root, query, criteriaBuilder) -> {
            return criteriaBuilder.and(
                criteriaBuilder.isNotNull(
                    criteriaBuilder.function("JSON_EXTRACT", String.class,
                        root.get(fieldName), criteriaBuilder.literal("$." + language))
                ),
                criteriaBuilder.notEqual(
                    criteriaBuilder.function("JSON_EXTRACT", String.class,
                        root.get(fieldName), criteriaBuilder.literal("$." + language)),
                    ""
                )
            );
        };
    }
}
