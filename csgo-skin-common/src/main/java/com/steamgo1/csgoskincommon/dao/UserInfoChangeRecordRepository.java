package com.steamgo1.csgoskincommon.dao;


import com.steamgo1.csgoskincommon.entity.UserInfoChangeRecordEntity;
import com.steamgo1.csgoskincommon.entity.enums.UserInfoChangeType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface UserInfoChangeRecordRepository extends JpaRepository<UserInfoChangeRecordEntity, Long> {
    UserInfoChangeRecordEntity findTopByUserIdAndTypeOrderByCreateTime(Long userId, UserInfoChangeType type);

    Boolean existsByUserIdAndType(Long userId, UserInfoChangeType type);
}
