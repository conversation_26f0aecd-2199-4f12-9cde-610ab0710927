package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.BattleHomeEntity;
import com.steamgo1.csgoskincommon.entity.enums.BattleHomeStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface BattleHomeReposiotry extends JpaRepository<BattleHomeEntity, Long>, JpaSpecificationExecutor<BattleHomeEntity> {
    //    Page<BattleHomeEntity> findByBattleHomeStatus(BattleHomeStatus battleHomeStatus, Pageable pageable);
//    List<BattleHomeEntity> findByBattleHomeStatus(BattleHomeStatus battleHomeStatus);
    List<BattleHomeEntity> findByBattleHomeStatusIn(List<BattleHomeStatus> battleHomeStatusSet);

    Page<BattleHomeEntity> findAllByCreateTimeAfter(Date beforeTime, Pageable pageable);

    Page<BattleHomeEntity> findAllByCreateTimeAfterAndIsHideIsFalse(Date beforeTime, Pageable pageable);

    @Query(value = "select count(*) from battle_home", nativeQuery = true)
    Integer totalBattomHome();
}
