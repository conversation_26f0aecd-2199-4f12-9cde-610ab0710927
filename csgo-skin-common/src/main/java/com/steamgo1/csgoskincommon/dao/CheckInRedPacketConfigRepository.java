package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.CheckInRedPacketConfigEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface CheckInRedPacketConfigRepository extends JpaRepository<CheckInRedPacketConfigEntity, Long> {
    List<CheckInRedPacketConfigEntity> findByCheckInRedPacketId(Long checkInRedPacketId);
}
