package com.steamgo1.csgoskincommon.dao;


import com.steamgo1.csgoskincommon.entity.UserEntity;
import com.steamgo1.csgoskincommon.entity.enums.UserType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository
public interface UserRepository extends JpaRepository<UserEntity, Integer>, JpaSpecificationExecutor<UserEntity> {

    UserEntity findByEmail(String email);
    UserEntity findUserByPhone(String phone);

    UserEntity findById(Long id);

    UserEntity findByWxOpenid(String wxOpenId);

    boolean existsByWxOpenid(String wxOpenI);


    List<UserEntity> findByTypeOrderByUpdateTime(UserType userType);

    @Query(value = "SELECT * FROM user where type = 1 ORDER BY RAND() LIMIT 1", nativeQuery = true)
    UserEntity getRandomRobot();

    Integer countByType(UserType type);

    // 统计用户数量
    @Query(value = "SELECT count(*) from user", nativeQuery = true)
    Integer totalUsers();

    // 统计今日新增
    @Query(value = "SELECT COUNT(*) FROM user WHERE DATE(create_time) = CURDATE()", nativeQuery = true)
    Integer dayTotalUsers();

    // 统计7日用户新增
    @Query(value = "SELECT DATE(create_time) AS day, COUNT(*) AS count FROM user WHERE create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) GROUP BY day", nativeQuery = true)
    List<Map<Date, Integer>> weekUsers();

    // 随机x个机器人
    @Query(value = "select * from user where type = ?1 order by rand() limit ?2", nativeQuery = true)
    List<UserEntity> randomCheatRobot(Integer type, Integer rounds_total);

    UserEntity findBySteamId(String steamOpenId);

    // 统计今日新增用户中开过免费箱子的用户数量
    @Query(value = "SELECT count(*) from (SELECT user.id from user LEFT JOIN record_open_case on record_open_case.user_id=user.id LEFT JOIN box on box.id=record_open_case.case_id WHERE box.type=0 and  date(user.create_time) = CURDATE() GROUP BY user.id) as subquery", nativeQuery = true)
    Integer dayFreeCase();


    // 统计今日新增用户中开过免费箱子的用户数量
    @Query(value = "SELECT count(*) from (SELECT user.id from user LEFT JOIN record_open_case on record_open_case.user_id=user.id LEFT JOIN box on box.id=record_open_case.case_id WHERE box.type=0  GROUP BY user.id) as subquery", nativeQuery = true)
    Integer freeCase();

    boolean existsByTradeOfferAccessUrl(String tradeOfferAccessUrl);

    // 用户注册数
    @Query(value = "select count(*) from user WHERE date(user.create_time) = :targetDate", nativeQuery = true)
    Integer dayUserRegistCount(@Param("targetDate") Date targetDate);

    // 用户注册数
    @Query(value = "select count(*) from user left join ocpc_baidu_data on user.id = ocpc_baidu_data.user_id and ocpc_baidu_data.type = 1 WHERE date(user.create_time) = :targetDate and ocpc_baidu_data.ocpc_channel_id = :channelId", nativeQuery = true)
    Integer dayUserRegistCountChannel(@Param("targetDate") Date targetDate, @Param("channelId") Long channelId);

    // 新用户付费数
    @Query(value = "SELECT count(*) from (SELECT user.id from user LEFT JOIN order_charge on user.id=order_charge.user_id WHERE date(user.create_time) = :targetDate and date(order_charge.create_time) = :targetDate and order_charge.status=1 GROUP BY user.id) as subquery", nativeQuery = true)
    Integer dayUserChargeCount(@Param("targetDate") Date targetDate);

    // 新用户付费数
    @Query(value = "SELECT count(*) from (SELECT user.id from user LEFT JOIN order_charge on user.id=order_charge.user_id left join ocpc_baidu_data on user.id = ocpc_baidu_data.user_id and ocpc_baidu_data.type = 1  WHERE date(user.create_time) = :targetDate and date(order_charge.create_time) = :targetDate and order_charge.status=1 and ocpc_baidu_data.ocpc_channel_id = :channelId GROUP BY user.id) as subquery", nativeQuery = true)
    Integer dayUserChargeCountChannel(@Param("targetDate") Date targetDate, @Param("channelId") Long channelId);

    // 老用户付费数
    @Query(value = "SELECT count(*) from (SELECT user.id from user LEFT JOIN order_charge on user.id=order_charge.user_id WHERE date(user.create_time) != :targetDate and date(order_charge.create_time) = :targetDate and order_charge.status=1 GROUP BY user.id) as subquery", nativeQuery = true)
    Integer dayOldUserChargeCount(@Param("targetDate") Date targetDate);

    // 老用户付费数
    @Query(value = "SELECT count(*) from (SELECT user.id from user LEFT JOIN order_charge on user.id=order_charge.user_id left join ocpc_baidu_data on user.id = ocpc_baidu_data.user_id and ocpc_baidu_data.type = 1 WHERE date(user.create_time) != :targetDate and date(order_charge.create_time) = :targetDate and order_charge.status=1 and ocpc_baidu_data.ocpc_channel_id = :channelId GROUP BY user.id) as subquery", nativeQuery = true)
    Integer dayOldUserChargeCountChannel(@Param("targetDate") Date targetDate, @Param("channelId") Long channelId);

    // 新用户付费金额
    @Query(value = "SELECT sum(order_charge.amount) from user LEFT JOIN order_charge on user.id=order_charge.user_id WHERE date(user.create_time) = :targetDate and date(order_charge.create_time) = :targetDate and order_charge.status=1", nativeQuery = true)
    BigDecimal newUserChargeAmount(@Param("targetDate") Date targetDate);

    // 新用户付费金额
    @Query(value = "SELECT sum(order_charge.amount) from user LEFT JOIN order_charge on user.id=order_charge.user_id  left join ocpc_baidu_data on user.id = ocpc_baidu_data.user_id and ocpc_baidu_data.type = 1  WHERE date(user.create_time) = :targetDate and date(order_charge.create_time) = :targetDate and order_charge.status=1  and ocpc_baidu_data.ocpc_channel_id = :channelId ", nativeQuery = true)
    BigDecimal newUserChargeAmountChannel(@Param("targetDate") Date targetDate, @Param("channelId") Long channelId);

    // 老用户付费金额
    @Query(value = "SELECT sum(order_charge.amount) from user LEFT JOIN order_charge on user.id=order_charge.user_id WHERE date(user.create_time) != :targetDate and date(order_charge.create_time) = :targetDate and order_charge.status=1", nativeQuery = true)
    BigDecimal oldUserChargeAmount(@Param("targetDate") Date targetDate);

    // 老用户付费金额
    @Query(value = "SELECT sum(order_charge.amount) from user LEFT JOIN order_charge on user.id=order_charge.user_id  left join ocpc_baidu_data on user.id = ocpc_baidu_data.user_id and ocpc_baidu_data.type = 1  WHERE date(user.create_time) != :targetDate and date(order_charge.create_time) = :targetDate and order_charge.status=1  and ocpc_baidu_data.ocpc_channel_id = :channelId ", nativeQuery = true)
    BigDecimal oldUserChargeAmountChannel(@Param("targetDate") Date targetDate, @Param("channelId") Long channelId);

    // 总付费金额
    @Query(value = "SELECT sum(order_charge.amount) from  order_charge where date(order_charge.create_time) = :targetDate and order_charge.status=1", nativeQuery = true)
    BigDecimal dayAmount(@Param("targetDate") Date targetDate);

    // 总付费金额
    @Query(value = "SELECT sum(order_charge.amount) from  order_charge left join ocpc_baidu_data on order_charge.user_id = ocpc_baidu_data.user_id and ocpc_baidu_data.type = 1 where date(order_charge.create_time) = :targetDate and order_charge.status=1 and ocpc_baidu_data.ocpc_channel_id = :channelId", nativeQuery = true)
    BigDecimal dayAmountChannel(@Param("targetDate") Date targetDate, @Param("channelId") Long channelId);

    // 总取回
    @Query(value = "SELECT  SUM(user_package.diamond) from user_package_pickup left JOIN user_package on user_package.id=user_package_pickup.user_package_id WHERE user_package_pickup.status = 4 and DATE(user_package_pickup.create_time) = :targetDate", nativeQuery = true)
    BigDecimal dayPackupAmount(@Param("targetDate") Date targetDate);

    // 总取回
    @Query(value = "SELECT  SUM(user_package.diamond) from user_package_pickup left JOIN user_package on user_package.id=user_package_pickup.user_package_id left join ocpc_baidu_data on user_package_pickup.id = ocpc_baidu_data.user_id and ocpc_baidu_data.type = 1 WHERE user_package_pickup.status = 4 and DATE(user_package_pickup.create_time) = :targetDate and ocpc_baidu_data.ocpc_channel_id = :channelId", nativeQuery = true)
    BigDecimal dayPackupAmountChannel(@Param("targetDate") Date targetDate, @Param("channelId") Long channelId);

    // 取回成功订单数量
    @Query(value = "SELECT  count(*) from user_package_pickup left JOIN user_package on user_package.id=user_package_pickup.user_package_id WHERE user_package_pickup.status = 4 and DATE(user_package_pickup.create_time) = :targetDate", nativeQuery = true)
    Integer countPackagePickupAmount(@Param("targetDate") Date targetDate);

    // 取回成功订单数量
    @Query(value = "SELECT  count(*) from user_package_pickup left JOIN user_package on user_package.id=user_package_pickup.user_package_id  left join ocpc_baidu_data on user_package_pickup.id = ocpc_baidu_data.user_id and ocpc_baidu_data.type = 1  WHERE user_package_pickup.status = 4 and DATE(user_package_pickup.create_time) = :targetDate  and ocpc_baidu_data.ocpc_channel_id = :channelId ", nativeQuery = true)
    Integer countPackagePickupAmountChannel(@Param("targetDate") Date targetDate, @Param("channelId") Long channelId);

    // 付费用户数
    @Query(value = "SELECT count(*) from (SELECT user.id from user LEFT JOIN order_charge on user.id=order_charge.user_id WHERE  date(order_charge.create_time) = :targetDate and order_charge.status=1 GROUP BY user.id) as subquery", nativeQuery = true)
    Integer allUserChargeCount(@Param("targetDate") Date targetDate);

    // 付费用户数
    @Query(value = "SELECT count(*) from (SELECT user.id from user LEFT JOIN order_charge on user.id=order_charge.user_id left join ocpc_baidu_data on user.id = ocpc_baidu_data.user_id and ocpc_baidu_data.type = 1  WHERE  date(order_charge.create_time) = :targetDate and order_charge.status=1 and ocpc_baidu_data.ocpc_channel_id = :channelId GROUP BY user.id) as subquery", nativeQuery = true)
    Integer allUserChargeCountChannel(@Param("targetDate") Date targetDate, @Param("channelId") Long channelId);

    List<UserEntity> findByType(UserType type);

    List<UserEntity> findByIdIn(List<Long> id);


}

