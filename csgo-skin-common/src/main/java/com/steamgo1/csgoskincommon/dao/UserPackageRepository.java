package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.UserPackageEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.persistence.LockModeType;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Repository
public interface UserPackageRepository extends JpaRepository<UserPackageEntity, Long>, JpaSpecificationExecutor<UserPackageEntity> {
    List<UserPackageEntity> findByUserId(Long userId);

    UserPackageEntity findByUserIdAndSkinId(Long userId, Long skinId);

    Page<UserPackageEntity> findByUserIdAndIsReceivedIsFalseAndIsSelledIsFalse(Long userId, Pageable pageable);

    Page<UserPackageEntity> findByUserIdAndIsReceivedIsTrue(Long userId, Pageable pageable);

    List<UserPackageEntity> findByUserIdAndIsSelledAndIsReceived(Long userId, Boolean isSelled, Boolean isReceived);

    Integer countByUserIdAndIsReceivedIsFalseAndIsSelledIsFalse(Long userId);

    // 查询用户库存总价值
    @Query(value = "SELECT SUM(user_package.diamond) from user_package  WHERE is_selled=0 and is_received=0 and user_id=?1", nativeQuery = true)
    BigDecimal userPackageTotalValue(Long userId);

    @Query(value = "SELECT SUM(diamond) from user_package WHERE is_selled=0 and is_received=0 and user_id=?1", nativeQuery = true)
    BigDecimal userPackageTotalValueNew(Long userId);


    //用户总共取回
    @Query(value = "SELECT  SUM(user_package.diamond) from user_package_pickup left JOIN user_package on user_package.id=user_package_pickup.user_package_id WHERE user_package_pickup.status = 4 and user_package.user_id=?1", nativeQuery = true)
    BigDecimal userPickUpPackageTotalValue(Long userId);

    // 查询用户取回库存总价值
    @Query(value = "SELECT SUM(skin.diamond) from user_package LEFT JOIN skin on user_package.skin_id=skin.id WHERE is_received=1 and user_id=?1", nativeQuery = true)
    BigDecimal userPackageReceivedTotalValue(Long userId);

    // 查询用户获取的最贵饰品top3(内部用户除外)
    @Query(value = "SELECT user.id userId, skin.id skinId, source, case_id from user_package Left Join user on user_package.user_id=user.id LEFT JOIN skin on user_package.skin_id=skin.id WHERE DATE(user_package.create_time) >= DATE_SUB(CURDATE(), INTERVAL 3 DAY) and user.type=0 order by user_package.diamond desc limit 3", nativeQuery = true)
    List<Map<String, Object>> queryWinSkinTop3();

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    UserPackageEntity findTopById(Long id);
}
