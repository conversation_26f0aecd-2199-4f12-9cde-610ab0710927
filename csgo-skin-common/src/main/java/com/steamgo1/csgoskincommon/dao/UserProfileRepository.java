package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.UserEntity;
import com.steamgo1.csgoskincommon.entity.UserProfileEntity;
import org.springframework.data.jpa.repository.JpaRepository;

public interface UserProfileRepository extends JpaRepository<UserProfileEntity, Long> {
    //    @Lock(LockModeType.PESSIMISTIC_WRITE)
    UserProfileEntity findByUserId(Long userId);

    UserProfileEntity findByUser(UserEntity user);
}
