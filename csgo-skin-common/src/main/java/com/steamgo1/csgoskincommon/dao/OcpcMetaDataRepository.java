package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.OcpcMetaDataEntity;
import com.steamgo1.csgoskincommon.entity.enums.OcpcMetaDataType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OcpcMetaDataRepository extends JpaRepository<OcpcMetaDataEntity, Long>, JpaSpecificationExecutor<OcpcMetaDataEntity> {
    List<OcpcMetaDataEntity> findByTypeAndUserId(OcpcMetaDataType type, Long userId);
}