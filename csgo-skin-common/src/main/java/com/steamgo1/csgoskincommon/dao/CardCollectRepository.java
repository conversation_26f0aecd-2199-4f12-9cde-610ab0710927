package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.CardCollectEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface CardCollectRepository extends JpaRepository<CardCollectEntity, Long> {
    CardCollectEntity findTopByIsActivateTrueAndIsDeletedIsFalseOrderByIdDesc();

    Boolean existsByIsActivateTrueAndIsDeletedIsFalseAndId(Long id);


    @Query(value = "SELECT card_collect_user.user_id from card_collect_user left JOIN card_collect_card on card_collect_card.id=card_collect_user.card_collect_card_id left JOIN card_collect on card_collect.id=card_collect_card.card_collect WHERE card_collect.id=?1 GROUP BY card_collect_user.user_id", nativeQuery = true)
    List<Long> querJoinUserId(Long id);
}
