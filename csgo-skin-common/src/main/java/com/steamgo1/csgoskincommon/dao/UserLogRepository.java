package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.UserEntity;
import com.steamgo1.csgoskincommon.entity.UserLogEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface UserLogRepository extends JpaRepository<UserLogEntity, Long> {
    UserLogEntity findTopByUserOrderByLoginTimeDesc(UserEntity user);
}
