package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.OcpcBaiduDataEntity;
import com.steamgo1.csgoskincommon.entity.enums.OcpcBaiduDataType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OcpcBaiduDataRepository extends JpaRepository<OcpcBaiduDataEntity, Long>, JpaSpecificationExecutor<OcpcBaiduDataEntity> {
    List<OcpcBaiduDataEntity> findByTypeAndUserId(OcpcBaiduDataType type, Long userId);
}
