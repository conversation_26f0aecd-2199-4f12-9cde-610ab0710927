package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.RequestLogEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.Date;

@Repository
public interface RequestLogRepository extends JpaRepository<RequestLogEntity, Long> {
    @Modifying
    @Transactional
    void deleteByCreateTimeBefore(Date createTime);
}
