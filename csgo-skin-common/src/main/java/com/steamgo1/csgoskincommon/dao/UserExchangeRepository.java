package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.UserExchangeEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserExchangeRepository extends JpaRepository<UserExchangeEntity, Long> {
    List<UserExchangeEntity> findByUserId(Long userId);

    Page<UserExchangeEntity> findByUserId(Long userId, Pageable pageable);

}
