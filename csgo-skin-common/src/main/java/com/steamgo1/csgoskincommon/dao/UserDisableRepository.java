package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.UserDisableEntity;
import com.steamgo1.csgoskincommon.entity.enums.UserDisableType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;


@Repository
public interface UserDisableRepository extends JpaRepository<UserDisableEntity, Long>, JpaSpecificationExecutor<UserDisableEntity> {
    UserDisableEntity findTopByUserIdAndTypeAndIsEffectiveIsTrue(Long userId, UserDisableType type);
}
