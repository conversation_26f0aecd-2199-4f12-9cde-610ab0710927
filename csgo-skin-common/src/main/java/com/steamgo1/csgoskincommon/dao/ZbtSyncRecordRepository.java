package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.ZbtSyncRecordEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;


@Repository
public interface ZbtSyncRecordRepository extends JpaRepository<ZbtSyncRecordEntity, Long> {
    Optional<ZbtSyncRecordEntity> findByOperationNo(String operationNo);
}
