package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.IpBlackEntity;
import com.steamgo1.csgoskincommon.entity.enums.Status;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IpBlackRepository extends JpaRepository<IpBlackEntity, Long> {
    List<IpBlackEntity> findByIpAndStatus(String ip, Status status);
}
