package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.UserInviteEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface UserInviteRepository extends JpaRepository<UserInviteEntity, Long> {

    UserInviteEntity findByUserId(Long userId);

    UserInviteEntity findByCode(String code);
}
