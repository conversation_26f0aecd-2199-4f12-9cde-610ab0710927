package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.UserBuySkinEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface UserBuySkinRepository extends JpaRepository<UserBuySkinEntity, Long> {
    Page<UserBuySkinEntity> findByUserId(Long userId, Pageable pageable);
}
