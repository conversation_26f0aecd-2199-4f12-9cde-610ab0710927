package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.AlgorithmDataEntity;
import com.steamgo1.csgoskincommon.entity.UserEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AlgorithmDataRepository extends JpaRepository<AlgorithmDataEntity, Integer> {
    AlgorithmDataEntity findByUser(UserEntity user);

    /**
     * 获取用户的算法数据
     *
     * @param userId
     * @param isUsed
     * @return
     */
    List<AlgorithmDataEntity> findByUserIdAndIsUsed(Long userId, Boolean isUsed);

    AlgorithmDataEntity findByUserIdAndIsUsedIsTrue(Long userId);

    Page<AlgorithmDataEntity> findByUserIdAndIsUsedIsFalse(Long userId, Pageable pageable);

    Page<AlgorithmDataEntity> findByUserIdAndIsUsedIsFalseAndIsDeletedIsFalse(Long userId, Pageable pageable);
}
