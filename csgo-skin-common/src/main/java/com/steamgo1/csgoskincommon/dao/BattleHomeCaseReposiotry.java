package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.BattleHomeCaseEntity;
import com.steamgo1.csgoskincommon.entity.BattleHomeEntity;
import com.steamgo1.csgoskincommon.entity.enums.BattleHomeCaseStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BattleHomeCaseReposiotry extends JpaRepository<BattleHomeCaseEntity, Long> {
    List<BattleHomeCaseEntity> findByBattleHome(BattleHomeEntity battleHomeEntity);

    BattleHomeCaseEntity findFirstByBattleHomeAndStatusOrderById(BattleHomeEntity battleHomeEntity, BattleHomeCaseStatus status);

    Boolean existsByBattleHomeAndStatus(BattleHomeEntity battleHomeEntity, BattleHomeCaseStatus status);
}
