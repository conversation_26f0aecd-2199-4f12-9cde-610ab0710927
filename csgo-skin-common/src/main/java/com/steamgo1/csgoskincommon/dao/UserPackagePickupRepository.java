package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.UserPackagePickupEntity;
import com.steamgo1.csgoskincommon.entity.enums.PackagePickupStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository
public interface UserPackagePickupRepository extends JpaRepository<UserPackagePickupEntity, Long>, JpaSpecificationExecutor<UserPackagePickupEntity> {
    List<UserPackagePickupEntity> findByUserId(Long userId);

    Page<UserPackagePickupEntity> findByUserId(Long userId, Pageable pageable);

    UserPackagePickupEntity findByUserIdAndUserPackageSkinId(Long userId, Long skinId);

    List<UserPackagePickupEntity> findByStatusIn(List<PackagePickupStatus> packagePickupStatuses);

    List<UserPackagePickupEntity> findByStatus(PackagePickupStatus packagePickupStatus);

    // 查询过去12小时拒绝报价次数
    @Query(value = "SELECT count(id) from user_package_pickup WHERE TO_SECONDS(NOW())-TO_SECONDS(update_time)<43200 and status=6 and user_id=?1", nativeQuery = true)
    Integer queryCancelCount(Long userId);

    // 统计今日取回消耗
    @Query(value = "SELECT  SUM(skin.diamond) from user_package_pickup left JOIN user_package on user_package.id=user_package_pickup.user_package_id left JOIN skin on skin.id=user_package.skin_id WHERE user_package_pickup.status not IN (5, 6) and DATE(user_package_pickup.create_time) = CURDATE()", nativeQuery = true)
    BigDecimal dayAmount();

    @Query(value = "SELECT  SUM(skin.diamond) from user_package_pickup left JOIN user_package on user_package.id=user_package_pickup.user_package_id left JOIN skin on skin.id=user_package.skin_id WHERE user_package_pickup.status = 4 and DATE(user_package_pickup.create_time) = CURDATE()", nativeQuery = true)
    BigDecimal dayAmountNet();

    // 统计7日用户取回消耗
    @Query(value = "SELECT DATE(user_package_pickup.create_time) AS day , SUM(skin.diamond) as amount from user_package_pickup left JOIN user_package on user_package.id=user_package_pickup.user_package_id left JOIN skin on skin.id=user_package.skin_id WHERE user_package_pickup.status not IN (5, 6) and user_package_pickup.create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) GROUP BY day", nativeQuery = true)
    List<Map<Date, BigDecimal>> weekAount();

    List<UserPackagePickupEntity> findByIsRemindIsFalse();


    @Query(value = "SELECT count(id) from user_package_pickup WHERE status = ?1", nativeQuery = true)
    Integer queryPickUpCount(Integer status);

}
