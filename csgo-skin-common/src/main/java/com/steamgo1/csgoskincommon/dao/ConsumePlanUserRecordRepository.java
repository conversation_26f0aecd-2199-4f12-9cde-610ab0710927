package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.ConsumePlanUserRecordEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface ConsumePlanUserRecordRepository extends JpaRepository<ConsumePlanUserRecordEntity, Long> {
    List<ConsumePlanUserRecordEntity> findByUserId(Long userId);

    Boolean existsByUserIdAndConsumePlanId(Long userId, Long consumePlanId);


    @Query(value = "select * from consume_plan_user_record WHERE DATE_FORMAT(create_time,  '%Y-%m-%d') = DATE_FORMAT(now(),'%Y-%m-%d') and user_id=?1", nativeQuery = true)
    List<ConsumePlanUserRecordEntity> dayUserConsumePlan(Long userId);

    @Query(value = "select * from consume_plan_user_record WHERE DATE_FORMAT(create_time,  '%Y-%m-%d') = DATE_FORMAT(now(),'%Y-%m-%d') and user_id=?1 and consume_plan_id=?2", nativeQuery = true)
    ConsumePlanUserRecordEntity existsDayByUserIdAndConsumePlanId(Long userId, Long consumePlanId);


}

