package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.OcpcBaiduAccountEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface OcpcBaiduAccountRepository extends JpaRepository<OcpcBaiduAccountEntity, Long> {
    OcpcBaiduAccountEntity findTopByRelamNameContains(String relmName);
}
