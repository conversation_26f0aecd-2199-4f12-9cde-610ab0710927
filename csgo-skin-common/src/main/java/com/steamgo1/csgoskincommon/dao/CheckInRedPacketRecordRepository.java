package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.CheckInRedPacketRecordEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;


@Repository
public interface CheckInRedPacketRecordRepository extends JpaRepository<CheckInRedPacketRecordEntity, Long> {
    // 用户本月领取此类型红包次数
    @Query(value = "SELECT COUNT(*) FROM check_in_red_packet_record WHERE MONTH(create_time) = MONTH(CURRENT_DATE()) AND YEAR(create_time) = YEAR(CURRENT_DATE()) and user_id=?1 and check_in_red_packet_id=?2", nativeQuery = true)
    Integer monthCheckInRedPacketRecordCount(Long userId, Long checkInRedPacketId);
}
