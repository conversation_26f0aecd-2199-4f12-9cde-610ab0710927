package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.RollHomeEntity;
import com.steamgo1.csgoskincommon.entity.RollHomeUserEntity;
import com.steamgo1.csgoskincommon.entity.UserEntity;
import com.steamgo1.csgoskincommon.entity.enums.RollHomeStatus;
import com.steamgo1.csgoskincommon.entity.enums.RollHomeType;
import com.steamgo1.csgoskincommon.entity.enums.UserType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface RollHomeUserRepository extends JpaRepository<RollHomeUserEntity, Long> {
    List<RollHomeUserEntity> findByRollHome(RollHomeEntity rollHome);

    List<RollHomeUserEntity> findByRollHomeOrderById(RollHomeEntity rollHome);

    Page<RollHomeUserEntity> findByRollHomeIdOrderById(Long rollHomeId, Pageable pageable);

    Boolean existsByRollHomeId(Long rollHomeId);

    Boolean existsByRollHomeAndUser(RollHomeEntity rollHomeEntity, UserEntity userEntity);

    List<RollHomeUserEntity> findByUserTypeAndRollHome(UserType userType, RollHomeEntity rollHome);

    Integer countByRollHome(RollHomeEntity rollHomeEntity);

    Page<RollHomeUserEntity> findByUserId(Long userId, Pageable pageable);

    Page<RollHomeUserEntity> findByUserIdAndRollHomeStatus(Long userId, RollHomeStatus rollHomeStatus, Pageable pageable);

    Page<RollHomeUserEntity> findByUserIdAndRollHomeStatusIn(Long userId, List<RollHomeStatus> rollHomeStatusList, Pageable pageable);

    Boolean existsByRollHomeRollHomeTypeAndUserId(RollHomeType rollHomeType, Long userId);

    List<RollHomeUserEntity> findByRollHomeIdAndUserIdNotIn(Long userId, Set<Long> userIds);

    List<RollHomeUserEntity> findByRollHomeAndUserType(RollHomeEntity rollHome, UserType userType);
}
