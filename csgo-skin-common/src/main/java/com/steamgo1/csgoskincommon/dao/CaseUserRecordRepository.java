package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.CaseUserRecordEntity;
import com.steamgo1.csgoskincommon.entity.enums.CaseType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.math.BigDecimal;

public interface CaseUserRecordRepository extends JpaRepository<CaseUserRecordEntity, Long>, JpaSpecificationExecutor<CaseUserRecordEntity> {
    Page<CaseUserRecordEntity> findByUserId(Long userId, Pageable pageable);

    Page<CaseUserRecordEntity> findByBoxId(Long caseId, Pageable pageable);

    Page<CaseUserRecordEntity> findByUserIdAndBoxId(Long userId, Long boxId, Pageable pageable);

    // 用户是否开过某类型箱子
    Boolean existsByUserIdAndBoxCaseType(Long userId, CaseType caseType);

    // 新用户箱子开的次数
    Integer countByUserIdAndBoxCaseType(Long userId, CaseType caseType);


    @Query(value = "select count(*) from record_open_case left join box on record_open_case.case_id=box.id WHERE DATE_FORMAT(record_open_case.create_time,  '%Y-%m-%d') = DATE_FORMAT(now(),'%Y-%m-%d') and record_open_case.user_id=?1 and box.type=?2", nativeQuery = true)
    Integer countDayByUserIdAndBoxCaseType(Long userId, Integer caseType);

    // 开箱次数
    Integer countByBoxId(Long caseId);

    // 开出饰品总价值
    @Query(value = "SELECT SUM(skin.diamond) from box left JOIN record_open_case on box.id=record_open_case.case_id left JOIN skin on record_open_case.skin_id=skin.id WHERE box.id=?1", nativeQuery = true)
    BigDecimal expenditureSkin(Long id);

    // 查询用户今天开启某个箱子的次数
    @Query(value = "select count(*) from record_open_case left join box on record_open_case.case_id=box.id WHERE DATE_FORMAT(record_open_case.create_time,  '%Y-%m-%d') = DATE_FORMAT(now(),'%Y-%m-%d') and record_open_case.user_id=?1 and box.id=?2", nativeQuery = true)
    Integer totalUserOpenDailyTasksCase(Long userId, Long boxId);

    // 查询用户今天开启某个箱子的次数
    @Query(value = "select count(*) from record_open_case", nativeQuery = true)
    Integer totalOpenCase();
}
