package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.BattleHomeEntity;
import com.steamgo1.csgoskincommon.entity.BattleHomeUserEntity;
import com.steamgo1.csgoskincommon.entity.UserEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Repository
public interface BattleHomeUserReposiotry extends JpaRepository<BattleHomeUserEntity, Long> {
    List<BattleHomeUserEntity> findByBattleHome(BattleHomeEntity battleHomeEntity);

    BattleHomeUserEntity findByBattleHomeAndUser(BattleHomeEntity battleHome, UserEntity user);

    Page<BattleHomeUserEntity> findByUserId(Long userId, Pageable pageable);

    @Query(value = "select id from battle_home_user where battle_name_id=?1", nativeQuery = true)
    Set<Long> findByBattleHomeId(Long battleHomeId);

    @Query(value = "SELECT battle_home_case_user_skin.user_id, SUM(skin.diamond) amount from battle_home_case_user_skin LEFT JOIN skin on battle_home_case_user_skin.skin_id = skin.id WHERE TO_DAYS(NOW())-TO_DAYS(battle_home_case_user_skin.create_time)=1 GROUP BY battle_home_case_user_skin.user_id ORDER BY amount DESC limit 10", nativeQuery = true)
    List<Map<String, Object>> queryYesterdayTop10();
}
