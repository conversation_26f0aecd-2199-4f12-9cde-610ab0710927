package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.OrderChargeEntity;
import com.steamgo1.csgoskincommon.entity.enums.OrderStatus;
import com.steamgo1.csgoskincommon.entity.enums.PayType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository
public interface OrderChargeRepository extends JpaRepository<OrderChargeEntity, Long>, JpaSpecificationExecutor<OrderChargeEntity> {
    OrderChargeEntity findByOrderNo(String orderNo);

    List<OrderChargeEntity> findByUserIdAndChargeGoodsIdAndPayTypeAndOrderStatus(Long userId, Long chargeOrdersId, PayType payType, OrderStatus orderStatus);

    @Query(value = "SELECT sum(actual_amount) from order_charge WHERE `status`=1 and user_id=?1", nativeQuery = true)
    BigDecimal userTotalCharge(Long userId);

    // 统计今日充值
    @Query(value = "SELECT sum(actual_amount) FROM order_charge WHERE DATE(create_time) = CURDATE() and status=1", nativeQuery = true)
    BigDecimal dayAmount();

    Page<OrderChargeEntity> findByUserId(Long userId, Pageable pageable);

    // 统计7日用户充值
    @Query(value = "SELECT DATE(create_time) AS day, sum(actual_amount) AS amout FROM order_charge WHERE create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) and status=1 GROUP BY day", nativeQuery = true)
    List<Map<Date, BigDecimal>> weekAmount();
}
