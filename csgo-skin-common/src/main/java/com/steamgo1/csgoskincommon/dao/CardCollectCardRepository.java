package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.CardCollectCardEntity;
import com.steamgo1.csgoskincommon.entity.CardCollectEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface CardCollectCardRepository extends JpaRepository<CardCollectCardEntity, Long> {
    List<CardCollectCardEntity> findByCardCollectOrderById(CardCollectEntity cardCollectEntity);

    List<CardCollectCardEntity> findByCardCollect(CardCollectEntity cardCollectEntity);

    List<CardCollectCardEntity> findByCardCollectId(Long cardCollectId);
}
