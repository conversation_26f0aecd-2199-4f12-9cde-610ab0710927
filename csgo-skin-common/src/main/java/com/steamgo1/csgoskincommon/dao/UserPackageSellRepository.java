package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.UserPackageSellEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface UserPackageSellRepository extends JpaRepository<UserPackageSellEntity, Long> {
    Page<UserPackageSellEntity> findByUserId(Long userId, Pageable pageable);
}
