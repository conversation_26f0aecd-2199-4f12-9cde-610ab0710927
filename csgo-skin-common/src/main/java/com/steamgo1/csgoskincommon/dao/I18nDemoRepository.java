package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.I18nDemoEntity;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 国际化演示数据访问层
 * 继承BaseI18nRepository获得通用的多语言搜索功能
 *
 * <AUTHOR>
 */
@Repository
public interface I18nDemoRepository extends BaseI18nRepository<I18nDemoEntity, Long> {


}
