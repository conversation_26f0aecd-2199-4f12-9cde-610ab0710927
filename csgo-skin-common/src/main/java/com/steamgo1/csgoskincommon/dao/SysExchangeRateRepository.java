package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.SysExchangeRateEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface SysExchangeRateRepository extends JpaRepository<SysExchangeRateEntity, Long> {
    SysExchangeRateEntity findFirstByOrderById();

    Boolean existsByIdNotNull();
}
