package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.PercentageUserRecordEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface RecordPercentageRepository extends JpaRepository<PercentageUserRecordEntity, Long>, JpaSpecificationExecutor<PercentageUserRecordEntity> {
    Page<PercentageUserRecordEntity> findByUserId(Long userId, Pageable pageable);

    Page<PercentageUserRecordEntity> findByIsWinIsTrue(Pageable pageable);

    List<PercentageUserRecordEntity> findByIsWinIsTrueOrderByUpdateTimeDesc();

    Integer countBySkinId(Long skinId);
}
