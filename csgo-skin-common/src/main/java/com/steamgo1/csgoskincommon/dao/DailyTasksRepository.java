package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.CaseEntity;
import com.steamgo1.csgoskincommon.entity.DailyTasksEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;


@Repository
public interface DailyTasksRepository extends JpaRepository<DailyTasksEntity, Long> {
    DailyTasksEntity findTopByBox(CaseEntity caseEntity);
}
