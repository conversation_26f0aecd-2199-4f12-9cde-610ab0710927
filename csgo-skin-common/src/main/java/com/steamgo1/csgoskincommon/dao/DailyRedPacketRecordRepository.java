package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.DailyRedPacketRecordEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface DailyRedPacketRecordRepository extends JpaRepository<DailyRedPacketRecordEntity, Long> {
    // 用户今天领取此类型红包次数
    @Query(value = "SELECT COUNT(*) FROM daily_red_packet_record WHERE DATE(create_time) = CURDATE() and user_id=?1 and daily_red_packet_id=?2", nativeQuery = true)
    Integer dayDailyRedPacketRecordCount(Long userId, Long dailyRedPacketId);

}
