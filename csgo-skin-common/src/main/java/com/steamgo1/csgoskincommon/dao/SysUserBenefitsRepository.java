package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.SysUserBenefitsEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface SysUserBenefitsRepository extends JpaRepository<SysUserBenefitsEntity, Long> {
    SysUserBenefitsEntity findFirstByOrderById();

    Boolean existsByIdNotNull();

}
