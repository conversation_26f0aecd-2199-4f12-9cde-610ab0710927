package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.CardCollectUserEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface CardCollectUserRepository extends JpaRepository<CardCollectUserEntity, Long> {
    Integer countByUserIdAndCardCollectCardId(Long userId, Long cardId);


    @Query(value = "SELECT user_id from card_collect_user WHERE card_collect_card_id=?1 GROUP BY user_id", nativeQuery = true)
    List<Long> queryUserIds(Long cardId);
}
