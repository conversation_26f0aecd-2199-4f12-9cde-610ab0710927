package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.CheckInRedPacketEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface CheckInRedPacketRepository extends JpaRepository<CheckInRedPacketEntity, Long> {
    List<CheckInRedPacketEntity> findByIsDeletedIsFalse();
}
