package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.RollHomeEntity;
import com.steamgo1.csgoskincommon.entity.RollHomeSkinEntity;
import com.steamgo1.csgoskincommon.entity.SkinEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RollHomeSkinRepository extends JpaRepository<RollHomeSkinEntity, Long> {
    List<RollHomeSkinEntity> findByRollHome(RollHomeEntity rollHome);

    //    List<RollHomeSkinEntity> findByRollHomeOrderById(RollHomeEntity rollHome);
    List<RollHomeSkinEntity> findByRollHomeAndIsDeletedIsFalseOrderById(RollHomeEntity rollHome);

    Page<RollHomeSkinEntity> findByRollHomeIdAndIsDeletedIsFalseOrderById(Long rollHomeId, Pageable pageable);

    List<RollHomeSkinEntity> findByUserIsNotNullOrderByUpdateTimeDesc();

    Page<RollHomeSkinEntity> findByUserIsNotNull(Pageable pageable);

    List<RollHomeSkinEntity> findBySkin(SkinEntity skin);

    List<RollHomeSkinEntity> findByRollHomeIdAndUserIsNotNull(Long rollHomeId);

    List<RollHomeSkinEntity> findByRollHomeIdAndUserIsNull(Long rollHomeId);

}
