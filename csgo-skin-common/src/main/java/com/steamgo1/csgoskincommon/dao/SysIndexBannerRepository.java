package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.SysIndexBannerEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SysIndexBannerRepository extends JpaRepository<SysIndexBannerEntity, Long> {
    List<SysIndexBannerEntity> findByIsDeletedIsFalseOrderByGradleDesc();
}
