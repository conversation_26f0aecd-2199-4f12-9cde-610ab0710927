package com.steamgo1.csgoskincommon.dao;


import com.steamgo1.csgoskincommon.entity.UserRedPacketRecordEntity;
import com.steamgo1.csgoskincommon.entity.enums.RedPacketMethod;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface UserRedPacketRecordRepository extends JpaRepository<UserRedPacketRecordEntity, Long> {
    Boolean existsByUserIdAndRedPacketId(Long userId, Long redPacketId);

    Boolean existsByUserId(Long userId);

    Boolean existsByUserIdAndRedPacketMethod(Long userId, RedPacketMethod redPacketMethod);

    Page<UserRedPacketRecordEntity> findByRedPacketId(Long redPacketId, Pageable pageable);
}
