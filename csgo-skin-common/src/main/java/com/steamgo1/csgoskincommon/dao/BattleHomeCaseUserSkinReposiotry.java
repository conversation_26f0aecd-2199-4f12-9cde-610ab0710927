package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.BattleHomeCaseUserSkinEntity;
import com.steamgo1.csgoskincommon.entity.BattleHomeEntity;
import com.steamgo1.csgoskincommon.entity.UserEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BattleHomeCaseUserSkinReposiotry extends JpaRepository<BattleHomeCaseUserSkinEntity, Long> {
    List<BattleHomeCaseUserSkinEntity> findByUserAndBattleHome(UserEntity user, BattleHomeEntity battleHome);

    List<BattleHomeCaseUserSkinEntity> findByBattleHome(BattleHomeEntity battleHome);
}
