package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.OrderChargeEntity;
import com.steamgo1.csgoskincommon.entity.PayChannelEntity;
import com.steamgo1.csgoskincommon.entity.enums.PayType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PayChannelRepository extends JpaRepository<PayChannelEntity, Long>, JpaSpecificationExecutor<PayChannelEntity> {
    
    List<PayChannelEntity> findByStatus(Integer status);
    PayChannelEntity findByPayTypeAndStatus(PayType payType, Integer status);
    PayChannelEntity findByCountry(String country);
}
