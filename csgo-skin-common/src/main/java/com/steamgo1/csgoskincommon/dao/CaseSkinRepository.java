package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.CaseEntity;
import com.steamgo1.csgoskincommon.entity.CaseLevelEntity;
import com.steamgo1.csgoskincommon.entity.CaseSkinEntity;
import com.steamgo1.csgoskincommon.entity.SkinEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CaseSkinRepository extends JpaRepository<CaseSkinEntity, Integer> {
    List<CaseSkinEntity> findByCaseEntity(CaseEntity caseEntity);

    List<CaseSkinEntity> findByCaseEntityOrderByGrade(CaseEntity caseEntity);

    List<CaseSkinEntity> findByCaseEntityOrderByGradeDesc(CaseEntity caseEntity);

    List<CaseSkinEntity> findByCaseEntityOrderByProbability(CaseEntity caseEntity);

    List<CaseSkinEntity> findByLevel(CaseLevelEntity caseLevelEntity);

    List<CaseSkinEntity> findBySkin(SkinEntity skinEntity);

    List<CaseSkinEntity> findBySkinId(Long skinId);

    List<CaseSkinEntity> findByCaseEntityId(Long caseId);

    void deleteByCaseEntity(CaseEntity caseEntity);

    CaseSkinEntity findByCaseEntityAndSkin(CaseEntity caseEntity, SkinEntity skinEntity);

    @Query(value = "SELECT * FROM CASE_SKIN order by id LIMIT ?1 OFFSET ?2", nativeQuery = true)
    List<CaseSkinEntity> findByOffset(Long limit, Long offset);

    List<CaseSkinEntity> findByCaseEntityAndSkinRarity(CaseEntity caseEntity, Long rarity);
}
