package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.CaseEntity;
import com.steamgo1.csgoskincommon.entity.CaseLevelEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CaseLevelRepository extends JpaRepository<CaseLevelEntity, Integer>, JpaSpecificationExecutor<CaseEntity> {

    CaseLevelEntity getByCaseEntityAndLevelNum(CaseEntity caseEntity, Integer levelNum);

    List<CaseLevelEntity> findByCaseEntityIdIn(List<Long> caseEntityIds);

    List<CaseLevelEntity> findByCaseEntity(CaseEntity caseEntity);

    @Query(value = "select max(box_level.level_num) from box_level where box_level.case_id = ?1 group by box_level.case_id", nativeQuery = true)
    Integer findMaxLevelNumByCaseEntity(Long caseId);

    CaseLevelEntity findById(Long id);

    void deleteByCaseEntityAndIdNotIn(CaseEntity caseEntity, List<Long> ids);
}
