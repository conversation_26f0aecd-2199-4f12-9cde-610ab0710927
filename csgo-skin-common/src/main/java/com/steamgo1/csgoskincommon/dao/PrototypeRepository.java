package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.PrototypeCategoryEntity;
import com.steamgo1.csgoskincommon.entity.PrototypeEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PrototypeRepository extends JpaRepository<PrototypeEntity, Integer> {
    PrototypeEntity findById(Long id);

    List<PrototypeEntity> findByPrototypeCategory(PrototypeCategoryEntity prototypeCategoryEntity);
}
