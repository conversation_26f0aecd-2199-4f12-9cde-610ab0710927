package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.AnnouncementEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AnnouncementRepository extends JpaRepository<AnnouncementEntity, Long>, JpaSpecificationExecutor<AnnouncementEntity> {
    List<AnnouncementEntity> findByIsShowIsTrue();
}
