package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.RedPacketEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface RedPacketRepository extends BaseI18nRepository<RedPacketEntity, Long> {
    RedPacketEntity findByIsDeletedIsFalseAndCode(String key);

    Boolean existsByIsDeletedIsFalseAndCode(String key);
}
