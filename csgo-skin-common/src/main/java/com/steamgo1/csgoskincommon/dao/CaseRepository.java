package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.CaseEntity;
import com.steamgo1.csgoskincommon.entity.enums.CaseType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CaseRepository extends JpaRepository<CaseEntity, Integer>, JpaSpecificationExecutor<CaseEntity> {
    Page<CaseEntity> findAllByCategoryAndIsDeleted(Long caseCategoryId, Boolean isDeleted, Pageable pageable);

    List<CaseEntity> findAllByCategoryAndIsDeleted(Long caseCategoryId, Boolean isDeleted);

    List<CaseEntity> findAllByIsDeleted(Boolean isDeleted);

    CaseEntity findById(Long caseId);

    CaseEntity findByIdAndIsDeletedAndIsSale(Long caseId, Boolean isDeleted, Boolean isSale);

    List<CaseEntity> findAllByCategoryAndIsDeletedAndIsSale(Long caseCategoryId, Boolean isDeleted, Boolean isSale);

    // 查询开箱箱子
    List<CaseEntity> findAllByCategoryAndIsDeletedIsFalseAndIsSaleIsTrueAndDisableOpenCaseIsFalse(Long caseCategoryId);

    // 查询对战箱子
    List<CaseEntity> findAllByCategoryAndIsDeletedIsFalseAndIsSaleIsTrueAndDisableBattleIsFalse(Long caseCategoryId);


    List<CaseEntity> findByIsRecommendIsTrueAndIsDeletedIsFalseAndIsSaleIsTrue();

    @Query(value = "SELECT * from box WHERE is_deleted=0 and disable_open_case=0 and type = 1 and is_sale=1 and price < 100 ORDER BY RAND() limit ?1", nativeQuery = true)
    List<CaseEntity> randomOpenCase(Integer total);

    @Query(value = "SELECT * from box WHERE is_deleted=0 and disable_battle=0 and type = 1 and is_sale=1 ORDER BY RAND() limit ?1", nativeQuery = true)
    List<CaseEntity> randomBattleCase(Integer total);

    CaseEntity findTopByIsDeletedIsFalseAndCaseType(CaseType caseType);

    List<CaseEntity> findByIsDeletedIsFalseAndCaseTypeOrderByGradle(CaseType caseType);

}
