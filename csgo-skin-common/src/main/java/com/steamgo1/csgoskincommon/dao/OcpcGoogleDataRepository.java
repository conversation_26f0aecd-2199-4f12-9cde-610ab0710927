package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.OcpcGoogleDataEntity;
import com.steamgo1.csgoskincommon.entity.enums.OcpcGoogleDataType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OcpcGoogleDataRepository extends JpaRepository<OcpcGoogleDataEntity, Long>, JpaSpecificationExecutor<OcpcGoogleDataEntity> {
    List<OcpcGoogleDataEntity> findByTypeAndUserId(OcpcGoogleDataType type, Long userId);
}
