package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.RollHomeEntity;
import com.steamgo1.csgoskincommon.entity.enums.RollHomeLotteryMethod;
import com.steamgo1.csgoskincommon.entity.enums.RollHomeStatus;
import com.steamgo1.csgoskincommon.entity.enums.RollHomeType;
import com.steamgo1.csgoskincommon.entity.enums.ThresholdType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RollHomeRepository extends JpaRepository<RollHomeEntity, Long>, JpaSpecificationExecutor<RollHomeEntity> {
    Page<RollHomeEntity> findByStatus(RollHomeStatus rollHomeStatus, Pageable pageable);

    Page<RollHomeEntity> findByStatusIn(List<RollHomeStatus> rollHomeStatuses, Pageable pageable);

    List<RollHomeEntity> findByStatusIn(List<RollHomeStatus> rollHomeStatuses);

    RollHomeEntity findTopByNameOrderByIdDesc(String name);

    List<RollHomeEntity> findByRollHomeTypeAndRollHomeLotteryMethodAndThresholdTypeAndStatusOrderByConsumeThreshold(RollHomeType rollHomeType, RollHomeLotteryMethod rollHomeLotteryMethod, ThresholdType thresholdType, RollHomeStatus status);

    Page<RollHomeEntity> findByStatusInAndIsRecommendIsTrue(List<RollHomeStatus> rollHomeStatuses, Pageable pageable);
}
