package com.steamgo1.csgoskincommon.dao;


import com.steamgo1.csgoskincommon.entity.DailyActivityEntity;
import com.steamgo1.csgoskincommon.entity.enums.DailyActivityType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository
public interface DailyActivityRepository extends JpaRepository<DailyActivityEntity, Long> {
    // 统计今日日活
    @Query(value = "SELECT COUNT(DISTINCT user_id) FROM daily_activity WHERE DATE(create_time) = CURDATE()", nativeQuery = true)
    Integer dayTotalUsers();

    // 统计7日用户新增
    @Query(value = "SELECT DATE(create_time) AS day, count(DISTINCT user_id) as count from daily_activity WHERE create_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) GROUP BY day", nativeQuery = true)
    List<Map<Date, Integer>> weekUsers();

    Boolean existsByUserIdAndTypeNot(Long userId, DailyActivityType type);

    // 用户今天某种活动次数
    @Query(value = "SELECT COUNT(*) FROM daily_activity WHERE DATE(create_time) = CURDATE() and user_id=?1 and type=?2", nativeQuery = true)
    Integer dayUserActivatyCount(Long userId, Integer type);


    // 用户本月某种活动次数
    @Query(value = "SELECT COUNT(*) FROM daily_activity WHERE  MONTH(create_time) = MONTH(CURRENT_DATE()) AND YEAR(create_time) = YEAR(CURRENT_DATE()) and user_id=?1 and type=?2", nativeQuery = true)
    Integer monthUserActivatyCount(Long userId, Integer type);

}
