package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.BattleHomeCaseUserRecordEntity;
import com.steamgo1.csgoskincommon.entity.BattleHomeEntity;
import com.steamgo1.csgoskincommon.entity.UserEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BattleHomeCaseUserRecordReposiotry extends JpaRepository<BattleHomeCaseUserRecordEntity, Long> {
    List<BattleHomeCaseUserRecordEntity> findByUserAndBattleHome(UserEntity user, BattleHomeEntity battleHome);

    List<BattleHomeCaseUserRecordEntity> findByBattleHome(BattleHomeEntity battleHome);
}
