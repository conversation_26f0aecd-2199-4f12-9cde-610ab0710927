package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.UserDiamondRecordEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserDiamondRecordRepository extends JpaRepository<UserDiamondRecordEntity, Long> {
    List<UserDiamondRecordEntity> findByUserId(Long userId);

    Page<UserDiamondRecordEntity> findByUserId(Long userId, Pageable pageable);

}
