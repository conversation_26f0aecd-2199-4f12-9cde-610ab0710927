package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.UserInviteEncourageRecordEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface UserInviteEncourageRecordRepository extends JpaRepository<UserInviteEncourageRecordEntity, Long> {
    Page<UserInviteEncourageRecordEntity> findByUserId(Long userId, Pageable pageable);
}
