package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.AdminUserEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AdminUserRepository extends JpaRepository<AdminUserEntity, Long> {
    AdminUserEntity findUserByUsername(String username);

    List<AdminUserEntity> findAllByIsSuperuser(boolean isSuperuser);
}
