package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.UserCoinRecordEntity;
import com.steamgo1.csgoskincommon.entity.enums.UserCoinChangeSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;

@Repository
public interface UserCoinRecordRepository extends JpaRepository<UserCoinRecordEntity, Long> {
    Page<UserCoinRecordEntity> findByUserId(Long userId, Pageable pageable);

    // 查询用户当日消费
    @Query(value = "select SUM(amount) from user_coin_record WHERE DATE_FORMAT(create_time,  '%Y-%m-%d') = DATE_FORMAT(now(),'%Y-%m-%d') and is_positive=0 and user_id=?1", nativeQuery = true)
    BigDecimal userDayConsume(Long userId);

    // 查询用户本周消费
    @Query(value = "select SUM(amount) from user_coin_record WHERE YEARWEEK(create_time, 1) = YEARWEEK(NOW(), 1) and is_positive=0 and user_id=?1", nativeQuery = true)
    BigDecimal userWeekConsume(Long userId);


    // 查询用户本月消费
    @Query(value = "select SUM(amount) from user_coin_record WHERE DATE_FORMAT(create_time,  '%Y-%m') = DATE_FORMAT(now(),'%Y-%m') and is_positive=0 and user_id=?1", nativeQuery = true)
    BigDecimal userMonthConsume(Long userId);

    // 查询用户当日充值
    @Query(value = "select SUM(amount) from user_coin_record WHERE DATE_FORMAT(create_time,  '%Y-%m-%d') = DATE_FORMAT(now(),'%Y-%m-%d') and source=0 and user_id=?1", nativeQuery = true)
    BigDecimal userDayCharge(Long userId);

    // 查询用户本周充值
    @Query(value = "select SUM(amount) from user_coin_record WHERE YEARWEEK(create_time, 1) = YEARWEEK(NOW(), 1) and source=0 and user_id=?1", nativeQuery = true)
    BigDecimal userWeekCharge(Long userId);


    // 查询用户本月充值
    @Query(value = "select SUM(amount) from user_coin_record WHERE DATE_FORMAT(create_time,  '%Y-%m') = DATE_FORMAT(now(),'%Y-%m') and source=0 and user_id=?1", nativeQuery = true)
    BigDecimal userMonthCharge(Long userId);

    // 用户总充值
    @Query(value = "select SUM(amount) from user_coin_record WHERE source=0 and user_id=?1", nativeQuery = true)
    BigDecimal userTotalCharge(Long userId);

    // 用户是否发生过充值
    Boolean existsByUserIdAndSource(Long userId, UserCoinChangeSource source);

    // 总消耗
    @Query(value = "select SUM(amount) from user_coin_record WHERE source=?1", nativeQuery = true)
    BigDecimal sourceTotalAmount(Integer source);

    // 用户总共获取的
    @Query(value = "select SUM(amount) from user_coin_record WHERE is_positive=0 and user_id=?1", nativeQuery = true)
    BigDecimal totalUserCoin(Long userId);
}
