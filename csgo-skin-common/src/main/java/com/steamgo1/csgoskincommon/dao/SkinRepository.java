package com.steamgo1.csgoskincommon.dao;

import com.steamgo1.csgoskincommon.entity.SkinEntity;
import com.steamgo1.csgoskincommon.enums.LanguageEnum;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface SkinRepository extends BaseI18nRepository<SkinEntity, Long> {
    SkinEntity queryByItemId(Long itemId);

    List<SkinEntity> findByItemId(Long itemId);

    /*default SkinEntity findByEnglishName(String englishName) {
        return findInLanguage("i18n_field_name", LanguageEnum.ENGLISH, englishName);
    }*/
    SkinEntity findByEnglishName(String englishName);

    @Query(value = "SELECT * FROM skin where enable_random = 1 ORDER BY RAND() LIMIT 1", nativeQuery = true)
    SkinEntity findRandomSkin();

    @Query(value = "SELECT * FROM skin where enable_percentage = 1 ORDER BY RAND() LIMIT 1", nativeQuery = true)
    SkinEntity randomPercentageSkin();

    @Query(value = "SELECT * FROM SKIN WHERE DIAMOND <= ?1 and substr(name, 1, 2) not in ?2 order by diamond desc limit 1", nativeQuery = true)
    SkinEntity findSkinByReference(BigDecimal reference, List<String> strs);


    @Query(value = "SELECT * FROM SKIN WHERE picture not like 'https://csgo-go.s3.us-west-2.amazonaws.com%' and is_deleted = 0", nativeQuery = true)
    List<SkinEntity> findSkinListToUploadStorage();

    // 查询商城在售
    Page<SkinEntity> findByIsDeletedIsFalseAndIsSaleIsTrue(@Nullable Specification<SkinEntity> spec, Pageable pageable);

    Page<SkinEntity> findByIsDeletedIsFalse(@Nullable Specification<SkinEntity> spec, Pageable pageable);

    default SkinEntity findTopByName(String name) {
        return findInCurrentLanguage("i18n_field_name", name);
    }

    Page<SkinEntity> findByIsDeletedIsFalse(Pageable pageable);

    Page<SkinEntity> findByIsSaleIsTrue(Pageable pageable);

}
