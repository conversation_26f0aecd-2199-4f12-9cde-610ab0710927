package com.steamgo1.csgoskincommon.utils;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class ReflectFieldCopyUtil {

    /**
     * 通过目标类Class创建实例并拷贝字段（无需提前实例化）
     *
     * @param targetClass 目标类Class对象
     * @param source      源对象
     * @return 新创建并拷贝后的目标对象
     */
    public static <T> T copyToNewInstance(Object source, Class<T> targetClass) {
        if (targetClass == null) {
            throw new IllegalArgumentException("目标类不能为null");
        }
        try {
            // 通过无参构造器创建实例
            T target = createInstance(targetClass);
            return copySameFields(target, source);
        } catch (NoSuchMethodException e) {
            throw new FieldCopyException("目标类缺少无参构造函数", e);
        }
    }

    private static <T> T createInstance(Class<T> clazz) throws NoSuchMethodException {
        try {
            Constructor<T> constructor = clazz.getDeclaredConstructor();
            constructor.setAccessible(true);
            return constructor.newInstance();
        } catch (InvocationTargetException | InstantiationException | IllegalAccessException e) {
            throw new FieldCopyException("实例化失败: " + clazz.getName(), e);
        }
    }

    /**
     * 拷贝同名同类型字段并返回目标对象（链式调用支持）
     *
     * @param target 目标对象（不可为null）
     * @param source 源对象
     * @return 拷贝后的目标对象
     */
    public static <T> T copySameFields(T target, Object source) {
        if (target == null) {
            throw new IllegalArgumentException("目标对象不能为null");
        }
        if (source == null) {
            return target; // 源为null时直接返回目标
        }

        Map<String, Field> targetFields = getDeclaredFieldsMap(target.getClass());
        Map<String, Field> sourceFields = getDeclaredFieldsMap(source.getClass());

        sourceFields.forEach((name, srcField) -> {
            Field targetField = targetFields.get(name);
            if (isFieldCopyable(srcField, targetField)) {
                copyFieldValue(source, target, srcField, targetField);
            }
        });

        return target;
    }

    // 获取类层次结构中的所有字段（包含父类）
    private static Map<String, Field> getDeclaredFieldsMap(Class<?> clazz) {
        Map<String, Field> fieldMap = new HashMap<>();
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            Arrays.stream(currentClass.getDeclaredFields())
                    .forEach(f -> fieldMap.put(f.getName(), f));
            currentClass = currentClass.getSuperclass();
        }
        return fieldMap;
    }

    // 字段可拷贝条件校验
    private static boolean isFieldCopyable(Field src, Field dest) {
        return dest != null
                && src.getType().equals(dest.getType())
                && !isStaticOrFinal(dest);
    }

    // 排除静态和final字段
    private static boolean isStaticOrFinal(Field field) {
        int modifiers = field.getModifiers();
        return java.lang.reflect.Modifier.isStatic(modifiers)
                || java.lang.reflect.Modifier.isFinal(modifiers);
    }

    // 执行字段值拷贝
    private static void copyFieldValue(Object srcObj, Object destObj,
                                       Field srcField, Field destField) {
        try {
            srcField.setAccessible(true);
            Object value = srcField.get(srcObj);

            if (value != null) {
                destField.setAccessible(true);
                destField.set(destObj, value);
            }
        } catch (IllegalAccessException e) {
            throw new FieldCopyException("字段拷贝失败: " + srcField.getName(), e);
        }
    }

    // 自定义异常类
    public static class FieldCopyException extends RuntimeException {
        public FieldCopyException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
