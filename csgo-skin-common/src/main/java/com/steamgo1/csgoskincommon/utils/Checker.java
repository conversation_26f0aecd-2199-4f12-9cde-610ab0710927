package com.steamgo1.csgoskincommon.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Checker {
    public static boolean checkSteamTradeoffer(String tradeoffer) {
        String reStr = "^https:\\/\\/steamcommunity\\.com\\/tradeoffer\\/new\\/\\?partner=\\d+?&token=\\S+";
        Pattern p = Pattern.compile(reStr);
        return p.matcher(tradeoffer).matches();
    }

    public static String getDomainName(String queryParam, int group) {
        String reStr = "^domain_name=(.*?)&(.*)";
        Pattern p = Pattern.compile(reStr);
        Matcher m = p.matcher(queryParam);
        if (m.find()) {
            if (group == 1) {
                return m.group(1);
            } else if (group == 2) {
                return m.group(2);
            }
        }
        return "";
    }


    public static void main(String[] args) {
        String queryParam = "domain_name=client.test.steamgo1.com&openid.ns=http://specs.openid.net/auth/2.0&openid.mode=id_res&openid.op_endpoint=https://steamcommunity.com/openid/login&openid.claimed_id=https://steamcommunity.com/openid/id/76561198835097066&openid.identity=https://steamcommunity.com/openid/id/76561198835097066&openid.return_to=http://127.0.0.1:5002/csgo/api/auth/steam/callback?domain_name=client.test.steamgo1.com&openid.response_nonce=2023-06-29T08:22:56Z4uVNURW/58sRQNGwlsl/xTYtpY4=&openid.assoc_handle=1234567890&openid.signed=signed,op_endpoint,claimed_id,identity,return_to,response_nonce,assoc_handle&openid.sig=VlX8rDSY82oJRZtRKA0iC8qETMI=";
        System.out.println(checkSteamTradeoffer("https://steamcommunity.com/tradeoffer/new/?partner=1202137454&token=wnbRaWrK"));
        System.out.println(getDomainName(queryParam, 1));
        System.out.println(getDomainName(queryParam, 2));
    }
}
