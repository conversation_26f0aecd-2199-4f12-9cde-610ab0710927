package com.steamgo1.csgoskincommon.utils;

import java.util.concurrent.ThreadLocalRandom;

public class RandomUtils {

    /**
     * 生成一个范围的随机数
     *
     * @param min
     * @param max
     * @return
     */
    public static int randomInt(int min, int max) {
        if (min > max) {
            return min;
        }
        return ThreadLocalRandom.current().nextInt(min, max + 1);
    }

    public static void main(String[] args) {
        System.out.println(randomInt(1, 3));
    }

    /**
     * 生成一个随机整数，其范围由 min、max 和 fix 参数动态确定。
     * <p>
     * 规则：
     * 1. 若 max >= fix，则上限为 max，否则为 fix。
     * 2. 若 min 大于等于最终确定的上限，直接返回 min。
     * 3. 否则生成 [min, 上限] 之间的随机整数（含两端）。
     *
     * @param min 最小值（包含）
     * @param max 初始上限候选值
     * @param fix 修正上限候选值
     * @return 符合规则的随机整数或 min（当参数不符合逻辑时）
     */
    public static int randomInt(int min, int max, int fix) {
        final int upper = Math.min(max, fix);
        return (min >= upper) ? min : ThreadLocalRandom.current().nextInt(min, upper + 1);
    }
}
