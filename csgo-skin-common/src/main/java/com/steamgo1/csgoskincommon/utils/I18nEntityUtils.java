package com.steamgo1.csgoskincommon.utils;

import com.steamgo1.csgoskincommon.enums.LanguageEnum;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 多语言实体工具类
 * 提供多语言字段的通用访问方法
 * 
 * <AUTHOR>
 */
@Component
public class I18nEntityUtils {
    
    /**
     * 根据当前语言获取字段值
     * 使用LanguageEnum枚举进行语言判断
     *
     * @param zhValue 中文值
     * @param enValue 英文值
     * @param defaultValue 默认值
     * @return 当前语言对应的值
     */
    public static String getCurrentLanguageValue(String zhValue, String enValue, String defaultValue) {
        String locale = LocaleContextHolder.getLocale().getLanguage();
        LanguageEnum currentLanguage = LanguageEnum.fromCode(locale);
        return getValueByLanguage(currentLanguage, zhValue, enValue, defaultValue);
    }
    
    /**
     * 根据指定语言代码获取字段值
     *
     * @param languageCode 语言代码
     * @param zhValue 中文值
     * @param enValue 英文值
     * @param defaultValue 默认值
     * @return 指定语言对应的值
     */
    public static String getValueByLanguage(String languageCode, String zhValue, String enValue, String defaultValue) {
        LanguageEnum language = LanguageEnum.fromCode(languageCode);
        return getValueByLanguage(language, zhValue, enValue, defaultValue);
    }

    /**
     * 根据语言枚举获取字段值
     * 使用枚举进行语言判断，便于扩展新语言
     *
     * @param language 语言枚举
     * @param zhValue 中文值
     * @param enValue 英文值
     * @param defaultValue 默认值
     * @return 指定语言对应的值
     */
    public static String getValueByLanguage(LanguageEnum language, String zhValue, String enValue, String defaultValue) {
        switch (language) {
            case CHINESE:
                return isNotEmpty(zhValue) ? zhValue : defaultValue;
            case ENGLISH:
                return isNotEmpty(enValue) ? enValue :
                       (isNotEmpty(zhValue) ? zhValue : defaultValue);
            case JAPANESE:
            case KOREAN:
            case RUSSIAN:
                // 预留其他语言，目前fallback到英文或中文
                return isNotEmpty(enValue) ? enValue :
                       (isNotEmpty(zhValue) ? zhValue : defaultValue);
            default:
                // 默认返回中文，如果中文为空则返回默认值
                return isNotEmpty(zhValue) ? zhValue : defaultValue;
        }
    }
    
    /**
     * 获取所有语言的字段值Map
     * 使用LanguageEnum枚举遍历所有支持的语言
     *
     * @param zhValue 中文值
     * @param enValue 英文值
     * @param defaultValue 默认值
     * @return Map<语言代码, 值>
     */
    public static Map<String, String> getI18nMap(String zhValue, String enValue, String defaultValue) {
        Map<String, String> map = new HashMap<>();

        // 遍历所有支持的语言
        for (LanguageEnum language : LanguageEnum.getSupportedLanguages()) {
            String value = getValueByLanguage(language, zhValue, enValue, defaultValue);
            if (isNotEmpty(value)) {
                map.put(language.getCode(), value);
            }
        }

        // 如果多语言字段都为空，使用默认值
        if (map.isEmpty() && isNotEmpty(defaultValue)) {
            map.put("default", defaultValue);
        }

        return map;
    }
    
    /**
     * 检查字符串是否不为空
     */
    private static boolean isNotEmpty(String value) {
        return value != null && !value.trim().isEmpty();
    }
    
    /**
     * 检查是否有完整的多语言数据
     */
    public static boolean hasCompleteI18nData(String zhValue, String enValue) {
        return isNotEmpty(zhValue) && isNotEmpty(enValue);
    }
    
    /**
     * 获取当前语言代码
     */
    public static String getCurrentLanguage() {
        return LocaleContextHolder.getLocale().getLanguage();
    }

    /**
     * 获取当前语言枚举
     */
    public static LanguageEnum getCurrentLanguageEnum() {
        return LanguageEnum.fromCode(getCurrentLanguage());
    }

    /**
     * 是否为中文环境
     */
    public static boolean isChineseLocale() {
        return getCurrentLanguageEnum().isChinese();
    }

    /**
     * 是否为英文环境
     */
    public static boolean isEnglishLocale() {
        return getCurrentLanguageEnum().isEnglish();
    }

    /**
     * 获取所有支持的语言代码
     */
    public static String[] getSupportedLanguageCodes() {
        LanguageEnum[] languages = LanguageEnum.getSupportedLanguages();
        String[] codes = new String[languages.length];
        for (int i = 0; i < languages.length; i++) {
            codes[i] = languages[i].getCode();
        }
        return codes;
    }

    /**
     * 检查语言是否支持
     */
    public static boolean isLanguageSupported(String languageCode) {
        return LanguageEnum.isSupported(languageCode);
    }
}
