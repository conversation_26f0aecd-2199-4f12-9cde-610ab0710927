package com.steamgo1.csgoskincommon.utils;

import com.alibaba.fastjson.JSONArray;
import com.steamgo1.csgoskincommon.enums.LanguageEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.net.URLEncoder;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

/**
 * 翻译工具类
 * 提供多语言翻译功能
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class TranslationUtils {

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 翻译文本到目标语言
     * 
     * @param originalText 原始文本内容
     * @param sourceLanguage 原始内容的语言类型
     * @param targetLanguage 目标语言类型
     * @return 翻译后的文本，翻译失败时返回原文
     */
    public String translateText(String originalText, LanguageEnum sourceLanguage, LanguageEnum targetLanguage) {
        return translateTextWithRetry(originalText, sourceLanguage, targetLanguage, 3);
    }
    
    /**
     * 带重试机制的翻译方法
     * 
     * @param originalText 原始文本内容
     * @param sourceLanguage 原始内容的语言类型
     * @param targetLanguage 目标语言类型
     * @param maxRetries 最大重试次数
     * @return 翻译后的文本，翻译失败时返回原文
     */
    private String translateTextWithRetry(String originalText, LanguageEnum sourceLanguage, LanguageEnum targetLanguage, int maxRetries) {
        // 参数校验
        if (originalText == null || originalText.trim().isEmpty()) {
            log.warn("翻译文本为空，返回空字符串");
            return "";
        }
        
        if (sourceLanguage == null || targetLanguage == null) {
            log.warn("源语言或目标语言为空，返回原文: {}", originalText);
            return originalText;
        }
        
        // 如果源语言和目标语言相同，直接返回原文
        if (sourceLanguage.equals(targetLanguage)) {
            log.debug("源语言和目标语言相同，返回原文: {}", originalText);
            return originalText;
        }

        // 清理输入文本，移除可能的语言标识
        String cleanedText = cleanInputText(originalText);
        if (cleanedText.trim().isEmpty()) {
            log.warn("清理后的文本为空，返回原文: {}", originalText);
            return originalText;
        }

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                // 使用Google翻译API进行翻译
                // String encodedText = URLEncoder.encode(cleanedText, StandardCharsets.UTF_8.name());
                String url = String.format(
                    "https://translate.googleapis.com/translate_a/single?client=gtx&sl=%s&tl=%s&dt=t&q=%s",
                    sourceLanguage.getCode(),
                    targetLanguage.getCode(),
                    cleanedText
                );

                log.info("发起翻译请求 (尝试 {}/{}): {} -> {}, 原文: {}, url: {}", 
                    attempt, maxRetries, sourceLanguage.getCode(), targetLanguage.getCode(), cleanedText, url);

                ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
                if (response.getStatusCode().equals(HttpStatus.OK)) {
                    String responseBody = response.getBody();
                    log.info("翻译响应: {}", responseBody);
                    String translatedText = parseTranslationResponse(responseBody);
                    
                    if (translatedText != null && !translatedText.trim().isEmpty()) {
                        // 验证翻译结果是否合理
                        if (isValidTranslation(cleanedText, translatedText, sourceLanguage, targetLanguage)) {
                            log.info("翻译成功 - 原文: {} ({}), 译文: {} ({})", 
                                cleanedText, sourceLanguage.getCode(), 
                                translatedText, targetLanguage.getCode());
                            return translatedText;
                        } else {
                            log.warn("翻译结果不合理，尝试重试: 原文={}, 译文={}", cleanedText, translatedText);
                        }
                    }
                } else {
                    log.warn("翻译API返回非200状态码 (尝试 {}/{}): {}", attempt, maxRetries, response.getStatusCode());
                }
            } catch (Exception e) {
                log.error("翻译失败 (尝试 {}/{}) - 原文: {} ({}), 目标语言: {}, 错误: {}", 
                    attempt, maxRetries, cleanedText, sourceLanguage.getCode(), targetLanguage.getCode(), e.getMessage());
            }
            
            // 如果不是最后一次尝试，等待一段时间再重试
            if (attempt < maxRetries) {
                try {
                    Thread.sleep(1000 * attempt); // 递增等待时间
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        // 所有重试都失败，返回原文
        log.warn("翻译失败，返回原文: {}", originalText);
        return originalText;
    }

    /**
     * 从英文翻译到目标语言（保持向后兼容）
     * 
     * @param englishText 英文文本
     * @param targetLanguage 目标语言
     * @return 翻译后的文本
     */
    public String translateFromEnglish(String englishText, LanguageEnum targetLanguage) {
        return translateText(englishText, LanguageEnum.ENGLISH, targetLanguage);
    }

    /**
     * 从中文翻译到目标语言
     * 
     * @param chineseText 中文文本
     * @param targetLanguage 目标语言
     * @return 翻译后的文本
     */
    public String translateFromChinese(String chineseText, LanguageEnum targetLanguage) {
        return translateText(chineseText, LanguageEnum.CHINESE, targetLanguage);
    }

    /**
     * 批量翻译到多个目标语言
     * 
     * @param originalText 原始文本
     * @param sourceLanguage 源语言
     * @param targetLanguages 目标语言数组
     * @return 语言代码到翻译结果的映射
     */
    public java.util.Map<String, String> translateToMultipleLanguages(
            String originalText, 
            LanguageEnum sourceLanguage, 
            LanguageEnum... targetLanguages) {
        
        java.util.Map<String, String> translations = new java.util.HashMap<>();
        
        if (targetLanguages == null || targetLanguages.length == 0) {
            log.warn("目标语言列表为空");
            return translations;
        }
        
        for (LanguageEnum targetLanguage : targetLanguages) {
            String translatedText = translateText(originalText, sourceLanguage, targetLanguage);
            translations.put(targetLanguage.getCode(), translatedText);
        }
        
        return translations;
    }

    /**
     * 翻译到所有支持的语言
     * 
     * @param originalText 原始文本
     * @param sourceLanguage 源语言
     * @return I18nField对象，包含所有语言的翻译
     */
    public I18nField translateToAllSupportedLanguages(String originalText, LanguageEnum sourceLanguage) {
        java.util.Map<LanguageEnum, String> translations = new java.util.HashMap<>();
        
        // 添加原文
        translations.put(sourceLanguage, originalText);
        
        // 翻译到其他支持的语言
        for (LanguageEnum targetLanguage : LanguageEnum.getSupportedLanguages()) {
            if (!targetLanguage.equals(sourceLanguage)) {
                String translatedText = translateText(originalText, sourceLanguage, targetLanguage);
                translations.put(targetLanguage, translatedText);
            }
        }
        
        return new I18nField(translations);
    }

    /**
     * 解析Google翻译API的响应
     * 
     * @param responseBody API响应体
     * @return 翻译结果，解析失败返回null
     */
    private String parseTranslationResponse(String responseBody) {
        try {
            log.debug("开始解析翻译响应: {}", responseBody);
            
            // 解析Google翻译返回的JSON格式
            JSONArray jsonArray = JSONArray.parseArray(responseBody);
            if (jsonArray != null && !jsonArray.isEmpty()) {
                JSONArray translations = jsonArray.getJSONArray(0);
                if (translations != null && !translations.isEmpty()) {
                    JSONArray firstTranslation = translations.getJSONArray(0);
                    if (firstTranslation != null && !firstTranslation.isEmpty()) {
                        String translatedText = firstTranslation.getString(0);
                        
                        // 检查是否需要URL解码
                        if (translatedText != null && translatedText.contains("%")) {
                            try {
                                String decodedText = URLDecoder.decode(translatedText, StandardCharsets.UTF_8.name());
                                log.debug("URL解码后的翻译结果: {}", decodedText);
                                return decodedText;
                            } catch (Exception decodeException) {
                                log.warn("URL解码失败，使用原始结果: {}", decodeException.getMessage());
                                return translatedText;
                            }
                        }
                        
                        return translatedText;
                    }
                }
            }
            
            log.warn("翻译响应格式异常，无法解析: {}", responseBody);
        } catch (Exception e) {
            log.error("解析翻译响应失败: {}, 响应内容: {}", e.getMessage(), responseBody);
        }
        return null;
    }

    /**
     * 清理输入文本，移除语言标识等无关内容
     * 
     * @param inputText 输入文本
     * @return 清理后的文本
     */
    private String cleanInputText(String inputText) {
        if (inputText == null) {
            return "";
        }
        
        String cleaned = inputText.trim();
        
        // 移除语言标识，如 "(zh)", "(en)" 等
        cleaned = cleaned.replaceAll("\\([a-z]{2}\\)", "").trim();
        
        // 移除多余的空格
        cleaned = cleaned.replaceAll("\\s+", " ").trim();
        
        return cleaned;
    }
    
    /**
     * 验证翻译结果是否合理
     * 
     * @param originalText 原文
     * @param translatedText 译文
     * @param sourceLanguage 源语言
     * @param targetLanguage 目标语言
     * @return true表示翻译结果合理
     */
    private boolean isValidTranslation(String originalText, String translatedText, 
                                     LanguageEnum sourceLanguage, LanguageEnum targetLanguage) {
        if (translatedText == null || translatedText.trim().isEmpty()) {
            return false;
        }
        
        // 检查是否包含URL编码字符
        if (translatedText.contains("%")) {
            return false;
        }
        
        // 检查是否与原文完全相同（除了语言标识）
        String cleanedOriginal = cleanInputText(originalText);
        if (cleanedOriginal.equals(translatedText)) {
            return false;
        }
        
        // 检查翻译结果长度是否合理（不能太短或太长）
        int originalLength = cleanedOriginal.length();
        int translatedLength = translatedText.length();
        
        // 翻译结果长度应该在原文长度的0.5-3倍之间
        if (translatedLength < originalLength * 0.5 || translatedLength > originalLength * 3) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查翻译服务是否可用
     * 
     * @return true表示服务可用
     */
    public boolean isTranslationServiceAvailable() {
        try {
            // 使用简单的测试翻译来检查服务可用性
            String testResult = translateText("test", LanguageEnum.ENGLISH, LanguageEnum.CHINESE);
            return testResult != null && !testResult.equals("test");
        } catch (Exception e) {
            log.error("翻译服务不可用: {}", e.getMessage());
            return false;
        }
    }
}
