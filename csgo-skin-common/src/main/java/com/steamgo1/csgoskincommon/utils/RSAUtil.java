package com.steamgo1.csgoskincommon.utils;

import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

public class RSAUtil {

    /**
     * 使用私钥对字符串进行RSA+SHA256签名
     *
     * @param privateKeyStr 私钥字符串
     * @param data          待签名字符串
     * @return 签名结果字符串
     * @throws Exception
     */
    public static String signWithRSAAndSHA256(String privateKeyStr, String data) throws Exception {
        byte[] privateKeyBytes = Base64.getDecoder().decode(privateKeyStr);
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(pkcs8KeySpec);
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(privateKey);
        signature.update(data.getBytes("UTF-8"));
        byte[] signBytes = signature.sign();
        return Base64.getEncoder().encodeToString(signBytes);
    }

    /**
     * 使用公钥验证RSA+SHA256签名
     *
     * @param publicKeyStr 公钥字符串
     * @param data         待验证字符串
     * @param sign         签名结果字符串
     * @return 验证结果
     * @throws Exception
     */
    public static boolean verifyWithRSAAndSHA256(String publicKeyStr, String data, String sign) throws Exception {
        byte[] publicKeyBytes = Base64.getDecoder().decode(publicKeyStr);
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(publicKeyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(x509KeySpec);
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initVerify(publicKey);
        signature.update(data.getBytes("UTF-8"));
        return signature.verify(Base64.getDecoder().decode(sign));
    }

    public static void main(String[] args) throws Exception {

        RSAUtil.signWithRSAAndSHA256("5DLWMFUR5s5lDota3XbRL1tONES4jY", "ddddddddddddd");
    }
}
