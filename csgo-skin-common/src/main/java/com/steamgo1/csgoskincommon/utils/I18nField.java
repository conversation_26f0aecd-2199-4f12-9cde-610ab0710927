package com.steamgo1.csgoskincommon.utils;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.steamgo1.csgoskincommon.converter.LanguageEnumKeyDeserializer;
import com.steamgo1.csgoskincommon.enums.LanguageEnum;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.HashMap;
import java.util.Map;

/**
 * 国际化字段类
 * 支持多语言数据存储和获取
 * 
 * <AUTHOR>
 */
public class I18nField {

    @JsonDeserialize(keyUsing = LanguageEnumKeyDeserializer.class)
    private Map<LanguageEnum, String> translations = new HashMap<>();
    
    public I18nField() {}

    /**
     * 基于Map<LanguageEnum, String>的构造方法
     * @param languageMap 语言枚举到值的映射
     */
    public I18nField(Map<LanguageEnum, String> languageMap) {
        this.translations = languageMap != null ? languageMap : new HashMap<>();
    }

    /**
     * 基于Map<String, String>的构造方法（兼容性）
     * @param stringMap 字符串键值对映射
     */
    public I18nField(Map<String, String> stringMap, boolean isStringMap) {
        this.translations = new HashMap<>();
        if (stringMap != null) {
            for (Map.Entry<String, String> entry : stringMap.entrySet()) {
                LanguageEnum language = LanguageEnum.fromCode(entry.getKey());
                this.translations.put(language, entry.getValue());
            }
        }
    }

    /**
     * 基于Map<LanguageEnum, String>的静态工厂方法
     * @param languageMap 语言枚举到值的映射
     * @return I18nField实例
     */
    public static I18nField fromLanguageMap(Map<LanguageEnum, String> languageMap) {
        return new I18nField(languageMap);
    }
    
    /**
     * 获取指定语言的值
     */
    public String get(LanguageEnum locale) {
        return translations.getOrDefault(locale, translations.get(LanguageEnum.ENGLISH));
    }

    /**
     * 获取指定语言的值（字符串参数）
     */
    public String get(String languageCode) {
        LanguageEnum language = LanguageEnum.fromCode(languageCode);
        return get(language);
    }
    
    /**
     * 获取当前语言的值
     */
    @JsonIgnore
    public String getCurrent() {
        String locale = LocaleContextHolder.getLocale().getLanguage();
        return get(LanguageEnum.fromCode(locale));
    }
    
    /**
     * 设置指定语言的值
     */
    public void put(LanguageEnum locale, String value) {
        translations.put(locale, value);
    }

    /**
     * 获取所有语言数据（字符串键）
     */
    @JsonIgnore
    public Map<String, String> getAllLanguages() {
        Map<String, String> result = new HashMap<>();
        for (Map.Entry<LanguageEnum, String> entry : translations.entrySet()) {
            result.put(entry.getKey().getCode(), entry.getValue());
        }
        return result;
    }
    
    /**
     * 检查是否为空
     */
    @JsonIgnore
    public boolean isEmpty() {
        return translations.isEmpty() || translations.values().stream().allMatch(v -> v == null || v.trim().isEmpty());
    }
    
    // getters and setters
    @JsonDeserialize(keyUsing = LanguageEnumKeyDeserializer.class)
    public Map<LanguageEnum, String> getTranslations() {
        return translations;
    }

    @JsonDeserialize(keyUsing = LanguageEnumKeyDeserializer.class)
    public void setTranslations(Map<LanguageEnum, String> translations) {
        this.translations = translations != null ? translations : new HashMap<>();
    }
    
    @Override
    public String toString() {
        return getCurrent();
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        I18nField i18nField = (I18nField) obj;
        return translations != null ? translations.equals(i18nField.translations) : i18nField.translations == null;
    }

    @Override
    public int hashCode() {
        return translations != null ? translations.hashCode() : 0;
    }

    public static I18nField of(Map<LanguageEnum, String> translations) {
        return new I18nField(translations);
    }

    public static I18nField empty() {
        return new I18nField();
    }
}
