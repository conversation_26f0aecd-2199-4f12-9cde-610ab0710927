package com.steamgo1.csgoskincommon.utils;

import java.util.ArrayList;
import java.util.List;

public class BatchQueryUtil {

    /**
     * 生成分页范围列表（支持Long大数）
     *
     * @param total     总记录数
     * @param batchSize 每批处理量
     * @return 分页范围列表
     */
    public static List<Range> generateBatchRanges(Long total, Long batchSize) {
        List<Range> ranges = new ArrayList<>();
        if (total <= 0 || batchSize <= 0) return ranges;

        // 计算总批次数（使用数学公式避免循环变量溢出）
        Long batchCount = (total + batchSize - 1) / batchSize;

        // 生成批次范围
        for (Long i = 0L; i < batchCount; i++) {  // 注意：这里实际仍受限于Java循环机制
            Long start = i * batchSize;
            Long end = Math.min((i + 1) * batchSize, total);
            ranges.add(new Range(start, end));
        }

        return ranges;
    }

    public static void main(String[] args) {
        List<Range> r = generateBatchRanges(100L, 50L);
        for (Range range : r) {
            System.out.println(range.getStart());
            System.out.println(range.getEnd());
        }
    }

    // 定义分段范围值承载对象（使用Long类型）
    public static class Range {
        private final Long start;
        private final Long end;

        public Range(Long start, Long end) {
            this.start = start;
            this.end = end;
        }

        public Long getStart() {
            return start;
        }

        public Long getEnd() {
            return end;
        }
    }
}
