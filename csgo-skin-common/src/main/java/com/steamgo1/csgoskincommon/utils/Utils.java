package com.steamgo1.csgoskincommon.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Utils {
    public static String generateOrderNo(Long userId, String prefix) {
        return prefix + DateUtils.now(DateUtils.DateFormat.LONG_DATE_PATTERN_WITH_MILSEC_NONE) + userId + CharUtil.getRandomNum(4);
    }

    public static String generateOperationNo(String prefix) {
        return prefix + DateUtils.now(DateUtils.DateFormat.LONG_DATE_PATTERN_WITH_MILSEC_NONE) + CharUtil.getRandomNum(4);
    }

    public static String regexrelamName(String url) {
        if (url == null) {
            return "";
        }
        String domainPatter = "[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\\.?";

        Pattern pattern = Pattern.compile(domainPatter, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(url);
        while (matcher.find()) {
            String domainName = matcher.group(0);
            return domainName;
        }
        return null;
    }

    public static String regexBaiduWorkId(String url) {
        String domainPatter = "e_keywordid2=(\\d*)";
        Pattern pattern = Pattern.compile(domainPatter, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(url);
        while (matcher.find()) {
            String wordId = matcher.group(1);
            return wordId;
        }
        return null;
    }


    public static String extractParam(String url, String paramName) {
        // 正则表达式：匹配 paramName= 后的值（非&字符组合）
        String regex = paramName + "=([^&]*)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(url);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    public static void main(String[] args) {
        System.out.println(regexBaiduWorkId("http://120csgo.wxtyxkj.cn?source=cs:go多普勒开箱子&plan=chongfan-PC【4.12+wuqixiang】-zanting&unit=PC【4.11+wuqixiang】&keyword=cs:go多普勒开箱子&e_creative={creative}&e_keywordid={keywordid}&e_keywordid2=591200816121"));
    }
}
