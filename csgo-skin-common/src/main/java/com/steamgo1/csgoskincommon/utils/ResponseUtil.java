package com.steamgo1.csgoskincommon.utils;

import com.steamgo1.csgoskincommon.enums.ResponseCode;
import com.steamgo1.csgoskincommon.vo.Response;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * 通用响应体
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Accessors(chain = true)
public class ResponseUtil {

    public static <T> Response<T> ok() {
        return new Response<>();
    }

    public static <T> Response<T> ok(T data) {
        return new Response<>(data);
    }

    public static <T> Response<T> ok(T data, String message) {
        return new Response<>(data, message);
    }

    public static <T> Response<T> fail() {
        return new Response<>(ResponseCode.FAIL);
    }

    public static <T> Response<T> fail(ResponseCode responseCode) {
        return new Response<>(responseCode);
    }

    public static <T> Response<T> fail(String message) {
        return new Response<>(ResponseCode.FAIL.getCode(), message);
    }

    public static <T> Response<T> fail(int respCode, String message) {
        return new Response<>(respCode, message);
    }


}
