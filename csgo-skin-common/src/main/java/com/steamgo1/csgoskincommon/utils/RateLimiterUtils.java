package com.steamgo1.csgoskincommon.utils;


import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;


/**
 * 限流
 */
@Component
@Slf4j
public class RateLimiterUtils {
    private static final ConcurrentMap<Long, RateLimiter> userRateLimiters = new ConcurrentHashMap<>();


    /**
     * 基于用户ID限流, 2秒钟就一次
     *
     * @param userId
     * @return
     */
    public static Boolean tryAcquire(Long userId) {
        RateLimiter rateLimiter = userRateLimiters.computeIfAbsent(userId, k -> RateLimiter.create(0.5));
        return rateLimiter.tryAcquire();
    }
}
