package com.steamgo1.csgoskincommon.utils;

import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;

public class CsvUtils {
    // 增强版本：自动创建不存在的路径
    public static void writeRecord(String[] args, String fileName) {
        Path outputPath = Paths.get("choujiangjieguo", fileName + ".csv");

        try {
            // 自动创建父级目录（递归创建）
            Path parentDir = outputPath.getParent();
            if (parentDir != null && !Files.exists(parentDir)) {
                Files.createDirectories(parentDir);
            }

            // 使用NIO方式写入（支持更完善的OpenOption配置）
            try (BufferedWriter writer = Files.newBufferedWriter(
                    outputPath,
                    StandardOpenOption.CREATE,
                    StandardOpenOption.APPEND
            )) {
                StringBuilder csvLine = new StringBuilder();
                for (int i = 0; i < args.length; i++) {
                    String field = args[i] != null ? args[i] : "";
                    boolean needQuotes = field.contains(",")
                            || field.contains("\"")
                            || field.contains("\n");

                    if (needQuotes) {
                        csvLine.append("\"")
                                .append(field.replace("\"", "\"\""))
                                .append("\"");
                    } else {
                        csvLine.append(field);
                    }

                    if (i < args.length - 1) {
                        csvLine.append(",");
                    }
                }
                csvLine.append(System.lineSeparator()); // 改用系统行分隔符
                writer.write(csvLine.toString());
            }
        } catch (IOException e) {
            throw new RuntimeException("CSV写入失败，路径: " + outputPath, e);
        }
    }
}
