package com.steamgo1.csgoskincommon.utils;

import com.steamgo1.csgoskincommon.enums.LanguageEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 翻译工具类使用示例
 * 展示如何使用新的翻译功能
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class TranslationExample {

    @Autowired
    private TranslationUtils translationUtils;

    /**
     * 示例1: 基本翻译功能
     * 从指定源语言翻译到目标语言
     */
    public void basicTranslationExample() {
        log.info("=== 基本翻译功能示例 ===");
        
        // 从中文翻译到英文
        String chineseText = "你好世界";
        String englishResult = translationUtils.translateText(chineseText, LanguageEnum.CHINESE, LanguageEnum.ENGLISH);
        log.info("中文 -> 英文: {} -> {}", chineseText, englishResult);
        
        // 从英文翻译到葡萄牙语
        String englishText = "Hello World";
        String portugueseResult = translationUtils.translateText(englishText, LanguageEnum.ENGLISH, LanguageEnum.PORTUGUESE);
        log.info("英文 -> 葡萄牙语: {} -> {}", englishText, portugueseResult);
        
        // 从葡萄牙语翻译到中文
        String portugueseText = "Olá Mundo";
        String chineseResult = translationUtils.translateText(portugueseText, LanguageEnum.PORTUGUESE, LanguageEnum.CHINESE);
        log.info("葡萄牙语 -> 中文: {} -> {}", portugueseText, chineseResult);
    }

    /**
     * 示例2: 便捷方法使用
     * 使用预定义的便捷方法
     */
    public void convenienceMethodsExample() {
        log.info("=== 便捷方法使用示例 ===");
        
        // 从英文翻译（向后兼容）
        String englishText = "AK-47 | Redline";
        String chineseResult = translationUtils.translateFromEnglish(englishText, LanguageEnum.CHINESE);
        String portugueseResult = translationUtils.translateFromEnglish(englishText, LanguageEnum.PORTUGUESE);
        
        log.info("从英文翻译:");
        log.info("  原文: {}", englishText);
        log.info("  中文: {}", chineseResult);
        log.info("  葡萄牙语: {}", portugueseResult);
        
        // 从中文翻译
        String chineseText = "AK-47 | 红线";
        String englishResult = translationUtils.translateFromChinese(chineseText, LanguageEnum.ENGLISH);
        String portugueseResult2 = translationUtils.translateFromChinese(chineseText, LanguageEnum.PORTUGUESE);
        
        log.info("从中文翻译:");
        log.info("  原文: {}", chineseText);
        log.info("  英文: {}", englishResult);
        log.info("  葡萄牙语: {}", portugueseResult2);
    }

    /**
     * 示例3: 批量翻译到多个语言
     */
    public void batchTranslationExample() {
        log.info("=== 批量翻译示例 ===");
        
        String originalText = "Counter-Strike Skin";
        LanguageEnum sourceLanguage = LanguageEnum.ENGLISH;
        
        // 翻译到多个指定语言
        Map<String, String> translations = translationUtils.translateToMultipleLanguages(
            originalText, 
            sourceLanguage, 
            LanguageEnum.CHINESE, 
            LanguageEnum.PORTUGUESE
        );
        
        log.info("批量翻译结果:");
        log.info("  原文: {} ({})", originalText, sourceLanguage.getCode());
        translations.forEach((languageCode, translatedText) -> 
            log.info("  {}: {}", languageCode, translatedText)
        );
    }

    /**
     * 示例4: 翻译到所有支持的语言
     */
    public void translateToAllLanguagesExample() {
        log.info("=== 翻译到所有支持语言示例 ===");
        
        String originalText = "AWP | Dragon Lore";
        LanguageEnum sourceLanguage = LanguageEnum.ENGLISH;
        
        // 翻译到所有支持的语言
        I18nField i18nField = translationUtils.translateToAllSupportedLanguages(originalText, sourceLanguage);
        
        log.info("翻译到所有支持语言:");
        log.info("  原文: {} ({})", originalText, sourceLanguage.getCode());
        
        // 显示所有语言的翻译结果
        Map<String, String> allTranslations = i18nField.getAllLanguages();
        allTranslations.forEach((languageCode, translatedText) -> 
            log.info("  {}: {}", languageCode, translatedText)
        );
    }

    /**
     * 示例5: 错误处理和边界情况
     */
    public void errorHandlingExample() {
        log.info("=== 错误处理示例 ===");
        
        // 空文本处理
        String emptyResult = translationUtils.translateText("", LanguageEnum.ENGLISH, LanguageEnum.CHINESE);
        log.info("空文本翻译结果: '{}'", emptyResult);
        
        // null文本处理
        String nullResult = translationUtils.translateText(null, LanguageEnum.ENGLISH, LanguageEnum.CHINESE);
        log.info("null文本翻译结果: '{}'", nullResult);
        
        // 相同语言翻译
        String sameLanguageResult = translationUtils.translateText("Hello", LanguageEnum.ENGLISH, LanguageEnum.ENGLISH);
        log.info("相同语言翻译结果: {}", sameLanguageResult);
        
        // null语言参数处理
        String nullLanguageResult = translationUtils.translateText("Hello", null, LanguageEnum.CHINESE);
        log.info("null语言参数翻译结果: {}", nullLanguageResult);
    }

    /**
     * 示例6: 服务可用性检查
     */
    public void serviceAvailabilityExample() {
        log.info("=== 服务可用性检查示例 ===");
        
        boolean isAvailable = translationUtils.isTranslationServiceAvailable();
        log.info("翻译服务是否可用: {}", isAvailable);
        
        if (isAvailable) {
            log.info("翻译服务正常，可以进行翻译操作");
        } else {
            log.warn("翻译服务不可用，请检查网络连接或服务状态");
        }
    }

    /**
     * 运行所有示例
     */
    public void runAllExamples() {
        try {
            basicTranslationExample();
            convenienceMethodsExample();
            batchTranslationExample();
            translateToAllLanguagesExample();
            errorHandlingExample();
            serviceAvailabilityExample();
        } catch (Exception e) {
            log.error("运行示例时发生错误: {}", e.getMessage(), e);
        }
    }
}
