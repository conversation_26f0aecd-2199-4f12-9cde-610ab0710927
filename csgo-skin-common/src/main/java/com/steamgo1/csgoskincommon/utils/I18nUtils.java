package com.steamgo1.csgoskincommon.utils;

import com.steamgo1.csgoskincommon.enums.LanguageEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Locale;

/**
 * 国际化工具类
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class I18nUtils {

    private static MessageSource messageSource;

    public I18nUtils(MessageSource messageSource) {
        I18nUtils.messageSource = messageSource;
    }

    /**
     * 获取当前Locale
     */
    public static Locale getCurrentLocale() {
        Locale locale = LocaleContextHolder.getLocale();
        log.debug("<-----------> Current Language = {}", locale.toString());
        return locale;
    }


    /**
     * 获取当前Locale
     */
    public static void setCurrentLocale(LanguageEnum locale) {
        LocaleContextHolder.setLocale( new Locale(locale.getCode()));
    }

    /**
     * 获取消息(无参数)
     *
     * @param code 消息代码
     * @return 国际化消息
     */
    public static String getMessage(String code) {
        return getMessage(code, null, "");
    }

    /**
     * 获取消息(带参数)
     *
     * @param code 消息代码
     * @param args 参数数组
     * @return 国际化消息
     */
    public static String getMessage(String code, Object[] args) {
        return getMessage(code, args, "");
    }

    /**
     * 获取消息(带默认值)
     *
     * @param code           消息代码
     * @param defaultMessage 默认消息
     * @return 国际化消息
     */
    public static String getMessage(String code, String defaultMessage) {
        return getMessage(code, null, defaultMessage);
    }

    /**
     * 获取完整消息
     *
     * @param code           消息代码
     * @param args           参数数组
     * @param defaultMessage 默认消息
     * @return 国际化消息
     */
    public static String getMessage(String code, Object[] args, String defaultMessage) {
        if (!StringUtils.hasText(code)) {
            return defaultMessage;
        }
        try {
            return messageSource.getMessage(code, args, defaultMessage, getCurrentLocale());
        } catch (Exception e) {
            return defaultMessage != null ? defaultMessage : code;
        }
    }

    /**
     * 获取消息(指定Locale)
     *
     * @param code           消息代码
     * @param args           参数数组
     * @param defaultMessage 默认消息
     * @param locale         指定的Locale
     * @return 国际化消息
     */
    public static String getMessage(String code, Object[] args, String defaultMessage, Locale locale) {
        if (!StringUtils.hasText(code)) {
            return defaultMessage;
        }
        try {
            return messageSource.getMessage(code, args, defaultMessage, locale != null ? locale : getCurrentLocale());
        } catch (Exception e) {
            return defaultMessage != null ? defaultMessage : code;
        }
    }

    /**
     * 检查是否存在指定的消息代码
     *
     * @param code 消息代码
     * @return 是否存在
     */
    public static boolean hasMessage(String code) {
        return hasMessage(code, getCurrentLocale());
    }

    /**
     * 检查是否存在指定的消息代码(指定Locale)
     *
     * @param code   消息代码
     * @param locale 指定的Locale
     * @return 是否存在
     */
    public static boolean hasMessage(String code, Locale locale) {
        if (!StringUtils.hasText(code)) {
            return false;
        }
        try {
            String message = messageSource.getMessage(code, null, locale != null ? locale : getCurrentLocale());
            return !code.equals(message);
        } catch (Exception e) {
            return false;
        }
    }

    // ==================== I18nField 相关方法 ====================

    /**
     * 安全获取I18nField的当前语言值
     */
    public static String safeGetCurrent(I18nField field) {
        if (field == null) {
            return "";
        }
        return field.getCurrent();
    }

    /**
     * 安全获取I18nField的指定语言值
     */
    public static String safeGet(I18nField field, LanguageEnum locale) {
        if (field == null) {
            return "";
        }
        return field.get(locale);
    }

    // ==================== 基于枚举的多语言方法 ====================

    /**
     * 获取当前语言枚举
     */
    public static LanguageEnum getCurrentLanguageEnum() {
        String locale = getCurrentLocale().getLanguage();
        return LanguageEnum.fromCode(locale);
    }

    /**
     * 根据枚举获取语言代码
     */
    public static String getLanguageCode(LanguageEnum language) {
        return language != null ? language.getCode() : LanguageEnum.getDefault().getCode();
    }

    /**
     * 获取所有支持的语言代码
     */
    public static String[] getSupportedLanguageCodes() {
        LanguageEnum[] languages = LanguageEnum.getSupportedLanguages();
        String[] codes = new String[languages.length];
        for (int i = 0; i < languages.length; i++) {
            codes[i] = languages[i].getCode();
        }
        return codes;
    }

    /**
     * 检查语言是否支持
     */
    public static boolean isLanguageSupported(String languageCode) {
        return LanguageEnum.isSupported(languageCode);
    }

    /**
     * 获取语言的显示名称
     */
    public static String getLanguageDisplayName(String languageCode) {
        LanguageEnum language = LanguageEnum.fromCode(languageCode);
        LanguageEnum currentLanguage = getCurrentLanguageEnum();
        return language.getDisplayName(currentLanguage);
    }

    /**
     * 根据枚举遍历处理多语言逻辑
     */
    public static java.util.Map<String, String> processAllLanguages(java.util.function.Function<LanguageEnum, String> processor) {
        java.util.Map<String, String> result = new java.util.HashMap<>();
        for (LanguageEnum language : LanguageEnum.getSupportedLanguages()) {
            String value = processor.apply(language);
            if (value != null && !value.trim().isEmpty()) {
                result.put(language.getCode(), value);
            }
        }
        return result;
    }

    /**
     * 根据枚举获取I18nField的值
     */
    public static String getI18nFieldValue(I18nField field, LanguageEnum language) {
        if (field == null || language == null) {
            return "";
        }
        return field.get(language);
    }
}
