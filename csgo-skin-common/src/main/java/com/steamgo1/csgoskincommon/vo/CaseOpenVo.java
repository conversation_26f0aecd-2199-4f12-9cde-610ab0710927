package com.steamgo1.csgoskincommon.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CaseOpenVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("箱子Id")
    private Long caseId;

    @ApiModelProperty("箱子名称")
    private String caseName;

    @ApiModelProperty("开箱数量")
    private Long openCount;

    @ApiModelProperty("消耗金币")
    private BigDecimal consumeCoins;

    @ApiModelProperty("出货总价")
    private BigDecimal totalPrice;

    @ApiModelProperty("箱子利润")
    private BigDecimal caseProfit;
}
