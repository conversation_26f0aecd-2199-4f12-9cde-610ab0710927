package com.steamgo1.csgoskincommon.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@ApiModel("roll房查询条件")
public class RollHomeFilterParamVO {
    @ApiModelProperty("Roll房类型")
    List<Map<String, Object>> rollHomeType;

    @ApiModelProperty("开奖方式")
    List<Map<String, Object>> rollHomeLotteryMethod;

    @ApiModelProperty("Roll房状态")
    List<Map<String, Object>> Status;

    @ApiModelProperty("消费阀值类型")
    List<Map<String, Object>> thresholdType;
}
