package com.steamgo1.csgoskincommon.vo;


import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("扎比特快速购买响应")
public class ZBTQuickBuyVO {
    private Boolean success;
    private String errorMsg;
    private Object errorData;

    private Integer errorCode;

    private OpenBuyResultDTO data;

    @Data
    public static class OpenBuyResultDTO {
        BigDecimal buyPrice;
        Integer delivery;
        String offerId;
        Long orderId;
    }
}
