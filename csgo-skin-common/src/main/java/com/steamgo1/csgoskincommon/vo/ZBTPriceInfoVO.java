package com.steamgo1.csgoskincommon.vo;


import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("扎比特饰品价格响应")
public class ZBTPriceInfoVO {
    private Boolean success;
    private String errorMsg;
    private Object errorData;

    private Integer errorCode;

    private List<PriceInfo> data;

    @Data
    public static class PriceInfo {
        Long appId;
        Long itemId;
        String marketHashName;
        Integer quantity;
        BigDecimal price;
        Integer autoDeliverQuantity;
        BigDecimal autoDeliverPrice;
        Integer manualQuantity;
        BigDecimal manualDeliverPrice;

    }
}
