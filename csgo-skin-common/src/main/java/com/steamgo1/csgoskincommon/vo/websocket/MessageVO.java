package com.steamgo1.csgoskincommon.vo.websocket;

import com.steamgo1.csgoskincommon.enums.WebSocketMessageMethod;
import com.steamgo1.csgoskincommon.enums.WebSocketMessageType;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel("websocket 消息")
public class MessageVO<T> {


    /**
     * 模式
     */
    private Integer method;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 响应数据
     */
    private T data;


    public MessageVO(WebSocketMessageMethod messageMethod, WebSocketMessageType messageType, T data) {
        this.method = messageMethod.getCode();
        this.type = messageType.getCode();
        this.data = data;
    }
}
