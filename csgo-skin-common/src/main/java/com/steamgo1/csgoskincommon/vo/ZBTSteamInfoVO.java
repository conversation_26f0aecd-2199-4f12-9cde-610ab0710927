package com.steamgo1.csgoskincommon.vo;


import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("交易链接查用户")
public class ZBTSteamInfoVO {
    private Integer appId;
    private Integer checkStatus;

    private List<Status> statusList;

    private SteamInfo steamInfo;


    @Data
    public static class Status {
        private String statusKey;
        private Integer statusValue;
    }

    @Data
    public static class SteamInfo {
        private String avatar;
        private String nickName;
        private String tradeToken;
        private String steamId;
    }

}

