package com.steamgo1.csgoskincommon.entity.listener;

import com.steamgo1.csgoskincommon.entity.RollHomeEntity;
import com.steamgo1.csgoskincommon.entity.RollHomeUserEntity;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import javax.persistence.PostPersist;


@Slf4j
public class RollHomeUserListener {

    @Value("${spring.redis.prefix.roll-home}")
    private String redisRollHomePrefix;

    @PostPersist
    public void postPersist(RollHomeUserEntity rollHomeUserEntity) {
        RollHomeEntity rollHomeEntity = rollHomeUserEntity.getRollHome();
        log.info("有人参与roll房，删除缓存 {} {}", rollHomeEntity.getId(), rollHomeEntity.getName());
        String redisRollHomeBaseInfoKey = redisRollHomePrefix + ":BASEINFO:" + rollHomeEntity.getId();
        String redisRollHomeKey = redisRollHomePrefix + ":" + rollHomeEntity.getId();
        if (RedisUtils.hasKey(redisRollHomeBaseInfoKey)) {
            RedisUtils.delete(redisRollHomeBaseInfoKey);
        }
        if (RedisUtils.hasKey(redisRollHomeKey)) {
            RedisUtils.delete(redisRollHomeKey);
        }
    }
}
