package com.steamgo1.csgoskincommon.entity.listener;

import com.steamgo1.csgoskincommon.entity.CaseEntity;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import javax.persistence.PostPersist;
import javax.persistence.PostUpdate;


@Slf4j
public class CaseListener {
    @Value("${spring.redis.prefix.case}")
    private String redisCasePrefix;

    @PostUpdate
    public void postUpdate(CaseEntity caseEntity) {
        log.info("箱子更新删除缓存 {} {}", caseEntity.getId(), caseEntity.getName());
        String redisCaseKey = redisCasePrefix + ":case_info:" + caseEntity.getId();
        if (RedisUtils.hasKey(redisCaseKey)) {
            RedisUtils.delete(redisCaseKey);
        }
        String redisKey = redisCasePrefix + ":case_of_category:open_case";
        if (RedisUtils.hasKey(redisKey)) {
            RedisUtils.delete(redisKey);
        }
        redisKey = redisCasePrefix + ":case_of_category:battle";
        if (RedisUtils.hasKey(redisKey)) {
            RedisUtils.delete(redisKey);
        }
    }

    @PostPersist
    public void postPersist(CaseEntity caseEntity) {
        String redisKey = redisCasePrefix + ":case_of_category:open_case";
        if (RedisUtils.hasKey(redisKey)) {
            RedisUtils.delete(redisKey);
        }
        redisKey = redisCasePrefix + ":case_of_category:battle";
        if (RedisUtils.hasKey(redisKey)) {
            RedisUtils.delete(redisKey);
        }
    }

}
