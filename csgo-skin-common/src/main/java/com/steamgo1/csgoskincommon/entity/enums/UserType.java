package com.steamgo1.csgoskincommon.entity.enums;

import com.google.common.collect.Lists;
import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;
import com.steamgo1.csgoskincommon.utils.I18nUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum UserType implements CodeValueBaseEnum {

    ACTUAL(1, "user.type.actual"),
    ROBOT(2, "user.type.robot"),
    INSIDER(3, "user.type.insider"),
    CHEAT_ROBOT(4, "user.type.cheat.robot"),
    ANCHOR(5, "user.type.anchor");

    private Integer code;
    private String i18nKey;

    UserType(int code, String i18nKey) {
        this.code = code;
        this.i18nKey = i18nKey;
    }

    /**
     * 获取当前语言的显示名称
     */
    public String getDisplayName() {
        return I18nUtils.getMessage(this.i18nKey);
    }

    //讲枚举转换成list格式，这样前台遍历的时候比较容易，列如 下拉框 后台调用toList方法，你就可以得到code 和name了
    public static List toList() {
        List list = Lists.newArrayList();//Lists.newArrayList()其实和new ArrayList()几乎一模
        //  一样, 唯一它帮你做的(其实是javac帮你做的), 就是自动推导(不是"倒")尖括号里的数据类型.

        for (UserType rollHomeType : UserType.values()) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("code", rollHomeType.getCode());
            map.put("name", rollHomeType.getDisplayName());
            list.add(map);
        }
        return list;
    }

    public static UserType instance(Integer code) {
        UserType[] enums = values();
        for (UserType rollHomeType : enums) {
            if (rollHomeType.getCode().equals(code)) {
                return rollHomeType;
            }
        }
        return null;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getValue() {
        return getDisplayName();
    }

    @Override
    public String description() {
        return code + "-" + getDisplayName();
    }
}
