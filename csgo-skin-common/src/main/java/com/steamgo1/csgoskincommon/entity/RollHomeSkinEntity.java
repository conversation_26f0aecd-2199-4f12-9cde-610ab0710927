package com.steamgo1.csgoskincommon.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Data
@ApiModel("roll房饰品")
public class RollHomeSkinEntity extends BaseModel implements Serializable {
    @ApiModelProperty("饰品")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "skin_id", referencedColumnName = "id")
    private SkinEntity skin;

    @ApiModelProperty("Roll房")
    @JsonIgnore
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "roll_name_id", referencedColumnName = "id")
    private RollHomeEntity rollHome;

    @ApiModelProperty("中奖用户")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @ApiModelProperty("Roll")
    @Column(name = "roll")
    private Integer roll;

    @ApiModelProperty("局数")
    @Column(name = "rounds")
    private Integer rounds;

    @ApiModelProperty("排序(数字越大优先级越高)")
    @Column(name = "grade")
    private Integer grade;

    @ApiModelProperty("是否删除")
    @Column(name = "is_deleted")
    private Boolean isDeleted = false;

}
