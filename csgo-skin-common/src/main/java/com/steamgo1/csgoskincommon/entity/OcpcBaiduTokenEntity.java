package com.steamgo1.csgoskincommon.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@Entity
@Table(name = "ocpc_baidu_token")
@ApiModel("百度sem信息")
public class OcpcBaiduTokenEntity extends BaseModel implements Serializable {
    @ApiModelProperty("token")
    @Column(name = "token", length = 128)
    private String token;

    @ApiModelProperty("realm_name")
    @Column(name = "relm_name", length = 256)
    private String relamName;
}
