package com.steamgo1.csgoskincommon.entity.enums;

import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;
import com.steamgo1.csgoskincommon.utils.I18nUtils;

public enum PayType implements CodeValueBaseEnum {

    /**
     * 微信支付
     */
    WECHATPAY(1, "pay.type.wechatpay"),

    /**
     * "支付宝
     */
    ALIPAY(2, "pay.type.alipay"),
    /**
     * "BOXPAY
     */
    EASY_ALIPAY(3, "pay.type.easy.alipay"),
    EASY_WECHATPAY(4, "pay.type.easy.wechatpay"),

    /**
     * "BOXPAY
     */
    BOX_ALIPAY(5, "pay.type.box.alipay"),
    BOX_WECHATPAY(6, "pay.type.box.wechatpay"),

    /**
     * 巴西信付支付
     */
    XINFU_BX_PAY(7, "pay.type.xinfu.bx.pay");

    private Integer code;

    private String i18nKey;

    PayType(Integer code, String i18nKey) {
        this.code = code;
        this.i18nKey = i18nKey;
    }

    public static PayType instance(Integer value) {
        PayType[] enums = values();
        for (PayType statusEnum : enums) {
            if (statusEnum.code.equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    /**
     * 获取当前语言的显示名称
     */
    public String getDisplayName() {
        return I18nUtils.getMessage(this.i18nKey);
    }

    @Override
    public String getValue() {
        return getDisplayName();
    }

    @Override
    public String description() {
        return code + "-" + getDisplayName();
    }
}
