package com.steamgo1.csgoskincommon.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Entity
@ApiModel("日常签到配置")
@Table(name = "check_in_red_packet_config")
public class CheckInRedPacketConfigEntity extends BaseModel implements Serializable {
    @ApiModelProperty("签到红包")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "check_in_red_packet_id", referencedColumnName = "id")
    private CheckInRedPacketEntity checkInRedPacket;

    @ApiModelProperty("最少金币数量")
    @Column(name = "min_coin")
    private BigDecimal minCoin;

    @ApiModelProperty("最多金币数量")
    @Column(name = "max_coin")
    private BigDecimal maxCoin;

    @ApiModelProperty("领取概率")
    @Column(name = "probability")
    private Integer probability;

}
