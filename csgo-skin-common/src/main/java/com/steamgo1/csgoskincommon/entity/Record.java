package com.steamgo1.csgoskincommon.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023-02-09 16:02:14
 */
@Data
@Entity
@Table(name = "package_record")
@ApiModel("用户背包记录")
public class Record implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @ApiModelProperty("ID")
    private Integer id;

    @CreationTimestamp
    @Column(name = "create_time", updatable = false)
    @ApiModelProperty("创建时间")
    private Date createTime;

    @UpdateTimestamp
    @Column(name = "update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @Column(name = "type")
    private Integer type;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "skin_id")
    private Long skinId;

    @Column(name = "initial_total")
    private Long initialTotal;

    @Column(name = "current_total")
    private Long currentTotal;

    @Column(name = "order_sell_id")
    private Long orderSellId;

    @Column(name = "order_pickup_id")
    private Long orderPickupId;

    @Column(name = "order_unpick_id")
    private Long orderUnpickId;
}
