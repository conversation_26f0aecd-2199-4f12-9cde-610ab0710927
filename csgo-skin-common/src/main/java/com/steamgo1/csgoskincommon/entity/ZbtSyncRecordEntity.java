package com.steamgo1.csgoskincommon.entity;

import com.steamgo1.csgoskincommon.entity.enums.ZbtSyncStatus;
import com.steamgo1.csgoskincommon.entity.enums.ZbtSyncType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@Entity
@Table(name = "zbt_sync_record")
@ApiModel("扎比特同步记录")
public class ZbtSyncRecordEntity extends BaseModel implements Serializable {
    @ApiModelProperty("同步类型")
    @Column(name = "type")
    private ZbtSyncType zbtSyncType;

    @ApiModelProperty("同步状态")
    @Column(name = "status")
    private ZbtSyncStatus zbtSyncStatus;

    @ApiModelProperty("操作流水号")
    @Column(name = "operation_no", length = 32)
    private String operationNo;

    @ApiModelProperty("备注")
    @Column(name = "remarks", length = 128)
    private String remarks;
}
