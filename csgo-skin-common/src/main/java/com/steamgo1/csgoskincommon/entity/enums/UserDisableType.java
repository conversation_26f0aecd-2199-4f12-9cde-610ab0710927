package com.steamgo1.csgoskincommon.entity.enums;

import com.google.common.collect.Lists;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import lombok.Getter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum UserDisableType {
    EXCHANGE(0, "user.disable.type.exchange"),
    PICK_UP(1, "user.disable.type.pick.up"),
    OPEN_CASE(2, "user.disable.type.open.case"),
    PERCENTAGE(3, "user.disable.type.percentage");

    @Getter
    private Integer code;
    @Getter
    private String i18nKey;

    UserDisableType(Integer code, String i18nKey) {
        this.code = code;
        this.i18nKey = i18nKey;
    }

    /**
     * 获取当前语言的显示名称
     */
    public String getDisplayName() {
        return I18nUtils.getMessage(this.i18nKey);
    }

    public String getValue() {
        return getDisplayName();
    }

    public static List toList() {
        List list = Lists.newArrayList();
        for (UserDisableType rollHomeType : UserDisableType.values()) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("code", rollHomeType.getCode());
            map.put("name", rollHomeType.getValue());
            list.add(map);
        }
        return list;
    }

    public static UserDisableType instance(Integer value) {
        UserDisableType[] enums = values();
        for (UserDisableType caseType : enums) {
            if (caseType.code.equals(value)) {
                return caseType;
            }
        }
        return null;
    }

    public Integer value() {
        return code;
    }
}
