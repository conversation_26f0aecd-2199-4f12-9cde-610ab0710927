package com.steamgo1.csgoskincommon.entity;

import com.steamgo1.csgoskincommon.entity.enums.PayType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@ApiModel("支付渠道")
@Table(name = "pay_channel")
public class PayChannelEntity extends BaseModel implements Serializable {
    
    @ApiModelProperty("名字")
    @Column(name = "name", nullable = false)
    private String name;
    
    @ApiModelProperty("类型")
    @Column(name = "pay_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private PayType payType;
    
    @ApiModelProperty("汇率")
    @Column(name = "exchange_rate", nullable = false, columnDefinition = "decimal(15,5)")
    private BigDecimal exchangeRate = BigDecimal.ONE;
    
    @ApiModelProperty("状态（1=可用,0=禁用）")
    @Column(name = "status", nullable = false)
    private Integer status = 1;
    
    @ApiModelProperty("图标")
    @Column(name = "image", nullable = false)
    private String image;

    @ApiModelProperty("国家")
    @Column(name = "country", nullable = false)
    private String country;
}