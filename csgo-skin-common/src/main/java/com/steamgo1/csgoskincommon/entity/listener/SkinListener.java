package com.steamgo1.csgoskincommon.entity.listener;

import com.steamgo1.csgoskincommon.entity.SkinEntity;
import com.steamgo1.csgoskincommon.service.CleanCacheService;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import com.steamgo1.csgoskincommon.utils.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.persistence.PostUpdate;


@Slf4j
@Component
public class SkinListener {
    @Value("${spring.redis.prefix.skin}")
    private String redisSkinPrefix;

    @PostUpdate
    public void postUpdate(SkinEntity skinEntity) {

        log.info("饰品更新删除缓存 {} {}", skinEntity.getId(), skinEntity.getName());
        String redisSkinKey = redisSkinPrefix + ":skin_info:" + skinEntity.getId();
        if (RedisUtils.hasKey(redisSkinKey)) {
            RedisUtils.delete(redisSkinKey);
        }
        CleanCacheService cleanCacheService = SpringUtil.getBean(CleanCacheService.class);
        cleanCacheService.cleanSkinCache(skinEntity.getId());
    }
}
