package com.steamgo1.csgoskincommon.entity;

import com.steamgo1.csgoskincommon.entity.enums.OrderStatus;
import com.steamgo1.csgoskincommon.entity.enums.PayType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023-02-09 16:02:14
 */
@Data
@Entity
@Table(name = "order_charge")
@ApiModel("用户充值订单")
public class OrderChargeEntity extends BaseModel implements Serializable {

    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @Column(name = "order_no", length = 32)
    @ApiModelProperty("订单号")
    private String orderNo;

    @Column(name = "amount")
    @ApiModelProperty("标价")
    private BigDecimal amount;

    @Column(name = "actual_amount")
    @ApiModelProperty("实际付款金额")
    private BigDecimal actualAmount;

    @Column(name = "pay_type")
    @ApiModelProperty("支付类型")
    private PayType payType;

    @Column(name = "pay_url", length = 256)
    @ApiModelProperty("支付链接")
    private String payUrl;

    @ApiModelProperty("订单商品")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "charge_goods_id", referencedColumnName = "id")
    private ChargeGoodsEntity chargeGoods;

    @Column(name = "status")
    @ApiModelProperty("订单状态")
    private OrderStatus orderStatus;

    @Column(name = "exchange_rate")
    @ApiModelProperty("汇率")
    private BigDecimal exchangeRate;
}
