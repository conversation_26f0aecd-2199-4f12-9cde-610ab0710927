package com.steamgo1.csgoskincommon.entity.enums;

import com.steamgo1.csgoskincommon.utils.I18nUtils;
import lombok.Getter;

public enum BattleHomeCaseStatus {
    UNOPEN(1, "battle.home.case.status.unopen"),
    WAIT(2, "battle.home.case.status.wait"),
    OPEN(3, "battle.home.case.status.open");

    @Getter
    private Integer code;
    @Getter
    private String i18nKey;

    BattleHomeCaseStatus(Integer code, String i18nKey) {
        this.code = code;
        this.i18nKey = i18nKey;
    }

    /**
     * 获取当前语言的显示名称
     */
    public String getDisplayName() {
        return I18nUtils.getMessage(this.i18nKey);
    }

    public String getValue() {
        return getDisplayName();
    }
}
