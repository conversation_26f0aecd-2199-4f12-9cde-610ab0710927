package com.steamgo1.csgoskincommon.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Entity
@ApiModel("消费返奖计划")
@Table(name = "consume_plan")
public class ConsumePlanEntity extends BaseModel implements Serializable {
    @ApiModelProperty("消费返奖计划名")
    @Column(name = "name", length = 32)
    private String name;

    @ApiModelProperty("领取次数")
    @Column(name = "total")
    private Integer total;

    @ApiModelProperty("领取阈值")
    @Column(name = "threshold")
    private BigDecimal threshold;


}
