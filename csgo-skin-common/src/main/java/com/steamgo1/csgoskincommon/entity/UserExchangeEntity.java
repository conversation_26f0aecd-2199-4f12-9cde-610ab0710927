package com.steamgo1.csgoskincommon.entity;

import com.steamgo1.csgoskincommon.entity.enums.UserExchangeStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Entity
@Table(name = "user_exchage")
@ApiModel("用户钻石兑换")
public class UserExchangeEntity extends BaseModel implements Serializable {
    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;


    @Column(name = "order_no")
    @ApiModelProperty("订单号")
    private String orderNo;

    @Column(name = "diamond_amount")
    @ApiModelProperty("消耗钻石总额")
    private BigDecimal diamondAmount;

    @Column(name = "coin_amount")
    @ApiModelProperty("兑换金币总额")
    private BigDecimal coinAmount;

    @Column(name = "status")
    @ApiModelProperty("状态")
    private UserExchangeStatus status;

    @Column(name = "remarks")
    @ApiModelProperty("备注(失败原因)")
    private String remarks;
}
