package com.steamgo1.csgoskincommon.entity.enums;

import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;
import com.steamgo1.csgoskincommon.utils.I18nUtils;

import java.util.ArrayList;
import java.util.List;

public enum BattleHomeStatus implements CodeValueBaseEnum {

    WAITING(1, "battle.home.status.waiting"),
    RUNNING(2, "battle.home.status.running"),
    FINISH(3, "battle.home.status.finish");

    private Integer code;
    private String i18nKey;

    BattleHomeStatus(int code, String i18nKey) {
        this.code = code;
        this.i18nKey = i18nKey;
    }

    public static List<BattleHomeStatus> unFinishStatus() {
        List<BattleHomeStatus> battleHomeStatusHashSet = new ArrayList<>();
        battleHomeStatusHashSet.add(BattleHomeStatus.RUNNING);
        battleHomeStatusHashSet.add(BattleHomeStatus.WAITING);
        return battleHomeStatusHashSet;
    }

    public static BattleHomeStatus instance(Integer value) {
        BattleHomeStatus[] enums = values();
        for (BattleHomeStatus battleHomeStatus : enums) {
            if (battleHomeStatus.code.equals(value)) {
                return battleHomeStatus;
            }
        }
        return null;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    /**
     * 获取当前语言的显示名称
     */
    public String getDisplayName() {
        return I18nUtils.getMessage(this.i18nKey);
    }

    @Override
    public String getValue() {
        return getDisplayName();
    }

    @Override
    public String description() {
        return code + "-" + getDisplayName();
    }
}
