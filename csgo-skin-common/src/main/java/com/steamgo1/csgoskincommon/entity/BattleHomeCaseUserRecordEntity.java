package com.steamgo1.csgoskincommon.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Data
@ApiModel("用户对战房开箱记录")
@Table(name = "battle_home_case_user_record")
public class BattleHomeCaseUserRecordEntity extends BaseModel implements Serializable {
    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @ApiModelProperty("对战房")
    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "battle_home_id", referencedColumnName = "id")
    private BattleHomeEntity battleHome;

    @ApiModelProperty("对战房箱子")
    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "battle_home_case_id", referencedColumnName = "id")
    private BattleHomeCaseEntity battleHomeCase;

    @ApiModelProperty("饰品")
    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "skin_id", referencedColumnName = "id")
    private SkinEntity skin;

    @ApiModelProperty("算法")
    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "algorithm_data_id", referencedColumnName = "id")
    private AlgorithmDataEntity algorithmData;

    @ApiModelProperty("roll")
    @Column(name = "roll")
    private Integer roll;

    @ApiModelProperty("rounds")
    @Column(name = "rounds")
    private Integer rounds;
}
