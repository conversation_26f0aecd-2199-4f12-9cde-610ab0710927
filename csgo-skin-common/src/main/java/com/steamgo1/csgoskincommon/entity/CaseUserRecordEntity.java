package com.steamgo1.csgoskincommon.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023-02-09 16:02:14
 */
@Data
@Entity
@Table(name = "record_open_case")
public class CaseUserRecordEntity extends BaseModel implements Serializable {
    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @ApiModelProperty("箱子")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "case_id", referencedColumnName = "id")
    private CaseEntity box;

    @ApiModelProperty("饰品")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "skin_id", referencedColumnName = "id")
    private SkinEntity skin;

    @ApiModelProperty("算法")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "algorithm_data_id", referencedColumnName = "id")
    private AlgorithmDataEntity algorithmData;

    @ApiModelProperty("roll")
    @Column(name = "roll")
    private Integer roll;

    @ApiModelProperty("rounds")
    @Column(name = "rounds")
    private Integer rounds;
}
