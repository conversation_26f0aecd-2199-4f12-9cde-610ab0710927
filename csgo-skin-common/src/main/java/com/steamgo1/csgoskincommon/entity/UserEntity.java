package com.steamgo1.csgoskincommon.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.steamgo1.csgoskincommon.entity.enums.UserType;
import com.steamgo1.csgoskincommon.entity.listener.UserListener;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Collection;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023-02-09 16:02:14
 */
@Data
@Entity
@Table(name = "user")
@ApiModel("用户信息")
@EntityListeners(value = {UserListener.class})
public class UserEntity extends BaseModel implements UserDetails, Serializable {
    @Column(name = "type")
    @ApiModelProperty("用户类型")
    private UserType type;

    @Column(name = "nickname", length = 16)
    @ApiModelProperty("昵称")
    private String nickname;

    @Column(name = "phone", length = 16)
    @ApiModelProperty("手机号")
    private String phone;

    @Column(name = "email", length = 16)
    @ApiModelProperty("手机号")
    private String email;

    @Column(name = "wx_openid", length = 32)
    @ApiModelProperty("微信ID")
    private String wxOpenid;

    @Column(name = "steam_openid", length = 32)
    @ApiModelProperty("SteamID")
    private String steamId;

    @Column(name = "trade_offer_access_url")
    @ApiModelProperty("steam交易链接")
    private String tradeOfferAccessUrl;

    @Column(name = "password", length = 64)
    @ApiModelProperty("密码")
    @JsonIgnore
    private String password;

    @Column(name = "avatar", length = 128)
    @ApiModelProperty("头像")
    private String avatar;
    @Column(name = "inviter")
    @ApiModelProperty("邀请人ID")
    private Long inviterId;

    @ApiModelProperty("渠道")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "channel", referencedColumnName = "id")
    private OcpcChannelEntity channel;

    @ApiModelProperty("lastLoginTime")
    private Date lastLoginTime;

    @Column(name = "is_first_charge")
    @ApiModelProperty("是否首充")
    private Boolean isFirstCharge = true;

    @Column(name = "is_ban")
    private Boolean isBan = false;

    @Column(name = "is_real")
    private Boolean isReal = false;

    @Column(name = "is_deleted")
    @JsonIgnore
    private Boolean isDeleted = false;

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return null;
    }

    @Override
    public String getUsername() {
        return null;
    }

    @Override
    public boolean isAccountNonExpired() {
        return false;
    }

    @Override
    public boolean isAccountNonLocked() {
        return false;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return false;
    }

    @Override
    public boolean isEnabled() {
        return false;
    }
}
