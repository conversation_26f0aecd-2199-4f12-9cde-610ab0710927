package com.steamgo1.csgoskincommon.entity.enums;

import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;
import com.steamgo1.csgoskincommon.utils.I18nUtils;

/**
 * 用户金币流水记录
 */
public enum UserCoinChangeSource implements CodeValueBaseEnum {
    /**
     * 充值
     */
    CHARGE(1, "user.coin.change.source.charge"),
    /**
     * 兑换
     */
    EXCHANGE(2, "user.coin.change.source.exchange"),
    /**
     * 开箱子
     */
    OPEN_CASE(3, "user.coin.change.source.open.case"),

    /**
     * 追梦
     */
    PERCENTAGE(4, "user.coin.change.source.percentage"),

    /**
     * Roll房
     */
    ROLL_HOME(5, "user.coin.change.source.roll.home"),

    /**
     * 对战
     */
    BATTLE(6, "user.coin.change.source.battle"),

    /**
     * 红包
     */
    RED_PACKET(7, "user.coin.change.source.red.packet"),
    /**
     * 注册赠送
     */
    REGISTER(8, "user.coin.change.source.register"),

    /**
     * 系统操作
     */
    SYS(9, "user.coin.change.source.system"),

    /**
     * 首充赠送
     */
    FIRST_CHARGE(10, "user.coin.change.source.first.charge"),

    /**
     * 邀请奖励
     */
    INVITE(11, "user.coin.change.source.invite"),

    /**
     * 集卡
     */
    ACTIVITY_CARD(12, "user.coin.change.source.activity.card"),
    /**
     * 每日充值红包
     */
    DAY_CHANGE_RED_PACKET(13, "user.coin.change.source.daily.charge.red.packet"),
    /**
     * 签到奖励
     */
    CHECK_IN(14, "user.coin.change.source.check.in"),

    /**
     * 其它
     */
    OTHER(9, "user.coin.change.source.other");


    private Integer code;
    private String i18nKey;

    UserCoinChangeSource(Integer code, String i18nKey) {
        this.code = code;
        this.i18nKey = i18nKey;
    }

    public static UserCoinChangeSource instance(Integer value) {
        UserCoinChangeSource[] enums = values();
        for (UserCoinChangeSource statusEnum : enums) {
            if (statusEnum.code.equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    /**
     * 获取当前语言的显示名称
     */
    public String getDisplayName() {
        return I18nUtils.getMessage(this.i18nKey);
    }

    @Override
    public String getValue() {
        return getDisplayName();
    }

    @Override
    public String description() {
        return code + "-" + getValue();
    }
}
