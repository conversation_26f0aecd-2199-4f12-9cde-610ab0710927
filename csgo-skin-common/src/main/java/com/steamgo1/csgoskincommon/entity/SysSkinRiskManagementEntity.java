package com.steamgo1.csgoskincommon.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Entity
@ApiModel("饰品价格同步风控")
@Table(name = "sys_skin_risk_management")
public class SysSkinRiskManagementEntity extends BaseModel implements Serializable {
    @ApiModelProperty("涨价阈值")
    @Column(name = "rise_threshold", columnDefinition = "decimal(18,3)")
    private BigDecimal riseThreshold;

    @ApiModelProperty("降价阈值")
    @Column(name = "reduction_threshold", columnDefinition = "decimal(18,3)")
    private BigDecimal reductionThreshold;

}
