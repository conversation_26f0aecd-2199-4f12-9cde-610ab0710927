package com.steamgo1.csgoskincommon.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@Entity
@Table(name = "ocpc_baidu_account")
@ApiModel("百度sem账号信息")
public class OcpcBaiduAccountEntity extends BaseModel implements Serializable {
    @ApiModelProperty("用户名")
    @Column(name = "account", length = 64)
    private String account;

    @ApiModelProperty("accessToken")
    @Column(name = "access_token", length = 512)
    private String accessToken;

    @ApiModelProperty("API ID")
    @Column(name = "app_id", length = 64)
    private String appId;

    @ApiModelProperty("SECERT KEY")
    @Column(name = "secret_key", length = 64)
    private String secretKey;

    @ApiModelProperty("realm_name")
    @Column(name = "relm_name", length = 256)
    private String relamName;

}
