package com.steamgo1.csgoskincommon.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@MappedSuperclass
public abstract class BaseUidModel implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "")
    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("uuid")
    @Column(name = "uid", length = 16)
    private String uid;

    @CreationTimestamp
    @Column(name = "create_time", updatable = false)
    @ApiModelProperty("创建时间")
    private Date createTime;

    @UpdateTimestamp
    @Column(name = "update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;
}
