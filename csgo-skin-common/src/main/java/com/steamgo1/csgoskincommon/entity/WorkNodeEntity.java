package com.steamgo1.csgoskincommon.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023-02-09 16:02:14
 */
@Data
@Entity
@Table(name = "worker_node")
@ApiModel("uuid")
public class WorkNodeEntity extends BaseModel implements Serializable {
    @Column(name = "HOST_NAME", length = 32)
    private String hostName;

    @Column(name = "PORT", length = 32)
    @ApiModelProperty("port")
    private String port;

    @Column(name = "TYPE")
    private Integer type;

    @Column(name = "LAUNCH_DATE")
    private Date launchDate;

    @Column(name = "MODIFIED")
    private Date modefied;

    @Column(name = "CREATED")
    private Date created;
}
