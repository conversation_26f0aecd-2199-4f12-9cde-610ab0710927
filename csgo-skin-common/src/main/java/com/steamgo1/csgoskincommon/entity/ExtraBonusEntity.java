package com.steamgo1.csgoskincommon.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Entity
@Table(name = "extra_bonus")
@ApiModel("出金活动")
public class ExtraBonusEntity extends BaseModel implements Serializable {
    @ApiModelProperty("活动名")
    @Column(name = "name", length = 32)
    private String name;

    @ApiModelProperty("图片")
    @Column(name = "picture", length = 156)
    private String picture;

    @ApiModelProperty("掉落概率")
    @Column(name = "probability", columnDefinition = "decimal(18,5)")
    private BigDecimal probability;

    @Column(name = "enable_open_case")
    @ApiModelProperty("开箱可用")
    private Boolean enableOpenCase = false;

    @Column(name = "enable_percentage")
    @ApiModelProperty("追梦可用")
    private Boolean enablePercentage = false;

    @ApiModelProperty("消费阈值")
    @Column(name = "consume_threshold")
    private BigDecimal consumeThreshold;

    @ApiModelProperty("是否激活")
    @Column(name = "is_activate")
    private Boolean isActivate = false;
}
