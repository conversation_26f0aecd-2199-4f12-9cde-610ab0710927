package com.steamgo1.csgoskincommon.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Entity
@Table(name = "user_invite")
@ApiModel("用户邀请表")
public class UserInviteEntity extends BaseModel implements Serializable {
    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @ApiModelProperty("邀请码")
    @Column(name = "code", length = 8)
    private String code;

    @ApiModelProperty("注册用户")
    @Column(name = "total_register")
    private Integer totalRegister;

    @ApiModelProperty("充值总额")
    @Column(name = "total_charge")
    private BigDecimal totalCharge;

    @ApiModelProperty("奖励总额")
    @Column(name = "total_encourage")
    private BigDecimal totalEncourage;

    @ApiModelProperty("免费宝箱开箱次数")
    @Column(name = "free_case")
    private Integer freeCase;

}
