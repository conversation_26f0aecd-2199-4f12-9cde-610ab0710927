package com.steamgo1.csgoskincommon.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023-02-09 16:02:14
 */
@Data
@Entity
@Table(name = "user_red_packet_record")
@ApiModel("用户领取红包记录")
public class UserRedPacketRecordEntity extends BaseModel implements Serializable {
    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @ApiModelProperty("红包")
    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "red_packet", referencedColumnName = "id")
    private RedPacketEntity redPacket;

}
