package com.steamgo1.csgoskincommon.entity;

import com.steamgo1.csgoskincommon.entity.listener.UserProfileListener;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023-02-09 16:02:14
 */
@Data
@Entity
@Table(name = "user_profile")
@EntityListeners(value = {UserProfileListener.class})
public class UserProfileEntity extends BaseModel implements Serializable {

    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;


    @Column(name = "diamond", columnDefinition = "decimal(18,2)")
    private BigDecimal diamond;

    @Column(name = "coin", columnDefinition = "decimal(18,2)")
    private BigDecimal coin;

    @Column(name = "lavel")
    private Integer lavel;

    @Column(name = "experience")
    private Integer experience;

    @Column(name = "real_name")
    private String realName;

    @Column(name = "id_card")
    private String idCard;


}
