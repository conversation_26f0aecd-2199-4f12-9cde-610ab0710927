package com.steamgo1.csgoskincommon.entity;

import com.steamgo1.csgoskincommon.entity.enums.CardCollectSource;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;


@Data
@Entity
@ApiModel("用户卡片")
@Table(name = "card_collect_user")
public class CardCollectUserEntity extends BaseModel implements Serializable {
    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @ApiModelProperty("卡片")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "card_collect_card_id", referencedColumnName = "id")
    private CardCollectCardEntity cardCollectCard;

    @ApiModelProperty("来源")
    @Column(name = "source")
    private CardCollectSource source;
}
