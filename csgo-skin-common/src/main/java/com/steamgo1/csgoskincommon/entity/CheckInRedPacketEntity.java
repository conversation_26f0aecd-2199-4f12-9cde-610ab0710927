package com.steamgo1.csgoskincommon.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@Entity
@ApiModel("日常签到")
@Table(name = "check_in_red_packet")
public class CheckInRedPacketEntity extends BaseModel implements Serializable {
    @ApiModelProperty("红包名称")
    @Column(name = "name", length = 32)
    private String name;


    @ApiModelProperty("领取阈值")
    @Column(name = "threshold")
    private Integer threshold;

    @ApiModelProperty("是否删除")
    @Column(name = "is_deleted")
    private Boolean isDeleted;

}
