package com.steamgo1.csgoskincommon.entity;

import com.steamgo1.csgoskincommon.converter.I18nFieldConverter;
import com.steamgo1.csgoskincommon.entity.listener.DataDictionaryListener;
import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;


@Data
@Entity
@Table(name = "data_dictionary")
@ApiModel("数据字典")
@EntityListeners(value = {DataDictionaryListener.class})
public class DataDictionaryEntity extends BaseModel implements Serializable {
    @ApiModelProperty("父ID")
    @Column(name = "parent_id")
    Long parentId;

    @Column(name = "i18n_field_name", columnDefinition = "JSON")
    @Convert(converter = I18nFieldConverter.class)
    @ApiModelProperty("字段名（多语言）")
    private I18nField i18nFieldName;

    // 虚拟字段，根据Accept-Language头部参数返回对应语言的文本
    @Transient
    @ApiModelProperty("字段名")
    private String name;

    @ApiModelProperty("字段别名")
    @Column(name = "alias", length = 32)
    private String alias;

    @ApiModelProperty("优先级(用于同级别排序)")
    @Column(name = "grade")
    private Integer gradle = 0;

    @ApiModelProperty("唯一标识")
    @Column(name = "code", length = 32, unique = true)
    private String code;

    @ApiModelProperty("图片")
    @Column(name = "img", length = 128)
    private String img;

    @ApiModelProperty("是否删除")
    @Column(name = "is_deleted")
    private Boolean isDeleted = false;

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getName() {
        return I18nUtils.getI18nFieldValue(i18nFieldName, I18nUtils.getCurrentLanguageEnum());
    }
}
