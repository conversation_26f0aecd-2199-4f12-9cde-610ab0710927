package com.steamgo1.csgoskincommon.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Data
@ApiModel("用户对战房开箱结果")
@Table(name = "battle_home_case_user_skin")
public class BattleHomeCaseUserSkinEntity extends BaseModel implements Serializable {
    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @ApiModelProperty("对战房")
    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "battle_home_id", referencedColumnName = "id")
    private BattleHomeEntity battleHome;


    @ApiModelProperty("饰品")
    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "skin_id", referencedColumnName = "id")
    private SkinEntity skin;

}
