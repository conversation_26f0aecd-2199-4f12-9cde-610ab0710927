package com.steamgo1.csgoskincommon.entity;

import com.steamgo1.csgoskincommon.entity.enums.CardCollectSource;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;


@Data
@Entity
@ApiModel("出金用户记录")
@Table(name = "extra_bonus_user")
public class ExtraBonusUserEntity extends BaseModel implements Serializable {
    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @ApiModelProperty("额外奖励")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "extr_abonus_id", referencedColumnName = "id")
    private ExtraBonusEntity extraBonus;

    @ApiModelProperty("来源")
    @Column(name = "source")
    private CardCollectSource source;

    @ApiModelProperty("是否已兑奖")
    @Column(name = "is_received")
    private Boolean isReceived = false;
}
