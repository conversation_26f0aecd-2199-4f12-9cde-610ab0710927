package com.steamgo1.csgoskincommon.entity.enums;

import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;
import com.steamgo1.csgoskincommon.utils.I18nUtils;

public enum UserPackageSource implements CodeValueBaseEnum {

    /**
     * 开箱子
     */
    OPEN_CASE(1, "user.package.source.open.case"),

    /**
     * 追梦
     */
    PERCENTAGE(2, "user.package.source.percentage"),

    /**
     * Roll房
     */
    ROLL_HOME(3, "user.package.source.roll.home"),

    /**
     * 对战
     */
    BATTLE(4, "user.package.source.battle"),

    /**
     * 购买
     */
    BUY(5, "user.package.source.buy"),

    /**
     * 系统
     */
    SYSTEM(6, "user.package.source.system"),

    /**
     * 其它
     */
    OTHER(7, "user.package.source.other");


    private Integer code;

    private String i18nKey;

    UserPackageSource(Integer num, String i18nKey) {
        this.code = num;
        this.i18nKey = i18nKey;
    }

    public static UserPackageSource instance(Integer value) {
        UserPackageSource[] enums = values();
        for (UserPackageSource statusEnum : enums) {
            if (statusEnum.code.equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    /**
     * 获取当前语言的显示名称
     */
    public String getDisplayName() {
        return I18nUtils.getMessage(this.i18nKey);
    }

    @Override
    public String getValue() {
        return getDisplayName();
    }

    @Override
    public String description() {
        return code + "-" + getDisplayName();
    }
}
