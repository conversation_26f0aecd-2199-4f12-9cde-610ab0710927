package com.steamgo1.csgoskincommon.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023-02-09 16:02:14
 * 饰品原型分类(近战，远程,表情)
 */
@Data
@Entity
@Table(name = "skin_prototype_category")
public class PrototypeCategoryEntity extends BaseModel implements Serializable {
    /**
     * 皮肤原型顶级分类名
     */
    @Column(name = "name", length = 16, unique = true)
    private String name;

    /**
     * 皮肤原型分类图片
     */
    @Column(name = "picture", length = 128, nullable = true)
    private String picture;
}
