package com.steamgo1.csgoskincommon.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@Entity
@Table(name = "ocpc_channel")
@ApiModel("渠道信息")
public class OcpcChannelEntity extends BaseModel implements Serializable {
    @ApiModelProperty("渠道名")
    @Column(name = "name", length = 128)
    private String name;

    @ApiModelProperty("渠道域名")
    @Column(name = "relm_name", length = 32)
    private String relamName;
}
