package com.steamgo1.csgoskincommon.entity.enums;

import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;
import com.steamgo1.csgoskincommon.utils.I18nUtils;

public enum OrderStatus implements CodeValueBaseEnum {
    /**
     * 没有付款.待付款
     */
    UNPAY(1, "order.status.unpay"),

    /**
     * 已经付款
     */
    PADYED(2, "order.status.paid"),

    /**
     * 交易失败
     */
    CLOSE(3, "order.status.cancel");

    private Integer code;
    private String i18nKey;

    OrderStatus(Integer code, String i18nKey) {
        this.code = code;
        this.i18nKey = i18nKey;
    }

    public static OrderStatus instance(Integer value) {
        OrderStatus[] enums = values();
        for (OrderStatus statusEnum : enums) {
            if (statusEnum.value().equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }

    public Integer value() {
        return code;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String description() {
        return code + ":" + i18nKey;
    }

    /**
     * 获取当前语言的显示名称
     */
    public String getDisplayName() {
        return I18nUtils.getMessage(this.i18nKey);
    }

    @Override
    public String getValue() {
        return getDisplayName();
    }
}
