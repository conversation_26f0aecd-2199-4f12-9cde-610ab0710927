package com.steamgo1.csgoskincommon.entity;

import com.steamgo1.csgoskincommon.converter.I18nFieldConverter;
import com.steamgo1.csgoskincommon.enums.LanguageEnum;
import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 国际化演示实体
 * 用于演示多语言字段的存储和搜索
 * 
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "i18n_demo")
@ApiModel("国际化演示")
public class I18nDemoEntity extends BaseModel implements Serializable {

    @Column(name = "i18n_field_text", columnDefinition = "JSON")
    @Convert(converter = I18nFieldConverter.class)
    @ApiModelProperty("文本内容（多语言）")
    private I18nField i18nFieldText;

    // 虚拟字段，根据Accept-Language头部参数返回对应语言的文本
    @Transient
    @ApiModelProperty("文本内容（当前语言）")
    private String text;

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getText() {
        return I18nUtils.getI18nFieldValue(i18nFieldText, I18nUtils.getCurrentLanguageEnum());
    }
}
