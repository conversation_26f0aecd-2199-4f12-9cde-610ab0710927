package com.steamgo1.csgoskincommon.entity;

import com.steamgo1.csgoskincommon.converter.I18nFieldConverter;
import com.steamgo1.csgoskincommon.entity.enums.CaseType;
import com.steamgo1.csgoskincommon.entity.listener.CaseListener;
import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023-02-09 16:02:14
 */
@Data
@Entity
@Table(name = "box")
@ApiModel("箱子信息")
@EntityListeners(value = {CaseListener.class})
public class CaseEntity extends BaseModel implements Serializable {
    /**
     * 新手或者老手
     */
    @Column(name = "type")
    @ApiModelProperty("箱子类别")
    private CaseType caseType;

    @Column(name = "i18n_field_name", columnDefinition = "JSON")
    @Convert(converter = I18nFieldConverter.class)
    @ApiModelProperty("箱子名（多语言）")
    private I18nField i18nFieldName;

    // 虚拟字段，根据Accept-Language头部参数返回对应语言的文本
    @Transient
    @ApiModelProperty("箱子名")
    private String name;

    @Column(name = "price")
    @ApiModelProperty("箱子价格")
    private BigDecimal price;

    /**
     * 前景图
     */
    @Column(name = "foreground_picture", length = 128)
    @ApiModelProperty("箱子前景图")
    private String foregroundPicture;

    @Column(name = "background_picture", length = 128)
    @ApiModelProperty("箱子背景图")
    private String backgroundPicture;

    @Column(name = "skin_total")
    @ApiModelProperty("箱子包含皮肤数量")
    private Integer skinTotal;

    @Column(name = "probability")
    @ApiModelProperty("箱子总概率")
    private BigDecimal probability;

    @ApiModelProperty("箱子优先级(用作排序，越大越靠前)")
    @Column(name = "gradle")
    private Integer gradle;

    @ApiModelProperty("箱子分类")
    @Column(name = "category")
    private Long category;

    @ApiModelProperty("追梦专属箱子")
    @Column(name = "disable_open_case")
    private Boolean disableOpenCase = false;

    @ApiModelProperty("开箱专属箱子")
    @Column(name = "disable_battle")
    private Boolean disableBattle = false;

    @Column(name = "is_sale")
    @ApiModelProperty("箱子是否在售")
    private Boolean isSale;

    @Column(name = "is_recommend")
    @ApiModelProperty("箱子是否推荐")
    private Boolean isRecommend;


    @Column(name = "is_deleted")
    @ApiModelProperty("箱子是否删除")
    private Boolean isDeleted = false;

    @Column(name = "is_protect")
    @ApiModelProperty("是否保底")
    private Boolean isProtect = true;

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getName() {
        return I18nUtils.getI18nFieldValue(i18nFieldName, I18nUtils.getCurrentLanguageEnum());
    }
}
