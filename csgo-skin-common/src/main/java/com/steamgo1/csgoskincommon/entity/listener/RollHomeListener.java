package com.steamgo1.csgoskincommon.entity.listener;

import com.steamgo1.csgoskincommon.entity.RollHomeEntity;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import javax.persistence.PostUpdate;


@Slf4j
public class RollHomeListener {

    @Value("${spring.redis.prefix.roll-home}")
    private String redisRollHomePrefix;

    @PostUpdate
    public void postUpdate(RollHomeEntity rollHomeEntity) {
        log.info("roll房更新删除缓存 {} {}", rollHomeEntity.getId(), rollHomeEntity.getName());
        String redisRollHomeBaseInfoKey = redisRollHomePrefix + ":BASEINFO:" + rollHomeEntity.getId();
        String redisRollHomeKey = redisRollHomePrefix + ":" + rollHomeEntity.getId();
        if (RedisUtils.hasKey(redisRollHomeBaseInfoKey)) {
            RedisUtils.delete(redisRollHomeBaseInfoKey);
        }
        if (RedisUtils.hasKey(redisRollHomeKey)) {
            RedisUtils.delete(redisRollHomeKey);
        }
    }
}
