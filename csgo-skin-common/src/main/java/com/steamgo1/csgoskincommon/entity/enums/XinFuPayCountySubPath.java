package com.steamgo1.csgoskincommon.entity.enums;

import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;

public enum XinFuPayCountySubPath implements CodeValueBaseEnum {

    /**
     * 微信支付
     */
    BX(1, "bx");

    private Integer code;

    private String subPath;

    XinFuPayCountySubPath(Integer num, String subPath) {
        this.code = num;
        this.subPath = subPath;
    }

    public static XinFuPayCountySubPath instance(Integer value) {
        XinFuPayCountySubPath[] enums = values();
        for (XinFuPayCountySubPath statusEnum : enums) {
            if (statusEnum.code.equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getValue() {
        return subPath;
    }

    @Override
    public String description() {
        return code + "-" + subPath;
    }
}
