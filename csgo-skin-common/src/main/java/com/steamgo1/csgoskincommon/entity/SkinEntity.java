package com.steamgo1.csgoskincommon.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.steamgo1.csgoskincommon.converter.I18nFieldConverter;
import com.steamgo1.csgoskincommon.entity.listener.SkinListener;
import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023-02-09 16:02:14
 */
@Data
@Entity
@Table(name = "skin")
@ApiModel("饰品信息")
@EntityListeners(value = {SkinListener.class})
public class SkinEntity extends BaseModel implements Serializable {

    @Column(name = "itemId")
    @ApiModelProperty("扎比特饰品ID")
    private Long itemId;

    @Column(name = "product_id")
    @ApiModelProperty("igxeID")
    private Long productId;

    @Column(name = "i18n_field_name", columnDefinition = "JSON")
    @Convert(converter = I18nFieldConverter.class)
    @ApiModelProperty("饰品名称（多语言）")
    private I18nField i18nFieldName;

    @Transient
    @ApiModelProperty("饰品名称")
    private String name;

    @Column(name = "english_name", length = 128, unique = true)
    @ApiModelProperty("饰品唯一英文")
    private String englishName;

    @Column(name = "i18n_field_short_name", columnDefinition = "JSON")
    @Convert(converter = I18nFieldConverter.class)
    @ApiModelProperty("短名称（多语言）")
    private I18nField i18nFieldShortName;

    @Column(name = "short_name", length = 64)
    @ApiModelProperty("短名称，去掉前缀")
    private String shortName;

    @Column(name = "picture", length = 512)
    @ApiModelProperty("饰品图片")
    private String picture;

    @Column(name = "prototype")
    @ApiModelProperty("原型")
    private Long prototype;

    @Column(name = "rarity")
    @ApiModelProperty("稀有度")
    private Long rarity;

    @Column(name = "rarityColor")
    @ApiModelProperty("稀有度颜色")
    private String rarityColor;

    @Column(name = "quality")
    @ApiModelProperty("质量")
    private Long quality;

    @Column(name = "qualityColor")
    @ApiModelProperty("质量颜色")
    private String qualityColor;

    @Column(name = "exterior")
    @ApiModelProperty("外观")
    private Long exterior;


    @Column(name = "quantity")
    @ApiModelProperty("在售数量")
    private Integer quantity;

    @Column(name = "price")
    @ApiModelProperty("在售最低价")
    private BigDecimal price;

    @Column(name = "auto_deliver_quantity")
    @ApiModelProperty("自动发货在售数量")
    private Integer autoDeliverQuantity;

    @Column(name = "auto_deliver_price")
    @ApiModelProperty("自动发货在售最低价")
    private BigDecimal autoDeliverPrice;

    @Column(name = "manual_quantity")
    @ApiModelProperty("人工发货在售数量")
    private Integer manualQuantity;

    @Column(name = "manual_deliver_price")
    @ApiModelProperty("人工发货在售最低价")
    private BigDecimal manualDeliverPrice;

    @Column(name = "diamond")
    @OrderBy("name ASC")
    @ApiModelProperty("价值钻石")
    private BigDecimal diamond;

    @Column(name = "enable_percentage")
    @ApiModelProperty("是否加入追梦奖池")
    private Boolean enablePercentage = false;

    @Column(name = "enable_random")
    @ApiModelProperty("是否加入随机奖池")
    private Boolean enableRandom = false;

    @Column(name = "lock_price")
    @ApiModelProperty("价格锁定")
    private Boolean lockPrice = false;

    @Column(name = "is_abnormal")
    @ApiModelProperty("是否异常")
    private Boolean isAbnormal = false;

    @Column(name = "is_sale")
    @ApiModelProperty("是否商城在售")
    private Boolean isSale = false;

    @Column(name = "is_deleted")
    @ApiModelProperty("是否删除")
    @JsonIgnore
    private Boolean isDeleted = false;

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getName() {
        return I18nUtils.getI18nFieldValue(i18nFieldName, I18nUtils.getCurrentLanguageEnum());
    }


    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getShortName() {
        return I18nUtils.getI18nFieldValue(i18nFieldShortName, I18nUtils.getCurrentLanguageEnum());
    }
}
