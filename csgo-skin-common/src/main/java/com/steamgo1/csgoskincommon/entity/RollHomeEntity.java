package com.steamgo1.csgoskincommon.entity;


import com.steamgo1.csgoskincommon.converter.I18nFieldConverter;
import com.steamgo1.csgoskincommon.entity.enums.RollHomeLotteryMethod;
import com.steamgo1.csgoskincommon.entity.enums.RollHomeStatus;
import com.steamgo1.csgoskincommon.entity.enums.RollHomeType;
import com.steamgo1.csgoskincommon.entity.enums.ThresholdType;
import com.steamgo1.csgoskincommon.entity.listener.RollHomeListener;
import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@ApiModel("roll房")
@EntityListeners(value = {RollHomeListener.class})
public class RollHomeEntity extends BaseModel implements Serializable {
    @Column(name = "name", length = 32)
    @ApiModelProperty("房间名称")
    private String name;

    @Column(name = "i18n_field_name", columnDefinition = "JSON")
    @Convert(converter = I18nFieldConverter.class)
    @ApiModelProperty("房间名称（多语言）")
    private I18nField i18nFieldName;

    @ApiModelProperty("创建人")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "admin_user_id", referencedColumnName = "id")
    private AdminUserEntity adminUser;

    @ApiModelProperty("房间类型")
    @Column(name = "type")
    private RollHomeType rollHomeType;

    @ApiModelProperty("开奖方式")
    @Column(name = "lottery_method")
    private RollHomeLotteryMethod rollHomeLotteryMethod;

    @ApiModelProperty("最高人数")
    @Column(name = "max_people")
    private Integer maxPeople;

    @ApiModelProperty("开奖时间")
    @Column(name = "lottery_time")
    private Date lotteryTime;

    @ApiModelProperty("参入阈值")
    @Column(name = "consume_threshold")
    private BigDecimal consumeThreshold;

    @ApiModelProperty("阈值类型")
    @Column(name = "threshold_type")
    private ThresholdType thresholdType;

    @ApiModelProperty("房间密码")
    @Column(name = "password")
    private String password;

    @Column(name = "secret_hash")
    @ApiModelProperty("私密哈希")
    private String secretHash;

    @Column(name = "secret_salt")
    @ApiModelProperty("私密盐值")
    private String secretSalt;

    @Column(name = "public_hash")
    @ApiModelProperty("公共哈希")
    private String publicHash;

    @Column(name = "client_seed", length = 128)
    @ApiModelProperty("种子")
    private String clientSeed;

    @Column(name = "remarks", length = 128)
    @ApiModelProperty("备注")
    private String remarks;

    @Column(name = "i18n_field_remarks", columnDefinition = "JSON")
    @Convert(converter = I18nFieldConverter.class)
    @ApiModelProperty("备注")
    private I18nField i18nFieldRemarks;

    @Column(name = "rabbit_message_d", length = 128)
    @ApiModelProperty("绑定的Rabbit messageId")
    private String rabbitMessageId;

    @ApiModelProperty("状态")
    @Column(name = "status")
    private RollHomeStatus status;

    @ApiModelProperty("是否推荐")
    private Boolean isRecommend;

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getName() {
        return I18nUtils.getI18nFieldValue(i18nFieldName, I18nUtils.getCurrentLanguageEnum());
    }

    /*
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getRemarks() {
        return I18nUtils.getI18nFieldValue(i18nFieldRemarks, I18nUtils.getCurrentLanguageEnum());
    }
}
