package com.steamgo1.csgoskincommon.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@Entity
@Table(name = "ocpc_360_token")
@ApiModel("360sem信息")
public class Ocpc360TokenEntity extends BaseModel implements Serializable {
    @ApiModelProperty("app_key")
    @Column(name = "app_key", length = 128)
    private String appKey;

    @ApiModelProperty("Secret")
    @Column(name = "secret", length = 128)
    private String secret;

    @ApiModelProperty("realm_name")
    @Column(name = "relm_name", length = 256)
    private String relamName;
}
