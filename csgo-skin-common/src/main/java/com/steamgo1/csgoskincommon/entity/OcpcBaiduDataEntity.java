package com.steamgo1.csgoskincommon.entity;

import com.steamgo1.csgoskincommon.entity.enums.OcpcBaiduDataType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Entity
@Table(name = "ocpc_baidu_data")
@ApiModel("百度sem 数据")
public class OcpcBaiduDataEntity extends BaseModel implements Serializable {
    @ApiModelProperty("渠道")
    @ManyToOne(cascade = {CascadeType.MERGE})
    @JoinColumn(name = "ocpc_channel_id", referencedColumnName = "id")
    private OcpcChannelEntity ocpcChannel;

    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.MERGE})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @ApiModelProperty("订单")
    @ManyToOne(cascade = {CascadeType.MERGE})
    @JoinColumn(name = "order_charge_id", referencedColumnName = "id")
    private OrderChargeEntity orderCharge;

    @ApiModelProperty("类型")
    @Column(name = "type")
    private OcpcBaiduDataType type;

    @ApiModelProperty("关键词")
    @Column(name = "key_word", length = 32)
    private String keyWord;

    @ApiModelProperty("unit")
    @Column(name = "unit", length = 32)
    private String unit;

    @ApiModelProperty("plan")
    @Column(name = "plan", length = 32)
    private String plan;

    @ApiModelProperty("创意ID")
    @Column(name = "creative_id", length = 32)
    private Long creativeId;

    @ApiModelProperty("出价")
    @Column(name = "price", length = 32)
    private BigDecimal price;

    @ApiModelProperty("用户IP")
    @Column(name = "ip", length = 64)
    private String ip;


}
