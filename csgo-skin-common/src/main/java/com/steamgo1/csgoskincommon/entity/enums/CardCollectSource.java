package com.steamgo1.csgoskincommon.entity.enums;

import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;
import com.steamgo1.csgoskincommon.utils.I18nUtils;

public enum CardCollectSource implements CodeValueBaseEnum {

    /**
     * 开箱子
     */
    OPEN_CASE(2, "card.collect.source.open.case"),

    /**
     * 追梦
     */
    PERCENTAGE(1, "card.collect.source.percentage"),

    /**
     * 对战
     */
    BATTLE(2, "card.collect.source.battle"),


    /**
     * 充值
     */
    CHARGE(3, "card.collect.source.charge"),

    /**
     * 签到
     */
    SIGN_IN(1, "card.collect.source.sign.in"),

    /**
     * 活动
     */
    ACTIVITY(4, "card.collect.source.activity");

    private Integer code;
    private String i18nKey;

    CardCollectSource(Integer code, String i18nKey) {
        this.code = code;
        this.i18nKey = i18nKey;
    }

    public static CardCollectSource instance(Integer value) {
        CardCollectSource[] enums = values();
        for (CardCollectSource statusEnum : enums) {
            if (statusEnum.code.equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    /**
     * 获取当前语言的显示名称
     */
    public String getDisplayName() {
        return I18nUtils.getMessage(this.i18nKey);
    }

    @Override
    public String getValue() {
        return getDisplayName();
    }

    @Override
    public String description() {
        return code + "-" + getValue();
    }
}
