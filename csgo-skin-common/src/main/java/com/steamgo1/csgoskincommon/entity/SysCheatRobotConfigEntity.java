package com.steamgo1.csgoskincommon.entity;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023-02-09 16:02:14
 */
@Data
@Entity
@Table(name = "sys_cheat_robot_config")
@ApiModel("全职机器人配置")
public class SysCheatRobotConfigEntity extends BaseModel implements Serializable {
    @Column(name = "enable_open_case")
    private Boolean enableOpenCase = true;

    @Column(name = "open_case_probability")
    private Integer openCaseProbability;

    @Column(name = "enable_percentage")
    private Boolean enablePercentage = true;

    @Column(name = "percentage_probability")
    private Integer percentageProbability;

    @Column(name = "enable_battle")
    private Boolean enableBattle = true;

    @Column(name = "battle_probability")
    private Integer battleProbability;

    @Column(name = "rounds_total")
    private Integer rounds_total = 0;

    @Column(name = "is_activate")
    private Boolean isActivate = false;

}
