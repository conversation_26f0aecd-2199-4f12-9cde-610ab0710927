package com.steamgo1.csgoskincommon.entity;

import com.steamgo1.csgoskincommon.entity.enums.OcpcGoogleDataType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Entity
@Table(name = "ocpc_google_data")
@ApiModel("Google OCPC 数据")
public class OcpcGoogleDataEntity extends BaseModel implements Serializable {
    @ApiModelProperty("渠道")
    @ManyToOne(cascade = {CascadeType.MERGE})
    @JoinColumn(name = "ocpc_channel_id", referencedColumnName = "id")
    private OcpcChannelEntity ocpcChannel;

    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.MERGE})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @ApiModelProperty("订单")
    @ManyToOne(cascade = {CascadeType.MERGE})
    @JoinColumn(name = "order_charge_id", referencedColumnName = "id")
    private OrderChargeEntity orderCharge;

    @ApiModelProperty("类型")
    @Column(name = "type")
    private OcpcGoogleDataType type;

    @ApiModelProperty("事件ID")
    @Column(name = "event_id", length = 64)
    private String eventId;

    @ApiModelProperty("客户端ID")
    @Column(name = "client_id", length = 128)
    private String clientId;

    @ApiModelProperty("GCLID")
    @Column(name = "gclid", length = 128)
    private String gclid;

    @ApiModelProperty("事件价值")
    @Column(name = "event_value")
    private BigDecimal eventValue;

    @ApiModelProperty("用户IP")
    @Column(name = "ip", length = 64)
    private String ip;

    @ApiModelProperty("用户代理")
    @Column(name = "user_agent", length = 512)
    private String userAgent;
}
