package com.steamgo1.csgoskincommon.entity;

import com.steamgo1.csgoskincommon.entity.listener.SkinListener;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023-02-09 16:02:14
 */
@Data
@Entity
@Table(name = "skin_rarity_color")
@ApiModel("饰品稀有度颜色")
@EntityListeners(value = {SkinListener.class})
public class SkinRarityColorEntity extends BaseModel implements Serializable {

    @Column(name = "code")
    @ApiModelProperty("稀有度code")
    private Integer code;

    @Column(name = "color")
    @ApiModelProperty("质量颜色")
    private String color;

    @Column(name = "color_name", length = 64)
    @ApiModelProperty("颜色名称")
    private String colorName;

}
