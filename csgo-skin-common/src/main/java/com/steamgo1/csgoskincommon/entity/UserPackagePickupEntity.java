package com.steamgo1.csgoskincommon.entity;

import com.steamgo1.csgoskincommon.entity.enums.PackagePickupStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023-02-09 16:02:14
 */
@Data
@Entity
@Table(name = "user_package_pickup")
@ApiModel("用户库存取回")
public class UserPackagePickupEntity extends BaseModel implements Serializable {
    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @ApiModelProperty("背包饰品")
    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "user_package_id", referencedColumnName = "id")
    private UserPackageEntity userPackage;

    @Column(name = "order_no")
    @ApiModelProperty("订单号")
    private String orderNo;

    @Column(name = "third_order_no")
    @ApiModelProperty("第三方订单号")
    private Long thirdOrderNo;

    @Column(name = "third_order_no_io")
    @ApiModelProperty("第三方订单号(IO661)")
    private String thirdOrderNoIo;

    @Column(name = "steam_trade_url")
    @ApiModelProperty("steam交易链接")
    private String steamTradeUrl;

    @Column(name = "status")
    @ApiModelProperty("状态")
    private PackagePickupStatus status;

    @Column(name = "old_order_no")
    @ApiModelProperty("旧订单号")
    private String oldOrderNo;

    @Column(name = "remarks")
    @ApiModelProperty("备注(失败原因)")
    private String remarks;

    @Column(name = "is_remind")
    @ApiModelProperty("已提醒")
    private Boolean isRemind = false;


}
