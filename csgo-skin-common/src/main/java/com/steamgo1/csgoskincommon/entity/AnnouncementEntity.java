package com.steamgo1.csgoskincommon.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@Entity
@Table(name = "site_announcement")
@ApiModel("公告")
public class AnnouncementEntity extends BaseModel implements Serializable {
    @ApiModelProperty("公告内容")
    @Column(name = "content", length = 128)
    private String content;

    @ApiModelProperty("排序")
    @Column(name = "grade")
    private Integer grade;


    @ApiModelProperty("是否上线")
    @Column(name = "is_show", length = 128)
    private Boolean isShow;
}
