package com.steamgo1.csgoskincommon.entity.enums;

import com.google.common.collect.Lists;
import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;
import com.steamgo1.csgoskincommon.utils.I18nUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum OcpcGoogleDataType implements CodeValueBaseEnum {

    CLICK(0, "ocpc.google.data.type.click"),
    REGISTER(1, "ocpc.google.data.type.register"),
    CHARGE(2, "ocpc.google.data.type.charge");

    private Integer code;
    private String i18nKey;

    OcpcGoogleDataType(int code, String i18nKey) {
        this.code = code;
        this.i18nKey = i18nKey;
    }

    /**
     * 获取当前语言的显示名称
     */
    public String getDisplayName() {
        return I18nUtils.getMessage(this.i18nKey);
    }

    //讲枚举转换成list格式，这样前台遍历的时候比较容易，列如 下拉框 后台调用toList方法，你就可以得到code 和name了
    public static List toList() {
        List list = Lists.newArrayList();

        for (OcpcGoogleDataType googleDataType : OcpcGoogleDataType.values()) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("code", googleDataType.getCode());
            map.put("name", googleDataType.getValue());
            list.add(map);
        }
        return list;
    }

    public static OcpcGoogleDataType instance(Integer code) {
        OcpcGoogleDataType[] enums = values();
        for (OcpcGoogleDataType googleDataType : enums) {
            if (googleDataType.getCode().equals(code)) {
                return googleDataType;
            }
        }
        return null;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getValue() {
        return getDisplayName();
    }

    @Override
    public String description() {
        return code + "-" + getValue();
    }
}
