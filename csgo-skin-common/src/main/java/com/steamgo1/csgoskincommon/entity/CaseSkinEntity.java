package com.steamgo1.csgoskincommon.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Entity
@Table(name = "case_skin")
@ApiModel("箱子皮肤关联信息")
public class CaseSkinEntity extends BaseModel implements Serializable {

    @ApiModelProperty("箱子")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "case_id", referencedColumnName = "id")
    private CaseEntity caseEntity;

    @ApiModelProperty("饰品")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "skin_id", referencedColumnName = "id")
    private SkinEntity skin;

    @ApiModelProperty("箱子中的饰品颜色")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "skin_rarity_color", referencedColumnName = "id")
    private SkinRarityColorEntity skinRarityColor;


    @ApiModelProperty("箱子中的饰品颜色等级")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "level_id", referencedColumnName = "id")
    private CaseLevelEntity level;

    @ApiModelProperty("中奖数量统计")
    @Column(name = "winningTotal")
    private Integer winningTotal;

    @ApiModelProperty("中奖概率")
    @Column(name = "probability", columnDefinition = "decimal(18,5)")
    private BigDecimal probability;

    @ApiModelProperty("排序(数字越大优先级越高)")
    @Column(name = "grade")
    private Integer grade;

    @ApiModelProperty("参考价格")
    @Column(name = "reference_price")
    private BigDecimal referencePrice;
}
