package com.steamgo1.csgoskincommon.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Entity
@Table(name = "user_invite_encourage_record")
@ApiModel("用户推广奖励记录")
public class UserInviteEncourageRecordEntity extends BaseModel implements Serializable {
    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @ApiModelProperty("订单")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "order_charge", referencedColumnName = "id")
    private OrderChargeEntity orderCharge;

    @ApiModelProperty("充值金币")
    @Column(name = "charge_coin")
    private BigDecimal chargeCoin;

    @ApiModelProperty("奖励金币")
    @Column(name = "encourage_coin")
    private BigDecimal encourageCoin;

    @ApiModelProperty("是否首充")
    @Column(name = "is_first_charge")
    private Boolean isFirstCharge;
}
