package com.steamgo1.csgoskincommon.entity.enums;

import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;
import com.steamgo1.csgoskincommon.utils.I18nUtils;

public enum BattleHomeMethod implements CodeValueBaseEnum {
    PVE(1, "battle.home.method.pve"),
    PVP(2, "battle.home.method.pvp");

    private Integer code;
    private String i18nKey;

    BattleHomeMethod(Integer code, String i18nKey) {
        this.code = code;
        this.i18nKey = i18nKey;
    }

    public static BattleHomeMethod instance(Integer value) {
        BattleHomeMethod[] enums = values();
        for (BattleHomeMethod battleHomeMethod : enums) {
            if (battleHomeMethod.code.equals(value)) {
                return battleHomeMethod;
            }
        }
        return null;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    /**
     * 获取当前语言的显示名称
     */
    public String getDisplayName() {
        return I18nUtils.getMessage(this.i18nKey);
    }

    @Override
    public String getValue() {
        return getDisplayName();
    }

    @Override
    public String description() {
        return code + "-" + getDisplayName();
    }
}
