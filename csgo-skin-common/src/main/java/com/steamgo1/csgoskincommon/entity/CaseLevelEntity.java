package com.steamgo1.csgoskincommon.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "box_level")
@ApiModel("箱子等级")
public class CaseLevelEntity extends BaseModel implements Serializable {

    @ApiModelProperty("箱子")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "case_id", referencedColumnName = "id")
    private CaseEntity caseEntity;

    @ApiModelProperty("等级")
    @Column(name = "level_num")
    private Integer levelNum;

}
