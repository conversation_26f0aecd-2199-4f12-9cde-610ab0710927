package com.steamgo1.csgoskincommon.entity;

import com.steamgo1.csgoskincommon.entity.enums.UserPackageSource;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023-02-09 16:02:14
 */
@Data
@Entity
@Table(name = "user_package")
@ApiModel("用户背包")
public class UserPackageEntity extends BaseModel implements Serializable {
    @ApiModelProperty("是否已取回")
    @Column(name = "is_received")
    Boolean isReceived = false;
    @ApiModelProperty("是否已出售")
    @Column(name = "is_selled")
    Boolean isSelled = false;
    @ApiModelProperty("饰品锁定")
    @Column(name = "is_locked")
    Boolean isLocked = false;
    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;
    @ApiModelProperty("饰品")
    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "skin_id", referencedColumnName = "id")
    private SkinEntity skin;
    @Column(name = "price")
    @ApiModelProperty("入库zbt价格")
    private BigDecimal price;
    @Column(name = "diamond")
    @ApiModelProperty("入库钻石价格")
    private BigDecimal diamond;
    @ApiModelProperty("来源")
    @Column(name = "source")
    private UserPackageSource source;
    @ApiModelProperty("箱子ID")
    @Column(name = "case_id")
    private Long caseId;

}
