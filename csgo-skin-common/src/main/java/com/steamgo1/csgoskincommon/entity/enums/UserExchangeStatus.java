package com.steamgo1.csgoskincommon.entity.enums;

import com.steamgo1.csgoskincommon.utils.I18nUtils;
import lombok.Getter;

public enum UserExchangeStatus {
    EXCHANGEING(1, "user.exchange.status.exchanging"),
    SUCCESS(2, "user.exchange.status.success"),
    FAIL(3, "user.exchange.status.fail");

    @Getter
    private Integer code;
    @Getter
    private String i18nKey;

    UserExchangeStatus(Integer code, String i18nKey) {
        this.code = code;
        this.i18nKey = i18nKey;
    }

    /**
     * 获取当前语言的显示名称
     */
    public String getDisplayName() {
        return I18nUtils.getMessage(this.i18nKey);
    }

    public String getValue() {
        return getDisplayName();
    }

    public static UserExchangeStatus instance(Integer value) {
        UserExchangeStatus[] enums = values();
        for (UserExchangeStatus caseType : enums) {
            if (caseType.code.equals(value)) {
                return caseType;
            }
        }
        return null;
    }

    public Integer value() {
        return code;
    }
}
