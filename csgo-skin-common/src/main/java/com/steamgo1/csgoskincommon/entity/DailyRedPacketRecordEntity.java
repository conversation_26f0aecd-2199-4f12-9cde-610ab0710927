package com.steamgo1.csgoskincommon.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@ApiModel("每日充值红包领取记录")
@Table(name = "daily_red_packet_record")
public class DailyRedPacketRecordEntity extends BaseModel implements Serializable {
    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @ApiModelProperty("每日红包")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "daily_red_packet_id", referencedColumnName = "id")
    private DailyRedPacketEntity dailyRedPacket;
}
