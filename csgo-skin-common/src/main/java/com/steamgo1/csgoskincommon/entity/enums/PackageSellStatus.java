package com.steamgo1.csgoskincommon.entity.enums;

import com.steamgo1.csgoskincommon.utils.I18nUtils;
import lombok.Getter;

public enum PackageSellStatus {
    SELLING(1, "package.sell.status.selling"),
    SUCCESS(2, "package.sell.status.success"),
    FAIL(3, "package.sell.status.fail");

    @Getter
    private Integer code;
    @Getter
    private String i18nKey;

    PackageSellStatus(Integer code, String i18nKey) {
        this.code = code;
        this.i18nKey = i18nKey;
    }

    /**
     * 获取当前语言的显示名称
     */
    public String getDisplayName() {
        return I18nUtils.getMessage(this.i18nKey);
    }

    public String getValue() {
        return getDisplayName();
    }
}
