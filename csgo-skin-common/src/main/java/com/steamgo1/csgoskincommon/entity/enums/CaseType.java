package com.steamgo1.csgoskincommon.entity.enums;

import com.steamgo1.csgoskincommon.utils.I18nUtils;
import lombok.Getter;

public enum CaseType {
    NOVICE(1, "case.type.novice"),
    ORDINARY(2, "case.type.ordinary"),
    CONSUMEPLAN(3, "case.type.consumeplan"),
    DAILY_TASKS(4, "case.type.daily.tasks");

    @Getter
    private Integer code;
    @Getter
    private String i18nKey;

    CaseType(Integer code, String i18nKey) {
        this.code = code;
        this.i18nKey = i18nKey;
    }

    public static CaseType instance(Integer value) {
        CaseType[] enums = values();
        for (CaseType caseType : enums) {
            if (caseType.value().equals(value)) {
                return caseType;
            }
        }
        return null;
    }

    public Integer value() {
        return code;
    }

    /**
     * 获取当前语言的类型名称
     */
    public String getType() {
        return I18nUtils.getMessage(this.i18nKey);
    }
}
