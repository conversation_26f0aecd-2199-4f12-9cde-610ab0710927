package com.steamgo1.csgoskincommon.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023-02-09 16:02:14
 */
@Data
@Entity
@Table(name = "user_algorithm_data")
@ApiModel("用户算法数据")
public class AlgorithmDataEntity extends BaseModel implements Serializable {
    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @Column(name = "secret_hash")
    @ApiModelProperty("私密哈希")
    private String secretHash;

    @Column(name = "secret_salt")
    @ApiModelProperty("私密盐值")
    private String secretSalt;

    @Column(name = "public_hash")
    @ApiModelProperty("公共哈希")
    private String publicHash;

    @Column(name = "rounds")
    @ApiModelProperty("回合数")
    private Integer rounds;

    @Column(name = "client_seed", length = 128)
    @ApiModelProperty("用户种子")
    private String clientSeed;

    @Column(name = "is_used")
    private Boolean isUsed;

    @Column(name = "is_deleted")
    private Boolean isDeleted = false;
}
