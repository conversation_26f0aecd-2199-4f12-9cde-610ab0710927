package com.steamgo1.csgoskincommon.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023-02-09 16:02:14
 */

@Validated
@Data
@Entity
@Table(name = "admin_user")
@ApiModel("后台用户信息")
public class AdminUserEntity extends BaseModel implements Serializable {
    @Column(name = "username", length = 16, unique = true)
    @ApiModelProperty("用户名")
    private String username;

    @Column(name = "password", length = 64)
    @ApiModelProperty("密码")
    private String password;

    @Column(name = "phone", length = 16)
    @ApiModelProperty("手机号")
    private String phone;

    @Column(name = "email", length = 32)
    @ApiModelProperty("邮箱")
    private String email;

    @Column(name = "is_supperuser")
    @ApiModelProperty("是否是超级用户")
    private Boolean isSuperuser = false;

    @Column(name = "is_ban")
    @ApiModelProperty("是否被禁用")
    private Boolean isBan = false;

    @Column(name = "is_expired")
    @ApiModelProperty("是否到期")
    private Boolean isExpired = false;

    @Column(name = "is_deleted")
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;


}
