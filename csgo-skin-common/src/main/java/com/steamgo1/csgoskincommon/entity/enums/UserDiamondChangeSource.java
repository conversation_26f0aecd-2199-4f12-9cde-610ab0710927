package com.steamgo1.csgoskincommon.entity.enums;

import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;
import com.steamgo1.csgoskincommon.utils.I18nUtils;

/**
 * 用户钻石流水记录
 */
public enum UserDiamondChangeSource implements CodeValueBaseEnum {
    /**
     * 兑换
     */
    EXCHANGE(1, "user.diamond.change.source.exchange"),

    /**
     * 出售
     */
    SELL(2, "user.diamond.change.source.sell"),
    /**
     * 购买
     */
    BUY(3, "user.diamond.change.source.buy"),

    /**
     * 其它
     */
    OTHER(4, "user.diamond.change.source.other");


    private Integer code;

    private String i18nKey;

    UserDiamondChangeSource(Integer num, String i18nKey) {
        this.code = num;
        this.i18nKey = i18nKey;
    }

    public static UserDiamondChangeSource instance(Integer value) {
        UserDiamondChangeSource[] enums = values();
        for (UserDiamondChangeSource statusEnum : enums) {
            if (statusEnum.code.equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    /**
     * 获取当前语言的显示名称
     */
    public String getDisplayName() {
        return I18nUtils.getMessage(this.i18nKey);
    }

    @Override
    public String getValue() {
        return getDisplayName();
    }

    @Override
    public String description() {
        return code + "-" + getDisplayName();
    }
}
