package com.steamgo1.csgoskincommon.entity;

import com.steamgo1.csgoskincommon.entity.enums.UserDisableType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023-02-09 16:02:14
 */
@Data
@Entity
@Table(name = "user_disable")
@ApiModel("用户功能禁用")
public class UserDisableEntity extends BaseModel implements Serializable {

    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @ApiModelProperty("禁用功能类型")
    @Column(name = "type")
    private UserDisableType type;


    @ApiModelProperty("禁用到期时间")
    @JoinColumn(name = "disable_expire")
    private Date disableExpire;

    @ApiModelProperty("备注")
    @Column(name = "remarks", length = 64)
    private String remarks;

    @ApiModelProperty("是否生效")
    @Column(name = "is_effective")
    private Boolean isEffective;
}
