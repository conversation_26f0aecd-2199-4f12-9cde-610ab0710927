package com.steamgo1.csgoskincommon.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Data
@ApiModel("请求日志记录")
@Table(name = "request_log")
public class RequestLogEntity extends BaseModel implements Serializable {

    @ApiModelProperty("请求路径")
    @Column(name = "request_path")
    private String requestPath;


    @ApiModelProperty("请求类型")
    @Column(name = "request_method")
    private String requestMethod;


    @ApiModelProperty("请求方法")
    @Column(name = "request_sign_method")
    private String requestSignMethod;


    @ApiModelProperty("请求参数")
    @Column(name = "request_param")
    private String requestParam;


    @ApiModelProperty("请求ip")
    @Column(name = "request_ip")
    private String requestIp;

}
