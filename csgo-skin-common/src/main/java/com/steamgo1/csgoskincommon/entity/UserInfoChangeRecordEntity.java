package com.steamgo1.csgoskincommon.entity;


import com.steamgo1.csgoskincommon.entity.enums.UserInfoChangeType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "user_info_change_record")
@ApiModel("用户修改个人信息记录")
public class UserInfoChangeRecordEntity extends BaseModel implements Serializable {
    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @ApiModelProperty("变更字段")
    @Column(name = "type")
    private UserInfoChangeType type;

    @ApiModelProperty("变更前")
    @Column(name = "before_value")
    private String beforeValue;

    @ApiModelProperty("变更值")
    @Column(name = "value")
    private String value;
}
