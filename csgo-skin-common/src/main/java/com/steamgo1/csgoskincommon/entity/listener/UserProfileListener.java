package com.steamgo1.csgoskincommon.entity.listener;

import com.steamgo1.csgoskincommon.entity.UserProfileEntity;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import javax.persistence.PostUpdate;


@Slf4j
public class UserProfileListener {
    @Value("${spring.redis.prefix.user}")
    private String redisUserPrefix;

    @PostUpdate
    public void postUpdate(UserProfileEntity userProfile) {
        log.info("用户信息更新删除缓存, {}", userProfile.getUser().getId());
        String userInfoRedisKey = redisUserPrefix + ":" + userProfile.getUser().getId();
        if (RedisUtils.hasKey(userInfoRedisKey)) {
            RedisUtils.delete(userInfoRedisKey);
        }
    }
}
