package com.steamgo1.csgoskincommon.entity;


import com.steamgo1.csgoskincommon.entity.enums.UserCoinChangeSource;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Entity
@Table(name = "user_coin_record")
@ApiModel("用户金币流水记录")
public class UserCoinRecordEntity extends BaseModel implements Serializable {
    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @ApiModelProperty("变更来源")
    @JoinColumn(name = "source")
    private UserCoinChangeSource source;

    @ApiModelProperty("变更来源ID")
    @JoinColumn(name = "source_id")
    private Long sourceId;

    @ApiModelProperty("是否正增长")
    @JoinColumn(name = "is_positive")
    private Boolean isPositive;

    @ApiModelProperty("金额")
    @JoinColumn(name = "amount")
    private BigDecimal amount;

    @ApiModelProperty("变更后金额")
    @JoinColumn(name = "after_amount")
    private BigDecimal afterAmount;


}
