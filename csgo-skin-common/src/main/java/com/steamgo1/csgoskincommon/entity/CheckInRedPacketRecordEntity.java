package com.steamgo1.csgoskincommon.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@ApiModel("日常签到领取记录")
@Table(name = "check_in_red_packet_record")
public class CheckInRedPacketRecordEntity extends BaseModel implements Serializable {
    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @ApiModelProperty("签到红包")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "check_in_red_packet_id", referencedColumnName = "id")
    private CheckInRedPacketEntity checkInRedPacket;

}
