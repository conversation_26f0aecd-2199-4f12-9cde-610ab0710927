package com.steamgo1.csgoskincommon.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023-02-09 16:02:14
 */
@Data
@Entity
@Table(name = "skin_prototype")
@ApiModel("皮肤原型表")
public class PrototypeEntity extends BaseModel implements Serializable {
    /**
     * 皮肤原型名(例:AK47)
     */
    @ApiModelProperty("皮肤原型名")
    @Column(name = "name", length = 16, unique = true)
    private String name;

    /**
     * 外键
     */
    @ApiModelProperty("皮肤原型字段")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "skin_prototype_category_id", referencedColumnName = "id")
    private PrototypeCategoryEntity prototypeCategory;
}
