package com.steamgo1.csgoskincommon.entity;

import com.steamgo1.csgoskincommon.entity.enums.Status;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Data
@ApiModel("黑名单ip")
@Table(name = "ip_black")
public class IpBlackEntity extends BaseModel implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("ip")
    @Column(name = "ip", unique = true, nullable = false)
    private String ip;

    @ApiModelProperty("状态")
    @Column(name = "status")
    private Status status;
}
