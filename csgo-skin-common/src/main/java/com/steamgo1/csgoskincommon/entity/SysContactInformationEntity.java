package com.steamgo1.csgoskincommon.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@Entity
@ApiModel("联系方式表")
@Table(name = "sys_contact_information")
public class SysContactInformationEntity extends BaseModel implements Serializable {
    @ApiModelProperty("QQ群")
    @Column(name = "qq_group", length = 128)
    private String qqGroup;

    @ApiModelProperty("微信")
    @Column(name = "wechat", length = 128)
    private String wechatUrl;
}
