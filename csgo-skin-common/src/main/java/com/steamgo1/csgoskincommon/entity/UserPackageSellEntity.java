package com.steamgo1.csgoskincommon.entity;

import com.steamgo1.csgoskincommon.entity.enums.PackageSellStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023-02-09 16:02:14
 */
@Data
@Entity
@Table(name = "user_package_sell")
@ApiModel("用户库存出售")
public class UserPackageSellEntity extends BaseModel implements Serializable {
    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @ApiModelProperty("背包饰品")
    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "user_package", referencedColumnName = "id")
    private UserPackageEntity userPackage;

    @Column(name = "order_no")
    @ApiModelProperty("订单号")
    private String orderNo;


    @Column(name = "status")
    @ApiModelProperty("状态")
    private PackageSellStatus status;

    @Column(name = "remarks")
    @ApiModelProperty("备注(失败原因)")
    private String remarks;
}
