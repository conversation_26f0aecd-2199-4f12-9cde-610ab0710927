package com.steamgo1.csgoskincommon.entity.listener;

import com.steamgo1.csgoskincommon.entity.DataDictionaryEntity;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import javax.persistence.PostPersist;
import javax.persistence.PostUpdate;


@Slf4j
public class DataDictionaryListener {
    @Value("${spring.redis.prefix.case}")
    private String redisCasePrefix;

    @Value("${spring.redis.prefix.skin}")
    private String redisSkinPrefix;

    @PostUpdate
    public void postUpdate(DataDictionaryEntity dataDictionaryEntity) {
        log.info("字典更新更新删除缓存 {} {}", dataDictionaryEntity.getId(), dataDictionaryEntity.getName());
        String redisKey = redisCasePrefix + ":case_of_category";
        if (RedisUtils.hasKey(redisKey)) {
            RedisUtils.delete(redisKey);
        }
        String redisSkinKey = redisSkinPrefix + ":QUERYPARAM";
        if (RedisUtils.hasKey(redisSkinKey)) {
            RedisUtils.delete(redisSkinKey);
        }
        String redisCaseCateGory = redisCasePrefix + ":case_category";
        if (RedisUtils.hasKey(redisCaseCateGory)) {
            RedisUtils.delete(redisCaseCateGory);
        }
    }

    @PostPersist
    public void postPersist(DataDictionaryEntity dataDictionaryEntity) {
        log.info("字典保存删除缓存 {} {}", dataDictionaryEntity.getId(), dataDictionaryEntity.getName());
        String redisKey = redisCasePrefix + ":case_of_category";
        if (RedisUtils.hasKey(redisKey)) {
            RedisUtils.delete(redisKey);
        }
        String redisSkinKey = redisSkinPrefix + ":QUERYPARAM";
        if (RedisUtils.hasKey(redisSkinKey)) {
            RedisUtils.delete(redisSkinKey);
        }
        String redisCaseCateGory = redisCasePrefix + ":case_category";
        if (RedisUtils.hasKey(redisCaseCateGory)) {
            RedisUtils.delete(redisCaseCateGory);
        }
    }

}
