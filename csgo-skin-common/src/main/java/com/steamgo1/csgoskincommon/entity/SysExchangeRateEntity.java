package com.steamgo1.csgoskincommon.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Entity
@ApiModel("汇率表")
@Table(name = "sys_exchange_rate")
public class SysExchangeRateEntity extends BaseModel implements Serializable {
    @ApiModelProperty("金币兑人民币")
    @Column(name = "coin_to_cny", columnDefinition = "decimal(18,3)")
    private BigDecimal coinTocny;

    @ApiModelProperty("金币兑钻石")
    @Column(name = "coin_to_diamond", columnDefinition = "decimal(18,3)")
    private BigDecimal coinToDiamond;

    @ApiModelProperty("扎比特兑人民币")
    @Column(name = "zbt_to_cny", columnDefinition = "decimal(18,3)")
    private BigDecimal zbtToCny;

    @ApiModelProperty("扎比特到人民币溢价")
    @Column(name = "zbt_to_cny_premium", columnDefinition = "decimal(18,3)")
    private BigDecimal zbtToCnyPremium;

    @ApiModelProperty("追梦平台溢价")
    @Column(name = "percentage_premium", columnDefinition = "decimal(18,3)")
    private BigDecimal percentagePremium;
}
