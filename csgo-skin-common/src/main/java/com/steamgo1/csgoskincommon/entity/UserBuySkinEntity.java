package com.steamgo1.csgoskincommon.entity;

import com.steamgo1.csgoskincommon.entity.enums.UserBuySkinStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023-02-09 16:02:14
 */
@Data
@Entity
@Table(name = "user_skin_buy")
@ApiModel("用户购买饰品")
public class UserBuySkinEntity extends BaseModel implements Serializable {
    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @ApiModelProperty("饰品")
    @ManyToOne(cascade = {CascadeType.REFRESH})
    @JoinColumn(name = "skin", referencedColumnName = "id")
    private SkinEntity skin;

    @Column(name = "diamond")
    @ApiModelProperty("消耗钻石")
    private BigDecimal diamond;

    @Column(name = "order_no")
    @ApiModelProperty("订单号")
    private String orderNo;


    @Column(name = "status")
    @ApiModelProperty("状态")
    private UserBuySkinStatus status;

    @Column(name = "remarks")
    @ApiModelProperty("备注(失败原因)")
    private String remarks;
}
