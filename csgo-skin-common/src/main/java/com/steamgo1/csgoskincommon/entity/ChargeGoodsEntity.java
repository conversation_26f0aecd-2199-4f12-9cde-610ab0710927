package com.steamgo1.csgoskincommon.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023-02-09 16:02:14
 */
@Data
@Entity
@Table(name = "charge_goods")
@ApiModel("充值商品")
public class ChargeGoodsEntity extends BaseModel implements Serializable {
    @Column(name = "price")
    @ApiModelProperty("金额")
    private BigDecimal price;

    @Column(name = "coin")
    @ApiModelProperty("金币数量")
    private BigDecimal coin;

    @Column(name = "background_url", length = 128)
    @ApiModelProperty("背景图片")
    private String backgroundUrl;

    @Column(name = "is_new_user")
    @ApiModelProperty("仅新用户可买")
    private Boolean isNewUser;

    @Column(name = "first_charge_free_coin")
    @ApiModelProperty("首冲赠送金币")
    private BigDecimal firstChargeFreeCoin;

    @ApiModelProperty("赠送比例")
    @Column(name = "free_coin_percentage")
    private BigDecimal freeCoinPercentage;

    @Column(name = "is_sell")
    @ApiModelProperty("是否上架")
    private Boolean isSell;
}
