package com.steamgo1.csgoskincommon.entity;


import com.steamgo1.csgoskincommon.entity.enums.DailyActivityType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@Entity
@Table(name = "daily_activity")
@ApiModel("日活表")
public class DailyActivityEntity extends BaseModel implements Serializable {
    @ApiModelProperty("用户ID")
    @Column(name = "user_id")
    Long userId;

    @ApiModelProperty("日活类型")
    @Column(name = "type")
    DailyActivityType type;
}
