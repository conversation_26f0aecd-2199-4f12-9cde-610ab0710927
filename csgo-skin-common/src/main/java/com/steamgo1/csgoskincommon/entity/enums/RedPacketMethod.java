package com.steamgo1.csgoskincommon.entity.enums;

import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;
import com.steamgo1.csgoskincommon.utils.I18nUtils;

public enum RedPacketMethod implements CodeValueBaseEnum {

    NOVICE(0, "red.packet.method.novice"),
    ORDINARY(1, "red.packet.method.ordinary");

    private Integer code;
    private String i18nKey;

    RedPacketMethod(int code, String i18nKey) {
        this.code = code;
        this.i18nKey = i18nKey;
    }

    public static RedPacketMethod instance(Integer code) {
        RedPacketMethod[] enums = values();
        for (RedPacketMethod rollHomeType : enums) {
            if (rollHomeType.getCode().equals(code)) {
                return rollHomeType;
            }
        }
        return null;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    /**
     * 获取当前语言的显示名称
     */
    public String getDisplayName() {
        return I18nUtils.getMessage(this.i18nKey);
    }

    @Override
    public String getValue() {
        return getDisplayName();
    }

    @Override
    public String description() {
        return code + "-" + getDisplayName();
    }
}
