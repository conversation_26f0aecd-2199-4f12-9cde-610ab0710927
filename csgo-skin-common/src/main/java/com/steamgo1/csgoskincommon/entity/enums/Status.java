package com.steamgo1.csgoskincommon.entity.enums;

import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;
import com.steamgo1.csgoskincommon.utils.I18nUtils;

public enum Status implements CodeValueBaseEnum {
    ClOSE(0, "status.disable"),
    OPEN(1, "status.enable");

    private Integer code;
    private String i18nKey;

    Status(Integer code, String i18nKey) {
        this.code = code;
        this.i18nKey = i18nKey;
    }

    /**
     * 获取当前语言的显示名称
     */
    public String getDisplayName() {
        return I18nUtils.getMessage(this.i18nKey);
    }

    @Override
    public String description() {
        return code + "-" + getDisplayName();
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getValue() {
        return getDisplayName();
    }

}
