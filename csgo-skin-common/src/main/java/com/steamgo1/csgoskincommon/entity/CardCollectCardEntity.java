package com.steamgo1.csgoskincommon.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;


@Data
@Entity
@ApiModel("卡片")
@Table(name = "card_collect_card")
public class CardCollectCardEntity extends BaseModel implements Serializable {
    @ApiModelProperty("活动")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "card_collect", referencedColumnName = "id")
    private CardCollectEntity cardCollect;

    @ApiModelProperty("卡片名")
    @Column(name = "name", length = 16)
    private String name;


    @Column(name = "picture", length = 512)
    @ApiModelProperty("卡片图片")
    private String picture;

    @ApiModelProperty("掉落概率")
    @Column(name = "probability", columnDefinition = "decimal(18,5)")
    private BigDecimal probability;

}
