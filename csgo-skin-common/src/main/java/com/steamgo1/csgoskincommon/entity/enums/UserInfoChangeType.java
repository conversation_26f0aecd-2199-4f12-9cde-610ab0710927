package com.steamgo1.csgoskincommon.entity.enums;

import com.google.common.collect.Lists;
import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;
import com.steamgo1.csgoskincommon.utils.I18nUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum UserInfoChangeType implements CodeValueBaseEnum {

    NICKNAME(0, "user.info.change.type.nickname"),
    AVATAR(1, "user.info.change.type.avatar"),
    TRADEOFFERACCESS_URL(2, "user.info.change.type.trade.url");
    private Integer code;
    private String i18nKey;

    UserInfoChangeType(int code, String i18nKey) {
        this.code = code;
        this.i18nKey = i18nKey;
    }

    /**
     * 获取当前语言的显示名称
     */
    public String getDisplayName() {
        return I18nUtils.getMessage(this.i18nKey);
    }

    //讲枚举转换成list格式，这样前台遍历的时候比较容易，列如 下拉框 后台调用toList方法，你就可以得到code 和name了
    public static List toList() {
        List list = Lists.newArrayList();

        for (UserInfoChangeType rollHomeType : UserInfoChangeType.values()) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("code", rollHomeType.getCode());
            map.put("name", rollHomeType.getValue());
            list.add(map);
        }
        return list;
    }

    public static UserInfoChangeType instance(Integer code) {
        UserInfoChangeType[] enums = values();
        for (UserInfoChangeType rollHomeType : enums) {
            if (rollHomeType.getCode().equals(code)) {
                return rollHomeType;
            }
        }
        return null;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getValue() {
        return getDisplayName();
    }

    @Override
    public String description() {
        return code + "-" + getValue();
    }
}
