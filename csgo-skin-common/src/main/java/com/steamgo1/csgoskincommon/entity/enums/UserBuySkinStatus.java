package com.steamgo1.csgoskincommon.entity.enums;

import com.steamgo1.csgoskincommon.utils.I18nUtils;
import lombok.Getter;

public enum UserBuySkinStatus {
    BUYING(1, "user.buy.skin.status.buying"),
    SUCCESS(2, "user.buy.skin.status.success"),
    FAIL(3, "user.buy.skin.status.fail");

    @Getter
    private Integer code;
    @Getter
    private String i18nKey;

    UserBuySkinStatus(Integer code, String i18nKey) {
        this.code = code;
        this.i18nKey = i18nKey;
    }

    /**
     * 获取当前语言的显示名称
     */
    public String getDisplayName() {
        return I18nUtils.getMessage(this.i18nKey);
    }

    public String getValue() {
        return getDisplayName();
    }

    public static UserBuySkinStatus instance(Integer value) {
        UserBuySkinStatus[] enums = values();
        for (UserBuySkinStatus caseType : enums) {
            if (caseType.code.equals(value)) {
                return caseType;
            }
        }
        return null;
    }

    public Integer value() {
        return code;
    }
}
