package com.steamgo1.csgoskincommon.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@Entity
@ApiModel("首页横幅表")
@Table(name = "sys_index_banner")
public class SysIndexBannerEntity extends BaseModel implements Serializable {
    @ApiModelProperty("首页banner")
    @Column(name = "img", length = 128)
    private String img;

    @ApiModelProperty("箱子优先级")
    @Column(name = "gradle")
    private Integer gradle;

    @Column(name = "is_deleted")
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;


}
