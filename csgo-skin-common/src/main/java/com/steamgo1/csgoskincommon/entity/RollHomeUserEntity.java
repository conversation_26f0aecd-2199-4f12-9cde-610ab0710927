package com.steamgo1.csgoskincommon.entity;

import com.steamgo1.csgoskincommon.entity.listener.RollHomeUserListener;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Data
@ApiModel("Roll房用户")
@EntityListeners(value = {RollHomeUserListener.class})
public class RollHomeUserEntity extends BaseModel implements Serializable {
    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @ApiModelProperty("Roll房")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "roll_name_id", referencedColumnName = "id")
    private RollHomeEntity rollHome;

    @ApiModelProperty("最小Roll点")
    @Column(name = "min_roll")
    private Integer minRoll;

    @ApiModelProperty("最大Roll点")
    @Column(name = "max_roll")
    private Integer maxRoll;

}
