package com.steamgo1.csgoskincommon.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@Entity
@Table(name = "ocpc_google_token")
@ApiModel("Google OCPC Token信息")
public class OcpcGoogleTokenEntity extends BaseModel implements Serializable {
    @ApiModelProperty("conversion_id")
    @Column(name = "conversion_id", length = 64)
    private String conversionId;

    @ApiModelProperty("conversion_label")
    @Column(name = "conversion_label", length = 64)
    private String conversionLabel;

    @ApiModelProperty("realm_name")
    @Column(name = "relm_name", length = 256)
    private String relamName;

    @ApiModelProperty("measurement_id")
    @Column(name = "measurement_id", length = 64)
    private String measurementId;

    @ApiModelProperty("api_secret")
    @Column(name = "api_secret", length = 128)
    private String apiSecret;
}
