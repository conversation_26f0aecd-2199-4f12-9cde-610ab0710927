package com.steamgo1.csgoskincommon.entity;


import com.steamgo1.csgoskincommon.entity.listener.CardCollectListener;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@ApiModel("集卡")
@Table(name = "card_collect")
@EntityListeners(value = {CardCollectListener.class})
public class CardCollectEntity extends BaseModel implements Serializable {
    @ApiModelProperty("活动名")
    @Column(name = "name", length = 32)
    private String name;

    @ApiModelProperty("详情")
    @Column(name = "details", length = 512)
    private String details;

    @ApiModelProperty("按钮图")
    @Column(name = "button_picture", length = 512)
    private String buttonPicture;

    @ApiModelProperty("背景图")
    @Column(name = "background_picture", length = 512)
    private String backgroundPicture;

    @ApiModelProperty("金币数量")
    @Column(name = "coin")
    private BigDecimal coin;

    @Column(name = "rabbit_message_d", length = 128)
    @ApiModelProperty("绑定的Rabbit messageId")
    private String rabbitMessageId;

    @ApiModelProperty("开奖时间")
    @Column(name = "expire_time")
    private Date expireTime;

    @ApiModelProperty("僵尸数量")
    @Column(name = "cheat_num")
    private Integer cheatNum = 0;

    @ApiModelProperty("是否激活")
    @Column(name = "is_activate")
    private Boolean isActivate = false;

    @ApiModelProperty("是否删除")
    @Column(name = "is_deleted")
    private Boolean isDeleted = false;
}
