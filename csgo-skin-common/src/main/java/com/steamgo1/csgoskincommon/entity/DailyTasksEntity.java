package com.steamgo1.csgoskincommon.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Entity
@ApiModel("日常任务箱子")
@Table(name = "daily_tasks")
public class DailyTasksEntity extends BaseModel implements Serializable {

    @ApiModelProperty("任务名")
    @Column(name = "name")
    private String name;

    @ApiModelProperty("充值阈值")
    @Column(name = "charge_threshold")
    private BigDecimal chargeThreshold;

    @ApiModelProperty("消费阈值")
    @Column(name = "consum_threshold")
    private BigDecimal consumThreshold;


    @ApiModelProperty("箱子")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "case_id", referencedColumnName = "id")
    private CaseEntity box;
}
