package com.steamgo1.csgoskincommon.entity;

import com.steamgo1.csgoskincommon.entity.listener.UserListener;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023-02-09 16:02:14
 */
@Data
@Entity
@Table(name = "user_log")
@ApiModel("用户信息")
@EntityListeners(value = {UserListener.class})
public class UserLogEntity extends BaseModel implements Serializable {
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @ApiModelProperty("用户Id")
    private UserEntity user;

    @Column(name = "login_time")
    @ApiModelProperty("登录时间")
    private Date loginTime;

}
