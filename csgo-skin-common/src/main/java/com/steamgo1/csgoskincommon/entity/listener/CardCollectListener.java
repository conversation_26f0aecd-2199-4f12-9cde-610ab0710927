package com.steamgo1.csgoskincommon.entity.listener;

import com.steamgo1.csgoskincommon.entity.CardCollectEntity;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import javax.persistence.PostUpdate;


@Slf4j
public class CardCollectListener {
    @Value("${spring.redis.prefix.activity}")
    private String redisActivityPrefix;

    @PostUpdate
    public void postUpdate(CardCollectEntity cardCollectEntity) {
        log.info("集卡活动更新或添加删除缓存 {} {}", cardCollectEntity.getId(), cardCollectEntity.getName());
        String userInfoRedisKey = redisActivityPrefix + ":CARD_COLLECT";
        if (RedisUtils.hasKey(userInfoRedisKey)) {
            RedisUtils.delete(userInfoRedisKey);
        }
    }
}
