package com.steamgo1.csgoskincommon.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@Entity
@Table(name = "storage")
@ApiModel("存储服务信息")
public class StorageEntity extends BaseModel implements Serializable {
    @Column(name = "key_name", length = 128)
    @ApiModelProperty("存储名")
    private String keyName;

    @Column(name = "file_name", length = 128)
    @ApiModelProperty("文件名")
    private String fileName;

    @Column(name = "size")
    @ApiModelProperty("文件大小")
    private Integer size;

    @Column(name = "type", length = 10)
    @ApiModelProperty("类型")
    private String type;

    @Column(name = "url", length = 256)
    @ApiModelProperty("访问url")
    private String url;

    @Column(name = "is_delete")
    @ApiModelProperty("是否删除")
    private Boolean isDeleted;
}
