package com.steamgo1.csgoskincommon.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

@Entity
@Data
@ApiModel("用户百分比记录")
@Table(name = "record_percentage")
public class PercentageUserRecordEntity extends BaseModel implements Serializable {
    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @ApiModelProperty("目标饰品")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "skin_id", referencedColumnName = "id")
    private SkinEntity skin;

    @ApiModelProperty("最小Roll点")
    @Column(name = "min_roll")
    private Integer minRoll;

    @ApiModelProperty("最大Roll点")
    @Column(name = "max_roll")
    private Integer maxRoll;

    @ApiModelProperty("算法")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "algorithm_data_id", referencedColumnName = "id")
    private AlgorithmDataEntity algorithmData;

    @ApiModelProperty("roll")
    @Column(name = "roll")
    private Integer roll;

    @ApiModelProperty("rounds")
    @Column(name = "rounds")
    private Integer rounds;

    @ApiModelProperty("是否中奖")
    @Column(name = "is_win")
    private Boolean isWin = false;

    @ApiModelProperty("消耗金币")
    @Column(name = "consume_coin")
    private BigDecimal consumeCoin;
}
