package com.steamgo1.csgoskincommon.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@ApiModel("消费返奖计划记录")
@Table(name = "consume_plan_user_record")
public class ConsumePlanUserRecordEntity extends BaseModel implements Serializable {
    @ApiModelProperty("计划")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "consume_plan_id", referencedColumnName = "id")
    private ConsumePlanEntity consumePlan;

    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;
}
