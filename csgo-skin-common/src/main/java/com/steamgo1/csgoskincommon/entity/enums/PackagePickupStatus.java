package com.steamgo1.csgoskincommon.entity.enums;

import com.steamgo1.csgoskincommon.utils.I18nUtils;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public enum PackagePickupStatus {
    PICKUPING(1, "package.pickup.status.pickuping"),
    TODO(2, "package.pickup.status.todo"),
    DOING(3, "package.pickup.status.doing"),
    WAIT_USER(4, "package.pickup.status.wait.user"),
    SUCCESS(5, "package.pickup.status.success"),
    FAIL(6, "package.pickup.status.fail"),
    CANCEL(7, "package.pickup.status.cancel"),
    CHECKING(8, "package.pickup.status.checking"),
    FROZEN(9, "package.pickup.status.frozen"),
    FAIL_STEAM(10, "package.pickup.status.fail.steam"),
    WAIT_SEND(11, "package.pickup.status.wait.send");
//    WAIT_RETRY(12, "失败重试");

    @Getter
    private Integer code;
    @Getter
    private String i18nKey;

    PackagePickupStatus(Integer code, String i18nKey) {
        this.code = code;
        this.i18nKey = i18nKey;
    }

    /**
     * 获取当前语言的显示名称
     */
    public String getDisplayName() {
        return I18nUtils.getMessage(this.i18nKey);
    }

    public String getValue() {
        return getDisplayName();
    }

    public static PackagePickupStatus instance(Integer value) {
        PackagePickupStatus[] enums = values();
        for (PackagePickupStatus caseType : enums) {
            if (caseType.value().equals(value)) {
                return caseType;
            }
        }
        return null;
    }

    public static List<PackagePickupStatus> unFinishStatus() {
        List<PackagePickupStatus> rollHomeStatuses = new ArrayList<>();
        rollHomeStatuses.add(PackagePickupStatus.PICKUPING);
        rollHomeStatuses.add(PackagePickupStatus.TODO);
        rollHomeStatuses.add(PackagePickupStatus.DOING);
        rollHomeStatuses.add(PackagePickupStatus.WAIT_USER);
        rollHomeStatuses.add(PackagePickupStatus.FROZEN);
        rollHomeStatuses.add(PackagePickupStatus.WAIT_SEND);
        return rollHomeStatuses;
    }

    public static List<PackagePickupStatus> failhStatus() {
        List<PackagePickupStatus> rollHomeStatuses = new ArrayList<>();
        rollHomeStatuses.add(PackagePickupStatus.FAIL);
        rollHomeStatuses.add(PackagePickupStatus.CANCEL);
        rollHomeStatuses.add(PackagePickupStatus.FAIL_STEAM);
        return rollHomeStatuses;
    }

    public Integer value() {
        return code;
    }
}
