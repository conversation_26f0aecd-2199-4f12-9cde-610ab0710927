package com.steamgo1.csgoskincommon.entity;


import com.steamgo1.csgoskincommon.entity.enums.BattleHomeCaseStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Data
@ApiModel("对战房箱子")
@Table(name = "battle_home_case")
public class BattleHomeCaseEntity extends BaseModel implements Serializable {
    @ApiModelProperty("箱子")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "case_id", referencedColumnName = "id")
    private CaseEntity box;

    @ApiModelProperty("对战房")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "battle_name_id", referencedColumnName = "id")
    private BattleHomeEntity battleHome;

    @Column(name = "status")
    private BattleHomeCaseStatus status;
}
