package com.steamgo1.csgoskincommon.entity;


import com.steamgo1.csgoskincommon.converter.I18nFieldConverter;
import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Entity
@ApiModel("每日充值红包")
@Table(name = "daily_red_packet")
public class DailyRedPacketEntity extends BaseModel implements Serializable {
    @ApiModelProperty("红包名称")
    @Column(name = "name", length = 32)
    private String name;

    @ApiModelProperty("金币数量")
    @Column(name = "coin")
    private BigDecimal coin;

    @Column(name = "i18n_field_name", columnDefinition = "JSON")
    @Convert(converter = I18nFieldConverter.class)
    @ApiModelProperty("红包名称（多语言）")
    private I18nField i18nFieldName;

    //    @ApiModelProperty("总份数")
//    @Column(name = "total")
//    private Integer total;
//
//    @ApiModelProperty("已兑换数量")
//    @Column(name = "total_current")
//    private Integer totalCurrent;
//
    @ApiModelProperty("领取阈值")
    @Column(name = "threshold")
    private BigDecimal threshold;

//    @ApiModelProperty("阈值类型")
//    @Column(name = "threshold_type")
//    private ThresholdType thresholdType;


    @ApiModelProperty("是否删除")
    @Column(name = "is_deleted")
    private Boolean isDeleted;

    /**
     * 重写get方法，根据Accept-Language头部参数返回对应语言的文本
     * 使用LanguageEnum枚举进行语言判断
     */
    public String getName() {
        return I18nUtils.getI18nFieldValue(i18nFieldName, I18nUtils.getCurrentLanguageEnum());
    }

}
