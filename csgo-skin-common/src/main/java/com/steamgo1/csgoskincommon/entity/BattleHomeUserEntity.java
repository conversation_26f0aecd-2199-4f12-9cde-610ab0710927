package com.steamgo1.csgoskincommon.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Data
@ApiModel("对战房用户")
@Table(name = "battle_home_user")
public class BattleHomeUserEntity extends BaseModel implements Serializable {
    @ApiModelProperty("用户")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private UserEntity user;

    @ApiModelProperty("对战房")
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "battle_name_id", referencedColumnName = "id")
    private BattleHomeEntity battleHome;

    @ApiModelProperty("是否房主")
    private Boolean isOwner = false;

    @ApiModelProperty("是否胜利")
    private Boolean isWin = false;
}
