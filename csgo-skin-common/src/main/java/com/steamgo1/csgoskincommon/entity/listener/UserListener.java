package com.steamgo1.csgoskincommon.entity.listener;

import com.steamgo1.csgoskincommon.entity.UserEntity;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import javax.persistence.PostUpdate;


@Slf4j
public class UserListener {
    @Value("${spring.redis.prefix.user}")
    private String redisUserPrefix;

    @PostUpdate
    public void postUpdate(UserEntity userEntity) {
        log.info("用户信息更新删除缓存 {} {}", userEntity.getId(), userEntity.getNickname());
        String userInfoRedisKey = redisUserPrefix + ":" + userEntity.getId();
        if (RedisUtils.hasKey(userInfoRedisKey)) {
            RedisUtils.delete(userInfoRedisKey);
        }
    }
}
