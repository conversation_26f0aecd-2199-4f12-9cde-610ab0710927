package com.steamgo1.csgoskincommon.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@Entity
@Table(name = "ocpc_meta_token")
@ApiModel("Meta OCPC Token信息")
public class OcpcMetaTokenEntity extends BaseModel implements Serializable {
    @ApiModelProperty("access_token")
    @Column(name = "access_token", length = 512)
    private String accessToken;

    @ApiModelProperty("pixel_id")
    @Column(name = "pixel_id", length = 64)
    private String pixelId;

    @ApiModelProperty("realm_name")
    @Column(name = "relm_name", length = 256)
    private String relamName;

    @ApiModelProperty("app_id")
    @Column(name = "app_id", length = 64)
    private String appId;
}