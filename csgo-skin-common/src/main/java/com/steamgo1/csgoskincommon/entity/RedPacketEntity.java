package com.steamgo1.csgoskincommon.entity;


import com.steamgo1.csgoskincommon.entity.enums.RedPacketMethod;
import com.steamgo1.csgoskincommon.entity.enums.RedPacketType;
import com.steamgo1.csgoskincommon.entity.enums.ThresholdType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@ApiModel("红包")
@Table(name = "red_packet")
public class RedPacketEntity extends BaseModel implements Serializable {
    @ApiModelProperty("红包名称")
    @Column(name = "name", length = 32)
    private String name;

    @ApiModelProperty("兑换码")
    @Column(name = "code", length = 32)
    private String code;

    @ApiModelProperty("金币数量")
    @Column(name = "coin")
    private BigDecimal coin;

    @ApiModelProperty("到期时间")
    @Column(name = "expire_time")
    private Date expireTime;

    @ApiModelProperty("总份数")
    @Column(name = "total")
    private Integer total;

    @ApiModelProperty("已兑换数量")
    @Column(name = "total_current")
    private Integer totalCurrent;

    @ApiModelProperty("消费阈值")
    @Column(name = "consume_threshold")
    private BigDecimal consumeThreshold;

    @ApiModelProperty("阈值类型")
    @Column(name = "threshold_type")
    private ThresholdType thresholdType;

    @ApiModelProperty("红包类型")
    @Column(name = "type")
    private RedPacketType type;

    @ApiModelProperty("红包模式")
    @Column(name = "method")
    private RedPacketMethod method;


    @ApiModelProperty("是否删除")
    @Column(name = "is_deleted")
    private Boolean isDeleted;

}
