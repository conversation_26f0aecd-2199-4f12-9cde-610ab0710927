package com.steamgo1.csgoskincommon.entity.enums;

import com.google.common.collect.Lists;
import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;
import com.steamgo1.csgoskincommon.utils.I18nUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum RollHomeLotteryMethod implements CodeValueBaseEnum {
    FIXTIME(1, "roll.home.lottery.method.fixtime"),
    FIXPEOPLE(2, "roll.home.lottery.method.fixpeople");

    private Integer code;
    private String i18nKey;

    RollHomeLotteryMethod(Integer code, String i18nKey) {
        this.code = code;
        this.i18nKey = i18nKey;
    }

    /**
     * 获取当前语言的显示名称
     */
    public String getDisplayName() {
        return I18nUtils.getMessage(this.i18nKey);
    }

    public static List toList() {
        List list = Lists.newArrayList();//Lists.newArrayList()其实和new ArrayList()几乎一模
        //  一样, 唯一它帮你做的(其实是javac帮你做的), 就是自动推导(不是"倒")尖括号里的数据类型.

        for (RollHomeLotteryMethod rollHomeLotteryMethod : RollHomeLotteryMethod.values()) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("code", rollHomeLotteryMethod.getCode());
            map.put("name", rollHomeLotteryMethod.getDisplayName());
            list.add(map);
        }
        return list;
    }

    public static RollHomeLotteryMethod instance(Integer code) {
        RollHomeLotteryMethod[] enums = values();
        for (RollHomeLotteryMethod rollHomeLotteryMethod : enums) {
            if (rollHomeLotteryMethod.getCode().equals(code)) {
                return rollHomeLotteryMethod;
            }
        }
        return null;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getValue() {
        return getDisplayName();
    }

    @Override
    public String description() {
        return code + "-" + getDisplayName();
    }
}
