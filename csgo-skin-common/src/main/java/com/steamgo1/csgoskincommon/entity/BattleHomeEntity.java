package com.steamgo1.csgoskincommon.entity;

import com.steamgo1.csgoskincommon.entity.enums.BattleHomeMethod;
import com.steamgo1.csgoskincommon.entity.enums.BattleHomeStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;


@Entity
@Data
@ApiModel("对战房")
@Table(name = "battle_home")
public class BattleHomeEntity extends BaseModel implements Serializable {
    @ApiModelProperty("对战房模式")
    @Column(name = "method")
    private BattleHomeMethod battleHomeMethod;


    @ApiModelProperty("对战房人数")
    @Column(name = "total_player")
    private Integer totalPlayer;


    @ApiModelProperty("对战房总消耗")
    @Column(name = "amount")
    private BigDecimal amount;

    @ApiModelProperty("对战房局数")
    private Integer rounds;

    @ApiModelProperty("对战房总局数")
    private Integer totalRounds;

    @ApiModelProperty("对战房状态")
    @Column(name = "status")
    private BattleHomeStatus battleHomeStatus;

    @ApiModelProperty("真实玩家隐藏")
    private Boolean isHide = false;
}
