package com.steamgo1.csgoskincommon.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Entity
@ApiModel("用户福利")
@Table(name = "sys_user_benafits")
public class SysUserBenefitsEntity extends BaseModel implements Serializable {
    @ApiModelProperty("注册赠送金币")
    @Column(name = "free_coin")
    private BigDecimal freeCoin;

    @ApiModelProperty("首充赠送比例")
    @Column(name = "first_charge_percentage")
    private BigDecimal firstChargePercentage;


    @ApiModelProperty("邀请首充赠送金币")
    @Column(name = "invite_first_charge_free_coin")
    private BigDecimal inviteFirstChargeFreeCoin;

    @ApiModelProperty("邀请充值赠送比例")
    @Column(name = "invite_encourage_percentage")
    private BigDecimal inviteEncoragePercentage;
}
