package com.steamgo1.csgoskincommon.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import org.springframework.security.core.parameters.P;

/**
 * 多语言枚举类
 * 定义系统支持的语言类型
 *
 * <AUTHOR>
 */
public enum LanguageEnum {
    
    /**
     * 中文
     */
    CHINESE("zh", "中文", "Chinese", 1),
    
    /**
     * 英文
     */
    ENGLISH("en", "英文", "English", 2),
    
    /**
     * 日文（预留）
     */
    JAPANESE("ja", "日文", "Japanese", 3),
    
    /**
     * 韩文（预留）
     */
    KOREAN("ko", "韩文", "Korean", 4),
    
    /**
     * 俄文（预留）
     */
    RUSSIAN("ru", "俄文", "Russian", 5),
    
    /**
     * 葡萄牙语
     */
    PORTUGUESE("pt", "葡萄牙语", "Portuguese", 6);
    
    /**
     * 语言代码
     */
    private final String code;

    /**
     * 中文名称
     */
    private final String chineseName;

    /**
     * 英文名称
     */
    private final String englishName;
    
    /**
     * 优先级（数字越小优先级越高）
     */
    private final int priority;
    
    LanguageEnum(String code, String chineseName, String englishName, int priority) {
        this.code = code;
        this.chineseName = chineseName;
        this.englishName = englishName;
        this.priority = priority;
    }
    
    @JsonValue
    public String getCode() {
        return code;
    }

    public String getChineseName() {
        return chineseName;
    }

    public String getEnglishName() {
        return englishName;
    }

    public int getPriority() {
        return priority;
    }
    
    /**
     * 根据语言代码获取枚举
     * 添加@JsonCreator注解支持JSON反序列化
     */
    @JsonCreator
    public static LanguageEnum fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return getDefault();
        }
        for (LanguageEnum language : values()) {
            if (language.getCode().equals(code)) {
                return language;
            }
        }
        return getDefault();
    }
    
    /**
     * 获取默认语言（中文）
     */
    public static LanguageEnum getDefault() {
        return ENGLISH;
    }
    
    /**
     * 获取当前支持的主要语言（中文和英文）
     */
    public static LanguageEnum[] getSupportedLanguages() {
        return new LanguageEnum[]{CHINESE, ENGLISH, PORTUGUESE};
    }
    
    /**
     * 检查是否为支持的语言
     */
    public static boolean isSupported(String code) {
        return fromCode(code) != getDefault() || getDefault().getCode().equals(code);
    }
    
    /**
     * 获取语言的显示名称（根据当前语言环境）
     */
    public String getDisplayName(LanguageEnum currentLanguage) {
        if (currentLanguage == CHINESE) {
            return this.chineseName;
        } else {
            return this.englishName;
        }
    }

    /**
     * 是否为中文
     */
    public boolean isChinese() {
        return this == CHINESE;
    }

    /**
     * 是否为英文
     */
    public boolean isEnglish() {
        return this == ENGLISH;
    }
    
    @Override
    public String toString() {
        return code;
    }
}
