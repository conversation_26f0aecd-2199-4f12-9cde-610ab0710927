package com.steamgo1.csgoskincommon.enums;

import com.steamgo1.csgoskincommon.utils.I18nUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ResponseCode {
//    SUCCESS(200, "请求成功"),
//    FAIL(500, "请求失败"),
//    HTTP_STATUS_200(200, "请求成功"),
//    HTTP_STATUS_400(400, "请求失败"),
//    HTTP_STATUS_401(401, "未认证"),
//    HTTP_STATUS_403(403, "无权限"),
//    HTTP_STATUS_500(500, "服务错误");

    SUCCESS(200, "response.success"),
    FAIL(500, "response.fail"),
    HTTP_STATUS_200(200, "response.http_200"),
    HTTP_STATUS_400(400, "response.http_400"),
    HTTP_STATUS_401(401, "response.http_401"),
    HTTP_STATUS_403(403, "response.http_403"),
    HTTP_STATUS_500(500, "response.http_500");
    private final int code;
    private final String message;

    public String getMessage() {
        // 国际化处理
        return I18nUtils.getMessage(message);
    }
}
