package com.steamgo1.csgoskincommon.enums;

import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;

public enum RabbitActivityType implements CodeValueBaseEnum {
    CARD_COLLECT(0, "集卡"),
    OTHER(1, "其它");
    private Integer code;
    private String value;

    RabbitActivityType(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String description() {
        return code + "-" + value;
    }

}
