package com.steamgo1.csgoskincommon.enums;

import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;

public enum WebSocketMessageType implements CodeValueBaseEnum {
    BATTLE_CREATE(1, "Battle房创建"),
    BATTLE_UPDATE(2, "Battle房变动"),
    BATTLE_RESULT(3, "Battle房当局结果"),
    ROLL_HOME_RESULT(4, "ROLLHOME房开奖"),
    ROLL_HOME_JOIN(5, "有人参加Roll房"),
    LOTTERY_RESULT(6, "有人抽中奖品"),
    PAY_NOTIFY(7, "支付回调"),
    PING(8, "心跳"),
    ANNOUNCEMENT(9, "公告"),
    CONSUME_PLAN(10, "消费计划提示"),
    BIND_WX(11, "关注公众号响应"),
    STATISTICS(12, "统计数据");

    private Integer code;
    private String value;

    WebSocketMessageType(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String description() {
        return code + "-" + value;
    }

}
