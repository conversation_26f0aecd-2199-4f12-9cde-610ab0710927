package com.steamgo1.csgoskincommon.enums;

import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;

/**
 * 用户中奖来源
 */
public enum LotterySource implements CodeValueBaseEnum {

    /**
     * 开箱子
     */
    OPEN_CASE(1, "开箱子"),

    /**
     * 追梦
     */
    PERCENTAGE(2, "追梦"),

    /**
     * Roll房
     */
    ROLL_HOME(3, "Roll房"),

    /**
     * 对战
     */
    BATTLE(4, "对战"),

    /**
     * 购买
     */
    BUY(5, "购买"),

    /**
     * 其它
     */
    OTHER(6, "其它");


    private Integer code;

    private String value;

    LotterySource(Integer num, String value) {
        this.code = num;
        this.value = value;
    }

    public static LotterySource instance(Integer value) {
        LotterySource[] enums = values();
        for (LotterySource statusEnum : enums) {
            if (statusEnum.code.equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String description() {
        return code + "-" + value;
    }
}
