package com.steamgo1.csgoskincommon.enums;

import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;

public enum OcpcRabbitmqType implements CodeValueBaseEnum {
    CLICK(0, "点击"),
    REGISTER(1, "注册"),
    CHARGE(2, "充值下单");
    private Integer code;
    private String value;

    OcpcRabbitmqType(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String description() {
        return code + "-" + value;
    }

}
