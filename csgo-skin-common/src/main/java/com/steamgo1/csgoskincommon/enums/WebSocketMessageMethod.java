package com.steamgo1.csgoskincommon.enums;

import com.steamgo1.csgoskincommon.enums.baseEnum.CodeValueBaseEnum;

public enum WebSocketMessageMethod implements CodeValueBaseEnum {
    SEND_ALL(1, "所有人消息"),
    SEND_ONE(2, "指定消息");

    private Integer code;
    private String value;

    WebSocketMessageMethod(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String description() {
        return code + "-" + value;
    }
}
