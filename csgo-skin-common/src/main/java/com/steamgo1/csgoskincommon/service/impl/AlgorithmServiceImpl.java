package com.steamgo1.csgoskincommon.service.impl;

import com.steamgo1.csgoskincommon.service.AlgorithmService;
import com.steamgo1.csgoskincommon.utils.HashUtils;
import org.springframework.stereotype.Service;


@Service
public class AlgorithmServiceImpl implements AlgorithmService {
    private Integer SEED_MIN_ROLL = 1;
    private Integer SEED_MAX_ROLL = 1000000;

    @Override
    public String getPublicHash(String secretHash, String secretSalt) {
        return HashUtils.SHA256(secretHash, secretSalt);
    }

    @Override
    public Integer getRoll(String secretHash, String clientSeed, Integer round) {
        String hash = HashUtils.SHA512(secretHash, clientSeed + round);
        return Integer.parseInt(hash.substring(0, 7), 16) % (SEED_MAX_ROLL - SEED_MIN_ROLL + 1) + SEED_MIN_ROLL;
    }

}
