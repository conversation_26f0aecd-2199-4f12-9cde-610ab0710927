package com.steamgo1.csgoskincommon.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.steamgo1.csgoskincommon.dao.*;
import com.steamgo1.csgoskincommon.entity.*;
import com.steamgo1.csgoskincommon.entity.enums.PackagePickupStatus;
import com.steamgo1.csgoskincommon.entity.enums.UserDisableType;
import com.steamgo1.csgoskincommon.entity.enums.ZbtSyncStatus;
import com.steamgo1.csgoskincommon.service.IO661Service;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import com.steamgo1.csgoskincommon.vo.IGXESteamIdVO;
import com.steamgo1.csgoskincommon.vo.IO661PriceInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.KeyFactory;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class IO661ServiceImpl implements IO661Service {

    // 应用标识
    private static final String appKey = "open_ls65s8daq9sy7tu9bn4k0ct8z8hst6go";
    // RSA私钥 - 移除头尾限定行
    private static final String privateKey = "MIIJQgIBADANBgkqhkiG9w0BAQEFAASCCSwwggkoAgEAAoICAQCECobsqxHNlKHUoLo3lm7SZcmeJh//RnlkCCh0WU3By8s/7kVmKmM0sULDvl/F/pmOGEAhIuHgMGc8RnxYxnQGGBnfgtVVDiKJKiMXPu+TNH+AcsUiiIJsQNDmrJFcumUctbveLbzSiWqo3iAMsBW/CfjlhtyPlOmbEDNfrEwOUrmX9Gw0hxCmkeVchpKgU7xK543Y1RgSScvhSG/vyPM28U18dUdzDeiJoPqflKhgcpMvaYUfZUPc8fG9CtH7KsiP5kSiJ+0jorX5DAWmSPluGup1PECc74dWtoqWdJTlo9mBeF5haSLJSMwGqIx+oVKzWP0sCEhqv64Nkzmln1QL0ZrXsRIIRFgDy72Hid9hc24qsI9W4c/D5SkCcfRDOm/SSxhe6dSVtQ5lebfS/aYIxYzF9VGztl3fj38yFkg7s+mJB0zs3gKtmiZJipVHoijdLKbHgPkgB0HUz03NdB+hixocmyASNiPsOpmUa62as7UhvRiPZRRY1wmMegfuqO89NONe+PchrTreLR52AXJ9IixR4NwrkTtfNcynytJnWHLeFaDs1IHoiVJvuWtsFdhz/PAS8MHbrx2zgRQA8msgnXAXiNyi/MXe2z/k6+mPKVkRacIcWG69nauhjmL0Wva84rH621gt0kXZ3pl1JW+zzMnRmRJT8AQ9q1LwtFwVxwIDAQABAoICAHcSdzId4uQdUQLKHqPaSP37RKIVh21RRE7s/G5PykslWJjCalsDhosR/z8+AmgufJMjU1GOf1sIy1AT0DbOX/rO6SYiefdF0+cV1Kc13h5FjWVsGlJWACdXHAupOzz/6gbF7DEj1c+WlFTMWuKWOEYa3BVAVM42X6OyLGHuoneYKj3Z8Wo9Esrjbo7N3JQwbvCtd8E+CYMUgYMbK7Z05L97Q4Ek0dXziOvz+47FJEI1kH2FDsONMnGgzXrIsytpHYyG/wWsM7GbHbcFHGMkE6szIpOuTiY36zhLTRb48v1TkOWsgpAdm30g5LxsaqYXHNkXOtfmcEezq2YaijXNRKBBVyDiOFVxnU6pv6IOgW4+xjNTsAI/sgFO7C98obMJrVPs2S7K08/43+sVklYRwOdvr8pcdsLWeK+uVjVmCfyftdbo4DaYk+rmRK/CUpb+7lSJdgrENss7CxLZ5fXFp8IYv9Ccn1csiTKrB9353y23kxMTToRXZgA86hdo72AUT9Ipugq31XtvEj51lDKrdwD28hJTjDtl27pSzAll3P0lfVtTiNAqclw455MyXjiAEDkkchg0gWU6sVUxh80rq8BGm+m0H5UMGGKbAOi1XUVAJ1/fIzdffFRsXhDQO1jM+MKEtlnZa9AqPeJ40idHYIxxZuuupaJjAkqOchu+IdhBAoIBAQDhaWcu+JgfhvjcIJUOKxUMumqFXMK2C/EskPlroc9E85ZkgvZj/theUzNOq25hBUYKJuCvJWASwswzn0rsDBkQ0zvW6jnUPA9cGL0b+ucvPbP97vDF80KV1V50E1VEEXIbJAdGozIZS7r7VR4c7UWqvXUB+t6d7awMMnjy1xTKM6miS7cps00ZwAk4N1FmxZzaLVYo44USpikyjONUudZeZPEgDS77nKlQpn8h0s+/MT1rMP1pq3Dg60jaSjcMskvYPKk87v49O32MhPNLie7l/j4yOkNMQy7kBNwBdLancZxwZQQ9bci58J01ugjI7RcsTdeh/w4n5GlL+azx4ktPAoIBAQCV9YO8Huoql3uQEH+4WXgmXd8AeR/KKc2NwZLsXJuWZUCEec8CZtH0VYqLZTF2oqB1H/TrPQzEodu6PoZGFNO96lC6rDNxIp/ck1F06sQsjLdQLo82zBDlHtkzQKOgiGhwhlahyCSWebJdHDDa738yKqfD5MY4TPxPA7DXFsC7Ben9pwFCMPKVU5SftsiRI3Ce0tjVT7iOmKzuaY7p+6cOsmLGrGp5mxPkDLDfvOFOPyUDvb9DyMT0h49Rx6DjPYSozUWl+AjPffrbs4eCO+wGqDkEVajDlyfoBh1aAdU4fqGpYMYafIUxOOwYw9bGRbWwoql1iUMTdRHKvGFnhZAJAoIBAA19U1F6SxBH8Yiq4oCwj1ftqAZHINfoKwyxuFt6mX7U3QEy3S/qxmQOsplYpQhlWmWtIfEZWYCF69aDB+bMaouZOJGJ/t/2oCywxlBwWXl4RddCf2I5RpjU5gwoRAgIyz1YkJ7nkU32PEBiPaUZhfvLzr7Jlpz45jzsoFRYDsRzBwnRYiq6sjiCR4r3tIVj+hkMcYWM6B/PldOSnuApJu0LOWrjnxYrr+5FqIILracReU165w911RGAwyFdI6I84VEVgENHQivD7aWFJ1Iz9PoKlwpGdlSQCLRYnhnwNCYdJHGNeg17DnIiNiZK9c+gcKyUE/172AsHHk4PqQvosP0CggEBAIG8ffM0H80nb0xNKeJY7VzsG7FyGTGzBtYcYbtBx43PHYk/RPxD7mLf2tQMgUWtHvsV4bPfBnvbuJih3VMmU6Zb4OoPbxXzKbEQdwmazovoVfP1xAtZc0h2bt0rkCSjvw+S+cJPwG+667goWpAfYcEKLEiLVX6CBEtr2wPK+IUVccH0SV91r3nHbK7c2RrQOW/+5H9v0DD2H67H/k+qW3aYmGJf6mI463WCEUgQRjCG6sQY1i+Ts+oGmdTby/KtV2ce6KHVO/IfgPewk4z1wJZl1aZNYYuN64WOPLiRG2IcFErQobNqhWT3ZTNrwxWYW79siwVX8oMT5oeqgO6qG9kCggEARRYi6wZSU7tNPvcfCVOW6BPT+9nyW+x2h3/az4cb10D2R8EsYj2WymBSdfUt6wY31TRrdohTJFwin5m6jfC4QuoHeQn78l1VrHS6uCjN00grhRQAzjq8eytygrofCdz63sCEedoJNJs0pvDuA8R1UnAbkvG53jY04b/sKubwu1XmcoYrcBGhm3F6UrnnkNS0IXU6TnkKgllzkfW5xZFVNeOu/75pMEc2kSjIEVPsxIRqbZh0YkccY8uMZ0DuNhonFDqHwHMK2oDNBQgFl86s4yuNUZE59lIDz2BA1gSkDH0r/KYZifhP4My/dE5Ypu0PW76UV6U+0KO4IOx4iP10QQ==";
    @Value("${spring.redis.prefix.user}")
    private String redisUserPrefix;
    @Value("${spring.redis.expire.disable-pickup}")
    private Long redisUserDisablePickupExpire;
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private SkinRepository skinRepository;

    @Autowired
    private SysExchangeRateRepository sysExchangeRateRepository;

    @Autowired
    private ZbtSyncRecordRepository zbtSyncRecordRepository;

    @Autowired
    private SysSkinRiskManagementRepository sysSkinRiskManagementRepository;

    @Autowired
    private UserPackagePickupRepository userPackagePickupRepository;

    @Autowired
    private UserDisableRepository userDisableRepository;

    @Autowired
    private UserPackageRepository userPackageRepository;

    /**
     * 格式化待签名字符串
     */
    private static String formatSignString(Map<String, Object> map) throws JsonProcessingException {
        Map<String, Object> sortedMap = new TreeMap<>(map);
        StringBuilder stringBuilder = new StringBuilder();
        String[] keys = sortedMap.keySet().toArray(new String[0]);
        Arrays.sort(keys);
        for (String key : keys) {
            Object value = sortedMap.get(key);
            if (Objects.nonNull(value) && !Objects.equals(key, "sign")) {
                stringBuilder.append(key).append((new ObjectMapper().writeValueAsString(value)));
            }
        }
        return stringBuilder.toString();
    }

    /**
     * 签名
     */
    private static String sign(String data) {
        try {
            Signature signature = Signature.getInstance("SHA256withRSA");
            signature.initSign(KeyFactory.getInstance("RSA")
                    .generatePrivate(new PKCS8EncodedKeySpec(Base64.getMimeDecoder().decode(privateKey))));
            signature.update(data.getBytes());
            return new String(Base64.getMimeEncoder().encode(signature.sign()));
        } catch (Exception e) {
            log.error(e.getMessage());
            return "";
        }
    }

    /**
     * 发送请求
     */
    public JSONObject request(String url, Map<String, Object> data) throws IOException {
        data.put("appKey", appKey);
        data.put("timestamp", LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        data.put("sign", sign(formatSignString(data)));
        log.info("IO661请求参数 {}", data);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        // 封装 HTTP 请求
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(data, headers);

        // 发起 POST 请求
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
        log.info("IO661请求响应: {}", responseEntity.toString());
        // 检查响应状态码并处理响应体
        if (responseEntity.getStatusCode().is2xxSuccessful()) {
            // 响应体转换为 JSON 对象
            JSONObject responseJson = JSONObject.parseObject(responseEntity.getBody());
            if (responseJson.getInteger("code") == 0) {
                JSONObject dataJson = responseJson.getJSONObject("data");
                log.info("IO661响应: {}", dataJson.toJSONString());
                return dataJson;
            }
            log.error("请求IO661失败: {}", responseJson.toJSONString());
            return null;
        } else {
            return null;
        }
    }

    @Override
    public void SyncSkinInfo(Long zbtSyncRecordId) {
        SysExchangeRateEntity sysExchangeRateEntity = sysExchangeRateRepository.findFirstByOrderById();
        ZbtSyncRecordEntity zbtSyncRecordEntity = zbtSyncRecordRepository.findById(zbtSyncRecordId).get();
        if (sysExchangeRateEntity == null) {
            log.error("同步扎比特数据失败：后台未配置汇率");
            zbtSyncRecordEntity.setZbtSyncStatus(ZbtSyncStatus.FAIL);
            zbtSyncRecordEntity.setRemarks("后台未配置汇率");
            zbtSyncRecordRepository.save(zbtSyncRecordEntity);
            return;
        }
        SysSkinRiskManagementEntity sysSkinRiskManagementEntity = sysSkinRiskManagementRepository.findFirstByOrderById();
        if (sysSkinRiskManagementEntity == null) {
            log.error("同步扎比特数据失败：后台未配置饰品风控");
            zbtSyncRecordEntity.setZbtSyncStatus(ZbtSyncStatus.FAIL);
            zbtSyncRecordEntity.setRemarks("后台未配置饰品风控");
            zbtSyncRecordRepository.save(zbtSyncRecordEntity);
            return;
        }
        Integer maxCount = 2;
        Integer pageNo = 0;
        while (maxCount-- > 0) {
            Pageable pageable = PageRequest.of(pageNo++, 15);
            Page<SkinEntity> skinEntities = skinRepository.findAll(pageable);
            if (skinEntities.isEmpty()) {
                return;
            }
            // 请求参数
            Map<String, Object> data = new HashMap<>();
            List<String> templateHashNames = skinEntities.stream().map(SkinEntity::getEnglishName).collect(Collectors.toList());
            data.put("templateHashNames", templateHashNames);
            // 请求地址
            String url = "https://open.io661.com/api/v1/market/batchGetTemplateOnSellInfo";
            // 发送请求
            try {
                JSONObject responseBody = request(url, data);
                if (responseBody == null) {
                    log.error("同步失败");
                    return;
                }
                JSONArray jsonArray = responseBody.getJSONArray("templateOnSellInfo");
                for (int i = 0; i < jsonArray.size(); i++) {
                    IO661PriceInfoVO priceInfoVO = jsonArray.getObject(i, IO661PriceInfoVO.class);

                    SkinEntity skin = skinRepository.findByEnglishName(priceInfoVO.getTemplateHashName());
                    if (skin == null) {
                        log.info("饰品不存在：{},跳过同步", skin.getEnglishName());
                        continue;
                    }
                    BigDecimal price = new BigDecimal(priceInfoVO.getOnSellMinPrice()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                    skin.setPrice(price);
                    skin.setDiamond(price.multiply(sysExchangeRateEntity.getZbtToCnyPremium()).setScale(2, BigDecimal.ROUND_UP));
                    skinRepository.save(skin);
                }
            } catch (Exception e) {
                log.error("同步失败： {}", e.getMessage());
            }
        }

    }

    @Override
    public void quickBuy(Long userPackagePickUpId) {
//        templateHashName	string
//        tradeUrl	string
//        maxPrice	int
//        merchantOrderNo	string
//        quick	boolean
        UserPackagePickupEntity userPackagePickupEntity = userPackagePickupRepository.findById(userPackagePickUpId).orElseGet(null);
        if (userPackagePickupEntity == null) {
            log.error("饰品取回订单不存在, userPackagePickUpId： {}", userPackagePickUpId);
            return;
        }
        SkinEntity skin = userPackagePickupEntity.getUserPackage().getSkin();
        if (skin.getPrice().equals(userPackagePickupEntity.getUserPackage().getPrice().multiply(BigDecimal.valueOf(1.05)))) {
            userPackagePickupEntity.setRemarks("商品涨价超过百分之五");
            userPackagePickupEntity.setStatus(PackagePickupStatus.FROZEN);
            userPackagePickupRepository.save(userPackagePickupEntity);
            return;
        }
//        IGXESteamIdVO igxeSteamIdVO = queryUserSteamId(userPackagePickupEntity.getSteamTradeUrl());
//        if(igxeSteamIdVO==null){
//            log.error("交易链接查询用户steamId失败,userPackagePickUpId：{}", userPackagePickUpId);
//            return;
//        }
        String url = "https://open.io661.com/api/v1/goods/buyingByTemplate";
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("templateHashName", skin.getEnglishName());
        paramMap.put("tradeUrl", userPackagePickupEntity.getSteamTradeUrl());
        paramMap.put("maxPrice", skin.getPicture());
        paramMap.put("merchantOrderNo", userPackagePickupEntity.getOrderNo());

        // 发送请求
        try {
            JSONObject responseBody = request(url, paramMap);
            if (responseBody == null) {
                log.error("购买失败");
                userPackagePickupEntity.setRemarks("购买失败");
                userPackagePickupEntity.setStatus(PackagePickupStatus.FROZEN);
                userPackagePickupRepository.save(userPackagePickupEntity);
                return;
            }
            userPackagePickupEntity.setThirdOrderNoIo(responseBody.getString("orderNo"));
            userPackagePickupRepository.save(userPackagePickupEntity);
        } catch (Exception e) {
            log.error("购买失败: {]", e.getMessage());
            userPackagePickupEntity.setRemarks("购买失败");
            userPackagePickupEntity.setStatus(PackagePickupStatus.FROZEN);
            userPackagePickupRepository.save(userPackagePickupEntity);
        }
    }

    @Override
    public void queryOrderStatus(Long userPackagePickUpId) {
//        merchantOrderNo
        UserPackagePickupEntity userPackagePickupEntity = userPackagePickupRepository.findById(userPackagePickUpId).get();
        if (userPackagePickupEntity.getThirdOrderNoIo() == null) {
            log.error("订单未提交成功,没有三方订单号");
            return;
        }
        String url = "https://open.io661.com/api/v1/goods/getOrderInfo";
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("merchantOrderNo", userPackagePickupEntity.getThirdOrderNoIo());
        // 发送请求
        try {
            JSONObject responseBody = request(url, paramMap);
            if (responseBody == null) {
                log.error("购买失败");
                userPackagePickupEntity.setRemarks("购买失败");
                userPackagePickupEntity.setStatus(PackagePickupStatus.FROZEN);
                userPackagePickupRepository.save(userPackagePickupEntity);
                return;
            }
//            11	订单创建中
//            21	等待发起报价
//            31	交易搁置
//            41	等待确认报价
//            51	交易成功
//            55	交易失败
            Integer status = responseBody.getInteger("status");
            Integer cancle_type = responseBody.getInteger("subStatus");
            if (status.equals(55)) {
//                    订单已取消
//                1120	等待支付
//                2120	等待卖家发起报价
//                3120	交易暂挂
//                3190	买家取消核实中
//                3193	卖家取消核实中
//                3196	系统取消核实中
//                4120	等待买家确认报价
//                5100	交易成功

//                5520	卖家取消订单

//                5540	买家取消订单（扣除违约金）
//                5541	买家恶意还价（扣除违约金）
//                5542	买家接收超时（扣除违约金）
//                5550	买家关闭订单

//                5580	系统取消订单
                userPackagePickupEntity.setStatus(PackagePickupStatus.CANCEL);
                switch (cancle_type) {
                    case 5520:
                        userPackagePickupEntity.setRemarks("卖家取消");
                        break;
                    case 5580:
                        userPackagePickupEntity.setRemarks("系统取消");
                        break;
                    case 5540:
                    case 5541:
                    case 5542:
                    case 5550:
                        userPackagePickupEntity.setRemarks("买家取消报价");
                        Integer userCancelCount = userPackagePickupRepository.queryCancelCount(userPackagePickupEntity.getUserPackage().getUser().getId());
                        log.info("用户: {} 已经拒绝报价次数：{}", userPackagePickupEntity.getUserPackage().getUser().getId(), userCancelCount);
                        if (userCancelCount >= 3) {
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(new Date());
                            //获取后一天
                            calendar.add(Calendar.DAY_OF_MONTH, 1);
                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            RedisUtils.save(redisUserPrefix + ":DISABLE_PICKUP:" + userPackagePickupEntity.getUserPackage().getUser().getId().toString(), simpleDateFormat.format(calendar.getTime()), redisUserDisablePickupExpire);
                            // 禁用功能
                            UserDisableEntity userDisableEntity = new UserDisableEntity();
                            userDisableEntity.setUser(userDisableEntity.getUser());
                            userDisableEntity.setIsEffective(true);
                            userDisableEntity.setType(UserDisableType.PICK_UP);
                            userDisableEntity.setDisableExpire(calendar.getTime());
                            userDisableEntity.setRemarks("拒绝报价超过3次");
                            userDisableRepository.save(userDisableEntity);
                            break;
                        }
                    default:
                        userPackagePickupEntity.setRemarks("未知原因");
                }

                // 饰品还原
                UserPackageEntity userPackageEntity = userPackageRepository.findById(userPackagePickupEntity.getUserPackage().getId()).orElse(null);
                if (userPackageEntity != null) {
                    userPackageEntity.setIsReceived(false);
                    userPackageRepository.save(userPackageEntity);
                }
            } else if (status.equals(41)) {
                userPackagePickupEntity.setStatus(PackagePickupStatus.WAIT_USER);
                userPackagePickupEntity.setRemarks("待买家接收报价");
            } else if (status.equals(51)) {
                userPackagePickupEntity.setStatus(PackagePickupStatus.SUCCESS);
                userPackagePickupEntity.setRemarks("已完成");
            } else {
                userPackagePickupEntity.setStatus(PackagePickupStatus.DOING);
                userPackagePickupEntity.setRemarks("等待卖家发货");
            }
        } catch (Exception e) {
            log.error("查询购买订单状态失败: {}", e.getMessage());
        }
    }

    @Override
    public void SyncSkinInfoOne(Long skinId) {
        SkinEntity skin = skinRepository.findById(skinId).orElse(null);
        if (skin == null) {
            log.error("饰品不存在：{}", skinId);
            return;
        }
        SysExchangeRateEntity sysExchangeRateEntity = sysExchangeRateRepository.findFirstByOrderById();
        if (sysExchangeRateEntity == null) {
            log.error("同步扎比特数据失败：后台未配置汇率");
            return;
        }
        // 请求参数
        Map<String, Object> data = new HashMap<>();
        List<String> templateHashNames = new ArrayList<>();
        templateHashNames.add(skin.getEnglishName());
        data.put("templateHashNames", templateHashNames);
        // 请求地址
        String url = "https://open.io661.com/api/v1/market/batchGetTemplateOnSellInfo";
        // 发送请求
        try {
            JSONObject responseBody = request(url, data);
            if (responseBody == null) {
                log.error("同步失败");
                return;
            }
            JSONArray dataArray = responseBody.getJSONArray("templateOnSellInfo");
            for (int i = 0; i < dataArray.size(); i++) {
                IO661PriceInfoVO priceInfoVO = dataArray.getObject(i, IO661PriceInfoVO.class);
                if (priceInfoVO.getTemplateHashName().equals(skin.getEnglishName())) {
                    BigDecimal price = new BigDecimal(priceInfoVO.getOnSellMinPrice()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                    skin.setPrice(price);
                    skin.setDiamond(price.multiply(sysExchangeRateEntity.getZbtToCnyPremium()).setScale(2, BigDecimal.ROUND_UP));
                    skinRepository.save(skin);
                }
            }
//            List<IO661PriceInfoVO> io661PriceInfoVOList = responseBody.getJSONArray("templateOnSellInfo", List.class);
//            for(IO661PriceInfoVO priceInfoVO : io661PriceInfoVOList){
//
//            }
        } catch (Exception e) {
            log.error("同步失败： {}", e.getMessage());
        }

    }

    @Override
    public IGXESteamIdVO queryUserSteamId(String trackLink) {
        return null;
    }

    @Override
    public void sycnSKinList(List<SkinEntity> skinEntityList) {

    }

    @Override
    public void getUserInfo() {
        // 请求参数
        Map<String, Object> data = new HashMap<>();
        String url = "https://open.io661.com/api/v1/user/getUserInfo";
        // 发送请求
        try {
            JSONObject responseBody = request(url, data);
            if (responseBody == null) {
                log.error("测试接口失败");
            }
        } catch (Exception e) {
            log.error("测试接口失败： {}", e.getMessage());
        }
    }
}
