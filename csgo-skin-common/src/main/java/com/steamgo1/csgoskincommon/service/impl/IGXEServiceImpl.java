package com.steamgo1.csgoskincommon.service.impl;

import cc.siyecao.uid.core.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskincommon.dao.*;
import com.steamgo1.csgoskincommon.entity.*;
import com.steamgo1.csgoskincommon.entity.enums.PackagePickupStatus;
import com.steamgo1.csgoskincommon.entity.enums.UserDisableType;
import com.steamgo1.csgoskincommon.entity.enums.ZbtSyncStatus;
import com.steamgo1.csgoskincommon.service.IGXEService;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import com.steamgo1.csgoskincommon.utils.StreamUtil;
import com.steamgo1.csgoskincommon.vo.IGXESteamIdVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;


@Slf4j
@Service
public class IGXEServiceImpl implements IGXEService {
    //    private final String RSA_PRI_KEY = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDZpjJ2DOtBtT3FsERRZ0QjvOlm81umAG6Fx6ub6tJij4Q5ocGEd/MBUTm8EL4HgjZTb562iwbr7hDtasbnHwo6BCzKDLB6o1Lq5sv2Z6tr0iB+bCGKsKTZjbBMXdDtsDQIVqkrwmTMvqvHvJ2isNxPZzFejp8CPWiuyp6/Bc5z9fnExYG5FxXgPedSNWz6DJOqZ2yKywyvY6BLltfeiX+Ncj7jieHBZp0PcM+qIHi+WB6JbsxjihG4xmCkeEYF2Z9SceSjIq1mlxSQMO55lWqMMRuiB4cxtCBcfEw9JWnq8xZ5dY5JZSM2INXcACXci7tJRZYmKQ0uevZTrFJL4+***************************+Vnzk9TJO1+dpxxS6hDxkEvxdCwPwFpbRMTj0YQW4wtI0L2ZUmZ6WQqT8Cwmy7/U9j89uHlNnUkGg/1HVZlNdoEKP0Zc59A8fERoif1ppirPBWF+ftnl+0k8ttTyS5ehNq6pKs+YTnixuuNlL/LAwPfHadW8Wof2Il6UeI9Lpjy0Sp7UUscWEz0r5L+NLgDfVL+09nnQpAgh8ZaafX4Bc31w0t9bIABwqe4xQular74fDuhQyWXHJsOQZeLPynlU3LEtl5AFpEtIz9eYAlqS1m2eoSfunFy8I1CHBwcbwm9tZzoQ7z/FBuPCC4OAAEnl/DL8NaKOwKBgQDuuX/lr2FeGAwNxzKoiariRyMWjJeLcymcAu4+E40XBpvcMGwpBDRQ2xhbT2NXG2HGEnIOazRG3AmqAHkG/xpF4PR98g3NreXTxhkAKXlAVntunoLouMye4BkqDeZCLsmzNznhVNU5zXMJSqOaOWlkzJCeb9yv7rwuUWFNZD6dwwKBgQDpZkO21Mm23AojyhAzJa9wR6GVDbEj/3oiR1uUTtxOTpXLsLYej68TJdoinkepmhCHUn29LywfHzKDTMaOkBP0/zVeiQ+Vb4Puhu5Mir69o0Q3TKVQETG+4Ql3tJdLEB0UwtFpZUa82HtmPznB9YNUIruLeLM8zdpntXV8yUwajwKBgBg78pThifPgYxDfAPCFETjpWdGHMjcw2K+CyKa6m18UB6yBqek8CQeQRle2pnIIUJOn+bYnAeaTndOJOsAQE522aXuDoA0aMzOdb5pJm4qBfQRMySNQ50zVmRhg0tgzIby0xYZvcznr9lqiHhug6mcWMTQ0TOX7SVU2Y7dUjtq7AoGBAL99WKcDU+/D/km4uavNCnA7RNWuoxweugyvsPIv9U4F6Z5xgCn4xrIWBy8J30/GCDOOOX1i23c5OE9DSVCj4svKKvZBy1hWYs7rQ03nM3ERJ6GbL7JOORsbNJJSxyIduU5UPE2VewDrN+r+ipuYm8QGrwpR3/RODBe0aA0Wdmr/AoGAIECQ0MBFvmdTt20nxwAQxEi6iMtPj5fI1NAjtNZVUHFhPbDk2t3s6F8bAJvlqNZLNxYYZPUpBho8u4Ek7IepOKuxQKP6iQ77EUi/+ICpHfXoZSUPlmqw/7RdN6CK5coVlIILOBHBnBOoPWbrkiUIEgApeo6mTTzPJPQXlm9moQ4=";
    private final String RSA_PRI_KEY = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDnz/K7O2dG6nmcZ6dIvKB4N4cEfs1WOlDjdq0mc8QlOdnCeDGXYfBQaIBGGJCryl/6jLTZTTWOIfDkCt5t5OWJzmLIxU9otRFDnC++hmdhmVPr4QNPxpDn5wZvaT2hu/0pnM5di5RfTWCPjCKPEMkVQ+P1YLt67d8A2R5zdPyYZDCNZoYZW0tEYU8dFvnAuBrI1xJuW+fjep9e0YAjqZ9ouC9UaD4gfTzEux3v7AZt6EYWv2hWll6rL3uqfFtbANWM1XuA1kS8uZqleCjSCtjV5qe/xupSEcx47jjW2dKkkbl49sYSKs5JC8trTBzRYxrqXNKwbRr85RXfwDrLb5RHAgMBAAECggEAXupERulxxhWnqaS/WAbz5YVG4U5SISZLjHdBOmdYPq3LPdcc5xEQ5ruDGjBgvBZm6h3GxtrYPNQk4BqbLH/2hFBfHjm74/GPmhFPc0Fw/wNa21AMnRN+rZVoiyGaPKjOZcVwsdi+7a6vBNeqJs4Gp5BevE/ZM+JF1wEPeNLDwlkr/ZqvH9/in6Hn7c3BLfeNNA81TEn6ww29EXXqLniE9jxY2pExggVtCa9o1yYAPveOBxhnAu82Zk4AXTmy4AGxhATeGmmhJk6HjdOJiGLm7cqjoyjJP1IUjWmauQqaHvzBqVtbdIc4LslJvWa7LsB0fa6DFqAgOzFxKcxuxW5h0QKBgQD6ppyO4sI/8LyqvU6WXp4o5GiOgDLlFGkH9XSnTirfPEZ937V4HQGfU+x/J2n1avGAtwCmiuObNDR+cLkSag6Nw0T5noR+1+LdJGNAKOQ0RuxTSkgko3NC6fIUU4Mc5m/2sgyKth1x7/sWZ62eRfhCXjJ3vn/WEdLOo0efL4FAOwKBgQDswmpd1zmZmAWS9OXqQ6lNid9wQ2rxPsKFin7ooPXfzS7TVWzhucRR30cRqQSBRUnzlIlBQhu6/2tsPfW7cdjogNOrMyB+K/kfzWlTeQuhtXxn084Q4F4zKvvWB5wOPazUrFhzayNkRCOmFjfHlrxExP76e8BJ8V76QSsxmbTnZQKBgBLSGeVcv4s/j1setdzyo9ucMwGoPKoY2z4evzCk3PFAPGbNOnoXa3zEN3jgw8MK0bGBjEn1GdwYkmyAGFV6ozcf13kK3xYLLdy/E7jImUIXpT0n0bz2jPwURvohvzGXWAblHa34uMNKascm59F/nYGWQyHcqwqu8e5UMleQoJrrAoGAeZuKgtTPkyLQiiEcDu3eAiO1DrayMWOpCmqOsPIsGF4NlSMln/OI8PycRmfoFnX+fAxpS4mnMK+M/7X7h+H8JcA1+JN0NxpjBYC+fs+Ht/G+Cp5yE50LBYEohVC3SWFIapxzeiUnspKFNQPodq3n2fLNfN8fb1t5C1V52Co5DzkCgYEAhGU/C+ul8wy32ZOdoSsixC/6NLIyPjfEmkeYntjSQ5UBPNbE+f7/XPWk0p4elcjQHh1AXerDcHY/VponPZqlPue2nYkPrYV5LfVTLDNMJwDYfvNmKRTxfhytC9pqyKl8Jv626sd28v0NUtsojy4gN7CEQ8m9itRRn/ncpTQUldk=";
    private final String baseUrl = "https://open.igxe.cn";
    private final String public_key = "O6ML7F1Nd4GUpsCJOwlNi90FwKRSwOnW";
    private final String partner_key = "5DLWMFUR5s5lDota3XbRL1tONES4jY";
    private final String aesKey = "ceLgB9y2qK1r4GCs";
    private final String AES_CBC_PCK_ALG = "AES/CBC/PKCS5Padding";
    private final byte[] AES_IV = initIv(AES_CBC_PCK_ALG);
    @Autowired
    UserPackagePickupRepository userPackagePickupRepository;
    @Autowired
    UserPackageRepository userPackageRepository;
    @Value("${site.skin-sync-time}")
    private long skinSyncTime;
    @Value("${spring.redis.prefix.skin}")
    private String redisSkinPrefix;
    @Value("${spring.redis.prefix.user}")
    private String redisUserPrefix;
    @Value("${spring.redis.expire.disable-pickup}")
    private Long redisUserDisablePickupExpire;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private DataDictionaryRepository dataDictionaryRepository;
    @Autowired
    private SkinRepository skinRepository;
    @Autowired
    private SysExchangeRateRepository sysExchangeRateRepository;
    @Autowired
    private ZbtSyncRecordRepository zbtSyncRecordRepository;
    @Autowired
    private UserDisableRepository userDisableRepository;

    @Autowired
    private SysSkinRiskManagementRepository sysSkinRiskManagementRepository;

    //初始向量的方法, 全部为0. 这里的写法适合于其它算法,针对AES算法的话,IV值一定是128位的(16字节).
    private static byte[] initIv(String fullAlg) {
        try {
            Cipher cipher = Cipher.getInstance(fullAlg);
            int blockSize = cipher.getBlockSize();
            byte[] iv = new byte[blockSize];
            for (int i = 0; i < blockSize; ++i) {
                iv[i] = 0;
            }
            return iv;
        } catch (Exception e) {

            int blockSize = 16;
            byte[] iv = new byte[blockSize];
            for (int i = 0; i < blockSize; ++i) {
                iv[i] = 0;
            }
            return iv;
        }
    }

    public String gen_sign(String content, String privateKey) throws Exception {
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
//        byte[] encodedKey = new ByteArrayInputStream(privateKey.getBytes()).;
        byte[] encodedKey = StreamUtil.readText(new ByteArrayInputStream(privateKey.getBytes())).getBytes();
        encodedKey = Base64.getDecoder().decode(encodedKey);
        PrivateKey priKey = keyFactory.generatePrivate(new PKCS8EncodedKeySpec(encodedKey));
        Signature signature = Signature.getInstance("SHA256WithRSA");
        signature.initSign(priKey);
        signature.update(content.getBytes("utf-8"));
        byte[] signed = signature.sign();
        return new String(Base64.getEncoder().encode(signed));
    }

    //购买参数加密
    public String buyEncrypt(String content, String aesKey, String charset) throws Exception {
        try {
            Cipher cipher = Cipher.getInstance(AES_CBC_PCK_ALG);
            SecretKeySpec aes_key = new SecretKeySpec(aesKey.getBytes("utf-8"), "AES");
            IvParameterSpec iv = new IvParameterSpec(AES_IV);
            cipher.init(Cipher.ENCRYPT_MODE, aes_key, iv);
            byte[] encryptBytes = cipher.doFinal(content.getBytes(charset));
            return new String(Base64.getEncoder().encode(encryptBytes));
        } catch (UnsupportedEncodingException e) {
            throw new Exception(e);
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    //查询商品
    public void testQueryProductList() throws Exception {
        String url = "https://open.igxe.cn/third/api/open/product_list";
        Map<String, String> paramMap = new HashMap<String, String>();
        paramMap.put("public_key", "O6ML7F1Nd4GUpsCJOwlNi90FwKRSwOnW");
        paramMap.put("partner_key", "5DLWMFUR5s5lDota3XbRL1tONES4jY");
//        paramMap.put("market_hash_name","AWP | Asiimov (Field-Tested)");
//        paramMap.put("product_id", "1");
        paramMap.put("t", String.valueOf(System.currentTimeMillis() / 1000));
        paramMap.put("page_size", "20");
        paramMap.put("page_no", "1");
        paramMap.put("app_id", "730");
        String signStr = "";
        List<String> keys = new ArrayList<String>(paramMap.keySet());
        Collections.sort(keys);
        for (String key : keys) {
            String temp = key + "=" + paramMap.get(key);
            if (StringUtils.isEmpty(signStr)) {
                signStr = temp;
            } else {
                signStr = signStr + "&" + temp;
            }
        }
        System.out.println(signStr);
        String sign = gen_sign(signStr, RSA_PRI_KEY);
        paramMap.put("sign", sign);
        System.out.println(paramMap.toString());
        MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            multiValueMap.add(entry.getKey(), entry.getValue());
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.set("Accept-Charset", "UTF-8");
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(multiValueMap, headers);
        RestTemplate restTemplate = new RestTemplate();


        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);
        HttpStatus statusCode = responseEntity.getStatusCode();
        String responseBody = responseEntity.getBody();

        // Print the response details
        String chineseStr = StringEscapeUtils.unescapeJava(responseBody);
        System.out.println("Status Code: " + statusCode);
        System.out.println("Response Body: " + chineseStr);
    }

    @Override
    @Async("async-executor-guava")
    public void SyncSkinInfo(Long zbtSyncRecordId) {
        String url = baseUrl + "/third/api/open/product_list";
        Boolean isMore = true;
        Integer pageNo = 1;
        SysExchangeRateEntity sysExchangeRateEntity = sysExchangeRateRepository.findFirstByOrderById();
        ZbtSyncRecordEntity zbtSyncRecordEntity = zbtSyncRecordRepository.findById(zbtSyncRecordId).get();
        if (sysExchangeRateEntity == null) {
            log.error("同步扎比特数据失败：后台未配置汇率");
            zbtSyncRecordEntity.setZbtSyncStatus(ZbtSyncStatus.FAIL);
            zbtSyncRecordEntity.setRemarks("后台未配置汇率");
            zbtSyncRecordRepository.save(zbtSyncRecordEntity);
            return;
        }
        SysSkinRiskManagementEntity sysSkinRiskManagementEntity = sysSkinRiskManagementRepository.findFirstByOrderById();
        if (sysSkinRiskManagementEntity == null) {
            log.error("同步扎比特数据失败：后台未配置饰品风控");
            zbtSyncRecordEntity.setZbtSyncStatus(ZbtSyncStatus.FAIL);
            zbtSyncRecordEntity.setRemarks("后台未配置饰品风控");
            zbtSyncRecordRepository.save(zbtSyncRecordEntity);
            return;
        }
        while (isMore) {
            Map<String, String> paramMap = new HashMap<String, String>();
            paramMap.put("public_key", "O6ML7F1Nd4GUpsCJOwlNi90FwKRSwOnW");
            paramMap.put("partner_key", "5DLWMFUR5s5lDota3XbRL1tONES4jY");
            paramMap.put("t", String.valueOf(System.currentTimeMillis() / 1000));
            paramMap.put("page_size", "200");
            paramMap.put("page_no", pageNo + "");
            paramMap.put("app_id", "730");
            String signStr = "";
            List<String> keys = new ArrayList<String>(paramMap.keySet());
            Collections.sort(keys);
            for (String key : keys) {
                String temp = key + "=" + paramMap.get(key);
                if (StringUtils.isEmpty(signStr)) {
                    signStr = temp;
                } else {
                    signStr = signStr + "&" + temp;
                }
            }
            System.out.println("签名字符串：" + signStr);
            String sign = null;
            try {
                sign = gen_sign(signStr, RSA_PRI_KEY);
            } catch (Exception e) {
                zbtSyncRecordEntity.setZbtSyncStatus(ZbtSyncStatus.FAIL);
                zbtSyncRecordEntity.setRemarks("签名失败");
                zbtSyncRecordRepository.save(zbtSyncRecordEntity);
                return;
            }
            paramMap.put("sign", sign);
            MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
            for (Map.Entry<String, String> entry : paramMap.entrySet()) {
                multiValueMap.add(entry.getKey(), entry.getValue());
            }
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            headers.set("Accept-Charset", "UTF-8");
            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(multiValueMap, headers);
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);
            if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                String responseStr = StringEscapeUtils.unescapeJava(responseEntity.getBody());
                JSONObject responseJson = JSONObject.parseObject(responseStr);
                System.out.println("响应数据：" + responseStr);
                if (responseJson.getInteger("code").equals(1)) {
                    JSONObject data = responseJson.getJSONObject("data");
                    // 解析到数据库
                    List<JSONObject> zbtJsonObject = data.getObject("products", ArrayList.class);
                    for (JSONObject jsonObject : zbtJsonObject) {
                        Long productId = jsonObject.getLong("product_id");
                        BigDecimal price = jsonObject.getBigDecimal("buy_min_price");
                        String marketHashName = jsonObject.getString("market_hash_name");
                        String name = jsonObject.getString("name");
                        String icornUrl = jsonObject.getString("icon_url");
                        SkinEntity skin = skinRepository.findByEnglishName(marketHashName);
                        log.info("同步饰品: {}, 价格：{}", name, price);
                        if (skin == null) {
                            log.info("饰品不存在：{},跳过同步", marketHashName);
                            continue;
                        }
                        skin.setProductId(productId);
                        skin.setPrice(price);
                        skin.setPicture(icornUrl);
                        skin.setDiamond(price.multiply(sysExchangeRateEntity.getZbtToCnyPremium()).setScale(2, BigDecimal.ROUND_UP));
                        skinRepository.save(skin);
                    }
                    Integer is_more = data.getJSONObject("page").getInteger("is_more");
                    isMore = is_more == 1 ? true : false;
                    pageNo += 1;
                    continue;
                }
            }
            zbtSyncRecordEntity.setZbtSyncStatus(ZbtSyncStatus.SUCCESS);
            zbtSyncRecordEntity.setRemarks("同步成功数量：" + pageNo * 200);
            zbtSyncRecordRepository.save(zbtSyncRecordEntity);
        }
    }

    @Override
    public void quickBuy(Long userPackagePickUpId) {
        UserPackagePickupEntity userPackagePickupEntity = userPackagePickupRepository.findById(userPackagePickUpId).orElseGet(null);
        if (userPackagePickupEntity == null) {
            log.error("饰品取回订单不存在, userPackagePickUpId： {}", userPackagePickUpId);
            return;
        }
        SkinEntity skin = userPackagePickupEntity.getUserPackage().getSkin();
        if (skin.getPrice().equals(userPackagePickupEntity.getUserPackage().getPrice().multiply(BigDecimal.valueOf(1.05)))) {
            userPackagePickupEntity.setRemarks("商品涨价超过百分之五");
            userPackagePickupEntity.setStatus(PackagePickupStatus.FROZEN);
            userPackagePickupRepository.save(userPackagePickupEntity);
            return;
        }
        if (skin.getProductId() == null) {
            log.error("igxe没有这个饰品: {}", skin.getEnglishName());
            userPackagePickupEntity.setStatus(PackagePickupStatus.FROZEN);
            userPackagePickupRepository.save(userPackagePickupEntity);
            return;
        }
        IGXESteamIdVO igxeSteamIdVO = queryUserSteamId(userPackagePickupEntity.getSteamTradeUrl());
        if (igxeSteamIdVO == null) {
            log.error("交易链接查询用户steamId失败,userPackagePickUpId：{}", userPackagePickUpId);
            return;
        }
        String url = baseUrl + "/third/api/open/product_buy";
        Map<String, String> paramMap = new HashMap<String, String>();
        paramMap.put("public_key", public_key);
        paramMap.put("partner_key", partner_key);
        paramMap.put("app_id", "730");
        paramMap.put("market_hash_name", skin.getEnglishName());
        paramMap.put("channal_order_num", userPackagePickupEntity.getOrderNo());
        paramMap.put("buyer_track_link", userPackagePickupEntity.getSteamTradeUrl());
        paramMap.put("buyer_steam_uid", igxeSteamIdVO.getSteamUid());
        paramMap.put("steam_date", igxeSteamIdVO.getSteamDate());
        paramMap.put("max_price", String.valueOf(skin.getPrice()));
        paramMap.put("t", String.valueOf(System.currentTimeMillis() / 1000));
        String signStr = "";
        List<String> keys = new ArrayList<String>(paramMap.keySet());
        Collections.sort(keys);
        for (String key : keys) {
            String temp = key + "=" + paramMap.get(key);
            if (StringUtils.isEmpty(signStr)) {
                signStr = temp;
            } else {
                signStr = signStr + "&" + temp;
            }
        }
        String sign = null;
        try {
            sign = gen_sign(signStr, RSA_PRI_KEY);
        } catch (Exception e) {
            log.error("签名错误");
            throw new RuntimeException(e);
        }
        paramMap.put("sign", sign);
        Map<String, String> buyParamMap = new HashMap<String, String>();
        String buy_param = null;
        try {
            buy_param = buyEncrypt(JSON.toJSONString(paramMap), aesKey, "utf-8");
        } catch (Exception e) {
            log.error("购买参数加密错误");
            throw new RuntimeException(e);
        }
        buyParamMap.put("public_key", public_key);
        buyParamMap.put("partner_key", partner_key);
        buyParamMap.put("sign", sign);
        buyParamMap.put("buy_param", buy_param);
        MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
        for (Map.Entry<String, String> entry : buyParamMap.entrySet()) {
            multiValueMap.add(entry.getKey(), entry.getValue());
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.set("Accept-Charset", "UTF-8");
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(multiValueMap, headers);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);
        if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
            String responseStr = StringEscapeUtils.unescapeJava(responseEntity.getBody());
            JSONObject responseJson = JSONObject.parseObject(responseStr);
            System.out.println("响应数据：" + responseStr);
            JSONObject data = responseJson.getJSONObject("data");
            if (responseJson.getInteger("code").equals(1)) {
                userPackagePickupEntity.setThirdOrderNo(data.getLong("order_id"));
                userPackagePickupRepository.save(userPackagePickupEntity);
            } else {
                // 余额不足70001, 无满足条件在售饰品1317, 饰品状态暂时冻结
                log.error("购买失败：{}", responseJson.getInteger("code"));
                userPackagePickupEntity.setRemarks(responseJson.getString("message"));
                userPackagePickupEntity.setStatus(PackagePickupStatus.FROZEN);
                userPackagePickupRepository.save(userPackagePickupEntity);
            }
        }
    }

    @Override
    public void queryOrderStatus(Long userPackagePickUpId) {
        UserPackagePickupEntity userPackagePickupEntity = userPackagePickupRepository.findById(userPackagePickUpId).get();
        if (userPackagePickupEntity.getThirdOrderNo() == null) {
            log.error("订单未提交成功,没有三方订单号");
            return;
        }
        String url = baseUrl + "/third/api/open/orderinfo";
        Map<String, String> paramMap = new HashMap<String, String>();
        paramMap.put("public_key", public_key);
        paramMap.put("partner_key", partner_key);
        paramMap.put("t", String.valueOf(System.currentTimeMillis() / 1000));
        paramMap.put("order_id", String.valueOf(userPackagePickupEntity.getThirdOrderNo()));
        String signStr = "";
        List<String> keys = new ArrayList<String>(paramMap.keySet());
        Collections.sort(keys);
        for (String key : keys) {
            String temp = key + "=" + paramMap.get(key);
            if (StringUtils.isEmpty(signStr)) {
                signStr = temp;
            } else {
                signStr = signStr + "&" + temp;
            }
        }
        String sign = null;
        try {
            sign = gen_sign(signStr, RSA_PRI_KEY);
        } catch (Exception e) {
            log.error("签名错误");
            return;
        }
        paramMap.put("sign", sign);
        MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            multiValueMap.add(entry.getKey(), entry.getValue());
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.set("Accept-Charset", "UTF-8");
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(multiValueMap, headers);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);
        if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
            String responseStr = StringEscapeUtils.unescapeJava(responseEntity.getBody());
            JSONObject responseJson = JSONObject.parseObject(responseStr);
            System.out.println("响应数据：" + responseStr);
            JSONObject data = responseJson.getJSONObject("data");
            if (responseJson.getInteger("code").equals(1)) {
                Integer status = data.getInteger("status");
                Integer cancle_type = data.getInteger("cancle_type");
                if (status.equals(1)) {
//                    订单已取消
                    userPackagePickupEntity.setStatus(PackagePickupStatus.CANCEL);
                    if (cancle_type.equals(1)) {
                        userPackagePickupEntity.setRemarks("其他");
                    } else if (cancle_type.equals(2)) {
                        userPackagePickupEntity.setRemarks("买家取消");
                    } else if (cancle_type.equals(3)) {
                        userPackagePickupEntity.setRemarks("卖家取消");
                    } else if (cancle_type.equals(4)) {
                        userPackagePickupEntity.setRemarks("买家取消报价");
                        Integer userCancelCount = userPackagePickupRepository.queryCancelCount(userPackagePickupEntity.getUserPackage().getUser().getId());
                        log.info("用户: {} 已经拒绝报价次数：{}", userPackagePickupEntity.getUserPackage().getUser().getId(), userCancelCount);
                        if (userCancelCount >= 3) {
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(new Date());
                            //获取后一天
                            calendar.add(Calendar.DAY_OF_MONTH, 1);
                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            RedisUtils.save(redisUserPrefix + ":DISABLE_PICKUP:" + userPackagePickupEntity.getUserPackage().getUser().getId().toString(), simpleDateFormat.format(calendar.getTime()), redisUserDisablePickupExpire);
                            // 禁用功能
                            UserDisableEntity userDisableEntity = new UserDisableEntity();
                            userDisableEntity.setUser(userDisableEntity.getUser());
                            userDisableEntity.setIsEffective(true);
                            userDisableEntity.setType(UserDisableType.PICK_UP);
                            userDisableEntity.setDisableExpire(calendar.getTime());
                            userDisableEntity.setRemarks("拒绝报价超过3次");
                            userDisableRepository.save(userDisableEntity);
                        }
                    } else if (cancle_type.equals(5)) {
                        userPackagePickupEntity.setRemarks("超时未收货（18小时）");
                        Integer userCancelCount = userPackagePickupRepository.queryCancelCount(userPackagePickupEntity.getUserPackage().getUser().getId());
                        log.info("用户: {} 已经拒绝报价次数：{}", userPackagePickupEntity.getUserPackage().getUser().getId(), userCancelCount);
                        if (userCancelCount >= 3) {
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(new Date());
                            //获取后一天
                            calendar.add(Calendar.DAY_OF_MONTH, 1);
                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            RedisUtils.save(redisUserPrefix + ":DISABLE_PICKUP:" + userPackagePickupEntity.getUserPackage().getUser().getId().toString(), simpleDateFormat.format(calendar.getTime()), redisUserDisablePickupExpire);
                            // 禁用功能
                            UserDisableEntity userDisableEntity = new UserDisableEntity();
                            userDisableEntity.setUser(userDisableEntity.getUser());
                            userDisableEntity.setIsEffective(true);
                            userDisableEntity.setType(UserDisableType.PICK_UP);
                            userDisableEntity.setDisableExpire(calendar.getTime());
                            userDisableEntity.setRemarks("拒绝报价超过3次");
                            userDisableRepository.save(userDisableEntity);
                        }
                    } else if (cancle_type.equals(6)) {
                        userPackagePickupEntity.setRemarks("卖家发货后12小时买家未收货，卖家取消订单");
                        Integer userCancelCount = userPackagePickupRepository.queryCancelCount(userPackagePickupEntity.getUserPackage().getUser().getId());
                        log.info("用户: {} 已经拒绝报价次数：{}", userPackagePickupEntity.getUserPackage().getUser().getId(), userCancelCount);
                        if (userCancelCount >= 3) {
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(new Date());
                            //获取后一天
                            calendar.add(Calendar.DAY_OF_MONTH, 1);
                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            RedisUtils.save(redisUserPrefix + ":DISABLE_PICKUP:" + userPackagePickupEntity.getUserPackage().getUser().getId().toString(), simpleDateFormat.format(calendar.getTime()), redisUserDisablePickupExpire);
                            // 禁用功能
                            UserDisableEntity userDisableEntity = new UserDisableEntity();
                            userDisableEntity.setUser(userDisableEntity.getUser());
                            userDisableEntity.setIsEffective(true);
                            userDisableEntity.setType(UserDisableType.PICK_UP);
                            userDisableEntity.setDisableExpire(calendar.getTime());
                            userDisableEntity.setRemarks("拒绝报价超过3次");
                            userDisableRepository.save(userDisableEntity);
                        }
                    } else {
                        userPackagePickupEntity.setRemarks("取消原因未知");
                    }
                    // 饰品还原
                    UserPackageEntity userPackageEntity = userPackageRepository.findById(userPackagePickupEntity.getUserPackage().getId()).orElse(null);
                    if (userPackageEntity != null) {
                        userPackageEntity.setIsReceived(false);
                        userPackageRepository.save(userPackageEntity);
                    }
                } else if (status.equals(2)) {
                    userPackagePickupEntity.setStatus(PackagePickupStatus.DOING);
                    userPackagePickupEntity.setRemarks("等待卖家发货");
                } else if (status.equals(4)) {
                    userPackagePickupEntity.setStatus(PackagePickupStatus.WAIT_USER);
                    userPackagePickupEntity.setRemarks("待买家接收报价");
                } else if (status.equals(5)) {
                    userPackagePickupEntity.setStatus(PackagePickupStatus.SUCCESS);
                    userPackagePickupEntity.setRemarks("已完成");
                }
                userPackagePickupRepository.save(userPackagePickupEntity);
            } else {
                log.error("查询订单失败：{}", responseJson.getString("message"));
            }
        }
    }

    @Override
    @Async("async-executor-guava")
    public void SyncSkinInfoOne(Long skinId) {
        SkinEntity syncSkin = skinRepository.findById(skinId).orElse(null);
        if (syncSkin == null) {
            log.error("饰品不存在：{}", skinId);
            return;
        }
        String url = baseUrl + "/third/api/open/product_list";
        Boolean isMore = true;
        Integer pageNo = 1;
        SysExchangeRateEntity sysExchangeRateEntity = sysExchangeRateRepository.findFirstByOrderById();
        if (sysExchangeRateEntity == null) {
            log.error("同步扎比特数据失败：后台未配置汇率");
            return;
        }
        while (isMore) {
            Map<String, String> paramMap = new HashMap<String, String>();
            paramMap.put("public_key", "O6ML7F1Nd4GUpsCJOwlNi90FwKRSwOnW");
            paramMap.put("partner_key", "5DLWMFUR5s5lDota3XbRL1tONES4jY");
            paramMap.put("t", String.valueOf(System.currentTimeMillis() / 1000));
            paramMap.put("market_hash_name", syncSkin.getEnglishName());
            paramMap.put("page_size", "200");
            paramMap.put("page_no", pageNo + "");
            paramMap.put("app_id", "730");
            String signStr = "";
            List<String> keys = new ArrayList<String>(paramMap.keySet());
            Collections.sort(keys);
            for (String key : keys) {
                String temp = key + "=" + paramMap.get(key);
                if (StringUtils.isEmpty(signStr)) {
                    signStr = temp;
                } else {
                    signStr = signStr + "&" + temp;
                }
            }
            System.out.println("签名字符串：" + signStr);
            String sign = null;
            try {
                sign = gen_sign(signStr, RSA_PRI_KEY);
            } catch (Exception e) {
                System.out.println("签名失败");
                return;
            }
            paramMap.put("sign", sign);
            MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
            for (Map.Entry<String, String> entry : paramMap.entrySet()) {
                multiValueMap.add(entry.getKey(), entry.getValue());
            }
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            headers.set("Accept-Charset", "UTF-8");
            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(multiValueMap, headers);
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);
            if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                String responseStr = StringEscapeUtils.unescapeJava(responseEntity.getBody());
                JSONObject responseJson = JSONObject.parseObject(responseStr);
                System.out.println("响应数据：" + responseStr);
                if (responseJson.getInteger("code").equals(1)) {
                    JSONObject data = responseJson.getJSONObject("data");
                    // 解析到数据库
                    List<JSONObject> zbtJsonObject = data.getObject("products", ArrayList.class);
                    for (JSONObject jsonObject : zbtJsonObject) {
                        Long productId = jsonObject.getLong("product_id");
                        BigDecimal price = jsonObject.getBigDecimal("buy_min_price");
                        String marketHashName = jsonObject.getString("market_hash_name");
                        String name = jsonObject.getString("name");
                        String icornUrl = jsonObject.getString("icon_url");
                        SkinEntity skin = skinRepository.findByEnglishName(marketHashName);
                        log.info("同步饰品: {}, 价格：{}", name, price);
                        if (skin == null) {
                            log.info("饰品不存在：{},跳过同步", marketHashName);
                            continue;
                        }
                        skin.setProductId(productId);
                        skin.setPrice(price);
                        skin.setPicture(icornUrl);
                        skin.setDiamond(price.multiply(sysExchangeRateEntity.getZbtToCnyPremium()).setScale(2, BigDecimal.ROUND_UP));
                        skinRepository.save(skin);
                    }
                    Integer is_more = data.getJSONObject("page").getInteger("is_more");
                    isMore = is_more == 1 ? true : false;
                    pageNo += 1;
                    continue;
                }
            }
            System.out.println("请求失败");
            return;
        }

    }

    @Override
    public IGXESteamIdVO queryUserSteamId(String trackLink) {
        String url = baseUrl + "/third/api/open/steamid";
        Map<String, String> paramMap = new HashMap<String, String>();
        paramMap.put("public_key", partner_key);
        paramMap.put("partner_key", partner_key);
        paramMap.put("t", String.valueOf(System.currentTimeMillis() / 1000));
        paramMap.put("track_link", trackLink);
        String signStr = "";
        List<String> keys = new ArrayList<String>(paramMap.keySet());
        Collections.sort(keys);
        for (String key : keys) {
            String temp = key + "=" + paramMap.get(key);
            if (StringUtils.isEmpty(signStr)) {
                signStr = temp;
            } else {
                signStr = signStr + "&" + temp;
            }
        }
        System.out.println("签名字符串：" + signStr);
        String sign = null;
        try {
            sign = gen_sign(signStr, RSA_PRI_KEY);
        } catch (Exception e) {
            System.out.println("签名失败");
            return null;
        }
        paramMap.put("sign", sign);
        MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            multiValueMap.add(entry.getKey(), entry.getValue());
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.set("Accept-Charset", "UTF-8");
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(multiValueMap, headers);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);
        if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
            String responseStr = StringEscapeUtils.unescapeJava(responseEntity.getBody());
            JSONObject responseJson = JSONObject.parseObject(responseStr);
            System.out.println("响应数据：" + responseStr);
            if (responseJson.getInteger("code").equals(1)) {
                JSONObject data = responseJson.getJSONObject("data");
                String steamDate = data.getString("steam_date");
                String steamUid = data.getString("steam_uid");
                IGXESteamIdVO igxeSteamIdVO = new IGXESteamIdVO();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
                Date date = null;
                try {
                    date = sdf.parse(steamDate);
                } catch (ParseException e) {
                    log.error("查询");
                    return null;
                }
                long epoch = date.getTime() / 1000;
                igxeSteamIdVO.setSteamDate(String.valueOf(epoch));
                igxeSteamIdVO.setSteamUid(steamUid);
                return igxeSteamIdVO;
            }
        }
        return null;
    }

    @Override
    @Async("async-executor-guava")
    public void sycnSKinList(List<SkinEntity> skinEntityList) {
        for (SkinEntity syncSkin : skinEntityList) {
            String url = baseUrl + "/third/api/open/product_list";
            Boolean isMore = true;
            Integer pageNo = 1;
            SysExchangeRateEntity sysExchangeRateEntity = sysExchangeRateRepository.findFirstByOrderById();
            if (sysExchangeRateEntity == null) {
                log.error("同步扎比特数据失败：后台未配置汇率");
                return;
            }
            while (isMore) {
                Map<String, String> paramMap = new HashMap<String, String>();
                paramMap.put("public_key", "O6ML7F1Nd4GUpsCJOwlNi90FwKRSwOnW");
                paramMap.put("partner_key", "5DLWMFUR5s5lDota3XbRL1tONES4jY");
                paramMap.put("t", String.valueOf(System.currentTimeMillis() / 1000));
                paramMap.put("market_hash_name", syncSkin.getEnglishName());
                paramMap.put("page_size", "200");
                paramMap.put("page_no", pageNo + "");
                paramMap.put("app_id", "730");
                String signStr = "";
                List<String> keys = new ArrayList<String>(paramMap.keySet());
                Collections.sort(keys);
                for (String key : keys) {
                    String temp = key + "=" + paramMap.get(key);
                    if (StringUtils.isEmpty(signStr)) {
                        signStr = temp;
                    } else {
                        signStr = signStr + "&" + temp;
                    }
                }
                System.out.println("签名字符串：" + signStr);
                String sign = null;
                try {
                    sign = gen_sign(signStr, RSA_PRI_KEY);
                } catch (Exception e) {
                    System.out.println("签名失败");
                    return;
                }
                paramMap.put("sign", sign);
                MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
                for (Map.Entry<String, String> entry : paramMap.entrySet()) {
                    multiValueMap.add(entry.getKey(), entry.getValue());
                }
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.MULTIPART_FORM_DATA);
                headers.set("Accept-Charset", "UTF-8");
                HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(multiValueMap, headers);
                RestTemplate restTemplate = new RestTemplate();
                ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);
                if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                    String responseStr = StringEscapeUtils.unescapeJava(responseEntity.getBody());
                    JSONObject responseJson = JSONObject.parseObject(responseStr);
                    System.out.println("响应数据：" + responseStr);
                    if (responseJson.getInteger("code").equals(1)) {
                        JSONObject data = responseJson.getJSONObject("data");
                        // 解析到数据库
                        List<JSONObject> zbtJsonObject = data.getObject("products", ArrayList.class);
                        for (JSONObject jsonObject : zbtJsonObject) {
                            Long productId = jsonObject.getLong("product_id");
                            BigDecimal price = jsonObject.getBigDecimal("buy_min_price");
                            String marketHashName = jsonObject.getString("market_hash_name");
                            String name = jsonObject.getString("name");
                            String icornUrl = jsonObject.getString("icon_url");
                            SkinEntity skin = skinRepository.findByEnglishName(marketHashName);
                            log.info("同步饰品: {}, 价格：{}", name, price);
                            if (skin == null) {
                                log.info("饰品不存在：{},跳过同步", marketHashName);
                                continue;
                            }
                            skin.setProductId(productId);
                            skin.setPrice(price);
                            skin.setPicture(icornUrl);
                            skin.setDiamond(price.multiply(sysExchangeRateEntity.getZbtToCnyPremium()).setScale(2, BigDecimal.ROUND_UP));
                            skinRepository.save(skin);
                        }
                        Integer is_more = data.getJSONObject("page").getInteger("is_more");
                        isMore = is_more == 1 ? true : false;
                        pageNo += 1;
                        continue;
                    }
                }
                System.out.println("请求失败");
                return;
            }
        }
    }

}
