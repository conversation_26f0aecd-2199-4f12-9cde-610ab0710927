package com.steamgo1.csgoskincommon.service;

import com.steamgo1.csgoskincommon.entity.SkinEntity;
import com.steamgo1.csgoskincommon.vo.ZBTSteamInfoVO;

import java.util.List;

public interface ZBTService {
    /**
     * 同步搜索条件
     */
    void SyncFilterParam();

    /**
     * 同步所有饰品
     *
     * @param zbtSyncRecordId
     */
    void SyncSkinInfo(Long zbtSyncRecordId);


    /**
     * 同步所有饰品
     *
     * @param
     */
    void SyncSkinInfoByDB();

    void SyncSkinInfoByDBNew();

    void SyncSkinInfoByDBIsSale();

    void uploadSkinPictureToStorage();

    /**
     * 扎比特快速购买
     *
     * @param userPackagePickUpId
     * @return
     */
    void quickBuy(Long userPackagePickUpId);

    /**
     * 訂單狀態
     */
    void queryOrderStatus(Long userPackagePickUpId);

    /**
     * 同步单个饰品
     *
     * @param skinId
     */
    void SyncSkinInfoOne(Long skinId);

    /**
     * 同步饰品列表
     *
     * @param skinEntityList
     */
    void SycnSKinList(List<SkinEntity> skinEntityList);

    /**
     *
     */
    ZBTSteamInfoVO querySteamInfoByTradeUrl(String tradeUrl);

    Boolean createSteamInfoByTradeUrl(String tradeUrl);

    /**
     * 同步商品在售状态
     */
    void SycnSkinSaleState();

    /**
     * 同步用户背包饰品
     * @param userpackageIds
     */
//    void syncUserPackageSkin(List<Long> userpackageIds);
}
