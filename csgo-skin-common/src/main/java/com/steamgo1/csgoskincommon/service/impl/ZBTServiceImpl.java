package com.steamgo1.csgoskincommon.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.steamgo1.csgoskincommon.dao.*;
import com.steamgo1.csgoskincommon.dto.*;
import com.steamgo1.csgoskincommon.entity.*;
import com.steamgo1.csgoskincommon.entity.enums.PackagePickupStatus;
import com.steamgo1.csgoskincommon.entity.enums.UserDisableType;
import com.steamgo1.csgoskincommon.entity.enums.ZbtSyncStatus;
import com.steamgo1.csgoskincommon.enums.LanguageEnum;
import com.steamgo1.csgoskincommon.service.StorageService;
import com.steamgo1.csgoskincommon.service.ZBTService;
import com.steamgo1.csgoskincommon.utils.I18nField;
import com.steamgo1.csgoskincommon.utils.I18nUtils;
import com.steamgo1.csgoskincommon.utils.TranslationUtils;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import com.steamgo1.csgoskincommon.utils.Utils;
import com.steamgo1.csgoskincommon.vo.ZBTPriceInfoVO;
import com.steamgo1.csgoskincommon.vo.ZBTQuickBuyVO;
import com.steamgo1.csgoskincommon.vo.ZBTSteamInfoVO;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.net.URLEncoder;

@Slf4j
@Service
public class ZBTServiceImpl implements ZBTService {
    @Autowired
    UserPackagePickupRepository userPackagePickupRepository;
    @Autowired
    UserPackageRepository userPackageRepository;
    @Value("${site.skin-sync-time}")
    private long skinSyncTime;
    @Value("${spring.redis.prefix.skin}")
    private String redisSkinPrefix;
    @Value("${spring.redis.prefix.user}")
    private String redisUserPrefix;
    @Value("${spring.redis.expire.disable-pickup}")
    private Long redisUserDisablePickupExpire;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private DataDictionaryRepository dataDictionaryRepository;
    @Autowired
    private SkinRepository skinRepository;
    @Autowired
    private SysExchangeRateRepository sysExchangeRateRepository;
    @Autowired
    private ZbtSyncRecordRepository zbtSyncRecordRepository;
    //    private String baseUrl = "http://app.zbt.com";
//    "2b15440e6faab6361cba96c85158d11d"
    private String baseUrl = "https://openapi.cs2dt.com";
    private String appKey = "697e08808401a2cdfe89225e61d23690";
    @Autowired
    private UserDisableRepository userDisableRepository;

    @Autowired
    private SysSkinRiskManagementRepository sysSkinRiskManagementRepository;

    @Autowired
    private StorageService storageService;

    @Autowired
    private TranslationUtils translationUtils;

    @Async("async-executor-guava")
    @Override
    public void SyncFilterParam() {
        String redisKey = redisSkinPrefix + ":QUERYPARAM";
        RedisUtils.delete(redisKey);
        Map<String, String> params = new HashMap<>();
        params.put("appId", "730");
        params.put("app-key", appKey);
        params.put("language", "zh_CN");
        ResponseEntity<String> response = restTemplate.getForEntity(baseUrl + "/v1/product/v1/filters?appId={appId}&app-key={app-key}&language={language}", String.class, params);
        System.out.println(response.getStatusCode());

        if (response.getStatusCode().equals(HttpStatus.OK)) {
            String responseData = response.getBody();
            JSONObject responseJsonData = JSONObject.parseObject(responseData);
            log.info("{}", responseJsonData);
            List<JSONObject> zbtFiltersDTOList = responseJsonData.getJSONObject("data").getObject("list", ArrayList.class);

            for (JSONObject jsonObject : zbtFiltersDTOList) {
                // 同步外观数据
                ZBTFiltersDTO zbtFiltersDTO = jsonObject.toJavaObject(ZBTFiltersDTO.class);
                if (zbtFiltersDTO.getKey().equals("Exterior")) {
                    DataDictionaryEntity exteriorParent = dataDictionaryRepository.findByCode("skin_exterior");
                    if (exteriorParent == null) {
                        exteriorParent = new DataDictionaryEntity();
                        exteriorParent.setName("饰品外观");
                        exteriorParent.setCode("skin_exterior");
                        dataDictionaryRepository.save(exteriorParent);
                    }
                    for (ZBTFiltersDTO subZbtFiltersDTO : zbtFiltersDTO.getList()) {
                        DataDictionaryEntity exterior = dataDictionaryRepository.findByCode(subZbtFiltersDTO.getKey());
                        if (exterior == null) {
                            exterior = new DataDictionaryEntity();
                            exterior.setParentId(exteriorParent.getId());
                            exterior.setCode(subZbtFiltersDTO.getKey());
                            exterior.setName(subZbtFiltersDTO.getName());
                        } else {
                            exterior.setParentId(exteriorParent.getId());
                            exterior.setCode(subZbtFiltersDTO.getKey());
                            exterior.setName(subZbtFiltersDTO.getName());
                        }
                        dataDictionaryRepository.save(exterior);
                    }

                }
                // 同步质量数据
                if (zbtFiltersDTO.getKey().equals("Quality")) {
                    DataDictionaryEntity qualityParent = dataDictionaryRepository.findByCode("skin_quality");
                    if (qualityParent == null) {
                        qualityParent = new DataDictionaryEntity();
                        qualityParent.setName("饰品质量");
                        qualityParent.setCode("skin_quality");
                        dataDictionaryRepository.save(qualityParent);
                    }
                    for (ZBTFiltersDTO subZbtFiltersDTO : zbtFiltersDTO.getList()) {
                        DataDictionaryEntity quality = dataDictionaryRepository.findByCode(subZbtFiltersDTO.getKey());
                        if (quality == null) {
                            quality = new DataDictionaryEntity();
                            quality.setParentId(qualityParent.getId());
                            quality.setCode(subZbtFiltersDTO.getKey());
                            quality.setName(subZbtFiltersDTO.getName());
                        } else {
                            quality.setParentId(qualityParent.getId());
                            quality.setCode(subZbtFiltersDTO.getKey());
                            quality.setName(subZbtFiltersDTO.getName());
                        }
                        dataDictionaryRepository.save(quality);
                    }
                }
                // 同步稀有度数据
                if (zbtFiltersDTO.getKey().equals("Rarity")) {
                    DataDictionaryEntity rarityParent = dataDictionaryRepository.findByCode("skin_rarity");
                    if (rarityParent == null) {
                        rarityParent = new DataDictionaryEntity();
                        rarityParent.setName("饰品稀有度");
                        rarityParent.setCode("skin_rarity");
                        dataDictionaryRepository.save(rarityParent);
                    }
                    for (ZBTFiltersDTO subZbtFiltersDTO : zbtFiltersDTO.getList()) {
                        DataDictionaryEntity rarity = dataDictionaryRepository.findByCode(subZbtFiltersDTO.getKey());
                        if (rarity == null) {
                            rarity = new DataDictionaryEntity();
                            rarity.setParentId(rarityParent.getId());
                            rarity.setCode(subZbtFiltersDTO.getKey());
                            rarity.setName(subZbtFiltersDTO.getName());
                        } else {
                            rarity.setParentId(rarityParent.getId());
                            rarity.setCode(subZbtFiltersDTO.getKey());
                            rarity.setName(subZbtFiltersDTO.getName());
                        }
                        dataDictionaryRepository.save(rarity);
                    }
                }
                // 同步饰品原型
                if (zbtFiltersDTO.getKey().equals("Type")) {
                    DataDictionaryEntity prototypeCategoryParent = dataDictionaryRepository.findByCode("skin_prototype_category");
                    if (prototypeCategoryParent == null) {
                        prototypeCategoryParent = new DataDictionaryEntity();
                        prototypeCategoryParent.setName("饰品原型分类");
                        prototypeCategoryParent.setCode("skin_prototype_category");
                        dataDictionaryRepository.save(prototypeCategoryParent);
                    }
                    for (ZBTFiltersDTO subZbtFiltersDTO : zbtFiltersDTO.getList()) {
                        DataDictionaryEntity prototypeCategory = dataDictionaryRepository.findByCode(subZbtFiltersDTO.getKey());
                        if (prototypeCategory == null) {
                            prototypeCategory = new DataDictionaryEntity();
                            prototypeCategory.setParentId(prototypeCategoryParent.getId());
                            prototypeCategory.setCode(subZbtFiltersDTO.getKey());
                            prototypeCategory.setName(subZbtFiltersDTO.getName());
                            prototypeCategory.setImg(subZbtFiltersDTO.getImage());
                        } else {
                            prototypeCategory.setParentId(prototypeCategoryParent.getId());
                            prototypeCategory.setCode(subZbtFiltersDTO.getKey());
                            prototypeCategory.setName(subZbtFiltersDTO.getName());
                            prototypeCategory.setImg(subZbtFiltersDTO.getImage());
                        }
                        dataDictionaryRepository.save(prototypeCategory);
                        for (ZBTFiltersDTO subSubZbtFiltersDTO : subZbtFiltersDTO.getList()) {
                            DataDictionaryEntity prototype = dataDictionaryRepository.findByCode(subSubZbtFiltersDTO.getKey());
                            if (prototype == null) {
                                prototype = new DataDictionaryEntity();
                                prototype.setParentId(prototypeCategory.getId());
                                prototype.setCode(subSubZbtFiltersDTO.getKey());
                                prototype.setName(subSubZbtFiltersDTO.getName());
                                prototype.setImg(subSubZbtFiltersDTO.getImage());
                            } else {
                                prototype.setParentId(prototypeCategory.getId());
                                prototype.setCode(subSubZbtFiltersDTO.getKey());
                                prototype.setName(subSubZbtFiltersDTO.getName());
                                prototype.setImg(subSubZbtFiltersDTO.getImage());
                            }
                            dataDictionaryRepository.save(prototype);
                        }
                    }
                }

            }

        }
    }


    @Async("async-executor-guava")
    @Override
    public void uploadSkinPictureToStorage() {
        String redisSkinKey = redisSkinPrefix + ":skin_info:";
        List<SkinEntity> skinEntityList = skinRepository.findSkinListToUploadStorage();
        int uploadedStorage = 0;
        for (SkinEntity skinEntity : skinEntityList) {
            try {
                skinEntity.setPicture(checkAndUploadImageToStorage(skinEntity.getPicture()));
                skinRepository.save(skinEntity);
                RedisUtils.delete(redisSkinKey + skinEntity.getId());
                uploadedStorage++;
            } catch (Exception e) {
                log.error("上传扎比特图片数据失败：{}", e.getMessage());
            }
        }
        log.info("===>上传storage： {}", uploadedStorage);
    }

    @Async("async-executor-guava")
    @Override
    public void SyncSkinInfo(Long zbtSyncRecordId) {
        Optional<ZbtSyncRecordEntity> zbtSyncRecordEntityOptional = zbtSyncRecordRepository.findById(zbtSyncRecordId);
        zbtSyncRecordEntityOptional.ifPresent(this::SyncSkinInfo);
    }

    private void SyncSkinInfo(ZbtSyncRecordEntity zbtSyncRecordEntity) {
        String redisSkinKey = redisSkinPrefix + ":skin_info:";
        Map<String, String> params = new HashMap<>();
        params.put("appId", "730");
        params.put("app-key", appKey);
        params.put("limit", "200");
        SysExchangeRateEntity sysExchangeRateEntity = sysExchangeRateRepository.findFirstByOrderById();
        if (sysExchangeRateEntity == null) {
            log.error("同步扎比特数据失败：后台未配置汇率");
            zbtSyncRecordEntity.setZbtSyncStatus(ZbtSyncStatus.FAIL);
            zbtSyncRecordEntity.setRemarks("后台未配置汇率");
            zbtSyncRecordRepository.save(zbtSyncRecordEntity);
            return;
        }
        SysSkinRiskManagementEntity sysSkinRiskManagementEntity = sysSkinRiskManagementRepository.findFirstByOrderById();
        if (sysSkinRiskManagementEntity == null) {
            log.error("同步扎比特数据失败：后台未配置饰品风控");
            zbtSyncRecordEntity.setZbtSyncStatus(ZbtSyncStatus.FAIL);
            zbtSyncRecordEntity.setRemarks("后台未配置饰品风控");
            zbtSyncRecordRepository.save(zbtSyncRecordEntity);
            return;
        }
        Integer successTotal = 0;
        Integer uploadedStorage = 0;
        for (Integer page = 1; page <= 30; page++) {
            log.info("同步扎比特数据, 第 {}/30页", page);
            params.put("page", page + "");
            ResponseEntity<String> response = restTemplate.getForEntity(baseUrl + "/v1/product/v2/search?appId={appId}&app-key={app-key}&page={page}&limit={limit}", String.class, params);
            if (response.getStatusCode().equals(HttpStatus.OK)) {
                String responseData = response.getBody();
                JSONObject responseJsonData = JSONObject.parseObject(responseData);
                if (!responseJsonData.getBoolean("success")) {
                    String errorMsg = responseJsonData.getString("errorMsg");
                    log.error("同步扎比特数据失败：{}", errorMsg);
                    zbtSyncRecordEntity.setZbtSyncStatus(ZbtSyncStatus.FAIL);
                    zbtSyncRecordEntity.setRemarks(errorMsg);
                    zbtSyncRecordRepository.save(zbtSyncRecordEntity);
                    return;
                }
                List<JSONObject> zbtJsonObject = responseJsonData.getJSONObject("data").getObject("list", ArrayList.class);
                for (JSONObject jsonObject : zbtJsonObject) {
                    ZBTSkinInfoDTO zbtSkinInfoDTO = jsonObject.toJavaObject(ZBTSkinInfoDTO.class);
                    SkinEntity skin = skinRepository.findByEnglishName(zbtSkinInfoDTO.getMarketHashName());
                    Boolean isNewSkin = false;
                    String pictureUrl = zbtSkinInfoDTO.getImageUrl();
                    // 删除缓存
                    if (skin == null) {
                        skin = new SkinEntity();
                        isNewSkin = true;
                    } else {
                        // 如果图片名相同，则不更新图片
                        if(extractFileName(skin.getPicture()).equals(extractFileName(zbtSkinInfoDTO.getImageUrl()))) {
                            pictureUrl = skin.getPicture();
                        };                        
                        RedisUtils.delete(redisSkinKey + skin.getId());
                    }
                    if (skin.getLockPrice()) {
                        log.info("同步跳过 -- 价格锁定：{}", skin.getId());
                        pictureUrl = checkAndUploadImageToStorage(pictureUrl);
                        skin.setPicture(pictureUrl);
                        skinRepository.save(skin);
                        uploadedStorage++;
                        continue;
                    }
                    Boolean isAbnormal = false;
                    BigDecimal beforePrice = skin.getPrice();
                    BigDecimal nowPrice = zbtSkinInfoDTO.getPriceInfo() == null ? null : zbtSkinInfoDTO.getPriceInfo().getPrice();
                    if (beforePrice == null || nowPrice == null) {
                        log.info("价格异常：{}", skin.getId());
                        isAbnormal = true;
                    } else if (beforePrice.divide(nowPrice, 2, RoundingMode.DOWN).compareTo(sysSkinRiskManagementEntity.getReductionThreshold()) < 0) {
                        log.info("降价超过阈值： {}, {}", skin.getId(), sysSkinRiskManagementEntity.getReductionThreshold());
                        isAbnormal = false;
                    } else if (beforePrice.divide(nowPrice, 2, RoundingMode.DOWN).compareTo(sysSkinRiskManagementEntity.getRiseThreshold()) > 0) {
                        log.info("涨价超过阈值： {}, {}", skin.getId(), sysSkinRiskManagementEntity.getReductionThreshold());
                        isAbnormal = false;
                    }
                    DataDictionaryEntity quality = dataDictionaryRepository.findTopByCodeIgnoreCase(zbtSkinInfoDTO.getQuality());
                    DataDictionaryEntity rarity = dataDictionaryRepository.findTopByCodeIgnoreCase(zbtSkinInfoDTO.getRarity());
                    DataDictionaryEntity exterior = dataDictionaryRepository.findTopByCodeIgnoreCase(zbtSkinInfoDTO.getExterior());
                    if (skin.getPrototype() == null) {
                        DataDictionaryEntity prototype = dataDictionaryRepository
                                .findByNameRightLikeDictName(zbtSkinInfoDTO.getShortName());
                        skin.setPrototype(prototype == null ? null : prototype.getId());
                        log.info("同步扎比特数据, 饰品原型：{}", skin.getPrototype());
                    }                    
                    skin.setItemId(zbtSkinInfoDTO.getItemId());
                    skin.setName(zbtSkinInfoDTO.getItemName());
                    skin.setEnglishName(zbtSkinInfoDTO.getMarketHashName());
                    skin.setShortName(zbtSkinInfoDTO.getShortName());
                    // skin.setPicture(zbtSkinInfoDTO.getImageUrl());
                    
                    skin.setRarity(rarity == null ? null : rarity.getId());
                    skin.setRarityColor(zbtSkinInfoDTO.getRarityColor());
                    skin.setQuality(quality == null ? null : quality.getId());
                    skin.setQualityColor(zbtSkinInfoDTO.getQualityColor());
                    skin.setExterior(exterior == null ? null : exterior.getId());
                    skin.setQuantity(zbtSkinInfoDTO.getPriceInfo() == null ? null : zbtSkinInfoDTO.getPriceInfo().getQuantity());
                    skin.setPrice(zbtSkinInfoDTO.getPriceInfo() == null ? null : zbtSkinInfoDTO.getPriceInfo().getPrice());
                    skin.setAutoDeliverPrice(zbtSkinInfoDTO.getPriceInfo() == null ? null : zbtSkinInfoDTO.getPriceInfo().getAutoDeliverPrice());
                    skin.setAutoDeliverQuantity(zbtSkinInfoDTO.getPriceInfo() == null ? null : zbtSkinInfoDTO.getPriceInfo().getAutoDeliverQuantity());
                    skin.setManualDeliverPrice(zbtSkinInfoDTO.getPriceInfo() == null ? null : zbtSkinInfoDTO.getPriceInfo().getManualDeliverPrice());
                    skin.setManualQuantity(zbtSkinInfoDTO.getPriceInfo() == null ? null : zbtSkinInfoDTO.getPriceInfo().getManualQuantity());
                    // 国际化处理
                    if(isNewSkin) {
                        Map<LanguageEnum, String> translations = new HashMap<LanguageEnum, String>();
                        Arrays.stream(LanguageEnum.getSupportedLanguages()).sequential().forEach(language -> {
                            if(language.equals(LanguageEnum.ENGLISH)) {
                                translations.put(LanguageEnum.ENGLISH, zbtSkinInfoDTO.getMarketHashName());
                            } else if (language.equals(LanguageEnum.CHINESE)) {
                                translations.put(LanguageEnum.CHINESE, zbtSkinInfoDTO.getItemName());
                            } else {
                                translations.put(language, translateToTargetLanguage(zbtSkinInfoDTO.getMarketHashName(), language));
                            }
                        });
                        skin.setI18nFieldName(new I18nField(translations));
                    } else {
                        SkinEntity finalSkin = skin;
                        Arrays.stream(LanguageEnum.getSupportedLanguages()).sequential().forEach(language -> {
                            if(language.equals(LanguageEnum.ENGLISH)) {
                                finalSkin.getI18nFieldName().put(LanguageEnum.ENGLISH, zbtSkinInfoDTO.getMarketHashName());
                            } else if (language.equals(LanguageEnum.CHINESE)) {
                                finalSkin.getI18nFieldName().put(LanguageEnum.CHINESE, zbtSkinInfoDTO.getItemName());
                            } else {
                                if(!finalSkin.getEnglishName().equals(zbtSkinInfoDTO.getMarketHashName()) || StrUtil.isEmpty(finalSkin.getI18nFieldName().get(language))) {
                                    finalSkin.getI18nFieldName().put(language, translateToTargetLanguage(zbtSkinInfoDTO.getMarketHashName(), language));
                                }
                            }
                        });
                    }
                    if (!isAbnormal) {
                        skin.setDiamond(zbtSkinInfoDTO.getPriceInfo() == null ? null : zbtSkinInfoDTO.getPriceInfo().getPrice().multiply(sysExchangeRateEntity.getZbtToCny()).multiply(sysExchangeRateEntity.getZbtToCnyPremium()).setScale(2, BigDecimal.ROUND_UP));
                    } else {
                        skin.setLockPrice(true);
                        skin.setIsAbnormal(true);
                    }
                    if (skin.getDiamond() == null) {
                        log.info("同步跳过 -- 价值钻石为空：{}", skin.getId());
                        pictureUrl = checkAndUploadImageToStorage(pictureUrl);
                        skin.setPicture(pictureUrl);
                        skinRepository.save(skin);
                        uploadedStorage++;
                        continue;
                    }
                    skin.setIsDeleted(false);

                    pictureUrl = checkAndUploadImageToStorage(pictureUrl);
                    skin.setPicture(pictureUrl);
                    skinRepository.save(skin);
                    uploadedStorage++;
                    successTotal += 1;
                }
            }

        }
        zbtSyncRecordEntity.setZbtSyncStatus(ZbtSyncStatus.SUCCESS);
        zbtSyncRecordEntity.setRemarks("同步成功数量：" + successTotal);
        zbtSyncRecordRepository.save(zbtSyncRecordEntity);
        log.info("同步成功数量：: {}, 上传storage： {}", successTotal, uploadedStorage);
    }

    private String checkAndUploadImageToStorage(String pictureUrl) {
        // Handle picture upload to S3
        if (StrUtil.isNotEmpty(pictureUrl)) {
            boolean needsUpload = !pictureUrl.contains(storageService.getStorage().getEndpoint());
            if (needsUpload) {
                try {
                    // Download image
                    ResponseEntity<byte[]> imageResponse = restTemplate.getForEntity(pictureUrl, byte[].class);
                    if (imageResponse.getStatusCode().equals(HttpStatus.OK)) {
                        byte[] imageData = imageResponse.getBody();
                        String fileName = extractFileName(pictureUrl);

                        // Upload to S3
                        StorageEntity storageEntity = storageService.store(
                                new ByteArrayInputStream(imageData),
                                imageData.length,
                                "image/jpeg",
                                fileName
                        );

                        pictureUrl = storageEntity.getUrl();
                        log.info("图片已上传到S3: {}", pictureUrl);
                    }
                } catch (Exception e) {
                    log.error("上传图片到S3失败: {}", e.getMessage());
                }
            }
        }
        return pictureUrl;
    }

    private String extractFileName(String url) {
        String fileName = url.substring(url.lastIndexOf('/') + 1);
        if (!fileName.contains(".")) {
            fileName += ".jpg";
        }
        return fileName;
    }

    @Override
    public void SyncSkinInfoByDB() {
        for (SkinEntity skinEntity : skinRepository.findAll()) {
            if (System.currentTimeMillis() - skinEntity.getUpdateTime().getTime() < skinSyncTime) {
                continue;
            }
            String redisSkinKey = redisSkinPrefix + ":skin_info:";
            Map<String, String> params = new HashMap<>();
            params.put("app-key", appKey);
            SysExchangeRateEntity sysExchangeRateEntity = sysExchangeRateRepository.findFirstByOrderById();
            if (sysExchangeRateEntity == null) {
                log.error("同步扎比特数据失败：后台未配置汇率");
                continue;
            }
            List<String> marketHashNameList = new ArrayList<>();
            marketHashNameList.add(skinEntity.getEnglishName());

            ZBTPriceInfoDTO zbtPriceInfoDTO = new ZBTPriceInfoDTO() {{
                setAppId(730);
                setMarketHashNameList(marketHashNameList);
            }};
            log.info("同步扎比特数据, 饰品名：", skinEntity.getEnglishName());
            ResponseEntity<ZBTPriceInfoVO> response = restTemplate.postForEntity(baseUrl + "/v1/product/price/info?app-key={app-key}", JSONObject.toJSON(zbtPriceInfoDTO), ZBTPriceInfoVO.class, params);
            if (response.getStatusCode().equals(HttpStatus.OK)) {
                ZBTPriceInfoVO zbtPriceInfoVO = response.getBody();
                log.info("扎比特相應：{}", zbtPriceInfoVO.toString());
                if (!zbtPriceInfoVO.getSuccess()) {
                    log.error("同步扎比特数据失败：{}", zbtPriceInfoVO.getErrorMsg());
                    continue;
                }
                List<ZBTPriceInfoVO.PriceInfo> zbtPriceInfoVOList = zbtPriceInfoVO.getData();
                if (zbtPriceInfoVOList.size() != 1) {
                    log.error("同步扎比特数据失败未查到");
                    continue;
                }
                // 删除缓存
                ZBTPriceInfoVO.PriceInfo zbtPriceInfo = zbtPriceInfoVOList.get(0);
                RedisUtils.delete(redisSkinKey + skinEntity.getId());
                skinEntity.setQuantity(zbtPriceInfo.getQuantity() == null ? null : zbtPriceInfo.getQuantity());
                skinEntity.setPrice(zbtPriceInfo.getPrice() == null ? null : zbtPriceInfo.getPrice());
                skinEntity.setAutoDeliverPrice(zbtPriceInfo.getAutoDeliverPrice() == null ? null : zbtPriceInfo.getAutoDeliverPrice());
                skinEntity.setAutoDeliverQuantity(zbtPriceInfo.getAutoDeliverQuantity() == null ? null : zbtPriceInfo.getAutoDeliverQuantity());
                skinEntity.setManualDeliverPrice(zbtPriceInfo.getManualDeliverPrice() == null ? null : zbtPriceInfo.getManualDeliverPrice());
                skinEntity.setManualQuantity(zbtPriceInfo.getManualQuantity() == null ? null : zbtPriceInfo.getManualQuantity());
                skinEntity.setDiamond(zbtPriceInfo.getPrice() == null ? null : zbtPriceInfo.getPrice().multiply(sysExchangeRateEntity.getZbtToCny()).multiply(sysExchangeRateEntity.getZbtToCnyPremium()).setScale(2, BigDecimal.ROUND_UP));
                if (skinEntity.getDiamond() == null) {
                    continue;
                }
                skinEntity.setIsDeleted(false);
                skinRepository.save(skinEntity);
                log.info("同步成功");
            }
        }
    }

    @Override
    public void SyncSkinInfoByDBNew() {
        SysExchangeRateEntity sysExchangeRateEntity = sysExchangeRateRepository.findFirstByOrderById();
        if (sysExchangeRateEntity == null) {
            log.error("同步扎比特数据失败：后台未配置汇率");
            return;
        }
        Pageable pageable = PageRequest.of(0, 200);
        Page<SkinEntity> skinEntities;
        do {
            skinEntities = skinRepository.findAll(pageable);
            List<String> marketHashNameList = skinEntities.stream().map(SkinEntity::getEnglishName).collect(Collectors.toList());
            String redisSkinKey = redisSkinPrefix + ":skin_info:";
            Map<String, String> params = new HashMap<>();
            params.put("app-key", appKey);
            ZBTPriceInfoDTO zbtPriceInfoDTO = new ZBTPriceInfoDTO() {{
                setAppId(730);
                setMarketHashNameList(marketHashNameList);
            }};
            try {
                ResponseEntity<ZBTPriceInfoVO> response = restTemplate.postForEntity(baseUrl + "/v1/product/price/info?app-key={app-key}", JSONObject.toJSON(zbtPriceInfoDTO), ZBTPriceInfoVO.class, params);
                if (response.getStatusCode().equals(HttpStatus.OK)) {
                    ZBTPriceInfoVO zbtPriceInfoVO = response.getBody();
                    log.info("扎比特响应：{}", zbtPriceInfoVO.toString());
                    if (!zbtPriceInfoVO.getSuccess()) {
                        log.error("同步扎比特数据失败：{}", zbtPriceInfoVO.getErrorMsg());
                        continue;
                    }
                    List<ZBTPriceInfoVO.PriceInfo> zbtPriceInfoVOList = zbtPriceInfoVO.getData();
//                if (zbtPriceInfoVOList.size() != 1) {
//                    log.error("同步扎比特数据失败未查到");
//                    continue;
//                }
                    // 删除缓存
                    Map<String, ZBTPriceInfoVO.PriceInfo> nameMap = new HashMap<>();
                    for (ZBTPriceInfoVO.PriceInfo zbtPriceInfo : zbtPriceInfoVOList) {
                        nameMap.put(zbtPriceInfo.getMarketHashName(), zbtPriceInfo);
                    }
                    for (SkinEntity skinEntity : skinEntities.getContent()) {
                        RedisUtils.delete(redisSkinKey + skinEntity.getId());
                        ZBTPriceInfoVO.PriceInfo zbtPriceInfo = nameMap.get(skinEntity.getEnglishName());
                        if (zbtPriceInfo == null) {
                            log.info("扎比特数据未查到,饰品ID {}, 饰品名: {}", skinEntity.getId(), skinEntity.getName());
                            skinEntity.setLockPrice(true);
                            skinEntity.setIsAbnormal(true);
                            continue;
                        }
                        if (zbtPriceInfo.getPrice() == null) {
                            log.info("扎比特数据异常,饰品ID {}, 饰品名: {}", skinEntity.getId(), skinEntity.getName());
                            skinEntity.setLockPrice(true);
                            skinEntity.setIsAbnormal(true);
                            continue;
                        }
                        log.info("成功同步饰品: {}, ID: {}, 历史价格: {}", skinEntity.getName(), skinEntity.getId(), skinEntity.getDiamond());
                        skinEntity.setQuantity(zbtPriceInfo.getQuantity() == null ? null : zbtPriceInfo.getQuantity());
                        skinEntity.setPrice(zbtPriceInfo.getPrice() == null ? null : zbtPriceInfo.getPrice());
                        skinEntity.setAutoDeliverPrice(zbtPriceInfo.getAutoDeliverPrice() == null ? null : zbtPriceInfo.getAutoDeliverPrice());
                        skinEntity.setAutoDeliverQuantity(zbtPriceInfo.getAutoDeliverQuantity() == null ? null : zbtPriceInfo.getAutoDeliverQuantity());
                        skinEntity.setManualDeliverPrice(zbtPriceInfo.getManualDeliverPrice() == null ? null : zbtPriceInfo.getManualDeliverPrice());
                        skinEntity.setManualQuantity(zbtPriceInfo.getManualQuantity() == null ? null : zbtPriceInfo.getManualQuantity());
                        skinEntity.setDiamond(zbtPriceInfo.getPrice() == null ? null : zbtPriceInfo.getPrice().multiply(sysExchangeRateEntity.getZbtToCny()).multiply(sysExchangeRateEntity.getZbtToCnyPremium()).setScale(2, BigDecimal.ROUND_UP));
                        skinEntity.setIsDeleted(false);
                        skinEntity.setIsAbnormal(false);
                        skinEntity.setLockPrice(false);
                        if (zbtPriceInfo.getPrice() == null || skinEntity.getDiamond() == null || skinEntity.getDiamond().compareTo(BigDecimal.ZERO) == 0) {
                            skinEntity.setIsDeleted(true);
                            skinEntity.setIsAbnormal(true);
                        }
                        log.info("同步成功");
                    }
                    skinRepository.saveAll(skinEntities);
//                return;
                }
            } catch (Exception e) {
                log.error("同步失败: {}", e.getMessage());
                try {
                    Thread.sleep(5);
                } catch (InterruptedException ex) {
                    continue;
                }
                continue;
            }
            pageable = pageable.next();
        } while (skinEntities.hasNext());
    }

    @Override
    public void SyncSkinInfoByDBIsSale() {
        SysExchangeRateEntity sysExchangeRateEntity = sysExchangeRateRepository.findFirstByOrderById();
        if (sysExchangeRateEntity == null) {
            log.error("同步扎比特数据失败：后台未配置汇率");
            return;
        }
        Pageable pageable = PageRequest.of(0, 200);
        Page<SkinEntity> skinEntities;
        do {
            skinEntities = skinRepository.findByIsSaleIsTrue(pageable);
            List<String> marketHashNameList = skinEntities.stream().map(SkinEntity::getEnglishName).collect(Collectors.toList());
            String redisSkinKey = redisSkinPrefix + ":skin_info:";
            Map<String, String> params = new HashMap<>();
            params.put("app-key", appKey);
            ZBTPriceInfoDTO zbtPriceInfoDTO = new ZBTPriceInfoDTO() {{
                setAppId(730);
                setMarketHashNameList(marketHashNameList);
            }};
            try {
                ResponseEntity<ZBTPriceInfoVO> response = restTemplate.postForEntity(baseUrl + "/v1/product/price/info?app-key={app-key}", JSONObject.toJSON(zbtPriceInfoDTO), ZBTPriceInfoVO.class, params);
                if (response.getStatusCode().equals(HttpStatus.OK)) {
                    ZBTPriceInfoVO zbtPriceInfoVO = response.getBody();
                    log.info("扎比特响应：{}", zbtPriceInfoVO.toString());
                    if (!zbtPriceInfoVO.getSuccess()) {
                        log.error("同步扎比特数据失败：{}", zbtPriceInfoVO.getErrorMsg());
                        continue;
                    }
                    List<ZBTPriceInfoVO.PriceInfo> zbtPriceInfoVOList = zbtPriceInfoVO.getData();
//                if (zbtPriceInfoVOList.size() != 1) {
//                    log.error("同步扎比特数据失败未查到");
//                    continue;
//                }
                    // 删除缓存
                    Map<String, ZBTPriceInfoVO.PriceInfo> nameMap = new HashMap<>();
                    for (ZBTPriceInfoVO.PriceInfo zbtPriceInfo : zbtPriceInfoVOList) {
                        nameMap.put(zbtPriceInfo.getMarketHashName(), zbtPriceInfo);
                    }
                    for (SkinEntity skinEntity : skinEntities.getContent()) {
                        RedisUtils.delete(redisSkinKey + skinEntity.getId());
                        ZBTPriceInfoVO.PriceInfo zbtPriceInfo = nameMap.get(skinEntity.getEnglishName());
                        if (zbtPriceInfo == null) {
                            log.info("扎比特数据未查到,饰品ID {}, 饰品名: {}", skinEntity.getId(), skinEntity.getName());
                            skinEntity.setLockPrice(true);
                            skinEntity.setIsAbnormal(true);
                            continue;
                        }
                        if (zbtPriceInfo.getPrice() == null) {
                            log.info("扎比特数据异常,饰品ID {}, 饰品名: {}", skinEntity.getId(), skinEntity.getName());
                            skinEntity.setLockPrice(true);
                            skinEntity.setIsAbnormal(true);
                            continue;
                        }
                        log.info("成功同步饰品: {}, ID: {}, 历史价格: {}", skinEntity.getName(), skinEntity.getId(), skinEntity.getDiamond());
                        skinEntity.setQuantity(zbtPriceInfo.getQuantity() == null ? null : zbtPriceInfo.getQuantity());
                        skinEntity.setPrice(zbtPriceInfo.getPrice() == null ? null : zbtPriceInfo.getPrice());
                        skinEntity.setAutoDeliverPrice(zbtPriceInfo.getAutoDeliverPrice() == null ? null : zbtPriceInfo.getAutoDeliverPrice());
                        skinEntity.setAutoDeliverQuantity(zbtPriceInfo.getAutoDeliverQuantity() == null ? null : zbtPriceInfo.getAutoDeliverQuantity());
                        skinEntity.setManualDeliverPrice(zbtPriceInfo.getManualDeliverPrice() == null ? null : zbtPriceInfo.getManualDeliverPrice());
                        skinEntity.setManualQuantity(zbtPriceInfo.getManualQuantity() == null ? null : zbtPriceInfo.getManualQuantity());
                        skinEntity.setDiamond(zbtPriceInfo.getPrice() == null ? null : zbtPriceInfo.getPrice().multiply(sysExchangeRateEntity.getZbtToCny()).multiply(sysExchangeRateEntity.getZbtToCnyPremium()).setScale(2, BigDecimal.ROUND_UP));
                        skinEntity.setIsDeleted(false);
                        skinEntity.setIsAbnormal(false);
                        skinEntity.setLockPrice(false);
                        log.info("同步成功");
                    }
                    skinRepository.saveAll(skinEntities);
//                return;
                }
            } catch (Exception e) {
                log.error("同步失败: {}", e.getMessage());
            }
            pageable = pageable.next();
        } while (skinEntities.hasNext());
    }

    @Override
    public void quickBuy(Long userPackagePickUpId) {
        UserPackagePickupEntity userPackagePickupEntity = userPackagePickupRepository.findById(userPackagePickUpId).get();
        SkinEntity skin = userPackagePickupEntity.getUserPackage().getSkin();
//        if(skin.getPrice().compareTo(userPackagePickupEntity.getUserPackage().getPrice().multiply(BigDecimal.valueOf(1.05)))){
//            userPackagePickupEntity.setRemarks("商品涨价超过百分之五");
//            userPackagePickupEntity.setStatus(PackagePickupStatus.FROZEN);
//            userPackagePickupRepository.save(userPackagePickupEntity);
//            return;
//        }
        if (skin.getPrice().compareTo(userPackagePickupEntity.getUserPackage().getPrice().multiply(BigDecimal.valueOf(1.05))) > 0) {
            if (skin.getPrice().compareTo(BigDecimal.valueOf(4.5)) < 0) {
                log.info("需要审核");
                userPackagePickupEntity.setStatus(PackagePickupStatus.CHECKING);
                userPackagePickupRepository.save(userPackagePickupEntity);
                return;
            }
            userPackagePickupEntity.setRemarks("商品涨价超过百分之五");
            userPackagePickupEntity.setStatus(PackagePickupStatus.FROZEN);
            userPackagePickupRepository.save(userPackagePickupEntity);
            return;
        }
        // 检查交易链接状态
        ZBTSteamInfoVO zbtSteamInfoVO = querySteamInfoByTradeUrl(userPackagePickupEntity.getSteamTradeUrl());
        if (zbtSteamInfoVO == null || !zbtSteamInfoVO.getCheckStatus().equals(1)) {
            userPackagePickupEntity.setRemarks("交易链接不可用");
            userPackagePickupEntity.setStatus(PackagePickupStatus.FROZEN);
            userPackagePickupRepository.save(userPackagePickupEntity);
        }
        JSONObject requestMap = new JSONObject();
        requestMap.put("language", "zh_CN");
        requestMap.put("app-key", appKey);
        ZBTQuickBuyDTO zbtQuickBuyDTO = new ZBTQuickBuyDTO() {{
            setAppid(730);
            setItemId(skin.getItemId());
            setMaxPrice(skin.getPrice());
            setMarketHashName(skin.getEnglishName());
            setOutTradeNo(userPackagePickupEntity.getOrderNo());
            setTradeUrl(userPackagePickupEntity.getSteamTradeUrl());
        }};
        requestMap.put("quickBuyParamV2DTO", zbtQuickBuyDTO);
        ResponseEntity<ZBTQuickBuyVO> response = restTemplate.postForEntity(baseUrl + "/v1/trade/v2/quick-buy?app-key={app-key}&language={language}", JSONObject.toJSON(zbtQuickBuyDTO), ZBTQuickBuyVO.class, requestMap);
        log.info("用户ID: {}, 饰品取回响应: {}", userPackagePickupEntity.getUser().getId(), response.getBody());
        if (response.getStatusCode().equals(HttpStatus.OK)) {
            ZBTQuickBuyVO zbtQuickBuyVO = response.getBody();
            if (!zbtQuickBuyVO.getSuccess()) {
                log.error("取回失败:{}, {}", userPackagePickUpId, zbtQuickBuyVO.getErrorMsg());
                userPackagePickupEntity.setRemarks(zbtQuickBuyVO.getErrorMsg());
                userPackagePickupEntity.setStatus(PackagePickupStatus.FROZEN);
                userPackagePickupRepository.save(userPackagePickupEntity);
//                // 余额不足
//                UserPackageEntity userPackageEntity = userPackageRepository.findById(userPackagePickupEntity.getUserPackage().getId()).orElse(null);
//                if(zbtQuickBuyVO.getErrorCode().equals(103011)){
//                    // 用户steam库未公开,steam账号不可交易
//                    String errorDataDetails = "";
//                    if(zbtQuickBuyVO.getErrorData()!=null){
//                        JSONObject errorData = JSONObject.parseObject(zbtQuickBuyVO.getErrorData().toString());
//                        errorDataDetails = errorData.getString("errorList");
//                    }
//                    userPackagePickupEntity.setRemarks(zbtQuickBuyVO.getErrorMsg() + errorDataDetails);
//                    userPackagePickupEntity.setStatus(PackagePickupStatus.FAIL_STEAM);
//                    userPackagePickupRepository.save(userPackagePickupEntity);
//                    // 饰品还原
//                    if(userPackageEntity!=null){
//                        userPackageEntity.setIsReceived(false);
//                        userPackageRepository.save(userPackageEntity);
//                    }
//                }else {
//                    // 余额不足70001, 无满足条件在售饰品1317, 饰品状态暂时冻结
//                    userPackagePickupEntity.setRemarks(zbtQuickBuyVO.getErrorMsg());
//                    userPackagePickupEntity.setStatus(PackagePickupStatus.FROZEN);
//                    userPackagePickupRepository.save(userPackagePickupEntity);
//                }
                return;
            }
            userPackagePickupEntity.setStatus(PackagePickupStatus.WAIT_SEND);
            userPackagePickupEntity.setThirdOrderNo(response.getBody().getData().getOrderId());
            userPackagePickupRepository.save(userPackagePickupEntity);
        } else {
            log.error("取货接口错误");
        }
    }

    @Override
    public void queryOrderStatus(Long userPackagePickUpId) {
        UserPackagePickupEntity userPackagePickupEntity = userPackagePickupRepository.findById(userPackagePickUpId).get();
        JSONObject requestMap = new JSONObject();
        requestMap.put("language", "zh_CN");
        requestMap.put("app-key", appKey);
        requestMap.put("orderId", userPackagePickupEntity.getThirdOrderNo());
        requestMap.put("outTradeNo", userPackagePickupEntity.getOrderNo());
        ResponseEntity<String> response = restTemplate.getForEntity(baseUrl + "/v1/order/v2/buy/detail?app-key={app-key}&language={language}&orderId={orderId}&outTradeNo={outTradeNo}", String.class, requestMap);
        log.info("取货状态响应: {}", response.getBody());
        if (response.getStatusCode().equals(HttpStatus.OK)) {
            String responseData = response.getBody();
            JSONObject responseJsonData = JSONObject.parseObject(responseData);
            if (responseJsonData.getBoolean("success") == false) {
                String errorMsg = responseJsonData.getString("errorMsg");
                log.error("查询失败：{}", errorMsg);
                return;
            }
            Integer status = responseJsonData.getJSONObject("data").getInteger("status");
            String failedDesc = responseJsonData.getJSONObject("data").getString("failedDesc");
            String failedCode = responseJsonData.getJSONObject("data").getString("failedCode");
            log.info("发货状态, 订单号: {}, 状态: {}, 错误: {}, 错误码: {}", userPackagePickupEntity.getOrderNo(), status, failedDesc, failedCode);
//            订单状态值 1 等待发货 3 等待收货 10 订单成功 11 订单失败
            switch (status) {
                case 1:
                    userPackagePickupEntity.setStatus(PackagePickupStatus.TODO);
                    userPackagePickupRepository.save(userPackagePickupEntity);
                    break;
                case 3:
                    userPackagePickupEntity.setStatus(PackagePickupStatus.WAIT_USER);
                    userPackagePickupRepository.save(userPackagePickupEntity);
                    break;
                case 10:
                    userPackagePickupEntity.setStatus(PackagePickupStatus.SUCCESS);
                    userPackagePickupRepository.save(userPackagePickupEntity);
                    break;
                case 11:
//                    以下错误原因会对买家进行交易罚款
//                            买家交易被封禁
//                    ORDER_DELIVER_FAILED_OF_RECEIVER_TRADE_BAN
//                            买家发起还价
//                    ORDER_FAILED_OF_BUYER_COUNTERED
//                            买家拒绝收货,拒绝了报价
//                    ORDER_FAILED_OF_BUYER_DECLINE
//
//                    自动发货，买家超时不收货, 系统取消
//                    ORDER_FAILED_OF_AUTO_DELIVER_ORDER_CANCEL_OF_BUYER_NOT_ACCEPT
//                    人工发货，买家超时不收货, 系统取消
//                    ORDER_FAILED_OF_MANUAL_DELIVER_ORDER_CANCEL_OF_BUYER_NOT_ACCEPT
//                            买家无法交易
//                    ORDER_DELIVER_FAILED_OF_RECEIVER_TRADE_DISABLE
//                            买家被游戏开发者封禁
//                    ORDER_DELIVER_FAILED_OF_RECEIVER_TRADE_GAME_DEVELOPER_BAN
//                    买家 vac
//                    ORDER_DELIVER_FAILED_OF_RECEIVER_TRADE_VAC
//
//                            买家屏蔽了卖家
//                    ORDER_DELIVER_FAILED_OF_RECEIVER_BLOCK_SENDER
//                            买家库存隐私
//                    ORDER_DELIVER_FAILED_OF_RECEIVER_INVENTORY_PRIVACY
//                            买家库存满
//                    ORDER_DELIVER_FAILED_OF_RECEIVER_INVENTORY_SLOT_FULL
//                            买家交易链接无效
//                    ORDER_DELIVER_FAILED_OF_RECEIVER_TRADE_URL_NOT_VALID
                    switch (failedCode) {
                        case "ORDER_DELIVER_FAILED_OF_RECEIVER_TRADE_BAN":
                        case "ORDER_FAILED_OF_BUYER_COUNTERED":
                        case "ORDER_FAILED_OF_BUYER_DECLINE":
                        case "ORDER_FAILED_OF_AUTO_DELIVER_ORDER_CANCEL_OF_BUYER_NOT_ACCEPT":
                        case "ORDER_FAILED_OF_MANUAL_DELIVER_ORDER_CANCEL_OF_BUYER_NOT_ACCEPT":
                        case "ORDER_DELIVER_FAILED_OF_RECEIVER_TRADE_DISABLE":
                        case "ORDER_DELIVER_FAILED_OF_RECEIVER_TRADE_GAME_DEVELOPER_BAN":
                        case "ORDER_DELIVER_FAILED_OF_RECEIVER_BLOCK_SENDER":
                        case "ORDER_DELIVER_FAILED_OF_RECEIVER_INVENTORY_PRIVACY":
                        case "ORDER_DELIVER_FAILED_OF_RECEIVER_INVENTORY_SLOT_FULL":
                        case "ORDER_DELIVER_FAILED_OF_RECEIVER_TRADE_URL_NOT_VALID":
                            userPackagePickupEntity.setStatus(PackagePickupStatus.FROZEN);
                            userPackagePickupEntity.setRemarks(failedDesc);
                            userPackagePickupRepository.save(userPackagePickupEntity);
                            Integer userCancelCount = userPackagePickupRepository.queryCancelCount(userPackagePickupEntity.getUser().getId());
                            log.info("用户: {} 已经拒绝报价或自身问题次数：{}", userPackagePickupEntity.getUser().getId(), userCancelCount);
                            if (userCancelCount >= 3) {
                                Calendar calendar = Calendar.getInstance();
                                calendar.setTime(new Date());
                                //获取后一天
                                calendar.add(Calendar.DAY_OF_MONTH, 1);
                                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                RedisUtils.save(redisUserPrefix + ":DISABLE_PICKUP:" + userPackagePickupEntity.getUser().getId().toString(), simpleDateFormat.format(calendar.getTime()), redisUserDisablePickupExpire);
                                // 禁用功能
                                UserDisableEntity userDisableEntity = new UserDisableEntity();
                                userDisableEntity.setUser(userDisableEntity.getUser());
                                userDisableEntity.setIsEffective(true);
                                userDisableEntity.setType(UserDisableType.PICK_UP);
                                userDisableEntity.setDisableExpire(calendar.getTime());
                                userDisableEntity.setRemarks("拒绝报价或账号问题超过3次");
                                userDisableRepository.save(userDisableEntity);
                            }
                            break;
                        default:
                            if (userPackagePickupEntity.getOldOrderNo() == null) {
                                SyncSkinInfoOne(userPackagePickupEntity.getUserPackage().getSkin().getId());
                                userPackagePickupEntity.setOldOrderNo(userPackagePickupEntity.getOrderNo());
                                userPackagePickupEntity.setOrderNo(Utils.generateOrderNo(userPackagePickupEntity.getUser().getId(), "PICKUP"));
                                userPackagePickupEntity.setStatus(PackagePickupStatus.PICKUPING);
                                userPackagePickupEntity.setRemarks(responseJsonData.getJSONObject("data").getString("failedDesc"));
                                userPackagePickupRepository.save(userPackagePickupEntity);
                                log.info("取回失败重试:ID: {}, {}", userPackagePickupEntity.getUser().getId(), userPackagePickupEntity.getRemarks());
                            } else {
                                userPackagePickupEntity.setStatus(PackagePickupStatus.CANCEL);
                                userPackagePickupEntity.setRemarks(responseJsonData.getJSONObject("data").getString("failedDesc"));
                                userPackagePickupRepository.save(userPackagePickupEntity);
                                // 饰品还原
                                UserPackageEntity userPackageEntity = userPackageRepository.findById(userPackagePickupEntity.getUserPackage().getId()).orElse(null);
                                if (userPackageEntity != null) {
                                    userPackageEntity.setIsReceived(false);
                                    userPackageRepository.save(userPackageEntity);
                                }
                            }
                            break;

                    }
//                    userPackagePickupEntity.setStatus(PackagePickupStatus.CANCEL);
//                    userPackagePickupEntity.setRemarks(responseJsonData.getJSONObject("data").getString("failedDesc"));
//                    userPackagePickupRepository.save(userPackagePickupEntity);
//                    // 饰品还原
//                    UserPackageEntity userPackageEntity = userPackageRepository.findById(userPackagePickupEntity.getUserPackage().getId()).orElse(null);
//                    if(userPackageEntity!=null){
//                        userPackageEntity.setIsReceived(false);
//                        userPackageRepository.save(userPackageEntity);
//                    }
//                    Integer userCancelCount =  userPackagePickupRepository.queryCancelCount(userPackageEntity.getUser().getId());
//                    log.info("用户: {} 已经拒绝报价次数：{}",userPackageEntity.getUser().getId(), userCancelCount);
//                    if(userCancelCount>=3){
//                        Calendar calendar=Calendar.getInstance();
//                        calendar.setTime(new Date());
//                        //获取后一天
//                        calendar.add(Calendar.DAY_OF_MONTH, 1);
//                        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                        RedisUtils.save(redisUserPrefix + ":DISABLE_PICKUP:" +userPackageEntity.getUser().getId().toString(), simpleDateFormat.format(calendar.getTime()), redisUserDisablePickupExpire);
//                        // 禁用功能
//                        UserDisableEntity userDisableEntity = new UserDisableEntity();
//                        userDisableEntity.setUser(userDisableEntity.getUser());
//                        userDisableEntity.setIsEffective(true);
//                        userDisableEntity.setType(UserDisableType.PICK_UP);
//                        userDisableEntity.setDisableExpire(calendar.getTime());
//                        userDisableEntity.setRemarks("拒绝报价超过3次");
//                        userDisableRepository.save(userDisableEntity);
//                    }
//                    break;


//                case 0:
//                    log.info("未付款");
//                    userPackagePickupEntity.setStatus(PackagePickupStatus.FAIL);
//                    userPackagePickupEntity.setRemarks("未付款");
//                    userPackagePickupRepository.save(userPackagePickupEntity);
//                    break;
//                case 1:
//                    log.info("待处理");
//                    userPackagePickupEntity.setStatus(PackagePickupStatus.TODO);
//                    userPackagePickupRepository.save(userPackagePickupEntity);
//                    break;
//                case 2:
//                    log.info("处理中");
//                    userPackagePickupEntity.setStatus(PackagePickupStatus.DOING);
//                    userPackagePickupRepository.save(userPackagePickupEntity);
//                    break;
//                case 3:
//                    log.info("待对方处理");
//                    userPackagePickupEntity.setStatus(PackagePickupStatus.WAIT_USER);
//                    userPackagePickupRepository.save(userPackagePickupEntity);
//                    break;
//                case 10:
//                    log.info("已完成");
//                    userPackagePickupEntity.setStatus(PackagePickupStatus.SUCCESS);
//                    userPackagePickupRepository.save(userPackagePickupEntity);
//                    break;
//                case 11:
//                    log.info("已取消");
//                    userPackagePickupEntity.setStatus(PackagePickupStatus.CANCEL);
//                    userPackagePickupEntity.setRemarks(responseJsonData.getJSONObject("data").getString("failedDesc"));
//                    userPackagePickupRepository.save(userPackagePickupEntity);
//                    // 饰品还原
//                    UserPackageEntity userPackageEntity = userPackageRepository.findById(userPackagePickupEntity.getUserPackage().getId()).orElse(null);
//                    if(userPackageEntity!=null){
//                        userPackageEntity.setIsReceived(false);
//                        userPackageRepository.save(userPackageEntity);
//                    }
//                    Integer userCancelCount =  userPackagePickupRepository.queryCancelCount(userPackageEntity.getUser().getId());
//                    log.info("用户: {} 已经拒绝报价次数：{}",userPackageEntity.getUser().getId(), userCancelCount);
//                    if(userCancelCount>=3){
//                        Calendar calendar=Calendar.getInstance();
//                        calendar.setTime(new Date());
//                        //获取后一天
//                        calendar.add(Calendar.DAY_OF_MONTH, 1);
//                        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                        RedisUtils.save(redisUserPrefix + ":DISABLE_PICKUP:" +userPackageEntity.getUser().getId().toString(), simpleDateFormat.format(calendar.getTime()), redisUserDisablePickupExpire);
//                        // 禁用功能
//                        UserDisableEntity userDisableEntity = new UserDisableEntity();
//                        userDisableEntity.setUser(userDisableEntity.getUser());
//                        userDisableEntity.setIsEffective(true);
//                        userDisableEntity.setType(UserDisableType.PICK_UP);
//                        userDisableEntity.setDisableExpire(calendar.getTime());
//                        userDisableEntity.setRemarks("拒绝报价超过3次");
//                        userDisableRepository.save(userDisableEntity);
//                    }
//                    break;
            }
        }
    }

    @Async("async-executor-guava")
    @Override
    public void SyncSkinInfoOne(Long skinId) {
        SkinEntity skinEntity = skinRepository.findById(skinId).orElse(null);
        if (skinEntity == null) {
            log.error("同步饰品失败 ：{}", skinId);
            return;
        }
        if (skinEntity.getLockPrice()) {
            log.warn("饰品价格锁定： {}", skinId);
        }
        String redisSkinKey = redisSkinPrefix + ":skin_info:";
        Map<String, String> params = new HashMap<>();
        params.put("app-key", appKey);
        SysExchangeRateEntity sysExchangeRateEntity = sysExchangeRateRepository.findFirstByOrderById();
        if (sysExchangeRateEntity == null) {
            log.error("同步扎比特数据失败：后台未配置汇率");
            return;
        }
        List<String> marketHashNameList = new ArrayList<>();
        marketHashNameList.add(skinEntity.getEnglishName());

        ZBTPriceInfoDTO zbtPriceInfoDTO = new ZBTPriceInfoDTO() {{
            setAppId(730);
            setMarketHashNameList(marketHashNameList);
        }};
        log.info("同步扎比特数据, 饰品名：", skinEntity.getEnglishName());
        ResponseEntity<ZBTPriceInfoVO> response = restTemplate.postForEntity(baseUrl + "/v1/product/price/info?app-key={app-key}", JSONObject.toJSON(zbtPriceInfoDTO), ZBTPriceInfoVO.class, params);
        if (response.getStatusCode().equals(HttpStatus.OK)) {
            ZBTPriceInfoVO zbtPriceInfoVO = response.getBody();
            log.info("扎比特响应：{}", zbtPriceInfoVO.toString());
            if (!zbtPriceInfoVO.getSuccess()) {
                log.error("同步扎比特数据失败：{}", zbtPriceInfoVO.getErrorMsg());
                return;
            }
            List<ZBTPriceInfoVO.PriceInfo> zbtPriceInfoVOList = zbtPriceInfoVO.getData();
            if (zbtPriceInfoVOList.size() != 1) {
                log.error("同步扎比特数据失败未查到");
                return;
            }
            ZBTPriceInfoVO.PriceInfo zbtPriceInfo = zbtPriceInfoVOList.get(0);
            // 判断是否价格异常
            SysSkinRiskManagementEntity sysSkinRiskManagementEntity = sysSkinRiskManagementRepository.findFirstByOrderById();
            Boolean isAbnormal = false;
            BigDecimal beforePrice = skinEntity.getPrice();
            BigDecimal nowPrice = zbtPriceInfo.getPrice();
            if (beforePrice == null || nowPrice == null) {
                log.info("价格异常：{}", skinEntity.getId());
                isAbnormal = true;
            } else if (beforePrice.divide(nowPrice, 2, RoundingMode.DOWN).compareTo(sysSkinRiskManagementEntity.getReductionThreshold()) < 0) {
                log.info("降价超过阈值： {}, {}", skinEntity.getId(), sysSkinRiskManagementEntity.getReductionThreshold());
                isAbnormal = true;
            } else if (beforePrice.divide(nowPrice, 2, RoundingMode.DOWN).compareTo(sysSkinRiskManagementEntity.getRiseThreshold()) > 0) {
                log.info("涨价超过阈值： {}, {}", skinEntity.getId(), sysSkinRiskManagementEntity.getReductionThreshold());
                isAbnormal = true;
            }
            RedisUtils.delete(redisSkinKey + skinEntity.getId());
            skinEntity.setQuantity(zbtPriceInfo.getQuantity() == null ? null : zbtPriceInfo.getQuantity());
            skinEntity.setPrice(zbtPriceInfo.getPrice() == null ? null : zbtPriceInfo.getPrice());
            skinEntity.setAutoDeliverPrice(zbtPriceInfo.getAutoDeliverPrice() == null ? null : zbtPriceInfo.getAutoDeliverPrice());
            skinEntity.setAutoDeliverQuantity(zbtPriceInfo.getAutoDeliverQuantity() == null ? null : zbtPriceInfo.getAutoDeliverQuantity());
            skinEntity.setManualDeliverPrice(zbtPriceInfo.getManualDeliverPrice() == null ? null : zbtPriceInfo.getManualDeliverPrice());
            skinEntity.setManualQuantity(zbtPriceInfo.getManualQuantity() == null ? null : zbtPriceInfo.getManualQuantity());
            if (!isAbnormal) {
                skinEntity.setDiamond(zbtPriceInfo.getPrice() == null ? null : zbtPriceInfo.getPrice().multiply(sysExchangeRateEntity.getZbtToCny()).multiply(sysExchangeRateEntity.getZbtToCnyPremium()).setScale(2, BigDecimal.ROUND_UP));
            } else {
                skinEntity.setLockPrice(true);
                skinEntity.setIsAbnormal(true);
            }
            if (skinEntity.getDiamond() == null) {
                return;
            }
            skinEntity.setIsDeleted(false);
            skinEntity.setUpdateTime(new Date());
            skinRepository.save(skinEntity);
            log.info("同步成功");
        }
    }

    @Async("async-executor-guava")
    @Override
    public void SycnSKinList(List<SkinEntity> skinEntityList) {
        List<String> marketHashNameList = new ArrayList<>();
        for (SkinEntity skinEntity : skinEntityList) {
            if (System.currentTimeMillis() - skinEntity.getUpdateTime().getTime() > skinSyncTime) {
                log.info("饰品上次同步时间大于{}毫秒: {} {}", skinSyncTime, skinEntity.getId(), skinEntity.getName());
                marketHashNameList.add(skinEntity.getEnglishName());
                continue;
            }
            log.info("跳过同步, 饰品上次同步时间小于{}毫秒: {} {} {}", skinSyncTime, skinEntity.getId(), skinEntity.getName(), skinEntity.getUpdateTime());
        }
        if (marketHashNameList.size() == 0) {
            return;
        }
        Map<String, String> params = new HashMap<>();
        params.put("app-key", appKey);
        SysExchangeRateEntity sysExchangeRateEntity = sysExchangeRateRepository.findFirstByOrderById();
        if (sysExchangeRateEntity == null) {
            log.error("同步扎比特数据失败：后台未配置汇率");
            return;
        }
        ZBTPriceInfoDTO zbtPriceInfoDTO = new ZBTPriceInfoDTO() {{
            setAppId(730);
            setMarketHashNameList(marketHashNameList);
        }};
        ResponseEntity<ZBTPriceInfoVO> response = restTemplate.postForEntity(baseUrl + "/v1/product/price/info?app-key={app-key}", JSONObject.toJSON(zbtPriceInfoDTO), ZBTPriceInfoVO.class, params);
        if (response.getStatusCode().equals(HttpStatus.OK)) {
            ZBTPriceInfoVO zbtPriceInfoVO = response.getBody();
            log.info("扎比特响应：{}", zbtPriceInfoVO.toString());
            if (!zbtPriceInfoVO.getSuccess()) {
                log.error("同步扎比特数据失败：{}", zbtPriceInfoVO.getErrorMsg());
                return;
            }
            List<ZBTPriceInfoVO.PriceInfo> zbtPriceInfoVOList = zbtPriceInfoVO.getData();
            if (zbtPriceInfoVOList.size() == 0) {
                log.error("同步扎比特数据失败未查到");
                return;
            }
            List<SkinEntity> successSkin = new ArrayList<>();
            SysSkinRiskManagementEntity sysSkinRiskManagementEntity = sysSkinRiskManagementRepository.findFirstByOrderById();
            for (ZBTPriceInfoVO.PriceInfo zbtPriceInfo : zbtPriceInfoVOList) {
                SkinEntity skin = skinRepository.findByEnglishName(zbtPriceInfo.getMarketHashName());
                if (skin == null) {
                    log.info("数据库不存在：{}", zbtPriceInfo.getMarketHashName());
                    continue;
                }
                if (skin.getLockPrice() != null && skin.getLockPrice()) {
                    log.info("价格锁定跳过同步");
                    continue;
                }
//                RedisUtils.delete(redisSkinKey + skin.getId());
                // 判断是否价格异常
                Boolean isAbnormal = false;
                BigDecimal beforePrice = skin.getPrice();
                BigDecimal nowPrice = zbtPriceInfo.getPrice();
                if (beforePrice == null || nowPrice == null) {
                    log.info("价格异常：{}", skin.getId());
                    isAbnormal = true;
                } else if (beforePrice.divide(nowPrice, 2, RoundingMode.DOWN).compareTo(sysSkinRiskManagementEntity.getReductionThreshold()) < 0) {
                    log.info("降价超过阈值： {}, {}", skin.getId(), sysSkinRiskManagementEntity.getReductionThreshold());
                    isAbnormal = true;

                } else if (beforePrice.divide(nowPrice, 2, RoundingMode.DOWN).compareTo(sysSkinRiskManagementEntity.getRiseThreshold()) > 0) {
                    log.info("涨价超过阈值： {}, {}", skin.getId(), sysSkinRiskManagementEntity.getReductionThreshold());
                    isAbnormal = true;
                }
                skin.setQuantity(zbtPriceInfo.getQuantity() == null ? null : zbtPriceInfo.getQuantity());
                skin.setPrice(zbtPriceInfo.getPrice() == null ? null : zbtPriceInfo.getPrice());
                skin.setAutoDeliverPrice(zbtPriceInfo.getAutoDeliverPrice() == null ? null : zbtPriceInfo.getAutoDeliverPrice());
                skin.setAutoDeliverQuantity(zbtPriceInfo.getAutoDeliverQuantity() == null ? null : zbtPriceInfo.getAutoDeliverQuantity());
                skin.setManualDeliverPrice(zbtPriceInfo.getManualDeliverPrice() == null ? null : zbtPriceInfo.getManualDeliverPrice());
                skin.setManualQuantity(zbtPriceInfo.getManualQuantity() == null ? null : zbtPriceInfo.getManualQuantity());
                if (!isAbnormal) {
                    skin.setDiamond(zbtPriceInfo.getPrice() == null ? null : zbtPriceInfo.getPrice().multiply(sysExchangeRateEntity.getZbtToCny()).multiply(sysExchangeRateEntity.getZbtToCnyPremium()).setScale(2, BigDecimal.ROUND_UP));
                } else {
                    skin.setLockPrice(true);
                    skin.setIsAbnormal(true);
                }
                if (skin.getDiamond() == null) {
                    continue;
                }
                skin.setUpdateTime(new Date());
                skin.setIsDeleted(false);
                successSkin.add(skin);
            }
            log.info("同步成功数量: {}", successSkin.size());
            skinRepository.saveAll(successSkin);
        }
    }

    @Override
    public ZBTSteamInfoVO querySteamInfoByTradeUrl(String tradeUrl) {
        JSONObject requestMap = new JSONObject();
        requestMap.put("appId", "730");
        requestMap.put("language", "zh_CN");
        requestMap.put("app-key", appKey);
        requestMap.put("tradeUrl", tradeUrl);
        requestMap.put("type", 1);
        log.info("requestMap: {}", requestMap.toString());
        ResponseEntity<String> response = restTemplate.getForEntity(baseUrl + "/v1/user/steam-info?app-key={app-key}&language={language}&appId={appId}&type={type}&tradeUrl={tradeUrl}", String.class, requestMap);
        log.info("扎比特响应：{}", response.getBody());
        if (response.getStatusCode().equals(HttpStatus.OK)) {
            String responseData = response.getBody();
            JSONObject responseJsonData = JSONObject.parseObject(responseData);
            if (responseJsonData.getBoolean("success") == false) {
                return null;
            }
            ZBTSteamInfoVO zbtSteamInfoVO = JSONObject.toJavaObject(responseJsonData.getJSONObject("data"), ZBTSteamInfoVO.class);
            return zbtSteamInfoVO;
        }
        return null;
    }

    @Override
    public Boolean createSteamInfoByTradeUrl(String tradeUrl) {
        Map<String, String> params = new HashMap<>();
        params.put("app-key", appKey);
        ZBTSteamCheckCreateDTO zbtSteamCheckCreateDTO = new ZBTSteamCheckCreateDTO() {{
            setType(1);
            setAppId(730);
            setTradeUrl(tradeUrl);
        }};
        log.info("创建Steam账号状态检测：{}", tradeUrl);
        ResponseEntity<JSONObject> response = restTemplate.postForEntity(baseUrl + "/v1/user/steam-check/create?app-key={app-key}", JSONObject.toJSON(zbtSteamCheckCreateDTO), JSONObject.class, params);
        if (response.getStatusCode().equals(HttpStatus.OK)) {
            JSONObject responseJson = response.getBody();
            log.info("创建Steam账号状态检测：{}", responseJson.toString());
            if (responseJson.getBoolean("success") == false) {
                log.error("创建Steam账号状态检测：{}", responseJson.getString("errorMsg"));
                return false;
            }
            return true;
        }
        log.error("创建Steam账号状态检测接口异常：{}", response.getBody());
        return false;
    }

    @Override
    public void SycnSkinSaleState() {
        ResponseEntity<String> response = restTemplate.getForEntity("https://api.eeskins.com/api/acc?page=1&page_size=883&sort=price&order=desc&name=&min_price=0.1", String.class);
        if (response.getStatusCode().equals(HttpStatus.OK)) {
            String responseData = response.getBody();
            JSONObject responseJsonData = JSONObject.parseObject(responseData);
            if (responseJsonData.getInteger("status_code") == 1000) {
                JSONArray jsonArray = responseJsonData.getJSONObject("data").getJSONArray("data");
                for (int i = 0; i < jsonArray.size(); i++) {
                    SkinEntity skin = skinRepository.findTopByName(jsonArray.getJSONObject(i).getString("name"));
                    if (skin != null) {
                        skin.setIsSale(true);
                        skinRepository.save(skin);
                    }
                }
            }
        }
    }

//    @Async("async-executor-guava")
//    @Override
//    public void syncUserPackageSkin(List<Long> userpackageIds) {
//        List<SkinEntity> skinEntityList = new ArrayList<>();
//        for(Long userPackageId: userpackageIds){
//            UserPackageEntity userPackageEntity = userPackageRepository.findById(userPackageId).orElse(null);
//            if(userPackageEntity != null){
//                skinEntityList.add(userPackageEntity.getSkin());
//            }
//        }
//        SycnSKinList(skinEntityList);
//    }

    /**
     * 翻译到目标语言（保持向后兼容）
     * @deprecated 请使用 TranslationUtils.translateFromEnglish() 方法
     */
    @Deprecated
    private String translateToTargetLanguage(String englishText, LanguageEnum targetLanguage) {
        return translationUtils.translateFromEnglish(englishText, targetLanguage);
    }
}
