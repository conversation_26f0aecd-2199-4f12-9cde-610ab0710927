package com.steamgo1.csgoskincommon.service;

public interface AlgorithmService {
    /**
     * 获取公共hash
     *
     * @param secretHash
     * @param secretSalt
     * @return
     */
    String getPublicHash(String secretHash, String secretSalt);

    /**
     * 获取Roll点
     *
     * @param secretHash
     * @param clientSeed
     * @param round
     * @return
     */
    Integer getRoll(String secretHash, String clientSeed, Integer round);
}
