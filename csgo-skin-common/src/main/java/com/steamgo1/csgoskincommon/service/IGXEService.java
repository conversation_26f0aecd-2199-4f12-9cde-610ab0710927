package com.steamgo1.csgoskincommon.service;

import com.steamgo1.csgoskincommon.entity.SkinEntity;
import com.steamgo1.csgoskincommon.vo.IGXESteamIdVO;

import java.util.List;

public interface IGXEService {
    /**
     * 同步所有饰品
     *
     * @param
     */
    void SyncSkinInfo(Long zbtSyncRecordId);

    /**
     * 购买
     *
     * @param userPackagePickUpId
     * @return
     */
    void quickBuy(Long userPackagePickUpId);


    /**
     * 查询订单状态
     *
     * @param userPackagePickUpId
     */
    void queryOrderStatus(Long userPackagePickUpId);

    /**
     * 同步单个饰品
     *
     * @param skinId
     */
    void SyncSkinInfoOne(Long skinId);

    /**
     * 查询用户steamId;
     */
    IGXESteamIdVO queryUserSteamId(String trackLink);

    /**
     * 同步饰品列表
     *
     * @param skinEntityList
     */
    void sycnSKinList(List<SkinEntity> skinEntityList);
}
