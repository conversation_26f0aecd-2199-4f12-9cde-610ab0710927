package com.steamgo1.csgoskincommon.service.impl;

import com.steamgo1.csgoskincommon.dao.UserLogRepository;
import com.steamgo1.csgoskincommon.entity.UserLogEntity;
import com.steamgo1.csgoskincommon.service.UserLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UserLogServiceImpl implements UserLogService {
    @Autowired
    private UserLogRepository userLogRepository;

    @Override
    public UserLogEntity save(UserLogEntity userLoginLogEntity) {
        if (userLoginLogEntity == null) {
            return null;
        }
        userLogRepository.save(userLoginLogEntity);
        return userLogRepository.save(userLoginLogEntity);
    }
}
