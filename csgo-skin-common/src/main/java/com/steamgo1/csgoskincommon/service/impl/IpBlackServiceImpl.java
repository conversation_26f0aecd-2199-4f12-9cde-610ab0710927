package com.steamgo1.csgoskincommon.service.impl;

import com.steamgo1.csgoskincommon.dao.IpBlackRepository;
import com.steamgo1.csgoskincommon.entity.IpBlackEntity;
import com.steamgo1.csgoskincommon.entity.enums.Status;
import com.steamgo1.csgoskincommon.service.IpBlackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class IpBlackServiceImpl implements IpBlackService {

    @Autowired
    private IpBlackRepository ipBlackRepository;


    @Override
    public List<IpBlackEntity> findByIp(String ip, Status status) {
        if (StringUtils.isBlank(ip)) {
            return Collections.emptyList();
        }
        List<IpBlackEntity> ipBlackEntities = ipBlackRepository.findByIpAndStatus(ip, status);
        return ipBlackEntities;
    }
}
