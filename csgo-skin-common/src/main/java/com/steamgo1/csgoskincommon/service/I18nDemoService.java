package com.steamgo1.csgoskincommon.service;

import com.steamgo1.csgoskincommon.entity.I18nDemoEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 国际化演示服务接口
 * 
 * <AUTHOR>
 */
public interface I18nDemoService {
    
    /**
     * 保存
     */
    I18nDemoEntity save(I18nDemoEntity entity);
    
    /**
     * 根据ID查找
     */
    I18nDemoEntity findById(Long id);
    
    /**
     * 查找所有
     */
    List<I18nDemoEntity> findAll();
    
    /**
     * 分页查找
     */
    Page<I18nDemoEntity> findAll(Pageable pageable);
    
    /**
     * 删除
     */
    void deleteById(Long id);
}
