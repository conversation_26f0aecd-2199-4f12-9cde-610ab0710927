package com.steamgo1.csgoskincommon.service.impl;

import com.steamgo1.csgoskincommon.dao.I18nDemoRepository;
import com.steamgo1.csgoskincommon.entity.I18nDemoEntity;
import com.steamgo1.csgoskincommon.service.I18nDemoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 国际化演示服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class I18nDemoServiceImpl implements I18nDemoService {

    @Autowired
    private I18nDemoRepository i18nDemoRepository;

    @Override
    public I18nDemoEntity save(I18nDemoEntity entity) {
        return i18nDemoRepository.save(entity);
    }
    
    @Override
    public I18nDemoEntity findById(Long id) {
        return i18nDemoRepository.findById(id).orElse(null);
    }
    
    @Override
    public List<I18nDemoEntity> findAll() {
        return i18nDemoRepository.findAll();
    }
    
    @Override
    public Page<I18nDemoEntity> findAll(Pageable pageable) {
        return i18nDemoRepository.findAll(pageable);
    }
    
    @Override
    public void deleteById(Long id) {
        i18nDemoRepository.deleteById(id);
    }
}
