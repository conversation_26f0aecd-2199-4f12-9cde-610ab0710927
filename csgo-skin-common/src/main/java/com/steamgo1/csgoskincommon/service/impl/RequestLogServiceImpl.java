package com.steamgo1.csgoskincommon.service.impl;

import com.steamgo1.csgoskincommon.dao.RequestLogRepository;
import com.steamgo1.csgoskincommon.entity.RequestLogEntity;
import com.steamgo1.csgoskincommon.service.RequestLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class RequestLogServiceImpl implements RequestLogService {

    @Autowired
    private RequestLogRepository requestLogRepository;

    @Override
    public void save(RequestLogEntity requestLog) {
        requestLogRepository.save(requestLog);
    }
}
