package com.steamgo1.csgoskincommon.service.impl;

import com.steamgo1.csgoskincommon.dao.CaseSkinRepository;
import com.steamgo1.csgoskincommon.dao.RollHomeSkinRepository;
import com.steamgo1.csgoskincommon.dao.SkinRepository;
import com.steamgo1.csgoskincommon.entity.CaseSkinEntity;
import com.steamgo1.csgoskincommon.entity.RollHomeSkinEntity;
import com.steamgo1.csgoskincommon.entity.SkinEntity;
import com.steamgo1.csgoskincommon.service.CleanCacheService;
import com.steamgo1.csgoskincommon.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class CleanCacheServiceImpl implements CleanCacheService {

    @Value("${spring.redis.prefix.case}")
    private String redisCasePrefix;

    @Value("${spring.redis.prefix.roll-home}")
    private String redisRollHomePrefix;

    @Autowired
    private CaseSkinRepository caseSkinRepository;

    @Autowired
    private RollHomeSkinRepository rollHomeSkinRepository;

    @Autowired
    private SkinRepository skinRepository;

    @Override
    @Async("async-executor-guava")
    public void cleanSkinCache(Long skinId) {
        SkinEntity skinEntity = skinRepository.findById(skinId).orElse(null);
        if (skinEntity == null) {
            return;
        }
        List<CaseSkinEntity> caseSkinEntityList = caseSkinRepository.findBySkin(skinEntity);
        caseSkinRepository.flush();
        for (CaseSkinEntity caseSkinEntity : caseSkinEntityList) {
            String redisCaseKey = redisCasePrefix + ":case_info:" + caseSkinEntity.getCaseEntity().getId();
            if (RedisUtils.hasKey(redisCaseKey)) {
                RedisUtils.delete(redisCaseKey);
            }
        }
        List<RollHomeSkinEntity> rollHomeSkinEntityList = rollHomeSkinRepository.findBySkin(skinEntity);
        for (RollHomeSkinEntity rollHomeSkinEntity : rollHomeSkinEntityList) {
            String redisRollHomeBaseInfoKey = redisRollHomePrefix + ":BASEINFO:" + rollHomeSkinEntity.getRollHome().getId();
            String redisRollHomeKey = redisRollHomePrefix + ":" + rollHomeSkinEntity.getRollHome().getId();
            if (RedisUtils.hasKey(redisRollHomeBaseInfoKey)) {
                RedisUtils.delete(redisRollHomeBaseInfoKey);
            }
            if (RedisUtils.hasKey(redisRollHomeKey)) {
                RedisUtils.delete(redisRollHomeKey);
            }
        }
    }
}
