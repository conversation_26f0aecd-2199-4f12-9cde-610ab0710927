package com.steamgo1.csgoskincommon.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel("扎比特创建Steam账号状态检测参数")
public class ZBTSteamCheckCreateDTO {
    private Integer type;
    private Integer appId;
    private String tradeUrl;
    /**
     * type
     * integer  |
     * null
     * 必需
     * 不同检测场景，1为购买，2为出售
     *
     * appId
     * integer  |
     * null
     * 游戏id
     * 必需
     * tradeUrl
     * string  |
     * null
     * 交易链接
     */
}
