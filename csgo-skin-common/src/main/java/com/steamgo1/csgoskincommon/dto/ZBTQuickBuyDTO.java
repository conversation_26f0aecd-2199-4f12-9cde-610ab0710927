package com.steamgo1.csgoskincommon.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("扎比特快速购买参数")
public class ZBTQuickBuyDTO {
    private Integer appid;
    private Integer delivery;
    private Long itemId;

    private Integer lowPrice;

    private String marketHashName;

    private BigDecimal maxPrice;


    private String outTradeNo;

    private String tradeUrl;
}
