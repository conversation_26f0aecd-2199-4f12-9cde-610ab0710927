package com.steamgo1.csgoskincommon.dto;


import com.steamgo1.csgoskincommon.enums.RabbitActivityType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("活动robbit信息")
public class RabbitActivityVO {
    @ApiModelProperty("rabbit信息ID")
    private String messageId;

    // 活动类型
    @ApiModelProperty("活动类型")
    private RabbitActivityType type;

    // ID
    @ApiModelProperty("活动ID")
    private Long id;
}
