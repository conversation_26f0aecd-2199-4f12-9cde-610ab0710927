package com.steamgo1.csgoskincommon.dto;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("扎比特饰品查询结果")
@JsonIgnoreProperties(ignoreUnknown = true)
public class ZBTSkinInfoDTO {
    Long itemId;

    String itemName;

    String marketHashName;

    String shortName;

    String imageUrl;

    String type;

    String typeName;

    String quality;

    String qualityName;

    String qualityColor;

    String rarity;

    String rarityName;

    String rarityColor;

    String exterior;

    String exteriorName;

    PriceInfo priceInfo;


    @Data
    public static class PriceInfo {
        Long userId;
        Integer quantity;
        BigDecimal price;
        Integer autoDeliverQuantity;
        BigDecimal autoDeliverPrice;
        Integer manualQuantity;
        BigDecimal manualDeliverPrice;

    }
}
