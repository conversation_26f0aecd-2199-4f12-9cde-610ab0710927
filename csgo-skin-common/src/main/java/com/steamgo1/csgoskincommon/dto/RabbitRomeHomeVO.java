package com.steamgo1.csgoskincommon.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("roll房robbit信息")
public class RabbitRomeHomeVO {
    // 信息ID
    @ApiModelProperty("rabbit信息ID")
    private String messageId;

    // 创建时间
    @ApiModelProperty("创建时间")
    private Date createTime;

    // RollHomeID
    @ApiModelProperty("rollHomeID")
    private Long rollHomeId;
}
