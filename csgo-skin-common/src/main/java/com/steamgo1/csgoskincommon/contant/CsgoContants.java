package com.steamgo1.csgoskincommon.contant;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public final class CsgoContants {

    public final static List<String> BLACK_LIST_SKIN_NAME = Collections.unmodifiableList(new ArrayList<>(Arrays.asList("音乐", "印花")));
    public final static Long ADMIN_ID = 9L;

    /**
     * 类型
     */
    public final class rollHomeJoinParam {
        public static final String ROLL_HOME_JOIN_DATA = "rollHomeJoinData";
    }

    /**
     * 百度地址参数名
     */
    public final class baiduColumnName {
        public static final String BAIDU_PLAN = "plan";
        public static final String BAIDU_UNIT = "unit";
        public static final String BAIDU_KEYWORD_ID2 = "e_keywordid2";
    }

    /**
     * 数字常量
     */
    public final class levelNumber {
        public static final int LEVEL_1 = 0;
        public static final int LEVEL_2 = 1;
        public static final int LEVEL_3 = 2;
        public static final int LEVEL_4 = 3;
        public static final int LEVEL_5 = 4;
    }

    /**
     * 自动创建日roll房名称
     */
    public final class rollHomeName {
        public final static String ROLL_HOME_50 = "roll.home.name.50";
        public final static String ROLL_HOME_100 = "roll.home.name.100";
        public final static String ROLL_HOME_500 = "roll.home.name.500";
        public final static String ROLL_HOME_1000 = "roll.home.name.1000";
        public final static String ROLL_HOME_2000 = "roll.home.name.2000";
        public final static String ROLL_HOME_5000 = "roll.home.name.5000";
    }

    /**
     * 自动创建周roll房名称
     */
    public final class weekRollHomeName {
        public final static String MEET_ROLL_HOME_500 = "week.roll.home.name.500";
        public final static String MEET_ROLL_HOME_2000 = "week.roll.home.name.2000";
        public final static String MEET_ROLL_HOME_5000 = "week.roll.home.name.5000";
        public final static String MEET_ROLL_HOME_10000 = "week.roll.home.name.10000";
    }

    public final class pageOffset {
        public final static long PAGE_SIZE_1 = 100L;
        public final static long PAGE_SIZE_2 = 500L;
        public final static long PAGE_SIZE_3 = 1000L;
        public final static long PAGE_SIZE_4 = 2000L;
    }

    /**
     * redis超时时间
     */
    public final class redisTimeout {
        public final static long REDIS_TIMEOUT_30S = 30L;
        public final static long REDIS_TIMEOUT_60S = 60L;
        public final static long REDIS_TIMEOUT_1H = 3600L;
        public final static long REDIS_TIMEOUT_1D = 86400L;
    }

    /**
     * queue
     */
    public final class rollHomeUser {
        public final static String ROLL_HOME_USER_1H = "rollhomeuser.1h.binding";
        public final static String ROLL_HOME_USER_2H = "rollhomeuser.2h.binding";
        public final static String ROLL_HOME_USER_3H = "rollhomeuser.3h.binding";
        public final static String ROLL_HOME_USER_4H = "rollhomeuser.4h.binding";
        public final static String ROLL_HOME_USER_5H = "rollhomeuser.5h.binding";
        public final static String ROLL_HOME_USER_6H = "rollhomeuser.6h.binding";
    }
}
