package com.steamgo1.csgoskincommon.jpa;

import org.hibernate.boot.model.naming.Identifier;
import org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl;
import org.hibernate.engine.jdbc.env.spi.JdbcEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LowerCasePhysicalNamingStrategy extends PhysicalNamingStrategyStandardImpl {
    Logger log = LoggerFactory.getLogger(LowerCasePhysicalNamingStrategy.class);
    @Override
    public Identifier toPhysicalTableName(Identifier name, JdbcEnvironment context) {
        // 将表名转换为小写
        log.info("toPhysicalTableName: {}", name.getText());
        return Identifier.toIdentifier(name.getText().toLowerCase(), name.isQuoted());
    }

    @Override
    public Identifier toPhysicalColumnName(Identifier name, JdbcEnvironment context) {
        // 可选：将列名也转换为小写
            log.info("toPhysicalColumnName: {}", name.getText());
        return Identifier.toIdentifier(name.getText().toLowerCase(), name.isQuoted());
    }
}
