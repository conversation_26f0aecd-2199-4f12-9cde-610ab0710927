# Meta OCPC SQL 脚本使用指南

## 概述

本指南提供了 Meta OCPC 系统的完整数据库脚本，包括表创建、数据迁移和维护脚本。

## 脚本文件说明

### 1. `create_meta_ocpc_tables.sql`
**用途**: 创建 Meta OCPC 相关的数据库表
**包含内容**:
- `ocpc_meta_token` 表：存储 Facebook/Instagram API 访问凭证
- `ocpc_meta_data` 表：存储 Meta OCPC 转化数据
- 基础索引和外键约束
- 示例数据插入

### 2. `create_complete_ocpc_system.sql`
**用途**: 创建完整的 OCPC 系统（包含所有平台）
**包含内容**:
- 所有 OCPC 平台的表结构（百度、Meta、Google、360）
- 完整的索引策略
- 外键约束
- 示例配置数据
- 数据验证查询

### 3. `migrate_meta_ocpc_data.sql`
**用途**: 迁移和优化现有的 Meta OCPC 数据
**包含内容**:
- 数据备份
- 数据完整性检查
- 数据清理和标准化
- 索引优化
- 迁移验证

## 使用步骤

### 步骤 1: 全新安装

如果是全新安装，执行完整系统脚本：

```sql
-- 执行完整的 OCPC 系统创建脚本
SOURCE /path/to/create_complete_ocpc_system.sql;
```

### 步骤 2: 仅安装 Meta OCPC

如果只需要 Meta OCPC 功能：

```sql
-- 执行 Meta OCPC 表创建脚本
SOURCE /path/to/create_meta_ocpc_tables.sql;
```

### 步骤 3: 数据迁移

如果需要迁移现有数据：

```sql
-- 执行数据迁移脚本
SOURCE /path/to/migrate_meta_ocpc_data.sql;
```

## 表结构详解

### ocpc_meta_token 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint(20) | 主键ID |
| access_token | varchar(512) | Facebook访问令牌 |
| pixel_id | varchar(64) | Facebook像素ID |
| relm_name | varchar(256) | 域名 |
| app_id | varchar(64) | Facebook应用ID |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |

### ocpc_meta_data 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint(20) | 主键ID |
| ocpc_channel_id | bigint(20) | 渠道ID（外键） |
| user_id | bigint(20) | 用户ID（外键） |
| order_charge_id | bigint(20) | 订单ID（外键） |
| type | varchar(20) | 事件类型（CLICK/REGISTER/CHARGE） |
| event_id | varchar(64) | 事件ID |
| campaign_id | varchar(32) | 广告系列ID |
| adset_id | varchar(32) | 广告组ID |
| ad_id | varchar(32) | 广告ID |
| click_id | varchar(128) | Facebook点击ID (fbclid) |
| event_value | decimal(10,2) | 事件价值 |
| ip | varchar(64) | 用户IP |
| user_agent | varchar(512) | 用户代理 |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |

## 配置示例

### 1. 添加 Meta Token 配置

```sql
INSERT INTO ocpc_meta_token (access_token, pixel_id, relm_name, app_id) 
VALUES (
    'EAABwzLixnjYBO...your_access_token',
    '123456789012345',
    'yourdomain.com',
    '123456789012345'
);
```

### 2. 添加渠道配置

```sql
INSERT INTO ocpc_channel (name, relm_name) 
VALUES ('Meta Facebook渠道', 'yourdomain.com');
```

## 常用查询

### 1. 查看 Meta OCPC 数据统计

```sql
SELECT 
    type,
    COUNT(*) as count,
    DATE(create_time) as date
FROM 
    ocpc_meta_data 
WHERE 
    create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY 
    type, DATE(create_time)
ORDER BY 
    date DESC, type;
```

### 2. 查看转化漏斗

```sql
SELECT 
    c.name as channel_name,
    SUM(CASE WHEN md.type = 'CLICK' THEN 1 ELSE 0 END) as clicks,
    SUM(CASE WHEN md.type = 'REGISTER' THEN 1 ELSE 0 END) as registers,
    SUM(CASE WHEN md.type = 'CHARGE' THEN 1 ELSE 0 END) as charges,
    ROUND(
        SUM(CASE WHEN md.type = 'REGISTER' THEN 1 ELSE 0 END) * 100.0 / 
        NULLIF(SUM(CASE WHEN md.type = 'CLICK' THEN 1 ELSE 0 END), 0), 2
    ) as click_to_register_rate,
    ROUND(
        SUM(CASE WHEN md.type = 'CHARGE' THEN 1 ELSE 0 END) * 100.0 / 
        NULLIF(SUM(CASE WHEN md.type = 'REGISTER' THEN 1 ELSE 0 END), 0), 2
    ) as register_to_charge_rate
FROM 
    ocpc_meta_data md
    LEFT JOIN ocpc_channel c ON md.ocpc_channel_id = c.id
WHERE 
    md.create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY 
    c.id, c.name
ORDER BY 
    charges DESC;
```

### 3. 查看收入统计

```sql
SELECT 
    DATE(md.create_time) as date,
    COUNT(*) as charge_count,
    SUM(md.event_value) as total_revenue,
    AVG(md.event_value) as avg_order_value
FROM 
    ocpc_meta_data md
WHERE 
    md.type = 'CHARGE'
    AND md.create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY 
    DATE(md.create_time)
ORDER BY 
    date DESC;
```

## 维护建议

### 1. 定期清理旧数据

```sql
-- 删除90天前的点击数据
DELETE FROM ocpc_meta_data 
WHERE 
    type = 'CLICK' 
    AND create_time < DATE_SUB(NOW(), INTERVAL 90 DAY);
```

### 2. 定期优化表

```sql
-- 分析表统计信息
ANALYZE TABLE ocpc_meta_token, ocpc_meta_data;

-- 优化表结构
OPTIMIZE TABLE ocpc_meta_token, ocpc_meta_data;
```

### 3. 监控表大小

```sql
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb,
    table_rows
FROM 
    information_schema.tables 
WHERE 
    table_schema = DATABASE() 
    AND table_name LIKE 'ocpc_meta_%'
ORDER BY 
    size_mb DESC;
```

## 故障排除

### 1. 外键约束错误

如果遇到外键约束错误，检查关联表是否存在：

```sql
-- 检查孤立的渠道关联
SELECT md.id, md.ocpc_channel_id 
FROM ocpc_meta_data md 
LEFT JOIN ocpc_channel c ON md.ocpc_channel_id = c.id 
WHERE md.ocpc_channel_id IS NOT NULL AND c.id IS NULL;
```

### 2. 性能问题

如果查询性能较差，检查索引使用情况：

```sql
-- 查看索引使用情况
SHOW INDEX FROM ocpc_meta_data;

-- 分析查询执行计划
EXPLAIN SELECT * FROM ocpc_meta_data WHERE type = 'CHARGE' AND create_time >= '2024-01-01';
```

## 注意事项

1. **备份数据**: 执行任何迁移脚本前，请先备份现有数据
2. **测试环境**: 建议先在测试环境中验证脚本
3. **权限管理**: 确保数据库用户有足够的权限执行这些操作
4. **监控性能**: 大量数据迁移可能影响性能，建议在低峰期执行
5. **数据一致性**: 迁移完成后验证数据完整性和一致性
