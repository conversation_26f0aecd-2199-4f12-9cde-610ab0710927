# TranslationUtils 翻译工具类使用指南

## 问题描述

在使用 `translateText` 方法时，遇到了以下问题：
- 原文：`测试房间0005 (zh)`
- 译文：`%E6%B5%8B%E8%AF%95%E6%88%BF%E9%97%B40005 (en)` （包含URL编码字符）

## 问题原因

1. **URL编码问题**：翻译API返回的结果包含URL编码字符，需要解码
2. **输入文本清理**：输入文本包含语言标识 `(zh)`，影响翻译质量
3. **缺乏验证机制**：没有验证翻译结果是否合理

## 修复方案

### 1. URL解码处理
```java
// 在 parseTranslationResponse 方法中添加URL解码
if (translatedText != null && translatedText.contains("%")) {
    try {
        String decodedText = URLDecoder.decode(translatedText, StandardCharsets.UTF_8.name());
        return decodedText;
    } catch (Exception decodeException) {
        log.warn("URL解码失败，使用原始结果: {}", decodeException.getMessage());
        return translatedText;
    }
}
```

### 2. 输入文本清理
```java
private String cleanInputText(String inputText) {
    if (inputText == null) {
        return "";
    }
    
    String cleaned = inputText.trim();
    
    // 移除语言标识，如 "(zh)", "(en)" 等
    cleaned = cleaned.replaceAll("\\([a-z]{2}\\)", "").trim();
    
    // 移除多余的空格
    cleaned = cleaned.replaceAll("\\s+", " ").trim();
    
    return cleaned;
}
```

### 3. 翻译结果验证
```java
private boolean isValidTranslation(String originalText, String translatedText, 
                                 LanguageEnum sourceLanguage, LanguageEnum targetLanguage) {
    if (translatedText == null || translatedText.trim().isEmpty()) {
        return false;
    }
    
    // 检查是否包含URL编码字符
    if (translatedText.contains("%")) {
        return false;
    }
    
    // 检查是否与原文完全相同（除了语言标识）
    String cleanedOriginal = cleanInputText(originalText);
    if (cleanedOriginal.equals(translatedText)) {
        return false;
    }
    
    // 检查翻译结果长度是否合理
    int originalLength = cleanedOriginal.length();
    int translatedLength = translatedText.length();
    
    if (translatedLength < originalLength * 0.5 || translatedLength > originalLength * 3) {
        return false;
    }
    
    return true;
}
```

### 4. 重试机制
```java
public String translateText(String originalText, LanguageEnum sourceLanguage, LanguageEnum targetLanguage) {
    return translateTextWithRetry(originalText, sourceLanguage, targetLanguage, 3);
}
```

## 使用示例

### 基本使用
```java
@Autowired
private TranslationUtils translationUtils;

// 翻译中文到英文
String result = translationUtils.translateText("测试房间0005 (zh)", LanguageEnum.CHINESE, LanguageEnum.ENGLISH);
// 预期结果：Test Room 0005

// 翻译英文到中文
String result2 = translationUtils.translateText("Test Room 0005 (en)", LanguageEnum.ENGLISH, LanguageEnum.CHINESE);
// 预期结果：测试房间0005
```

### 批量翻译
```java
// 翻译到多个语言
Map<String, String> translations = translationUtils.translateToMultipleLanguages(
    "测试房间0005", 
    LanguageEnum.CHINESE, 
    LanguageEnum.ENGLISH, 
    LanguageEnum.PORTUGUESE
);

// 翻译到所有支持的语言
I18nField i18nField = translationUtils.translateToAllSupportedLanguages(
    "测试房间0005", 
    LanguageEnum.CHINESE
);
```

## 修复效果

### 修复前
- 输入：`测试房间0005 (zh)`
- 输出：`%E6%B5%8B%E8%AF%95%E6%88%BF%E9%97%B40005 (en)` （URL编码字符）

### 修复后
- 输入：`测试房间0005 (zh)`
- 输出：`Test Room 0005` （正确的中文到英文翻译）

## 主要改进

1. **自动清理输入文本**：移除语言标识和多余空格
2. **URL解码处理**：自动处理翻译API返回的URL编码字符
3. **翻译结果验证**：确保翻译结果合理有效
4. **重试机制**：提高翻译成功率
5. **详细日志记录**：便于问题排查和监控

## 注意事项

1. **网络依赖**：翻译功能依赖Google翻译API，需要网络连接
2. **API限制**：Google翻译API有调用频率限制，建议合理使用
3. **错误处理**：翻译失败时会返回原文，确保系统稳定性
4. **字符编码**：使用UTF-8编码，支持多语言字符
