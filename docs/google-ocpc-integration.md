# Google OCPC 集成文档

## 概述

本文档描述了如何在现有的 OCPC 系统中集成 Google OCPC 功能。Google OCPC 支持通过 Google Analytics 4 Measurement Protocol 和 Google Ads 转化跟踪来实现用户行为的跟踪和转化优化。

## 新增功能

### 1. 数据库表

#### ocpc_google_token 表
存储 Google OCPC 配置信息：
- `conversion_id`: Google Ads 转化 ID
- `conversion_label`: Google Ads 转化标签
- `relm_name`: 域名
- `measurement_id`: Google Analytics 测量 ID
- `api_secret`: Google Analytics API 密钥

#### ocpc_google_data 表
存储 Google OCPC 数据：
- `type`: 数据类型（0-点击，1-注册，2-充值）
- `gclid`: Google Click ID
- `event_value`: 事件价值
- `ip`: 用户 IP
- 其他关联字段

### 2. 新增实体类

- `OcpcGoogleTokenEntity`: Google OCPC Token 实体
- `OcpcGoogleDataEntity`: Google OCPC 数据实体
- `OcpcGoogleDataType`: Google OCPC 数据类型枚举

### 3. 新增 Repository

- `OcpcGoogleTokenRepository`: Google Token 数据访问层
- `OcpcGoogleDataRepository`: Google 数据访问层

### 4. 服务层扩展

在 `OcpcService` 接口中新增方法：
- `sendRegisterToGoogle(String ip)`: 发送注册事件到 Google
- `addClickGoogleData(String ip)`: 添加点击数据
- `addRegisterGoogleData(String ip, Long userId)`: 添加注册数据
- `addChangeGoogleData(String ip, Long userId, Long orderChargeId)`: 添加充值数据

### 5. 消费者更新

`OcpcConsumer` 已更新以支持 Google OCPC：
- 检测 URL 中的 `gclid=` 参数来识别 Google 流量
- 根据事件类型调用相应的 Google OCPC 方法

### 6. 配置文件

在所有环境配置文件中添加了 Google OCPC 配置：
```yaml
ocpc:
  google:
    BASE_OCPC_URL: https://www.google-analytics.com/mp/collect?measurement_id=%s&api_secret=%s
    RETRY_TIMES: 3
```

### 7. 管理后台支持

新增管理接口：
- `GET /ocpc/google/token`: 查询 Google Token 配置
- `GET /ocpc/google/data`: 查询 Google OCPC 数据

## 使用方法

### 1. 配置 Google Token

在管理后台或直接在数据库中配置 Google OCPC Token：

```sql
INSERT INTO ocpc_google_token (conversion_id, conversion_label, relm_name, measurement_id, api_secret) 
VALUES ('AW-123456789', 'abcdefg', 'yourdomain.com', 'G-XXXXXXXXXX', 'your_api_secret_here');
```

### 2. URL 参数

确保推广链接包含 `gclid` 参数，例如：
```
https://yourdomain.com/landing?gclid=TeSter-123&other_params=value
```

### 3. 事件流程

1. **点击事件**: 用户点击广告时，系统检测到 `gclid` 参数并记录点击数据
2. **注册事件**: 用户注册时，系统发送转化事件到 Google Analytics 和 Google Ads
3. **充值事件**: 用户充值时，系统发送带有价值的转化事件

### 4. 数据查询

通过管理后台可以查询 Google OCPC 数据：
- 按类型筛选（点击/注册/充值）
- 按时间范围筛选
- 按渠道筛选
- 按 IP 地址筛选

## 技术实现

### Google Analytics 4 Measurement Protocol

使用 GA4 Measurement Protocol 发送事件：
```json
{
  "client_id": "gclid_value",
  "events": [{
    "name": "conversion",
    "params": {
      "send_to": "AW-123456789/abcdefg"
    }
  }]
}
```

### 重试机制

实现了自动重试机制，配置的重试次数为 3 次，确保数据传输的可靠性。

### 异步处理

所有 Google OCPC 请求都是异步处理，不会影响用户体验。

## 注意事项

1. 确保 Google Analytics 和 Google Ads 账户已正确配置
2. API Secret 需要从 Google Analytics 4 属性中获取
3. 转化 ID 和标签需要从 Google Ads 中获取
4. 测试时注意检查 Google 端的数据接收情况

## 故障排除

1. 检查配置文件中的 URL 格式是否正确
2. 验证 Google Token 配置是否完整
3. 查看日志中的错误信息
4. 确认网络连接到 Google 服务器正常
