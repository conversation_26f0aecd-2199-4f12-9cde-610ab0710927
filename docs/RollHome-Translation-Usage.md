# RollHome多语言翻译功能使用指南

## 概述

RollHome服务现在支持自动多语言翻译功能。当添加或更新Roll房时，如果多语言字段（`i18nFieldName`、`i18nFieldRemarks`）缺少某些语言的内容，而对应的中文字段（`name`、`remarks`）有内容，系统会自动使用翻译工具类进行翻译。

## 功能特性

### 1. 自动翻译触发条件

- **多语言字段为空**：如果 `i18nFieldName` 或 `i18nFieldRemarks` 为 null，系统会使用中文内容翻译到所有支持的语言
- **缺少特定语言**：如果多语言字段中缺少某些语言的内容（null 或空字符串），系统会自动翻译补充
- **中文内容变化**：如果中文内容发生变化，系统会重新翻译所有其他语言，即使它们已有内容
- **中文内容存在**：只有当对应的中文字段（`name`、`remarks`）有内容时才会进行翻译

### 2. 支持的方法

- `addRollHome(RollHomeAddDTO rollHomeAddDTO)` - 添加Roll房
- `addRollHomeByTask(RollHomeAddDTO rollHomeAddDTO)` - 通过任务添加Roll房
- `updateRollHome(RollHomeUpdateDTO rollHomeUpdateDTO)` - 更新Roll房

## 使用示例

### 示例1：添加Roll房 - 完全依赖翻译

```java
RollHomeAddDTO dto = new RollHomeAddDTO();
dto.setName("VIP专属房间");           // 中文名称
dto.setRemarks("仅限VIP用户参与");     // 中文备注
// 不设置 i18nFieldName 和 i18nFieldRemarks

// 调用服务
RollHomeFullVO result = rollHomeService.addRollHome(dto);

// 系统会自动翻译：
// - i18nFieldName: {"zh": "VIP专属房间", "en": "VIP Exclusive Room", "pt": "Sala Exclusiva VIP"}
// - i18nFieldRemarks: {"zh": "仅限VIP用户参与", "en": "VIP users only", "pt": "Apenas usuários VIP"}
```

### 示例2：添加Roll房 - 部分翻译

```java
RollHomeAddDTO dto = new RollHomeAddDTO();
dto.setName("高级竞技房");
dto.setRemarks("高手对决专用房间");

// 设置部分多语言内容
I18nField nameField = new I18nField();
nameField.put(LanguageEnum.ENGLISH, "Advanced Arena"); // 已有英文
// 缺少葡萄牙语

I18nField remarksField = new I18nField();
remarksField.put(LanguageEnum.PORTUGUESE, "Sala para especialistas"); // 已有葡萄牙语
// 缺少英文

dto.setI18nFieldName(nameField);
dto.setI18nFieldRemarks(remarksField);

// 调用服务
RollHomeFullVO result = rollHomeService.addRollHome(dto);

// 系统会自动补充缺失的翻译：
// - nameField 会添加葡萄牙语翻译
// - remarksField 会添加英文翻译
// - 中文内容会被设置为传入的 name 和 remarks
```

### 示例3：更新Roll房 - 智能翻译

```java
RollHomeUpdateDTO dto = new RollHomeUpdateDTO();
dto.setId(1L);
dto.setName("更新后的房间名称");
dto.setRemarks("更新后的房间描述");

// 原有多语言字段可能已经有一些内容
I18nField existingNameField = new I18nField();
existingNameField.put(LanguageEnum.CHINESE, "旧的房间名称"); // 原有中文
existingNameField.put(LanguageEnum.ENGLISH, "Old Room Name");
existingNameField.put(LanguageEnum.PORTUGUESE, "Nome da Sala Antiga");

dto.setI18nFieldName(existingNameField);
// 不设置 i18nFieldRemarks，让系统完全翻译

// 调用服务
rollHomeService.updateRollHome(dto);

// 系统处理结果：
// - nameField 检测到中文内容变化，重新翻译所有语言
//   {"zh": "更新后的房间名称", "en": "Updated Room Name", "pt": "Nome da Sala Atualizada"}
// - remarksField 从零开始翻译到所有语言
```

### 示例4：中文内容变化 - 重新翻译

```java
RollHomeUpdateDTO dto = new RollHomeUpdateDTO();
dto.setId(1L);
dto.setName("VIP至尊房间");  // 新的中文内容
dto.setRemarks("专为VIP用户打造的高级房间");

// 原有多语言字段已有完整翻译
I18nField existingNameField = new I18nField();
existingNameField.put(LanguageEnum.CHINESE, "普通VIP房间");  // 旧的中文内容
existingNameField.put(LanguageEnum.ENGLISH, "Regular VIP Room");
existingNameField.put(LanguageEnum.PORTUGUESE, "Sala VIP Regular");

I18nField existingRemarksField = new I18nField();
existingRemarksField.put(LanguageEnum.CHINESE, "VIP用户专用");  // 旧的中文内容
existingRemarksField.put(LanguageEnum.ENGLISH, "For VIP users only");
existingRemarksField.put(LanguageEnum.PORTUGUESE, "Apenas para usuários VIP");

dto.setI18nFieldName(existingNameField);
dto.setI18nFieldRemarks(existingRemarksField);

// 调用服务
rollHomeService.updateRollHome(dto);

// 系统检测到中文内容变化，重新翻译所有语言：
// nameField:
//   {"zh": "VIP至尊房间", "en": "VIP Supreme Room", "pt": "Sala VIP Suprema"}
// remarksField:
//   {"zh": "专为VIP用户打造的高级房间", "en": "Premium room designed for VIP users", "pt": "Sala premium projetada para usuários VIP"}
```

### 示例5：任务添加Roll房

```java
RollHomeAddDTO dto = new RollHomeAddDTO();
dto.setName("每日竞技房");
dto.setRemarks("每日定时开启的竞技房间");

// 系统任务通常不设置多语言字段，完全依赖翻译
RollHomeEntity result = rollHomeService.addRollHomeByTask(dto);

// 自动翻译结果会保存到数据库
```

## 翻译逻辑详解

### 1. 处理流程

```
1. 检查中文内容是否存在
   ├─ 如果为空 → 直接返回原多语言字段
   └─ 如果有内容 → 继续处理

2. 检查多语言字段状态
   ├─ 如果为null → 使用中文翻译到所有支持的语言
   └─ 如果存在 → 检查中文内容是否变化

3. 检查中文内容变化
   ├─ 获取原有中文内容
   ├─ 比较新旧中文内容
   └─ 记录是否发生变化

4. 逐个检查支持的语言
   ├─ 中文 → 直接使用传入的中文内容
   ├─ 其他语言 → 检查翻译条件
   │   ├─ 无内容 OR 中文内容变化 → 重新翻译
   │   └─ 有内容 AND 中文内容未变化 → 保留原内容

5. 返回处理后的多语言字段
   ├─ 如果有翻译操作 → 创建新的I18nField
   └─ 如果无翻译操作 → 只更新中文内容
```

### 2. 支持的语言

当前支持的语言包括：
- 中文 (zh)
- 英文 (en)  
- 葡萄牙语 (pt)
- 其他语言（根据 LanguageEnum.getSupportedLanguages() 动态获取）

### 3. 翻译源语言

- **固定为中文**：系统假定 `name` 和 `remarks` 字段的内容是中文
- **翻译方向**：中文 → 其他支持的语言

## 错误处理

### 1. 翻译失败处理

- 如果翻译服务不可用或翻译失败，系统会记录日志但不会抛出异常
- 翻译失败的语言会使用原文（中文）作为备用内容

### 2. 参数验证

- 中文内容为 null 或空字符串时，不会进行翻译
- 多语言字段为 null 时，会创建新的 I18nField 对象

### 3. 日志记录

系统会记录以下日志：
- 多语言字段为空时的翻译操作
- 中文内容变化时的重新翻译操作
- 缺少特定语言内容时的翻译操作
- 翻译完成的确认信息

```
INFO - 多语言字段为空，使用中文内容进行翻译: VIP专属房间
INFO - 中文内容发生变化: '旧房间名称' -> '新房间名称'，将重新翻译所有语言
INFO - 语言 en 缺少内容，进行翻译: VIP专属房间 -> ?
INFO - 中文内容变化，重新翻译语言 en: 新房间名称 -> ?
INFO - 完成多语言字段重新翻译，中文内容变化导致所有语言更新
INFO - 完成多语言字段翻译，更新了缺失的语言内容
```

## 性能考虑

### 1. 翻译调用优化

- 只有在确实缺少内容时才会调用翻译服务
- 已有内容的语言不会重复翻译
- 批量翻译减少网络请求次数

### 2. 建议

- 在前端尽量提供完整的多语言内容，减少服务端翻译
- 对于重要内容，建议人工翻译而不是依赖机器翻译
- 定期检查翻译质量，必要时进行人工校正

## 配置要求

确保以下配置正确：

1. **TranslationUtils 注入**：
```java
@Autowired
private TranslationUtils translationUtils;
```

2. **RestTemplate 配置**：
```java
@Bean
public RestTemplate restTemplate() {
    return new RestTemplate();
}
```

3. **网络连接**：确保服务器可以访问Google翻译API

## 测试

运行测试以验证功能：

```bash
mvn test -Dtest=RollHomeServiceTranslationTest
```

测试覆盖了各种翻译场景，包括：
- 多语言字段为空的情况
- 部分语言缺失的情况
- 所有语言都存在的情况
- 翻译失败的处理

## 注意事项

1. **翻译质量**：机器翻译可能不够准确，重要内容建议人工校验
2. **网络依赖**：功能依赖于外部翻译服务，网络问题可能影响翻译
3. **性能影响**：翻译是网络请求，会增加响应时间
4. **数据一致性**：确保中文字段和多语言字段的中文内容保持一致
