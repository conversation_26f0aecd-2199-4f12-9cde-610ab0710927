# OCPC测试Controller使用指南

## 概述

OCPC测试Controller (`OcpcTestController`) 是一个专门用于测试OCPC（Optimized Cost Per Click）功能的控制器，提供了全面的测试接口来验证各个平台（百度、Google、Meta、360）的OCPC功能。

## 功能特性

### 1. 基础测试功能
- **健康检查**: 验证服务状态
- **配置测试**: 检查所有OCPC配置
- **渠道测试**: 验证渠道配置
- **Token测试**: 验证各平台Token配置

### 2. 数据查询测试
- **百度数据查询测试**: 测试百度OCPC数据查询功能
- **Google数据查询测试**: 测试Google OCPC数据查询功能
- **Meta数据查询测试**: 测试Meta OCPC数据查询功能

### 3. 高级测试功能
- **自定义测试场景**: 支持自定义测试参数和场景
- **性能压力测试**: 并发性能测试
- **数据一致性测试**: 验证数据一致性
- **异常场景测试**: 测试各种异常情况处理

### 4. 批量测试功能
- **批量数据查询测试**: 同时测试多个平台
- **模拟数据上报测试**: 模拟各平台数据上报
- **配置完整性检查**: 全面检查配置完整性

## API接口说明

### 基础测试接口

#### 1. 健康检查
```http
GET /ocpc/test/health
```

**响应示例:**
```json
{
  "code": 200,
  "data": {
    "status": "UP",
    "timestamp": 1703123456789,
    "service": "OCPC Test Controller",
    "version": "1.0.0",
    "message": "OCPC测试服务运行正常"
  }
}
```

#### 2. 测试所有OCPC配置
```http
GET /ocpc/test/all-config
```

**响应示例:**
```json
{
  "code": 200,
  "data": {
    "channels": [...],
    "channelCount": 5,
    "baiduTokens": [...],
    "baiduTokenCount": 3,
    "googleTokenCount": 2,
    "metaTokenCount": 1,
    "testStatus": "SUCCESS",
    "message": "所有OCPC配置测试完成"
  }
}
```

#### 3. 测试渠道配置
```http
GET /ocpc/test/channels
```

#### 4. 测试Token配置
```http
GET /ocpc/test/tokens
```

### 数据查询测试接口

#### 1. 测试百度OCPC数据查询
```http
GET /ocpc/test/baidu/data?size=10&page=0&ocpcChannelId=1
```

#### 2. 测试Google OCPC数据查询
```http
GET /ocpc/test/google/data?size=10&page=0
```

#### 3. 测试Meta OCPC数据查询
```http
GET /ocpc/test/meta/data?size=10&page=0
```

### 高级测试接口

#### 1. 批量测试所有平台数据查询
```http
POST /ocpc/test/batch/data-query
Content-Type: application/json

{
  "pageSize": 10,
  "page": 0
}
```

#### 2. 模拟数据上报测试
```http
POST /ocpc/test/simulate/data-upload
Content-Type: application/json

{
  "platform": "baidu",
  "testType": "conversion"
}
```

#### 3. 自定义测试场景
```http
POST /ocpc/test/advanced/custom
Content-Type: application/json

{
  "testType": "custom",
  "platform": "baidu",
  "channelId": 1,
  "testData": {
    "keyword": "测试关键词",
    "conversionValue": 100.00
  },
  "testParams": {
    "timeout": 30000,
    "retryTimes": 3,
    "environment": "test"
  }
}
```

#### 4. 性能压力测试
```http
POST /ocpc/test/performance/stress
Content-Type: application/json

{
  "concurrency": 10,
  "totalRequests": 100,
  "platform": "baidu"
}
```

#### 5. 数据一致性测试
```http
POST /ocpc/test/consistency/data
Content-Type: application/json

{
  "platform": "all",
  "sampleSize": 50
}
```

#### 6. 异常场景测试
```http
POST /ocpc/test/exception/scenarios
Content-Type: application/json

{
  "scenarios": ["timeout", "invalid_token", "network_error", "invalid_data"]
}
```

#### 7. 配置完整性检查
```http
GET /ocpc/test/config/integrity
```

## 测试数据结构

### OcpcTestDTO
```java
{
  "testType": "string",           // 测试类型
  "platform": "string",          // 平台类型 (baidu/google/meta/360)
  "channelId": "long",           // 渠道ID
  "userId": "long",              // 用户ID
  "orderId": "long",             // 订单ID
  "testData": {},                // 测试数据
  "batchTest": "boolean",        // 批量测试标识
  "testCount": "integer",        // 测试数量
  "startTime": "datetime",       // 开始时间
  "endTime": "datetime",         // 结束时间
  "testParams": {                // 测试参数
    "timeout": "long",           // 超时时间(毫秒)
    "retryTimes": "integer",     // 重试次数
    "simulateFailure": "boolean", // 是否模拟失败
    "delay": "long",             // 延迟时间(毫秒)
    "environment": "string"      // 测试环境
  }
}
```

### OcpcTestResultVO
```java
{
  "testId": "string",            // 测试ID
  "testType": "string",          // 测试类型
  "platform": "string",         // 平台类型
  "status": "string",            // 测试状态
  "success": "boolean",          // 成功标识
  "startTime": "datetime",       // 开始时间
  "endTime": "datetime",         // 结束时间
  "duration": "long",            // 执行时长(毫秒)
  "testData": {},                // 测试数据
  "responseData": {},            // 响应数据
  "performanceMetrics": {        // 性能指标
    "responseTime": "long",      // 响应时间(毫秒)
    "throughput": "double",      // 吞吐量(QPS)
    "successRate": "double",     // 成功率
    "errorRate": "double"        // 错误率
  }
}
```

## 使用示例

### 1. 基础健康检查
```bash
curl -X GET "http://localhost:5000/ocpc/test/health"
```

### 2. 测试所有配置
```bash
curl -X GET "http://localhost:5000/ocpc/test/all-config"
```

### 3. 执行压力测试
```bash
curl -X POST "http://localhost:5000/ocpc/test/performance/stress" \
  -H "Content-Type: application/json" \
  -d '{
    "concurrency": 5,
    "totalRequests": 50,
    "platform": "baidu"
  }'
```

### 4. 自定义测试
```bash
curl -X POST "http://localhost:5000/ocpc/test/advanced/custom" \
  -H "Content-Type: application/json" \
  -d '{
    "testType": "conversion",
    "platform": "baidu",
    "testData": {
      "keyword": "游戏皮肤",
      "conversionValue": 99.99
    }
  }'
```

## 注意事项

1. **测试环境**: 建议在测试环境中使用，避免影响生产数据
2. **并发限制**: 压力测试时注意并发数设置，避免对系统造成过大压力
3. **数据安全**: 测试数据不包含真实用户信息
4. **日志记录**: 所有测试操作都会记录详细日志
5. **错误处理**: 测试失败时会返回详细的错误信息

## 扩展功能

该测试Controller支持扩展，可以根据需要添加新的测试场景和功能：

1. **新平台支持**: 添加新的广告平台测试
2. **自定义验证规则**: 扩展数据验证逻辑
3. **报告生成**: 生成详细的测试报告
4. **监控集成**: 集成监控系统进行实时监控

## 故障排除

### 常见问题

1. **配置测试失败**: 检查数据库连接和OCPC相关表数据
2. **Token验证失败**: 确认各平台Token配置正确
3. **压力测试超时**: 调整超时时间和并发数
4. **数据查询异常**: 检查查询参数和数据权限

### 日志查看

测试过程中的详细日志可以在应用日志中查看：
```bash
tail -f /path/to/logs/application.log | grep "OCPC"
```
